# [YooKassa API SDK](../home.md)

# Class: \YooKassa\Model\PersonalData\PersonalDataType
### Namespace: [\YooKassa\Model\PersonalData](../namespaces/yookassa-model-personaldata.md)
---
**Summary:**

Класс, представляющий модель PersonalDataType.

**Description:**

Тип персональных данных — цель, для которой вы будете использовать данные.
Возможное значение:
- `sbp_payout_recipient` — [выплаты с проверкой получателя](/developers/payouts/scenario-extensions/recipient-check)(только для выплат через СБП);
- `payout_statement_recipient` — [выплаты с передачей данных получателя выплаты для выписок из реестра](/developers/payouts/scenario-extensions/recipient-data-send).

---
### Constants
| Visibility | Name | Flag | Summary |
| ----------:| ---- | ---- | ------- |
| public | [PAYOUT_STATEMENT_RECIPIENT](../classes/YooKassa-Model-PersonalData-PersonalDataType.md#constant_PAYOUT_STATEMENT_RECIPIENT) |  | Выплаты с передачей данных получателя |
| public | [SBP_PAYOUT_RECIPIENT](../classes/YooKassa-Model-PersonalData-PersonalDataType.md#constant_SBP_PAYOUT_RECIPIENT) |  | Выплаты с проверкой получателя |

---
### Properties
| Visibility | Name | Flag | Summary |
| ----------:| ---- | ---- | ------- |
| protected | [$validValues](../classes/YooKassa-Model-PersonalData-PersonalDataType.md#property_validValues) |  |  |

---
### Methods
| Visibility | Name | Flag | Summary |
| ----------:| ---- | ---- | ------- |
| public | [getEnabledValues()](../classes/YooKassa-Common-AbstractEnum.md#method_getEnabledValues) |  | Возвращает значения в enum'е значения которых разрешены. |
| public | [getValidValues()](../classes/YooKassa-Common-AbstractEnum.md#method_getValidValues) |  | Возвращает все значения в enum'e. |
| public | [valueExists()](../classes/YooKassa-Common-AbstractEnum.md#method_valueExists) |  | Проверяет наличие значения в enum'e. |

---
### Details
* File: [lib/Model/PersonalData/PersonalDataType.php](../../lib/Model/PersonalData/PersonalDataType.php)
* Package: YooKassa\Model
* Class Hierarchy: 
  * [\YooKassa\Common\AbstractEnum](../classes/YooKassa-Common-AbstractEnum.md)
  * \YooKassa\Model\PersonalData\PersonalDataType

* See Also:
  * [](https://yookassa.ru/developers/api)

---
### Tags
| Tag | Version | Description |
| --- | ------- | ----------- |
| category |  | Class |
| author |  | <EMAIL> |

---
## Constants
<a name="constant_PAYOUT_STATEMENT_RECIPIENT" class="anchor"></a>
###### PAYOUT_STATEMENT_RECIPIENT
Выплаты с передачей данных получателя

```php
PAYOUT_STATEMENT_RECIPIENT = 'payout_statement_recipient'
```


<a name="constant_SBP_PAYOUT_RECIPIENT" class="anchor"></a>
###### SBP_PAYOUT_RECIPIENT
Выплаты с проверкой получателя

```php
SBP_PAYOUT_RECIPIENT = 'sbp_payout_recipient'
```



---
## Properties
<a name="property_validValues"></a>
#### protected $validValues : array
---
**Type:** <a href="../array"><abbr title="array">array</abbr></a>
Массив принимаемых enum&#039;ом значений
**Details:**



---
## Methods
<a name="method_getEnabledValues" class="anchor"></a>
#### public getEnabledValues() : string[]

```php
Static public getEnabledValues() : string[]
```

**Summary**

Возвращает значения в enum'е значения которых разрешены.

**Details:**
* Inherited From: [\YooKassa\Common\AbstractEnum](../classes/YooKassa-Common-AbstractEnum.md)

**Returns:** string[] - Массив разрешённых значений


<a name="method_getValidValues" class="anchor"></a>
#### public getValidValues() : array

```php
Static public getValidValues() : array
```

**Summary**

Возвращает все значения в enum'e.

**Details:**
* Inherited From: [\YooKassa\Common\AbstractEnum](../classes/YooKassa-Common-AbstractEnum.md)

**Returns:** array - Массив значений в перечислении


<a name="method_valueExists" class="anchor"></a>
#### public valueExists() : bool

```php
Static public valueExists(mixed $value) : bool
```

**Summary**

Проверяет наличие значения в enum'e.

**Details:**
* Inherited From: [\YooKassa\Common\AbstractEnum](../classes/YooKassa-Common-AbstractEnum.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">mixed</code> | value  | Проверяемое значение |

**Returns:** bool - True если значение имеется, false если нет



---

### Top Namespaces

* [\YooKassa](../namespaces/yookassa.md)

---

### Reports
* [Errors - 0](../reports/errors.md)
* [Markers - 0](../reports/markers.md)
* [Deprecated - 25](../reports/deprecated.md)

---

This document was automatically generated from source code comments on 2024-07-02 using [phpDocumentor](http://www.phpdoc.org/)

&copy; 2024 YooMoney