<?php

namespace WhichBrowser\Data;

DeviceModels::$KDDI_INDEX = array (
  '@CA' => 
  array (
    0 => 'CA11',
    1 => 'CA12',
    2 => 'CA13',
    3 => 'CA14',
    4 => 'CA21',
    5 => 'CA22',
    6 => 'CA23',
    7 => 'CA24',
    8 => 'CA25',
    9 => 'CA26',
    10 => 'CA27',
    11 => 'CA28',
    12 => 'CA31',
    13 => 'CA32',
    14 => 'CA33',
    15 => 'CA34',
    16 => 'CA35',
    17 => 'CA36',
    18 => 'CA37',
    19 => 'CA38',
    20 => 'CA39',
    21 => 'CA3A',
    22 => 'CA3B',
    23 => 'CA3C',
    24 => 'CA3D',
    25 => 'CA3E',
    26 => 'CA3F',
    27 => 'CA3G',
    28 => 'CA3H',
    29 => 'CA3I',
    30 => 'CA3J',
    31 => 'CA3K',
  ),
  '@DN' => 
  array (
    0 => 'DN01',
    1 => 'DN11',
  ),
  '@ER' => 
  array (
    0 => 'ERK0',
  ),
  '@FJ' => 
  array (
    0 => 'FJ31',
  ),
  '@HI' => 
  array (
    0 => 'HI01',
    1 => 'HI02',
    2 => 'HI11',
    3 => 'HI12',
    4 => 'HI13',
    5 => 'HI14',
    6 => 'HI21',
    7 => 'HI23',
    8 => 'HI24',
    9 => 'HI31',
    10 => 'HI32',
    11 => 'HI33',
    12 => 'HI34',
    13 => 'HI35',
    14 => 'HI36',
    15 => 'HI37',
    16 => 'HI38',
    17 => 'HI39',
    18 => 'HI3A',
    19 => 'HI3B',
    20 => 'HI3C',
    21 => 'HI3D',
    22 => 'HI3E',
    23 => 'HI3F',
    24 => 'HI3G',
    25 => 'HI3H',
  ),
  '@KC' => 
  array (
    0 => 'KC3Q',
    1 => 'KC3S',
    2 => 'KC3V',
    3 => 'KC11',
    4 => 'KC12',
    5 => 'KC13',
    6 => 'KC14',
    7 => 'KC15',
    8 => 'KC21',
    9 => 'KC22',
    10 => 'KC23',
    11 => 'KC24',
    12 => 'KC25',
    13 => 'KC26',
    14 => 'KC27',
    15 => 'KC28',
    16 => 'KC29',
    17 => 'KC2A',
    18 => 'KC31',
    19 => 'KC32',
    20 => 'KC33',
    21 => 'KC34',
    22 => 'KC35',
    23 => 'KC36',
    24 => 'KC37',
    25 => 'KC38',
    26 => 'KC39',
    27 => 'KC3A',
    28 => 'KC3B',
    29 => 'KC3D',
    30 => 'KC3E',
    31 => 'KC3G',
    32 => 'KC3H',
    33 => 'KC3I',
    34 => 'KC3K',
    35 => 'KC3M',
    36 => 'KC3N',
    37 => 'KC3O',
    38 => 'KC3P',
    39 => 'KC3R',
    40 => 'KC3T',
    41 => 'KC3U',
    42 => 'KC3W',
    43 => 'KC3X',
    44 => 'KC3Y',
    45 => 'KC3Z',
    46 => 'KC41',
    47 => 'KC42',
    48 => 'KC43',
    49 => 'KC44',
    50 => 'KC45',
    51 => 'KC46',
    52 => 'KC47',
    53 => 'KC48',
    54 => 'KC4A',
    55 => 'KCC1',
    56 => 'KCC2',
    57 => 'KCI1',
    58 => 'KCI2',
    59 => 'KCT1',
    60 => 'KCT2',
    61 => 'KCT3',
    62 => 'KCT4',
    63 => 'KCT5',
    64 => 'KCT6',
    65 => 'KCT7',
    66 => 'KCT8',
    67 => 'KCT9',
    68 => 'KCTA',
    69 => 'KCTB',
    70 => 'KCTC',
  ),
  '@MA' => 
  array (
    0 => 'MA11',
    1 => 'MA12',
    2 => 'MA13',
    3 => 'MA21',
    4 => 'MA31',
    5 => 'MA32',
    6 => 'MA33',
    7 => 'MA34',
    8 => 'MA35',
    9 => 'MAC1',
    10 => 'MAC2',
    11 => 'MAI1',
    12 => 'MAI2',
    13 => 'MAT1',
    14 => 'MAT2',
    15 => 'MAT3',
  ),
  '@MI' => 
  array (
    0 => 'MIT1',
  ),
  '@PT' => 
  array (
    0 => 'PT21',
    1 => 'PT23',
    2 => 'PT33',
    3 => 'PT34',
    4 => 'PT35',
    5 => 'PT36',
  ),
  '@SA' => 
  array (
    0 => 'SA21',
    1 => 'SA22',
    2 => 'SA24',
    3 => 'SA25',
    4 => 'SA26',
    5 => 'SA27',
    6 => 'SA28',
    7 => 'SA29',
    8 => 'SA2A',
    9 => 'SA31',
    10 => 'SA32',
    11 => 'SA33',
    12 => 'SA34',
    13 => 'SA35',
    14 => 'SA36',
    15 => 'SA37',
    16 => 'SA38',
    17 => 'SA39',
    18 => 'SA3A',
    19 => 'SA3B',
    20 => 'SA3C',
    21 => 'SA3D',
    22 => 'SA3E',
  ),
  '@SH' => 
  array (
    0 => 'SH31',
    1 => 'SH32',
    2 => 'SH33',
    3 => 'SH34',
    4 => 'SH35',
    5 => 'SH36',
    6 => 'SH37',
    7 => 'SH38',
    8 => 'SH39',
    9 => 'SH3A',
    10 => 'SH3B',
    11 => 'SH3C',
    12 => 'SH3D',
    13 => 'SH3E',
    14 => 'SH3F',
    15 => 'SH3G',
    16 => 'SH3H',
    17 => 'SH3I',
    18 => 'SH3J',
    19 => 'SH3K',
    20 => 'SH3L',
  ),
  '@SN' => 
  array (
    0 => 'SN3K',
    1 => 'SN3T',
    2 => 'SN11',
    3 => 'SN13',
    4 => 'SN14',
    5 => 'SN15',
    6 => 'SN16',
    7 => 'SN17',
    8 => 'SN21',
    9 => 'SN22',
    10 => 'SN23',
    11 => 'SN24',
    12 => 'SN25',
    13 => 'SN26',
    14 => 'SN27',
    15 => 'SN28',
    16 => 'SN29',
    17 => 'SN31',
    18 => 'SN32',
    19 => 'SN33',
    20 => 'SN34',
    21 => 'SN35',
    22 => 'SN36',
    23 => 'SN37',
    24 => 'SN38',
    25 => 'SN39',
    26 => 'SN3A',
    27 => 'SN3B',
    28 => 'SN3C',
    29 => 'SN3D',
    30 => 'SN3E',
    31 => 'SN3F',
    32 => 'SN3G',
    33 => 'SN3H',
    34 => 'SN3I',
    35 => 'SN3J',
    36 => 'SN3L',
    37 => 'SN3M',
    38 => 'SN3N',
    39 => 'SN3O',
    40 => 'SN3P',
    41 => 'SN3Q',
    42 => 'SN3R',
    43 => 'SN3S',
    44 => 'SN3U',
    45 => 'SN3V',
  ),
  '@ST' => 
  array (
    0 => 'ST22',
    1 => 'ST33',
    2 => 'ST11',
    3 => 'ST12',
    4 => 'ST13',
    5 => 'ST14',
    6 => 'ST21',
    7 => 'ST23',
    8 => 'ST24',
    9 => 'ST25',
    10 => 'ST26',
    11 => 'ST27',
    12 => 'ST28',
    13 => 'ST29',
    14 => 'ST2A',
    15 => 'ST2C',
    16 => 'ST2D',
    17 => 'ST31',
    18 => 'ST32',
    19 => 'ST34',
  ),
  '@SY' => 
  array (
    0 => 'SY12',
    1 => 'SY13',
    2 => 'SY14',
    3 => 'SY15',
    4 => 'SYC1',
    5 => 'SYT1',
    6 => 'SYT2',
    7 => 'SYT3',
    8 => 'SYT4',
    9 => 'SY01',
    10 => 'SY02',
    11 => 'SY03',
    12 => 'SY11',
  ),
  '@TS' => 
  array (
    0 => 'TS3Q',
    1 => 'TS3U',
    2 => 'TS3W',
    3 => 'TS01',
    4 => 'TS11',
    5 => 'TS12',
    6 => 'TS13',
    7 => 'TS14',
    8 => 'TS21',
    9 => 'TS22',
    10 => 'TS23',
    11 => 'TS24',
    12 => 'TS25',
    13 => 'TS26',
    14 => 'TS27',
    15 => 'TS28',
    16 => 'TS29',
    17 => 'TS2A',
    18 => 'TS2B',
    19 => 'TS2C',
    20 => 'TS2D',
    21 => 'TS2E',
    22 => 'TS31',
    23 => 'TS32',
    24 => 'TS33',
    25 => 'TS34',
    26 => 'TS35',
    27 => 'TS36',
    28 => 'TS37',
    29 => 'TS38',
    30 => 'TS39',
    31 => 'TS3A',
    32 => 'TS3B',
    33 => 'TS3C',
    34 => 'TS3D',
    35 => 'TS3E',
    36 => 'TS3G',
    37 => 'TS3H',
    38 => 'TS3I',
    39 => 'TS3J',
    40 => 'TS3K',
    41 => 'TS3L',
    42 => 'TS3M',
    43 => 'TS3N',
    44 => 'TS3O',
    45 => 'TS3P',
    46 => 'TS3R',
    47 => 'TS3S',
    48 => 'TS3T',
    49 => 'TS3V',
    50 => 'TS3X',
    51 => 'TS3Y',
    52 => 'TS3Z',
    53 => 'TS41',
    54 => 'TSC1',
    55 => 'TSI1',
    56 => 'TST1',
    57 => 'TST2',
    58 => 'TST3',
    59 => 'TST4',
    60 => 'TST5',
    61 => 'TST6',
    62 => 'TST7',
  ),
);
