{"__meta": {"id": "X23ea8ab6efa1b023a746e60459e93b5a", "datetime": "2025-06-08 16:24:01", "utime": **********.674419, "method": "GET", "uri": "/customer/check/delivery?customer_id=7", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.130484, "end": **********.674443, "duration": 0.5439589023590088, "duration_str": "544ms", "measures": [{"label": "Booting", "start": **********.130484, "relative_start": 0, "end": **********.60241, "relative_end": **********.60241, "duration": 0.4719259738922119, "duration_str": "472ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.602422, "relative_start": 0.471937894821167, "end": **********.674446, "relative_end": 3.0994415283203125e-06, "duration": 0.07202410697937012, "duration_str": "72.02ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44025024, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.02169, "accumulated_duration_str": "21.69ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.6390939, "duration": 0.02044, "duration_str": "20.44ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 94.237}, {"sql": "select * from `customers` where `customers`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.664178, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 94.237, "width_percent": 5.763}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-213371632 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-213371632\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1525578015 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1525578015\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1685229625 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1685229625\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1588506954 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=u1db3a%7C1749399485619%7C2%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImxwcEZncjlNRWl2aUZoaTlXbGpWNHc9PSIsInZhbHVlIjoiNmdORGJQVFE4aEJxTnhWNlpGa2d3OUFWT0lLdkltYlR6OTBXeTlab0NzNWJkaUg5akRGeHNwaXBISU1sTTRVRm9CTG8zQmlyZmgyYkdyMkxCNGdIaUNaRURIUjNXaDZYU09zem5pTSs3YmY5ZVBrcVpHS3pqOEdWKzVObFR0M09QSjhZN1dVUzBxckZyc0pETW5hVVMzZVN3K0oyeFJiS2ZhdVZoTyt0Mm5SVmpNSmJPeWtPNjVYUkRsL2tEYVRrMFk0TnpDaEVFSmlmRjN1bCs2aUI5bWIzWWtXMU1FTmxhQUZMelMxdDBJWHdiV2poVnhCekRtelNoUzIyT2dXUWpGZVRUdzFkL1BSVUQwbU90TjJZMGFXTHVMaTZQT1UwMjJMYXJnaU9Gb3UzeUsweGtCWGxLbXlHUmNOTmd5bEZ1UFRxVnRoN1lndzR0UWhmMlkvSFBnUkVSdm1wSnU2TStoMmsvL28wcWhtMW1wQUJSYXhHZkhNZ0RQdFJzMGFMeXZlL0hFeE9pZHdGTTFDdkFxNnFmMmJkZWZ6UFhvcHl4Z08wRDlJU3ZNT0ZveTJPc3NhN0ZBckdSVk4zQkdydlhXNDhNTjlCT0Uzd3VRbEVZVzlpMUhWaldEdktLcHZiM0Z1UFQ5VVFqT0NTbStNcXBJTUFwRGxwS0hjYWJwSnIiLCJtYWMiOiJjZTY4NTdjNDJkNzgxMGNlYzFiMDdhNjAyMmRlZmU5ZmJjMzExNTgzMjUyYzY3YzQ2ZmU5YWYzOGEwMzUwYjAyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Inozamp3OXh1aUZzbDJuTVZ1U2pRTHc9PSIsInZhbHVlIjoiRjV2MkZFTDZ2aFM1b2ZQWUtEQmNtUWY5Y0xTV0xBV2hFWHBpNVlTalZQTWozdjg4WmM3NVBuMk5kcmZWQWRYR3ZhR0RIWjBEK3NFblBlSUpHUXQ3V3BYMkdLZ0VNZ2gvdUVnVmZwdHZOOEVjUno4OEU2azNGK2tLYmRnTENTMm9SQUlmNEhjOXdjYThieEl1Q3Y4RDJraXdlK3p2K1FIOU9IdE5SazRndkIzR00zN2xBMFpBZnNMMWg2bk1ZWVB0OVlQOXNvTktsb0VQU3NxWitDVlgrQlVud1YzcEZtbVRVUHZTekxsQTZERWNoTndUMURFcjFKRS93YlQrRmpuWjlHbW1odEZxZjJ3dHd3cEduNkhvbU9LcHdUMTVYWXp2R2dHclNVa05idXd1aFFpR1ZKOTE1SE52NzFVMlhFZHZWTzg0ZUc4TzRkbXU4UWl6VzM0RlFuZFJuNHlrVS9hZFZIRG1rYWlsYnB6WGRtcTNDUkoxcUNGelNWVUt4WHI5ZCt6Mk05b1N2SldaeHV4cTlIMERySlBDTllTak5FRzNCN1hsQ0RYbmZwMHlaRjZiT2o2bE1wNXJURFRXNTJaRlFxaUI2NlAwcEErRFBLSitkZUxlekZvVFRNeWhCS21PMVVzdy82TUp3K3BUNWlNbzZKZWhRMitPcjJwNm5xMnYiLCJtYWMiOiI1OTU5NTgyNjNkNTYxNGU1YzlmMGZhZjIwZGE2YTM1ZDNlNGM5OTk0YTUwZTgwYmI5Mzk1ZTNhMDc4OGUwZWZlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1588506954\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1636105861 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xNV7SvMX3AgbiuDVO62eNXfryfZD4KbWr3rEIkXt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1636105861\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1664164272 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 16:24:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IktSSkRnZVNGbWt4UDlZNXIzUFRjWXc9PSIsInZhbHVlIjoiMjAxNVQrZ1ZYSjc0V0V1ejZrdHZtdldHUFFNOHBjU2tDM0hBZ2Fac2kwdThreWtPUDNzMDFrNU1uUGsrSlNOL2c1VVFzQ3E4TFpQclB1aWc3Yyt6L1A0T2QwYnZ0T0FZMDJUSEszNHJqVTF4cmNaZXB5UU0wK0wyYVdQcEw4VWMzZmtqeEh2Y2R2SnY2NnVuUnp1eEgyYk1qemVsZkE3QkliUCtmRjF3eHBZK2g2eUNrNlJhd2JFM3diNG9rclVjMGFTWHVXeHVya1NWYjUyUDBHSEg0V1BYdHd2RkVmQW1vbStVVzI1aEk3UTJmeU5OK0hiSFpJTm1lQmh2TXNscG9KZi8xTnY3LzZMUVZSVjFqS1pCMTd1OVFMdnJRREx5eTUwa09VVmFLQ2pSZE5EL09TSEJZUEV6c2hRd2dxR25iYWFEVGFSSG1BQnM2MExxcStYNXNlVUk2ZHhMYzQ0SGNiTlZFNlNDNThzZlc3blIvSlI0cjJTdHJSdEhHTForVUdGVjVHdjNwN0t0SHRDWkJrbXpSeFF2T0VIWUxIajIrWit3TENGbm5hMkhoczVtREpWUHJhVjVLUXR0Z3kyNE5raTg2c2RwbHB3Tzd1dTZpNEl6SC9HNVpSWlE2dW5MZVdIV29NK09NL1NLMldaSHY4N0paU1B0UmRaUkdHSHQiLCJtYWMiOiI3NzRhYzIyNzhlZWU3OGY0OGRiNzJkNzEwZGQ3YTM3ODg4Yzc1MzI5MTY2Y2M0YmRkZTNiNjNlYjQ0MDI5OTAzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 18:24:01 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkFObU1SeXd5bUtldFBYekliZVFiOEE9PSIsInZhbHVlIjoidERCckdqZUJWSEw3Yk9ld0dkOVRPNDJFS015TS9CaU9HUGNpNkFsWlhiV3djMXQ4T3U3S1UwbktveWJxNXZlRjZwTnl4V2hUQXpVMFFDRmJ6TC93RmJYM29ydTg4aTNLckp4VDRsK0U4MXpuN2RWMDl1Z3RvcnhlTHd5eUlyWlkvakdCS3FrMHBieWt3aHo3cGlvTVZuTCtaZVJPTmpjUEdMSGF0WUFFdEF6Q1psY1lvZUJCMDlkdGFGdGtkRFZRdzd1YzlIQ1JLdFFPQzhQZVNpckZ3WnptcEl2a0VRc08yajBpNnlkTkdHWUd2NjhkQVdjU3plb293RE1GaTJWOU5EUlRHZzFmMklZSFRLcW9JdTNrSFpBKzc0MWdGTG9PL2REYldsMldZdUE3QmlGUzdNaFU2T2t3N2ZDeEVaY3R0U05QWGV1WDZyc2VNTFR5OVhKbTZnVTBvSE81N29nK0pjdlNtTyt5d09Xc1U1V3NhODV3cXBBNk9QMCtzYnBTaXFFU1k3aUE4NEY1aU9EU3lJK2djWlp5R2FadnBKaWV1Vm9INzVMcisxRStuWUhDaXVXUGQ2QjFLbVJFN0pSMFo1a1FVN2pGMklkMG5iNlFSazZxTVp5QWRvcWJtZThJbHU2RTgzMlN6T2sxcjlVNXRnaFZyT2xSV0V6VC9GbksiLCJtYWMiOiJmYWEzN2IzZGVjNzE5MzFjN2IyOGE3NWY5NDQzYTAzODNkM2NjMmM2NTFkYWQ0MGFiNWZkMDhhN2U2YjlkNTQxIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 18:24:01 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IktSSkRnZVNGbWt4UDlZNXIzUFRjWXc9PSIsInZhbHVlIjoiMjAxNVQrZ1ZYSjc0V0V1ejZrdHZtdldHUFFNOHBjU2tDM0hBZ2Fac2kwdThreWtPUDNzMDFrNU1uUGsrSlNOL2c1VVFzQ3E4TFpQclB1aWc3Yyt6L1A0T2QwYnZ0T0FZMDJUSEszNHJqVTF4cmNaZXB5UU0wK0wyYVdQcEw4VWMzZmtqeEh2Y2R2SnY2NnVuUnp1eEgyYk1qemVsZkE3QkliUCtmRjF3eHBZK2g2eUNrNlJhd2JFM3diNG9rclVjMGFTWHVXeHVya1NWYjUyUDBHSEg0V1BYdHd2RkVmQW1vbStVVzI1aEk3UTJmeU5OK0hiSFpJTm1lQmh2TXNscG9KZi8xTnY3LzZMUVZSVjFqS1pCMTd1OVFMdnJRREx5eTUwa09VVmFLQ2pSZE5EL09TSEJZUEV6c2hRd2dxR25iYWFEVGFSSG1BQnM2MExxcStYNXNlVUk2ZHhMYzQ0SGNiTlZFNlNDNThzZlc3blIvSlI0cjJTdHJSdEhHTForVUdGVjVHdjNwN0t0SHRDWkJrbXpSeFF2T0VIWUxIajIrWit3TENGbm5hMkhoczVtREpWUHJhVjVLUXR0Z3kyNE5raTg2c2RwbHB3Tzd1dTZpNEl6SC9HNVpSWlE2dW5MZVdIV29NK09NL1NLMldaSHY4N0paU1B0UmRaUkdHSHQiLCJtYWMiOiI3NzRhYzIyNzhlZWU3OGY0OGRiNzJkNzEwZGQ3YTM3ODg4Yzc1MzI5MTY2Y2M0YmRkZTNiNjNlYjQ0MDI5OTAzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 18:24:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkFObU1SeXd5bUtldFBYekliZVFiOEE9PSIsInZhbHVlIjoidERCckdqZUJWSEw3Yk9ld0dkOVRPNDJFS015TS9CaU9HUGNpNkFsWlhiV3djMXQ4T3U3S1UwbktveWJxNXZlRjZwTnl4V2hUQXpVMFFDRmJ6TC93RmJYM29ydTg4aTNLckp4VDRsK0U4MXpuN2RWMDl1Z3RvcnhlTHd5eUlyWlkvakdCS3FrMHBieWt3aHo3cGlvTVZuTCtaZVJPTmpjUEdMSGF0WUFFdEF6Q1psY1lvZUJCMDlkdGFGdGtkRFZRdzd1YzlIQ1JLdFFPQzhQZVNpckZ3WnptcEl2a0VRc08yajBpNnlkTkdHWUd2NjhkQVdjU3plb293RE1GaTJWOU5EUlRHZzFmMklZSFRLcW9JdTNrSFpBKzc0MWdGTG9PL2REYldsMldZdUE3QmlGUzdNaFU2T2t3N2ZDeEVaY3R0U05QWGV1WDZyc2VNTFR5OVhKbTZnVTBvSE81N29nK0pjdlNtTyt5d09Xc1U1V3NhODV3cXBBNk9QMCtzYnBTaXFFU1k3aUE4NEY1aU9EU3lJK2djWlp5R2FadnBKaWV1Vm9INzVMcisxRStuWUhDaXVXUGQ2QjFLbVJFN0pSMFo1a1FVN2pGMklkMG5iNlFSazZxTVp5QWRvcWJtZThJbHU2RTgzMlN6T2sxcjlVNXRnaFZyT2xSV0V6VC9GbksiLCJtYWMiOiJmYWEzN2IzZGVjNzE5MzFjN2IyOGE3NWY5NDQzYTAzODNkM2NjMmM2NTFkYWQ0MGFiNWZkMDhhN2U2YjlkNTQxIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 18:24:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1664164272\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1264130593 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1264130593\", {\"maxDepth\":0})</script>\n"}}