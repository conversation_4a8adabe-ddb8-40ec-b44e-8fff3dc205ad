{"__meta": {"id": "X9ced9b86c7d71092429403cc6170fc90", "datetime": "2025-06-08 15:30:50", "utime": **********.926866, "method": "GET", "uri": "/add-to-cart/3/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.218442, "end": **********.926887, "duration": 0.7084450721740723, "duration_str": "708ms", "measures": [{"label": "Booting", "start": **********.218442, "relative_start": 0, "end": **********.734833, "relative_end": **********.734833, "duration": 0.5163910388946533, "duration_str": "516ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.734846, "relative_start": 0.5164041519165039, "end": **********.92689, "relative_end": 2.86102294921875e-06, "duration": 0.19204378128051758, "duration_str": "192ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 53718224, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1322\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1322-1579</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.02556, "accumulated_duration_str": "25.56ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.801567, "duration": 0.01967, "duration_str": "19.67ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 76.956}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.839024, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 76.956, "width_percent": 3.365}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.871351, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 80.321, "width_percent": 4.656}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.876902, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 84.977, "width_percent": 5.243}, {"sql": "select * from `product_services` where `product_services`.`id` = '3' limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1326}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.886869, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1326", "source": "app/Http/Controllers/ProductServiceController.php:1326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1326", "ajax": false, "filename": "ProductServiceController.php", "line": "1326"}, "connection": "ty", "start_percent": 90.219, "width_percent": 4.617}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 3 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["3", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1330}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.8961542, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "ty", "start_percent": 94.836, "width_percent": 5.164}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 16,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1380241352 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1380241352\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.885063, "xdebug_link": null}]}, "session": {"_token": "WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:1 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"originalquantity\" => 12\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/add-to-cart/3/pos", "status_code": "<pre class=sf-dump id=sf-dump-51012894 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-51012894\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1150986832 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1150986832\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2049761268 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749396510751%7C76%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InhYU0Rlc0dsK01EQmxQTGFOWjlXd3c9PSIsInZhbHVlIjoiY1k0WVdMeGl2bFYxbmNMMEsrNTlQcU1hNjlpWENPTk1YZ3A3VmxiNmN5dDh1M00wNS9PUjB6Vm04eTUvWStLbllDS3Q0MVhSUWYvM3BOWTdPRUtsVyt5bnFnNEFrNmxoUzF4SXZjbnpCUWg5ZnNLSUZucXI4OFlpMk5jOGFIMlZtS3BmVUFhMTZac2k1NEVic0xxZ0RHZWVPTW04UzRzZE9MN0taQ01YSmtGOHFzOGtKb3VPeFVFSFM2SloyWkNzeDVlV0tNd0p0YTVWcXIwSnJ1ZWRUQ0UxOGpxMUtDUmhxK25zaVZyTCtyMU5PbnZ5OTVDTk5MSHRjYWh4d0JIcVVQZS9LbHhBaktEYWVwR2k5ODdGd0VkYUNXY2lHNTRJUkVWcFdLUGxZQjN3V3RNMEs1dnEvdHZuVVBVdmpnT1B4VVpkMXlzS1BpYVBIek9md25YNnVrOS9DV1NERHl6T1hsUWE4YjREbUFRUEw1Mi9vYllCanhFR1lqTnNHK0prTHdCUFhKVHB0amxxSHA1eTJPZ3crYWM2amY1U3pqWHNwRU9zSDluL2RST2tNRTVuRFBGT2l6dFdpU2hGeitJNGpIMzN1SVNkMnNpeU5Gc3JLRWZleXNoL25aaHdIYlA5UmV3OEVCazZoUm4wUjhmRmtxZE9udWJjbWFSM2JiSWciLCJtYWMiOiIwNWU4ODgzY2M0NTMzYTBlMTE4OWY2OTFmNmU4OGU4YTVjOTY5YmRiNzA0NmY2OGFlNmYyYjQyYWMzNjM4YTZiIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImIweDd3SU5uK283TVpiSmJYcSt0bUE9PSIsInZhbHVlIjoiU0VacExSUnlJQmREVzB5cUdQU2pYVVVSVG4xMlI3Y29hRkt4eGM5UWZ2NGlISkp3WEF1d2FiaCtJN0RHWlZsSU1xbVU5Z2tsT3h6TlB0YWJhdkZ5bTdpeEpQdkUvWGhQR09lbllVNzMrYWZOclJwbkdYbzdxRTBBemxHbXZ6Y1d6dzZaNU1BTHNzNU5UYzNySFNBWmpYcEFoY2hpaHE0VEZDeWVkOUJxTERWUkFRc09jMEd1RHZGaTY4Y05Vc0tWUGxFa3NuSGlnL2hzNmtDdVl3cGRpTVdFZXZHTmV6WmRTa2F3eUxDWlc0aGFrZ1lIeGpJSGtaSDQ2UUFmd3BocFNZV3JIaHVXa0tLMTRHdWhLQmdQYlQxWnlzbjQ4Q05ERXMrKzVkeTNNa091bWNJMlNEM2Q5c3JwQlhRa3ZKWmY3b0hKY0tienJhMDB6dHl4T0JEY25aaENDeHp4ckZST2ltR0VRSkdSK0gzTjNhdENZTDlqL1dLYUVnYW82VkZVUzlINjRxRXVYcm1aMk4yVklFZStoSGhTT0d4VTVnNElhSGo1T0hFMGF3YjVydnlPTHZCc0NndTYybm5hYVNpRWJvdk5id2RqQmwvMDJzeThNREJZTXAzbEpTTm5pcG1mZ0hSTkpsNmxuVjFLYzRGL2xWbU9yT01XWis3czdlTGIiLCJtYWMiOiI1ZGQ1OWU1MGYzMTk4MzEyMmM0MTZhZjBiYzY1MTc0ZWVmM2U5MDlkYmQxZDIxZmRkYjRiZTRiMTBjMDBhMjQ5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2049761268\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-803960804 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xNV7SvMX3AgbiuDVO62eNXfryfZD4KbWr3rEIkXt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-803960804\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-277450330 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:30:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkFLRE9YV01QTWdmcjVXejk2c2dLNGc9PSIsInZhbHVlIjoiWWpIajlsZk16L3kxbmRIVWhqM094VE1SUkZQNXI2R1IzMWJDRys0S3VCZUcwSWhrc0pDa0hFejEzK3EvSmJreVJvWmJHN01ZTlBUTUZxUmxGekkrb3VMek8vNGQ2NE1nd0tpSmRqQkI1V3dvSGpDb1NOTXlYakxLOUhYVTY1ajdZS2xaVXlodEtuTXlndzJRWXRTTjNlU0Q5SEtSaHJKRmVEdWNsa3NIMlNoeUJuaWRjaDVXRWV6blNVL2Q0eVJaVzlhQ01vQmdTaG5NWGtCNVgvNWEyUVlpMmdKU1BvVGtoN1puY3FjRStNQ3l6MnRYTndobDJrR3ZCeUJJY25PaDZ3cmdRcDJuaWtVNVFZTzhleGNKWExYS01LcGRwcWgydHFEZUVsN0MrRXRuR01PQVpCTm1aaWE4WFltOWhXM1A0a0FxMGsxeEptNC9JQXdpRmw2NllDa1lyc0FhWGw3VHh1UWlTN0pqUWhyS3ZJY3RGdE4xYjRXRUp5Vkw4ODM2UTRKOTRZUUlqTnQ2aVZoNk1lMk93RDFsRTV5TmJhL0Jib0tvSmdsWlAvL3o1djB5WWp6ZUdyNGFibUs5NEVjN1YwT2ttKyt4YjFJdDhDVzhaQndhRE5zbFFaTlc1VVIyZ254N09pQndGMDU0bXRjQnliQjNiNFlLTmdnNzRYM1YiLCJtYWMiOiIxNjUzOGUwN2Q5YWFkNzZiOTYxOTU3YWYzNWI1Mzk1NzdiYWNjYTEzYmQzOTkxYTE5NTViNDZlN2MzMzE5ZmRjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:30:50 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjFYQ3pPY3RZNmJRZmRKeVZoc3c5MWc9PSIsInZhbHVlIjoiNWorZlVqSDB1V1c0OEdDeFdoMVBOdWdxOE42S1YvQlhwY2tkS00ySERjREd4SlVLTFp4ZDY1QXhsR3VVbzFVUldjaThFNmJRTWtyQjZhUmdtQitjdVhzNUV6ak9TRlY5MkJEMGp6aXJhZ0l1OFV5N0t1SkRQT2d0VE1oZERLaXNiUnppbWRpemNLcjlQcWd5U1hRNExXMkk1Sk1pK2NHRFlkQkFzanV6Q090V2hueWMzU3BrdExYMmxCT00rNmJ3OS9uTUFpMmRtUGNkQmt2a1pubFB5Vjl1L2JZNCtVci9VbzJqcUtKZzNtMUkyWjR5MnF0RjVsSDk2QWZwb3J0SnZWdjlMa0NiL0Naa3hJVWcwL2wzd2lCZmtBUjdlK0RGQVdtMUlmNmJFdUlkSnFxaGVSQWZoS1lTdFZPOG5QYmVDN3d2emE1OUN1aXE0eWtLU2ZCWXhIRmdYRUVhRTJyU0JGZWR0MGFUdlJuZldpNi9KOFpiYVFEdldwMVo3MGNocW1PVHV2RGIyL2NtQkxCL2g5NEp3ck56R1ZlWElPT3pMekRocWtZdWcwZURVemlhcDFSTkppZXpOeUlDTElJc1Y3T041THNKWGc0RjlFVGMrNDQ0OVcvUXlRaGFQdnAzbFFSR1Z0QURaeitkMTk5N2lKM3VnOTZjVjJKREhGZEMiLCJtYWMiOiIzYzYxNzUzYzM2ZmQyYmYyZDYyZjMxNzM5YTAzMDNjZDU2MjQ3NWU0MzlkNTZjN2Q0OWVhODlkYzdkMGJiNzliIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:30:50 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkFLRE9YV01QTWdmcjVXejk2c2dLNGc9PSIsInZhbHVlIjoiWWpIajlsZk16L3kxbmRIVWhqM094VE1SUkZQNXI2R1IzMWJDRys0S3VCZUcwSWhrc0pDa0hFejEzK3EvSmJreVJvWmJHN01ZTlBUTUZxUmxGekkrb3VMek8vNGQ2NE1nd0tpSmRqQkI1V3dvSGpDb1NOTXlYakxLOUhYVTY1ajdZS2xaVXlodEtuTXlndzJRWXRTTjNlU0Q5SEtSaHJKRmVEdWNsa3NIMlNoeUJuaWRjaDVXRWV6blNVL2Q0eVJaVzlhQ01vQmdTaG5NWGtCNVgvNWEyUVlpMmdKU1BvVGtoN1puY3FjRStNQ3l6MnRYTndobDJrR3ZCeUJJY25PaDZ3cmdRcDJuaWtVNVFZTzhleGNKWExYS01LcGRwcWgydHFEZUVsN0MrRXRuR01PQVpCTm1aaWE4WFltOWhXM1A0a0FxMGsxeEptNC9JQXdpRmw2NllDa1lyc0FhWGw3VHh1UWlTN0pqUWhyS3ZJY3RGdE4xYjRXRUp5Vkw4ODM2UTRKOTRZUUlqTnQ2aVZoNk1lMk93RDFsRTV5TmJhL0Jib0tvSmdsWlAvL3o1djB5WWp6ZUdyNGFibUs5NEVjN1YwT2ttKyt4YjFJdDhDVzhaQndhRE5zbFFaTlc1VVIyZ254N09pQndGMDU0bXRjQnliQjNiNFlLTmdnNzRYM1YiLCJtYWMiOiIxNjUzOGUwN2Q5YWFkNzZiOTYxOTU3YWYzNWI1Mzk1NzdiYWNjYTEzYmQzOTkxYTE5NTViNDZlN2MzMzE5ZmRjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:30:50 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjFYQ3pPY3RZNmJRZmRKeVZoc3c5MWc9PSIsInZhbHVlIjoiNWorZlVqSDB1V1c0OEdDeFdoMVBOdWdxOE42S1YvQlhwY2tkS00ySERjREd4SlVLTFp4ZDY1QXhsR3VVbzFVUldjaThFNmJRTWtyQjZhUmdtQitjdVhzNUV6ak9TRlY5MkJEMGp6aXJhZ0l1OFV5N0t1SkRQT2d0VE1oZERLaXNiUnppbWRpemNLcjlQcWd5U1hRNExXMkk1Sk1pK2NHRFlkQkFzanV6Q090V2hueWMzU3BrdExYMmxCT00rNmJ3OS9uTUFpMmRtUGNkQmt2a1pubFB5Vjl1L2JZNCtVci9VbzJqcUtKZzNtMUkyWjR5MnF0RjVsSDk2QWZwb3J0SnZWdjlMa0NiL0Naa3hJVWcwL2wzd2lCZmtBUjdlK0RGQVdtMUlmNmJFdUlkSnFxaGVSQWZoS1lTdFZPOG5QYmVDN3d2emE1OUN1aXE0eWtLU2ZCWXhIRmdYRUVhRTJyU0JGZWR0MGFUdlJuZldpNi9KOFpiYVFEdldwMVo3MGNocW1PVHV2RGIyL2NtQkxCL2g5NEp3ck56R1ZlWElPT3pMekRocWtZdWcwZURVemlhcDFSTkppZXpOeUlDTElJc1Y3T041THNKWGc0RjlFVGMrNDQ0OVcvUXlRaGFQdnAzbFFSR1Z0QURaeitkMTk5N2lKM3VnOTZjVjJKREhGZEMiLCJtYWMiOiIzYzYxNzUzYzM2ZmQyYmYyZDYyZjMxNzM5YTAzMDNjZDU2MjQ3NWU0MzlkNTZjN2Q0OWVhODlkYzdkMGJiNzliIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:30:50 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-277450330\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>12</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}