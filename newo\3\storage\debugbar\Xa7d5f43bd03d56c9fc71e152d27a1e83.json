{"__meta": {"id": "Xa7d5f43bd03d56c9fc71e152d27a1e83", "datetime": "2025-06-08 15:28:58", "utime": **********.976127, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749396537.802716, "end": **********.976153, "duration": 1.1734368801116943, "duration_str": "1.17s", "measures": [{"label": "Booting", "start": 1749396537.802716, "relative_start": 0, "end": **********.799423, "relative_end": **********.799423, "duration": 0.9967069625854492, "duration_str": "997ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.799444, "relative_start": 0.9967279434204102, "end": **********.976156, "relative_end": 3.0994415283203125e-06, "duration": 0.1767120361328125, "duration_str": "177ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45201312, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00836, "accumulated_duration_str": "8.36ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.888772, "duration": 0.00468, "duration_str": "4.68ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 55.981}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.9174378, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 55.981, "width_percent": 11.364}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.942596, "duration": 0.00188, "duration_str": "1.88ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 67.344, "width_percent": 22.488}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.9592052, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 89.833, "width_percent": 10.167}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1021179794 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1021179794\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1086376024 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1086376024\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-757675982 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-757675982\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-904349360 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=xh9goj%7C1749396524286%7C5%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InlZYUd3c00yb3BDWmdQK1pZcUxnWGc9PSIsInZhbHVlIjoiQ01kbm92Wm9hT3FwTzB0NnZwSjlNaWtBWDhHdnozR3NZNldwNDg5Wkxhb3VObmVvYzF3cnFJRmZXeG0vUnNEdEtGYTFWelNBcUxyTWoxNE1wVThYTHFkZGw3TWtRdHhDMSsyYjJOZ2ZuZ0hPZXdYdVRqbVVnMjlLaW8xbDVDYkFXWHhwcEJZeHp0WGM4ZEc2S09Qb2pTM1lzOHZ5U3BsYlBQSFEydFU1Q1Byc0ZNS0pDR0J3Y2JGeS9abmN5QjJtd0pVSDA3a01FTzdpK3ZnS0VuSDlkTzdUZDlraFV6ajNaYzQza24xVjAxUHFyZEUwTjN5Yjh4Z084QXJNazFibjVKdEpTRitGWkNGRWthWjhsNE5sRTY5SmZDOFJud3duNEd0d2c2My9QcUNYTmQ1RzUwVVI2UmdUNnJpbUNoMTlCVnNEKzhkQk5KTlF5Zks4RERpSFB2MDV4WHVNeUFYa3pnWDVQaDJQcUlsL1BKQk5GY1ZhTG1GN0tDRkxHK2tQbXRFdXh4czJHNVdRTTNTb29vd245dUo1ZlFWOGd3QnNOSlZZZjQyc3BjSGZndkQyb0lNSGtsalY5UXhtQ1h2bXlKNFY1ajc5cG1JWlp2RFY1bUhtcmZSRE5PNFNpbkp6MHBLMDF6QklUbCtVbTE1VG1OV1kvei93SUxucXNmNFciLCJtYWMiOiJmNWE1ZmY2ZjZmYjY3ZTljNmM1ZjgyN2Y3ZjgyNWM3NWI2NzM3NTk4MDE4NDA5ZjJhNmJmN2YzYTRkMDUxM2E2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlJpdE5nUllrSHZXSCtFMlFzMlk3OVE9PSIsInZhbHVlIjoiUUlQZ1NOTE9CaUduUXZkbTNKdU1QYlpLeDlsN1JFNi9YN2lSUXkyZWNWVk5xL3RyNU9WMVpRUDJUVi9rSmh5Yi9HSll2dklINDhyaFpDMFlpSmE1Nm4xQXRLOGhycW1jUmFOL0VtaktCT0pqVm9tUGZReU9xaEo2WDFKVTdVVnB5Q1UyYXdVdmw1UUVaOHlGMStVcGZyL1NrdzdIYWNEM3BESURvMGl0dXJFKzNsemVha296MjY4TDVBSGU1aWU0RjVmR1Q4MXFtVU1YL1dkRTFWZ0FtVnIwN1FjZXoxYU5Wd2VZeUZ2T3F4aTJqYmdMK01HeWlWOUpBaG93a2srZmNBeFJRUUlhaDVEWVlrN2tHeXhEUWZDTk9XdzJSeXR0a2VWRzhuMHVkVXJ4eC9Lb3hhVWJGQVRHa3FQa3NoTFdZNGN0MmNNZTlRVFRlbVhDSTExZTZ6ZzdFVFQxQmpxR1JFUVJLSlZZK2ppMHVxU1R0LzNVajR1M1kyais1KzNDWTZOc2FsRSt0Y055THRHNjFrUGR3cnJvZEN5V2IyVjJoem5NdHlmekY4cHFvdEI0bkt3Qk13NEY2a29mRjVYTDNMYnIrS2taa3EvaDVtaTU1S1kydkJXU2tlOUFYQm9iV0dFcVZ5eHdldXpWeG9VYlA1OW9nS3YzT25zdFdMdUQiLCJtYWMiOiJhYTQ1NDgxY2ViNmYxN2Y0YTIyZjk4Mjg4ZDUxMzI4ZjllYTNlMGI5Yjc4N2Q2OWNlNTM4NTc5NTE5ZTY2NDk3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-904349360\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-587661308 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6s1OaNA8h4DHGuLO0qhsUHg7TnRJSWwbuakqNtUI</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-587661308\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-86236838 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:28:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InFyQUFQMnIvYUNMR0dNTlRCUFdiZ1E9PSIsInZhbHVlIjoiZC9TdTh1dlNFQXJEcFVMdnlTN01meFVlRWhIYjRYc21lWUhmUE4wREd3U0JHN2ZzY3FvUFh6YVkydC9iVXFyZEZ3QWZPNW1OVHZVYUo5TWtZTFZYeTNLM3hSMmhoWVdRWkRGOFRiaUxMRXdXNWhySC9vNHUrOVZxd2lSb2dhRHlLNnBGS09IaGJIckhpSWRPclNBNjJ0YmVucWEvOG9OMUpScVZ2RW1qNjVNOUFueng1NEhBdjRmSGN2Qjg4cVg3eUNuRU9ITnpiZXQ3MEI3Z05neElCTGpxZW1ZTHkzODNMbkN3WTQ2TDI5NlRhS1R5TVBsU0UySmFoVDBCRWNndkJtQzhXZWROeDFFRlkxQ3doankwZmhKaDJVTmkwTkk1NWFMdjNDWk80amsyZWlLQzBiaDdxWjltLzJ3VGltWWVialdsZ2RQcVRlNFFZYVh3WVZIQktHc284RmR0N1Z1RWx1cVNsZmtDbDl1RXdIMDlZeXFUWXdlMXBOZWw5WEh6bnBtWXRBbmpVQzZTR0lab3pXM3FVdS9qYzdGUXlaNGwvbTZvQ1JlaTNiMWY5MEJBQi96RE9CMUc4bzliSUpQOGZockdZNld4TmFjeHVzcVpsN1VhRXJzNUhJSWpkdHg3eC8wRUh0bTdWaDM0R2R2Smd5YjBxRXpBUk9UbG45d0wiLCJtYWMiOiJiMTA0M2UzYmI5ODc1MzY0NWIxM2E3ZTc3MjBjZmUwYzY0MGVmYTdkYzVlMzRhMmU2NTU0NTQzMGRlMzFlZGNiIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:28:58 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkpxYVdoY3NRN1ZWSWRzL0tsUEd6d0E9PSIsInZhbHVlIjoiWGpJV3BGRjVwWll2YWxuQjFJUForb25rUnNsMUtJbVU4ZXhZeWt3c2hhZFBFbkZJRVBhWFlPcWNURlE2NnJ4OS8wUHZRUHhqU21NZU1VU2lXdzJlUVFuZmliQm9ZMitIcHUxR001WG5Oa1h4bXlJbFlwNnlub3VYNUdSUHVnOWRoSE5KUWlIeUxZVTR1c3oxd0dwenZiU1VsOXg1OVZPN0JXbk02dFl4VVVjamFkVG5EcG5WKzFhNlZEMHA5Z0ZtSU5Cb2dBU1kzMHFOUFluK3dMei9QYmUwQ01Yb1dncVcwUjhlTm14dXVnU3RBNmdoU0lDUnVnU0pDMGRDNksyQUU0ZzlXU0tiMzJBUWZBVUFwTjBsSDFFcGhvNEFqMUFXbGMrWXRZQXZUbEVBSmpOYkxGQi96cmkrYTNjWE81REVBemVaeHBoMFAxbFJpeVJTSjBqK3B3Uy9MU0xNWGJmU2daa01yTTU2YlVzQllMWUN3N0ZjWHdsaEJYenBkVXJvQTZlNlEycUlqK2hQUzNxK29SZzBOLyszeUNTb1ZOV2crVGNNZmp3MTVwVWxXL0dBZUptdnloZHEwWCsrbVVndU8yQ3ZGWWdIdnNmUDdxOGMxa2tzYWU4UkM4SG1vVEViamxqaHJXWDFSclF0enN4TFNwSFFONk5XWm03ck5TMG4iLCJtYWMiOiJmMGJjODVkYjA5ZjQ5OGYyN2MxMjcyOTkwMzE1MDcxNTI2MTc1OTE1ZThlYTQzODk2NTlhMjc4NzFlYTc5N2I5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:28:58 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InFyQUFQMnIvYUNMR0dNTlRCUFdiZ1E9PSIsInZhbHVlIjoiZC9TdTh1dlNFQXJEcFVMdnlTN01meFVlRWhIYjRYc21lWUhmUE4wREd3U0JHN2ZzY3FvUFh6YVkydC9iVXFyZEZ3QWZPNW1OVHZVYUo5TWtZTFZYeTNLM3hSMmhoWVdRWkRGOFRiaUxMRXdXNWhySC9vNHUrOVZxd2lSb2dhRHlLNnBGS09IaGJIckhpSWRPclNBNjJ0YmVucWEvOG9OMUpScVZ2RW1qNjVNOUFueng1NEhBdjRmSGN2Qjg4cVg3eUNuRU9ITnpiZXQ3MEI3Z05neElCTGpxZW1ZTHkzODNMbkN3WTQ2TDI5NlRhS1R5TVBsU0UySmFoVDBCRWNndkJtQzhXZWROeDFFRlkxQ3doankwZmhKaDJVTmkwTkk1NWFMdjNDWk80amsyZWlLQzBiaDdxWjltLzJ3VGltWWVialdsZ2RQcVRlNFFZYVh3WVZIQktHc284RmR0N1Z1RWx1cVNsZmtDbDl1RXdIMDlZeXFUWXdlMXBOZWw5WEh6bnBtWXRBbmpVQzZTR0lab3pXM3FVdS9qYzdGUXlaNGwvbTZvQ1JlaTNiMWY5MEJBQi96RE9CMUc4bzliSUpQOGZockdZNld4TmFjeHVzcVpsN1VhRXJzNUhJSWpkdHg3eC8wRUh0bTdWaDM0R2R2Smd5YjBxRXpBUk9UbG45d0wiLCJtYWMiOiJiMTA0M2UzYmI5ODc1MzY0NWIxM2E3ZTc3MjBjZmUwYzY0MGVmYTdkYzVlMzRhMmU2NTU0NTQzMGRlMzFlZGNiIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:28:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkpxYVdoY3NRN1ZWSWRzL0tsUEd6d0E9PSIsInZhbHVlIjoiWGpJV3BGRjVwWll2YWxuQjFJUForb25rUnNsMUtJbVU4ZXhZeWt3c2hhZFBFbkZJRVBhWFlPcWNURlE2NnJ4OS8wUHZRUHhqU21NZU1VU2lXdzJlUVFuZmliQm9ZMitIcHUxR001WG5Oa1h4bXlJbFlwNnlub3VYNUdSUHVnOWRoSE5KUWlIeUxZVTR1c3oxd0dwenZiU1VsOXg1OVZPN0JXbk02dFl4VVVjamFkVG5EcG5WKzFhNlZEMHA5Z0ZtSU5Cb2dBU1kzMHFOUFluK3dMei9QYmUwQ01Yb1dncVcwUjhlTm14dXVnU3RBNmdoU0lDUnVnU0pDMGRDNksyQUU0ZzlXU0tiMzJBUWZBVUFwTjBsSDFFcGhvNEFqMUFXbGMrWXRZQXZUbEVBSmpOYkxGQi96cmkrYTNjWE81REVBemVaeHBoMFAxbFJpeVJTSjBqK3B3Uy9MU0xNWGJmU2daa01yTTU2YlVzQllMWUN3N0ZjWHdsaEJYenBkVXJvQTZlNlEycUlqK2hQUzNxK29SZzBOLyszeUNTb1ZOV2crVGNNZmp3MTVwVWxXL0dBZUptdnloZHEwWCsrbVVndU8yQ3ZGWWdIdnNmUDdxOGMxa2tzYWU4UkM4SG1vVEViamxqaHJXWDFSclF0enN4TFNwSFFONk5XWm03ck5TMG4iLCJtYWMiOiJmMGJjODVkYjA5ZjQ5OGYyN2MxMjcyOTkwMzE1MDcxNTI2MTc1OTE1ZThlYTQzODk2NTlhMjc4NzFlYTc5N2I5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:28:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-86236838\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}