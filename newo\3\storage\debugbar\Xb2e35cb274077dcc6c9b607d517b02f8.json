{"__meta": {"id": "Xb2e35cb274077dcc6c9b607d517b02f8", "datetime": "2025-06-08 15:28:59", "utime": 1749396539.094264, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749396537.7997, "end": 1749396539.094292, "duration": 1.2945919036865234, "duration_str": "1.29s", "measures": [{"label": "Booting", "start": 1749396537.7997, "relative_start": 0, "end": **********.903852, "relative_end": **********.903852, "duration": 1.104151964187622, "duration_str": "1.1s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.903874, "relative_start": 1.1041738986968994, "end": 1749396539.094295, "relative_end": 3.0994415283203125e-06, "duration": 0.19042110443115234, "duration_str": "190ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45184480, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.03356, "accumulated_duration_str": "33.56ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.989138, "duration": 0.02958, "duration_str": "29.58ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 88.141}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": 1749396539.0398278, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 88.141, "width_percent": 2.354}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": 1749396539.061694, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 90.495, "width_percent": 5.185}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": 1749396539.077678, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 95.679, "width_percent": 4.321}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1547743951 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1547743951\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1276587608 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1276587608\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-294063152 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-294063152\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1741759084 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=xh9goj%7C1749396524286%7C5%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InlZYUd3c00yb3BDWmdQK1pZcUxnWGc9PSIsInZhbHVlIjoiQ01kbm92Wm9hT3FwTzB0NnZwSjlNaWtBWDhHdnozR3NZNldwNDg5Wkxhb3VObmVvYzF3cnFJRmZXeG0vUnNEdEtGYTFWelNBcUxyTWoxNE1wVThYTHFkZGw3TWtRdHhDMSsyYjJOZ2ZuZ0hPZXdYdVRqbVVnMjlLaW8xbDVDYkFXWHhwcEJZeHp0WGM4ZEc2S09Qb2pTM1lzOHZ5U3BsYlBQSFEydFU1Q1Byc0ZNS0pDR0J3Y2JGeS9abmN5QjJtd0pVSDA3a01FTzdpK3ZnS0VuSDlkTzdUZDlraFV6ajNaYzQza24xVjAxUHFyZEUwTjN5Yjh4Z084QXJNazFibjVKdEpTRitGWkNGRWthWjhsNE5sRTY5SmZDOFJud3duNEd0d2c2My9QcUNYTmQ1RzUwVVI2UmdUNnJpbUNoMTlCVnNEKzhkQk5KTlF5Zks4RERpSFB2MDV4WHVNeUFYa3pnWDVQaDJQcUlsL1BKQk5GY1ZhTG1GN0tDRkxHK2tQbXRFdXh4czJHNVdRTTNTb29vd245dUo1ZlFWOGd3QnNOSlZZZjQyc3BjSGZndkQyb0lNSGtsalY5UXhtQ1h2bXlKNFY1ajc5cG1JWlp2RFY1bUhtcmZSRE5PNFNpbkp6MHBLMDF6QklUbCtVbTE1VG1OV1kvei93SUxucXNmNFciLCJtYWMiOiJmNWE1ZmY2ZjZmYjY3ZTljNmM1ZjgyN2Y3ZjgyNWM3NWI2NzM3NTk4MDE4NDA5ZjJhNmJmN2YzYTRkMDUxM2E2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlJpdE5nUllrSHZXSCtFMlFzMlk3OVE9PSIsInZhbHVlIjoiUUlQZ1NOTE9CaUduUXZkbTNKdU1QYlpLeDlsN1JFNi9YN2lSUXkyZWNWVk5xL3RyNU9WMVpRUDJUVi9rSmh5Yi9HSll2dklINDhyaFpDMFlpSmE1Nm4xQXRLOGhycW1jUmFOL0VtaktCT0pqVm9tUGZReU9xaEo2WDFKVTdVVnB5Q1UyYXdVdmw1UUVaOHlGMStVcGZyL1NrdzdIYWNEM3BESURvMGl0dXJFKzNsemVha296MjY4TDVBSGU1aWU0RjVmR1Q4MXFtVU1YL1dkRTFWZ0FtVnIwN1FjZXoxYU5Wd2VZeUZ2T3F4aTJqYmdMK01HeWlWOUpBaG93a2srZmNBeFJRUUlhaDVEWVlrN2tHeXhEUWZDTk9XdzJSeXR0a2VWRzhuMHVkVXJ4eC9Lb3hhVWJGQVRHa3FQa3NoTFdZNGN0MmNNZTlRVFRlbVhDSTExZTZ6ZzdFVFQxQmpxR1JFUVJLSlZZK2ppMHVxU1R0LzNVajR1M1kyais1KzNDWTZOc2FsRSt0Y055THRHNjFrUGR3cnJvZEN5V2IyVjJoem5NdHlmekY4cHFvdEI0bkt3Qk13NEY2a29mRjVYTDNMYnIrS2taa3EvaDVtaTU1S1kydkJXU2tlOUFYQm9iV0dFcVZ5eHdldXpWeG9VYlA1OW9nS3YzT25zdFdMdUQiLCJtYWMiOiJhYTQ1NDgxY2ViNmYxN2Y0YTIyZjk4Mjg4ZDUxMzI4ZjllYTNlMGI5Yjc4N2Q2OWNlNTM4NTc5NTE5ZTY2NDk3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1741759084\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1514416540 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6s1OaNA8h4DHGuLO0qhsUHg7TnRJSWwbuakqNtUI</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1514416540\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:28:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik0xZjNuM2k5NmZkd1MyMVVXSVF0WVE9PSIsInZhbHVlIjoia0p4YnJyU1A0UUg5WUp6LzRwUHRYbE12bUdKaytZeDAxQ0dhM3FyQ215VGk4dkRVUXE5L0xJcUpyOUM2SDIvZ1A3b2toTVQ1UTBIKzUvSmhJRkFlVFlsL3AwckJMTmkvYnJzSjlsVURnc1JjakdMTmw2a1VJQUVQVnFsMlFiOVBkSkpsNGNNbm5BdkpTRXpIRlpyS1pwRnRBUDgzQmR2bXA5RHF1OFAvL2xueGFpRDJhaWdmQm91NlcxRTc5UHFkbGJZdUlxbEdxYkhmYnVTU0lmeHBBN2hMOUwrZEd5d1oyaWtOeHVrRWU1a2p6SkZaQUwvU0xnSXduOUhoNHdmTXMwbkNSbGdMTnFjeWdVNVY2SlQ0ay9vRWN1NnhUZDYydFJhOStlMzhZRXQ0YXlLa1RPb2x3L0tTdFhiL2R2U2VEWVZLWHpCSk1EODU0YkhqbkRmZXk5dkNNN3hxak1mTUtsR3ZYeURqZ2o0MW1FbHlLUjA0OE10U01JbXNablZBcXlybGJ6bHlKSlEwLzFvbGJycm9sWlFJWTJKVnZ1T3FuQ3BYb244NHRoZ0FBUU5VcUlGRERmeVM3OHlOZk40bmpVUXlvVllpenRMM0xHK2JtMDhGKzg4UmZWbnFDRy9JSmhNRW4vQ1FYeTYyeVZndEQ2L3FXTThTTlF6bjJiTnAiLCJtYWMiOiJiNDQxMTU1N2JkODU3NThiZmU5YjhkY2VkYzY5ZGIwMDE2Y2Q0ZWEyZTc5YTQ3NDNhYmZkYTA3MTdhMTY1Yzk4IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:28:59 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Iml2TkVGSUZYNG1xSE1pVGI5MnRUSUE9PSIsInZhbHVlIjoiUFljam04K0VGZEgyWWI4YnZtWkJ4azVGMWgrVzdZYnlkOGExdzNOK0k3UFpteFZ3bXo3ZlVUWlFuYjVWOXZPcFk2eUZ6WVVXeURnc1Rjb0MrbnIrQkVZbmtjaXhjaGNYcEQrVk1oa0RHVUVBQnhseSt4VmNsSkhzZDZ1RGxMLzhvWUgzdG1vVlNqVUJybytJSUJVVnhJQjZnbFhjYXBYemFQclNxTzRvS0xvK29ZMm5PbkYvMC9Oc0k5bEw1ajlSaWtaU1hLNFdHT2hQQnhtdnNhblZwVXBuSEhReVF1dlVkU0pweFhKOEhlMWNYdjRaQ3FTVVl1ckxqRUNNbzRUU3B3SFc3TSt1dzAxRm9CRnM2bFhBVHJvUUhkUjhWL2tNYmF3U0l4bStaS01mT2dBOS90V2ZLNHdtZjlyUUVpeGozT2w4UEIxZmMxdDlMNjZoeHFGS2M0Qm5IdnBTZ2x1K2dGemZFNzhNMzRLM0pXT0swS0FUd1EzMnB0dXdHU2Zrdnc5QUNJM3VYdEZMNzVaTWtKWmJjZUNmU0c3UFo4Nk82SGIvRWNuSHQwRE8zY05Rem1EdXZ0RitDT0tDQ09wNU8wM21kREkyVFJHWWFZRzhFbDFwUTdtNThvNzhOV1MvUUcxU3YxUDlSS1lNN3NuODlaMDJWOThvRUpVSjhBdDUiLCJtYWMiOiJkMTllZDc3ODkzZTE3YzZmOGY5NTViN2Q1Nzc1ZDMxODc2ODhkZGU2ZTYzMWE1YThkY2Q4OGIyYjFjMzE0MGUyIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:28:59 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik0xZjNuM2k5NmZkd1MyMVVXSVF0WVE9PSIsInZhbHVlIjoia0p4YnJyU1A0UUg5WUp6LzRwUHRYbE12bUdKaytZeDAxQ0dhM3FyQ215VGk4dkRVUXE5L0xJcUpyOUM2SDIvZ1A3b2toTVQ1UTBIKzUvSmhJRkFlVFlsL3AwckJMTmkvYnJzSjlsVURnc1JjakdMTmw2a1VJQUVQVnFsMlFiOVBkSkpsNGNNbm5BdkpTRXpIRlpyS1pwRnRBUDgzQmR2bXA5RHF1OFAvL2xueGFpRDJhaWdmQm91NlcxRTc5UHFkbGJZdUlxbEdxYkhmYnVTU0lmeHBBN2hMOUwrZEd5d1oyaWtOeHVrRWU1a2p6SkZaQUwvU0xnSXduOUhoNHdmTXMwbkNSbGdMTnFjeWdVNVY2SlQ0ay9vRWN1NnhUZDYydFJhOStlMzhZRXQ0YXlLa1RPb2x3L0tTdFhiL2R2U2VEWVZLWHpCSk1EODU0YkhqbkRmZXk5dkNNN3hxak1mTUtsR3ZYeURqZ2o0MW1FbHlLUjA0OE10U01JbXNablZBcXlybGJ6bHlKSlEwLzFvbGJycm9sWlFJWTJKVnZ1T3FuQ3BYb244NHRoZ0FBUU5VcUlGRERmeVM3OHlOZk40bmpVUXlvVllpenRMM0xHK2JtMDhGKzg4UmZWbnFDRy9JSmhNRW4vQ1FYeTYyeVZndEQ2L3FXTThTTlF6bjJiTnAiLCJtYWMiOiJiNDQxMTU1N2JkODU3NThiZmU5YjhkY2VkYzY5ZGIwMDE2Y2Q0ZWEyZTc5YTQ3NDNhYmZkYTA3MTdhMTY1Yzk4IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:28:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Iml2TkVGSUZYNG1xSE1pVGI5MnRUSUE9PSIsInZhbHVlIjoiUFljam04K0VGZEgyWWI4YnZtWkJ4azVGMWgrVzdZYnlkOGExdzNOK0k3UFpteFZ3bXo3ZlVUWlFuYjVWOXZPcFk2eUZ6WVVXeURnc1Rjb0MrbnIrQkVZbmtjaXhjaGNYcEQrVk1oa0RHVUVBQnhseSt4VmNsSkhzZDZ1RGxMLzhvWUgzdG1vVlNqVUJybytJSUJVVnhJQjZnbFhjYXBYemFQclNxTzRvS0xvK29ZMm5PbkYvMC9Oc0k5bEw1ajlSaWtaU1hLNFdHT2hQQnhtdnNhblZwVXBuSEhReVF1dlVkU0pweFhKOEhlMWNYdjRaQ3FTVVl1ckxqRUNNbzRUU3B3SFc3TSt1dzAxRm9CRnM2bFhBVHJvUUhkUjhWL2tNYmF3U0l4bStaS01mT2dBOS90V2ZLNHdtZjlyUUVpeGozT2w4UEIxZmMxdDlMNjZoeHFGS2M0Qm5IdnBTZ2x1K2dGemZFNzhNMzRLM0pXT0swS0FUd1EzMnB0dXdHU2Zrdnc5QUNJM3VYdEZMNzVaTWtKWmJjZUNmU0c3UFo4Nk82SGIvRWNuSHQwRE8zY05Rem1EdXZ0RitDT0tDQ09wNU8wM21kREkyVFJHWWFZRzhFbDFwUTdtNThvNzhOV1MvUUcxU3YxUDlSS1lNN3NuODlaMDJWOThvRUpVSjhBdDUiLCJtYWMiOiJkMTllZDc3ODkzZTE3YzZmOGY5NTViN2Q1Nzc1ZDMxODc2ODhkZGU2ZTYzMWE1YThkY2Q4OGIyYjFjMzE0MGUyIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:28:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-161737942 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-161737942\", {\"maxDepth\":0})</script>\n"}}