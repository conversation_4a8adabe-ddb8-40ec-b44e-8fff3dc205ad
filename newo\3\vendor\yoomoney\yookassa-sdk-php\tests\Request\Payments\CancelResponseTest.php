<?php

/*
* The MIT License
*
* Copyright (c) 2024 "YooMoney", NBСO LLC
*
* Permission is hereby granted, free of charge, to any person obtaining a copy
* of this software and associated documentation files (the "Software"), to deal
* in the Software without restriction, including without limitation the rights
* to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
* copies of the Software, and to permit persons to whom the Software is
* furnished to do so, subject to the following conditions:
*
* The above copyright notice and this permission notice shall be included in
* all copies or substantial portions of the Software.
*
* THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
* IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
* FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
* AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
* LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
* OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
* THE SOFTWARE.
*/

namespace Tests\YooKassa\Request\Payments;

use Exception;
use Tests\YooKassa\AbstractTestCase;
use Datetime;
use YooKassa\Model\Deal\SafeDeal;
use YooKassa\Model\Metadata;
use YooKassa\Model\Payment\Payment;
use YooKassa\Request\Deals\DealsResponse;
use YooKassa\Request\Payments\CancelResponse;

/**
 * CancelResponseTest
 *
 * @category    ClassTest
 * <AUTHOR>
 * @link        https://yookassa.ru/developers/api
 */
class CancelResponseTest extends AbstractTestCase
{
    protected CancelResponse $object;

    /**
     * @param mixed|null $value
     * @return CancelResponse
     */
    protected function getTestInstance(mixed $value = null): CancelResponse
    {
        return new CancelResponse($value);
    }

    /**
     * @return void
     */
    public function testCancelResponseClassExists(): void
    {
        $this->object = $this->getMockBuilder(CancelResponse::class)->getMockForAbstractClass();
        $this->assertTrue(class_exists(CancelResponse::class));
        $this->assertInstanceOf(CancelResponse::class, $this->object);
    }

    /**
     * Test valid method "jsonSerialize"
     * @dataProvider validClassDataProvider
     * @param mixed $value
     *
     * @return void
     */
    public function testJsonSerialize(mixed $value): void
    {
        $instance = $this->getTestInstance($value);
        self::assertEquals((new Payment($value))->toArray(), $instance->toArray());
    }

    /**
     * @return array
     * @throws Exception
     */
    public function validClassDataProvider(): array
    {
        $result = [];
        for ($i = 0; $i < 4; $i++) {
            $result[] = $this->getValidDataProviderByClass(new CancelResponse());
        }
        return $result;
    }
}
