<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة حرارية - <?php echo e($details['pos_id']); ?></title>
    <style>
        @media print {
            @page {
                size: 80mm 297mm;
                margin: 0;
            }

            body {
                width: 74mm;
                margin: 0 auto;
                padding: 3mm;
                direction: rtl;
                font-family: 'Courier New', monospace;
                font-size: 10px;
                overflow: hidden;
            }
        }

        body {
            width: 74mm;
            margin: 0 auto;
            padding: 3mm;
            direction: rtl;
            font-family: 'Courier New', monospace;
            font-size: 10px;
            background: white;
            color: #000;
        }

        /* ضمان أن جميع النصوص باللون الأسود الداكن والخط العريض */
        * {
            color: #000 !important;
            font-weight: bold !important;
        }

        .thermal-print {
            width: 100%;
            text-align: center;
        }

        .header {
            text-align: center;
            margin-bottom: 10px;
        }

        .logo-container {
            text-align: center;
            margin-bottom: 5px;
        }

        .logo-img {
            max-width: 90%;
            max-height: 20mm;
            margin-bottom: 5px;
        }

        .company-name {
            font-weight: bold;
            font-size: 14px;
            margin-bottom: 3px;
            color: #000 !important;
        }

        .receipt-info {
            font-size: 9px;
            margin-bottom: 5px;
            text-align: center;
        }

        .receipt-info div {
            font-size: 10px;
            margin-bottom: 2px;
            font-weight: bold;
            color: #000 !important;
        }

        .divider {
            border-top: 1px dashed #000;
            margin: 5px 0;
        }

        .solid-divider {
            border-top: 1px solid #000;
            margin: 5px 0;
        }

        .invoice-type {
            font-weight: bold;
            font-size: 12px;
            margin: 5px 0;
            color: #000 !important;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 5px 0;
            font-size: 9px;
        }

        th, td {
            padding: 3px;
            text-align: center;
            border: none;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            color: #000 !important;
            font-weight: bold !important;
        }

        th {
            font-weight: bold;
            border-bottom: 2px solid #000;
            color: #000 !important;
        }

        th:nth-child(1), td:nth-child(1) { text-align: right; width: 45%; }
        th:nth-child(2), td:nth-child(2) { text-align: center; width: 15%; }
        th:nth-child(3), td:nth-child(3) { text-align: center; width: 20%; }
        th:nth-child(4), td:nth-child(4) { text-align: left; width: 20%; }

        .totals {
            margin: 10px 0;
            font-size: 10px;
            font-weight: bold;
            color: #000 !important;
        }

        .totals div {
            display: flex;
            justify-content: space-between;
            margin-bottom: 3px;
            padding: 2px 0;
            color: #000 !important;
            font-weight: bold !important;
        }

        .total-final {
            font-weight: bold;
            border-top: 2px solid #000;
            padding-top: 5px !important;
            margin-top: 5px;
            font-size: 11px;
            color: #000 !important;
        }

        .thank-you {
            text-align: center;
            font-size: 10px;
            margin: 10px 0;
            font-weight: bold;
            color: #000 !important;
        }

        .barcode-middle {
            text-align: center;
            margin: 10px auto;
        }

        .barcode-middle img {
            max-width: 90%;
            height: 30px;
            margin: 0 auto;
        }

        .qr-code {
            text-align: center;
            margin: 10px auto;
        }

        .qr-code img {
            width: 50mm;
            height: auto;
            max-width: 90%;
            margin: 0 auto;
            display: block;
        }
    </style>
</head>
<body>
    <div class="thermal-print">
        <!-- شعار واسم الشركة -->
        <div class="header">
            <?php if(!empty($company_logo)): ?>
            <div class="logo-container">
                <img src="<?php echo e($company_logo); ?>" alt="<?php echo e($settings['company_name'] ?? 'شركتي'); ?>" class="logo-img" onerror="this.style.display='none'">
            </div>
            <?php endif; ?>
            <div class="company-name"><?php echo e($settings['company_name'] ?? 'شركتي'); ?></div>

            <div class="receipt-info">
                <div>العنوان: <?php echo e($settings['company_address'] ?? 'المملكة العربية السعودية'); ?></div>
                <div>الرقم الضريبي: <?php echo e($settings['vat_number'] ?? '123456789012345'); ?></div>
            </div>
        </div>

        <div class="solid-divider"></div>

        <!-- نوع الفاتورة -->
        <div class="invoice-type">
            <div>فاتورة مبسطة</div>
        </div>

        <div class="divider"></div>

        <!-- بيانات الفاتورة -->
        <div class="receipt-info">
            <?php if(isset($details['date'])): ?>
            <div>التاريخ: <?php echo e($details['date']); ?></div>
            <?php endif; ?>
            <div>رقم الفاتورة: <?php echo e($details['pos_id']); ?></div>
            <div>الكاشير: <?php echo e($details['user']['name'] ?? ''); ?></div>
            <?php if(isset($details['customer']['name'])): ?>
            <div>العميل: <?php echo e($details['customer']['name']); ?></div>
            <?php endif; ?>
        </div>

        <div class="solid-divider"></div>

        <!-- جدول المنتجات -->
        <table>
            <thead>
                <tr>
                    <th>الصنف</th>
                    <th>الكمية</th>
                    <th>السعر</th>
                    <th>الإجمالي</th>
                </tr>
            </thead>
            <tbody>
                <?php $__currentLoopData = $pos->items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <tr>
                    <td><?php echo e($item->product ? $item->product->name : 'منتج غير معروف'); ?></td>
                    <td><?php echo e($item->quantity); ?></td>
                    <td><?php echo e(number_format((float)$item->price, 2)); ?> ﷼</td>
                    <td><?php echo e(number_format((float)($item->price * $item->quantity), 2)); ?> ﷼</td>
                </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </tbody>
        </table>

        <div class="solid-divider"></div>

        <!-- الباركود -->
        <?php if(isset($barcode)): ?>
        <div class="barcode-middle">
            <?php
                $barcode_text = $details['pos_id'];
                $barcode_image = DNS1D::getBarcodePNG($barcode_text, 'C128', 1.2, 30);
            ?>
            <img src="data:image/png;base64,<?php echo e($barcode_image); ?>" alt="Barcode">
            <div style="font-size: 8px; margin-top: 2px; color: #000;"><?php echo e($details['pos_id']); ?></div>
        </div>
        <?php endif; ?>

        <div class="solid-divider"></div>

        <!-- الإجماليات -->
        <div class="totals">
            <?php
                // حساب الإجمالي النهائي أولاً من بيانات الفاتورة
                $total = $pos->getTotal(); // الحصول على الإجمالي النهائي من الفاتورة

                // استخراج ضريبة القيمة المضافة 15% من الإجمالي النهائي
                // الضريبة = (الإجمالي × 15) ÷ 115
                $totalTax = ($total * 15) / 115;

                // المجموع الفرعي = الإجمالي - الضريبة
                $subtotal = $total - $totalTax;

                $discount = $pos->getTotalDiscount();
            ?>
            <div>
                <span>المجموع الفرعي:</span>
                <span><?php echo e(number_format((float)$subtotal, 2)); ?> ﷼</span>
            </div>
            <?php if($discount > 0): ?>
            <div>
                <span>الخصم:</span>
                <span><?php echo e(number_format((float)$discount, 2)); ?> ﷼</span>
            </div>
            <?php endif; ?>
            <div>
                <span>ضريبة القيمة المضافة (15%):</span>
                <span><?php echo e(number_format((float)$totalTax, 2)); ?> ﷼</span>
            </div>
            <div class="total-final">
                <span>الإجمالي:</span>
                <span><?php echo e(number_format((float)$total, 2)); ?> ﷼</span>
            </div>
        </div>

        <div class="divider"></div>

        <!-- الشكر -->
        <div class="thank-you">
            <div>شكراً للتسوق معنا. نرجو زيارتكم مرة أخرى.</div>
        </div>

        <!-- رمز QR -->
        <div class="qr-code">
            <?php
                // استخدام نفس طريقة الحساب للـ QR Code
                $qr_total = $pos->getTotal(); // الحصول على الإجمالي النهائي
                $qr_totalTax = ($qr_total * 15) / 115; // استخراج الضريبة 15%
                $qr_subtotal = $qr_total - $qr_totalTax; // المجموع الفرعي
                $qr_discount = $pos->getTotalDiscount();

                $qr_text = "Invoice: " . $details['pos_id'] . "\n";
                $qr_text .= "Company: " . ($settings['company_name'] ?? 'شركتي') . "\n";
                $qr_text .= "Date: " . (isset($details['date']) ? $details['date'] : date('Y-m-d')) . "\n";
                $qr_text .= "Total: " . number_format((float)$qr_total, 2) . " SAR\n";
                $qr_text .= "VAT Number: " . ($settings['vat_number'] ?? '123456789012345');
                $qr_image = DNS2D::getBarcodePNG($qr_text, 'QRCODE', 6, 6);
            ?>
            <img src="data:image/png;base64,<?php echo e($qr_image); ?>" alt="QR Code">
        </div>
    </div>

    <script>
        // طباعة تلقائية عند تحميل الصفحة
        window.onload = function() {
            setTimeout(function() {
                window.print();
                // إغلاق النافذة بعد الطباعة
                setTimeout(function() {
                    window.close();
                }, 1000);
            }, 500);
        };
    </script>
</body>
</html>
<?php /**PATH C:\laragon\www\to\newo\3\resources\views/pos/thermal_print_clean.blade.php ENDPATH**/ ?>