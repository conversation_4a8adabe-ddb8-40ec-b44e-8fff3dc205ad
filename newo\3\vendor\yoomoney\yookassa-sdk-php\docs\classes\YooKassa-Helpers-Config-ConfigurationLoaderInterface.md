# [YooKassa API SDK](../home.md)

# Interface: ConfigurationLoaderInterface
### Namespace: [\YooKassa\Helpers\Config](../namespaces/yookassa-helpers-config.md)
---
**Summary:**

Interface ConfigurationLoaderInterface.

---
### Constants
* No constants found

---
### Methods
| Visibility | Name | Flag | Summary |
| ----------:| ---- | ---- | ------- |
| public | [getConfig()](../classes/YooKassa-Helpers-Config-ConfigurationLoaderInterface.md#method_getConfig) |  |  |
| public | [load()](../classes/YooKassa-Helpers-Config-ConfigurationLoaderInterface.md#method_load) |  |  |

---
### Details
* File: [lib/Helpers/Config/ConfigurationLoaderInterface.php](../../lib/Helpers/Config/ConfigurationLoaderInterface.php)
* Package: \YooKassa\Helpers
* See Also:
  * [](https://yookassa.ru/developers/api)

---
### Tags
| Tag | Version | Description |
| --- | ------- | ----------- |
| category |  | Interface |
| author |  | <EMAIL> |

---
## Methods
<a name="method_getConfig" class="anchor"></a>
#### public getConfig() : array

```php
public getConfig() : array
```

**Details:**
* Inherited From: [\YooKassa\Helpers\Config\ConfigurationLoaderInterface](../classes/YooKassa-Helpers-Config-ConfigurationLoaderInterface.md)

**Returns:** array - 


<a name="method_load" class="anchor"></a>
#### public load() : \YooKassa\Helpers\Config\ConfigurationLoaderInterface

```php
public load() : \YooKassa\Helpers\Config\ConfigurationLoaderInterface
```

**Details:**
* Inherited From: [\YooKassa\Helpers\Config\ConfigurationLoaderInterface](../classes/YooKassa-Helpers-Config-ConfigurationLoaderInterface.md)

**Returns:** \YooKassa\Helpers\Config\ConfigurationLoaderInterface - 




---

### Top Namespaces

* [\YooKassa](../namespaces/yookassa.md)

---

### Reports
* [Errors - 0](../reports/errors.md)
* [Markers - 0](../reports/markers.md)
* [Deprecated - 25](../reports/deprecated.md)

---

This document was automatically generated from source code comments on 2024-07-02 using [phpDocumentor](http://www.phpdoc.org/)

&copy; 2024 YooMoney