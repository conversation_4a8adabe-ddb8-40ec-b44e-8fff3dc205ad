<?php

/**
 * The MIT License
 *
 * Copyright (c) 2023 "YooMoney", NBСO LLC
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */

namespace YooKassa\Validator\Constraints;

use Attribute;
use YooKassa\Validator\Exceptions\ValidatorParameterException;

/**
 * @Annotation
 * @Target({"PROPERTY", "ANNOTATION"})
 */
#[Attribute(Attribute::TARGET_PROPERTY | Attribute::IS_REPEATABLE)]
class Count extends AbstractConstraint
{
    private string $minMessage = 'This collection should contain {{ limit }} element or more.';
    private string $maxMessage = 'This collection should contain {{ limit }} element or less.';
    private ?int $min;
    private ?int $max;

    public function __construct(
        int $min = null,
        int $max = null,
    )
    {
        $this->min = $min;
        $this->max = $max;

        if (null === $this->min && null === $this->max) {
            throw new ValidatorParameterException('Either option "min", "max" must be given for constraint');
        }
    }

    /**
     * @return int|null
     */
    public function getMin(): ?int
    {
        return $this->min;
    }

    /**
     * @return int|null
     */
    public function getMax(): ?int
    {
        return $this->max;
    }

    /**
     * @return string
     */
    public function getMinMessage(): string
    {
        return str_replace('{{ limit }}', $this->min, $this->minMessage);
    }

    /**
     * @return string
     */
    public function getMaxMessage(): string
    {
        return str_replace('{{ limit }}', $this->max, $this->maxMessage);
    }
}