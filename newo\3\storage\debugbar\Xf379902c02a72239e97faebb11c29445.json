{"__meta": {"id": "Xf379902c02a72239e97faebb11c29445", "datetime": "2025-06-08 15:40:00", "utime": 1749397200.019489, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.232351, "end": 1749397200.019515, "duration": 0.7871639728546143, "duration_str": "787ms", "measures": [{"label": "Booting", "start": **********.232351, "relative_start": 0, "end": **********.903077, "relative_end": **********.903077, "duration": 0.6707258224487305, "duration_str": "671ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.90309, "relative_start": 0.670738935470581, "end": 1749397200.019518, "relative_end": 2.86102294921875e-06, "duration": 0.11642789840698242, "duration_str": "116ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45152560, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02571, "accumulated_duration_str": "25.71ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9530451, "duration": 0.02346, "duration_str": "23.46ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 91.249}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.991023, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 91.249, "width_percent": 3.306}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (17) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": 1749397200.001445, "duration": 0.0014, "duration_str": "1.4ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 94.555, "width_percent": 5.445}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-867314613 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-867314613\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-895324935 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-895324935\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1211147408 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1211147408\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1094085655 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6ImNiYzkwakxlbFl5M0hmWXd6cnhmZWc9PSIsInZhbHVlIjoiaWxjU3hBK1J4cjlkSXBwVGZ2eXNWZz09IiwibWFjIjoiYmJkM2U2NzNiY2EzMmQyY2VlY2YyNjdhNmQxNjY4ZGM2NTgxOWJlMmVmMWJjZWUzNDYzYzA3MTViZjAzYzRhNCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwl%7C0%7C1982; _clsk=o6pnpa%7C1749396672276%7C5%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik9ESzlPL3B3MGFIanJESTh5cHk3amc9PSIsInZhbHVlIjoiZnhJQXdDLzRCTDFDa0JCSitpQjU1ZVJhMEhBMytGL3d0bjZkd3FSZm1abW9GZUF6a0FVLzJpdkRNakhhVUxjTklWT3ZSck9XTm1KbVVNK3pvbkJ2YXh0REIvNzRHdlM3ay92NVhLRlJMaEFkM1VCUjFJRk1nZXZlT1A5bmRtS2srdFZORndDbTZ6T1RXc1ZMc2t3aHdHZTdsYnJUcFFsZG5CU1BqcnBXZ1dNakVhM2krblJUQ0xtb1lJc3RUQWQzVHRIeVJQdE5tekVlN2lEQmkyNDVUWEpqa3dqanNLUGFudW5oSitycmhYbVRRUmVKYzBudzE4OER5ZTVwb2w0dTBHZmFNM2ExcVY3K2xKQ3Y0WWxkS3lGQTN3ODZpNTdEeUFqZnVRN3hqbDR4alFORXdxZ3FsQ0pTMTE3NjFqVFNkS25FTS8xMDZ3a0ZSSWNnYjlURDZ1ZC9DVFNCSGZ3NEN2cm5iMWdXNXMrVTFHbFd5cVRhcUlIZkZ3WWc2ei9nVlNkVGU4K3Ewcm1rUUZlQVlEckFkbmJTYXVIM0hzREtnRmxJN3B0WVd1QmZvSlNhOVhnbDRlNEdBb3ZxVG40dHRhMk9EVVlzeDdzNDRDMVMyTDJxSzU5d3Y2aGVtYkxkM09nUFBaRERKR2NOMHNIMDBtcEs5WEtITk5Za3FQaWYiLCJtYWMiOiJiZDM0OTVkZDgwMzc4YjcwMTlmNTIyNDQ1MTNiOTVlYWRmYjEzZDc2MWRhMjA3NjhlNzkzYjQwZmUzZTMyZDJjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImI3eU5CWDZaNnl2V0JyNWJtaDUvL1E9PSIsInZhbHVlIjoiQ1g2aEJZK3dGd1UrMTM3TkozNHdXSzFpOU0xZ3RhdUFUYkkzZ2E2S0JpNkorNUpnU0tkTHA3aFhrZm5VMVgraUEwd08xK3h1WG1OS3ZUdTVhZDkrZi9RaFU0dHRqQjdGclVHc0pyejFkWXVvYnpac3pGbVNSQXZONFN5eWV5ek4yUmQ2R1dYdHFnWGlUZ3JEN3Jzb0ZDRlNSS25yTFNLNk9vdjlqczFIN2Z3SmRFcFV5UE5hZklpNFFzb01OcWhnVTJIaGJXYWJnOHpSWFNOTjlXZDdqWWQ1QndnZGJWRG53YXdQcG8rNTJmcVlzanhvam5QOVpHK0JUanB6cG8yZjE1UTcrMlVIYTF4aWlzYjJYZzhhZWZNQWI1WE5XOE9VTFpLY1NvNUl2d1pwOThMZENJTHRJcFlMTHdZZXRuWWpmNEZXMjZSY3JBRG53aEpMalV6djNGalJ4eHBKQUJ1TURSUjVYZU9IYVJ0SnlncFB3Tno3cHRvSXpveHlBc0VOS1FqRnhtSTNLSTFDY1hqR1JQUmVwNllQWG9SYm5FaExUZjB3Ynk3Ty84SloySjFuMGdTL0JOUnNHWEdLRkR3RmtvelZvYkpjbWxKejM1cktWbG5GS0c3aWFhNzVVTXZ5U3lVcWt1bVBNaFZhczBGVGlDeDZ5ODZTQlJ1UjN5NEMiLCJtYWMiOiI3OGUyNDEzZmQ3NDgyOTUxM2Y4ZDNkZDRjOGMzYmMzZTIxYTRjMGMwMTI4NGRmNzE0NmJlOGQxMWMyMjc4YmU3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1094085655\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1189433205 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">COI6IIrAbucReABHBN7o3ZaIzRbwBCI1gfwrcfCo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1189433205\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-159150278 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:40:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkpWRTVrNUVvMjBPUlJHSnd5dmdDTlE9PSIsInZhbHVlIjoiRTZLTUpYYmJLSXhVUVFzeEcvTGkvL291bzlXNzBTQWlKS1BFWWpQeGpnZHVaRkZ3NmlCdGN1OVZGQTdSMWMxRVVhbFB3NjdyTFpyczhjT0pWYVVselZDZ201SjIwTE5CMXR0OHQzckZWT0RQbnJWUnhMOG03V2dBQUU1Q2tiMFJYcFVFb3pWckFrOGhrU0N5RkxEUWlBdzFZa2dMbjVUK29idWduUUNPRzFFelcvbXpORWV5eUkrQkxWeFhKNkRKWHkvRkpQU1BUN256UzdsYUY0a0ZyYThxOXgwaXp6am5oYjhSaTg0eGF2QW8zOTM3d0FtZW16cHZ3N3Rpd0N6dDZaWjY1T09FSFhxYmZqMnBuSm9FU09qcE50dVlSN0NNL1pHbjFYL0szd255SDNFZTBpVzRKUHZxT25wakNtQ1JyS1VyU05lK3pISUNQKzZnTHRPUlh2bU9PYWFKalhOYzJ2b09mUTJiOVBlMTExY3kwalZ3NVpzWHU2amc4N29ORzJTRGFlRlFIN05mZWlXOUdSQ2hKUm5zelQxSlgxNHhjUXVPMFNsRDR3OUo5ZmE1bTF4ZGs2ckhpZmdkTTBib2xYaHdabDRDdFM5Vkt3WUozQUY0QjRWeFV4bGFOWGhyNnNCVUhCZlZNY3hZUS9ZRWdxd1VieS9EaGFhblUxWVUiLCJtYWMiOiJmY2MwZWI3MzI0OTc0ZDViYzlkODEyMDEyYWZlZTI2NDgwNDQ4YjEwNmM5NGE3MjI1YTZlNjhmNDg3MDUxM2E3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:40:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkRTWEg5TnhiM0VhdHVoamxYeGIrSGc9PSIsInZhbHVlIjoidngzWjExeU9jOTZCcWZsSFQ4TW5sVGNFOEJlSzhrYTJOMEFsczYrZkNMb0tHRGxDSjI5MjBmY0krSm4zeDFzZVU2TjAvbENaL3BSUnpqdk8rcVUxbXhsazJ2dVBwbUFLTVlzSkRXQlFqcVhQU1ZFVGVQcmRXc0RxSzdacUJkeXRuczY2eFRBcFZrRlNNcmxtY1FQanhZL1JFSWQ4aGU2OFBtVUM4N1l6VytZQ29WMVJrTGVQMkV0ZTM5WDl5eFltUnR0N0MxaXM3bkJBMXVVeXFLakxzYjkwRlphZG1kMFk3dUtma0Fzekx4ay9yck9wZzBGMDIzZFJQYlltdVRlMU0yK2FnMDYyYm1MOTNmRTJnWlA2MitIYU01N05VV3lDV1BXTE5wS2J6d09yZG8xU2tOMjVOMk1JVUxrTTFaejFDeEtvR3FCdS9XelVSaEd5dlJrZytjYUNhejVHbEpnTDJZUmZZd25LVlJGUWdacmFSa1hSbEptOTB6SVgrdTFsYjg5OFBnYWpYdHczakZGUjdFdmROZWNzUUJUYnlSSUVjVW5kTGRmNzJVWm41Wm9LQ3RuRkxsd1hoUDlXR2ZLb3Buanh2Vy9PU1NuMWtnSkJrbUFUTXFiUVRpN2VmVnJPMEpIVTFIZ2N6cGJJYXdhMkExWWpwUmF2eTJIZW5PM1ciLCJtYWMiOiIzNTk0NzhlODhmZjNiMjg2MjhiMjQ4YjM3NmRjYThjMGJhOWFmZmMwM2JkZGE2MmEwYjExODNhMGIzYTlhZTQ3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:40:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkpWRTVrNUVvMjBPUlJHSnd5dmdDTlE9PSIsInZhbHVlIjoiRTZLTUpYYmJLSXhVUVFzeEcvTGkvL291bzlXNzBTQWlKS1BFWWpQeGpnZHVaRkZ3NmlCdGN1OVZGQTdSMWMxRVVhbFB3NjdyTFpyczhjT0pWYVVselZDZ201SjIwTE5CMXR0OHQzckZWT0RQbnJWUnhMOG03V2dBQUU1Q2tiMFJYcFVFb3pWckFrOGhrU0N5RkxEUWlBdzFZa2dMbjVUK29idWduUUNPRzFFelcvbXpORWV5eUkrQkxWeFhKNkRKWHkvRkpQU1BUN256UzdsYUY0a0ZyYThxOXgwaXp6am5oYjhSaTg0eGF2QW8zOTM3d0FtZW16cHZ3N3Rpd0N6dDZaWjY1T09FSFhxYmZqMnBuSm9FU09qcE50dVlSN0NNL1pHbjFYL0szd255SDNFZTBpVzRKUHZxT25wakNtQ1JyS1VyU05lK3pISUNQKzZnTHRPUlh2bU9PYWFKalhOYzJ2b09mUTJiOVBlMTExY3kwalZ3NVpzWHU2amc4N29ORzJTRGFlRlFIN05mZWlXOUdSQ2hKUm5zelQxSlgxNHhjUXVPMFNsRDR3OUo5ZmE1bTF4ZGs2ckhpZmdkTTBib2xYaHdabDRDdFM5Vkt3WUozQUY0QjRWeFV4bGFOWGhyNnNCVUhCZlZNY3hZUS9ZRWdxd1VieS9EaGFhblUxWVUiLCJtYWMiOiJmY2MwZWI3MzI0OTc0ZDViYzlkODEyMDEyYWZlZTI2NDgwNDQ4YjEwNmM5NGE3MjI1YTZlNjhmNDg3MDUxM2E3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:40:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkRTWEg5TnhiM0VhdHVoamxYeGIrSGc9PSIsInZhbHVlIjoidngzWjExeU9jOTZCcWZsSFQ4TW5sVGNFOEJlSzhrYTJOMEFsczYrZkNMb0tHRGxDSjI5MjBmY0krSm4zeDFzZVU2TjAvbENaL3BSUnpqdk8rcVUxbXhsazJ2dVBwbUFLTVlzSkRXQlFqcVhQU1ZFVGVQcmRXc0RxSzdacUJkeXRuczY2eFRBcFZrRlNNcmxtY1FQanhZL1JFSWQ4aGU2OFBtVUM4N1l6VytZQ29WMVJrTGVQMkV0ZTM5WDl5eFltUnR0N0MxaXM3bkJBMXVVeXFLakxzYjkwRlphZG1kMFk3dUtma0Fzekx4ay9yck9wZzBGMDIzZFJQYlltdVRlMU0yK2FnMDYyYm1MOTNmRTJnWlA2MitIYU01N05VV3lDV1BXTE5wS2J6d09yZG8xU2tOMjVOMk1JVUxrTTFaejFDeEtvR3FCdS9XelVSaEd5dlJrZytjYUNhejVHbEpnTDJZUmZZd25LVlJGUWdacmFSa1hSbEptOTB6SVgrdTFsYjg5OFBnYWpYdHczakZGUjdFdmROZWNzUUJUYnlSSUVjVW5kTGRmNzJVWm41Wm9LQ3RuRkxsd1hoUDlXR2ZLb3Buanh2Vy9PU1NuMWtnSkJrbUFUTXFiUVRpN2VmVnJPMEpIVTFIZ2N6cGJJYXdhMkExWWpwUmF2eTJIZW5PM1ciLCJtYWMiOiIzNTk0NzhlODhmZjNiMjg2MjhiMjQ4YjM3NmRjYThjMGJhOWFmZmMwM2JkZGE2MmEwYjExODNhMGIzYTlhZTQ3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:40:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-159150278\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1138360748 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1138360748\", {\"maxDepth\":0})</script>\n"}}