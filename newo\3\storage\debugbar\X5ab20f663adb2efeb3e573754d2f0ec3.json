{"__meta": {"id": "X5ab20f663adb2efeb3e573754d2f0ec3", "datetime": "2025-06-08 15:42:40", "utime": **********.932933, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.271648, "end": **********.932953, "duration": 0.6613049507141113, "duration_str": "661ms", "measures": [{"label": "Booting", "start": **********.271648, "relative_start": 0, "end": **********.834337, "relative_end": **********.834337, "duration": 0.5626890659332275, "duration_str": "563ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.834348, "relative_start": 0.5627000331878662, "end": **********.932956, "relative_end": 3.0994415283203125e-06, "duration": 0.09860801696777344, "duration_str": "98.61ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45152152, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02668, "accumulated_duration_str": "26.68ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.872984, "duration": 0.02457, "duration_str": "24.57ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 92.091}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.911368, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 92.091, "width_percent": 3.973}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (17) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.921387, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 96.064, "width_percent": 3.936}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-548402063 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-548402063\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1238078625 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1238078625\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-391900577 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-391900577\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-297625929 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwl%7C0%7C1982; _clsk=o6pnpa%7C1749397357416%7C11%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjcxTWNPcmRNU0hZbHpsNmpnQ0dYa1E9PSIsInZhbHVlIjoiZzlsYjFxY3RSaUNpdkU3aVZiNlhSdEVDbXZYQndYVWtWNXo1ZDYzUjZyV0Y1bmRKNHlDRUV2N2l1c3hKVkFvZjRRUjM4V042bjJYTHZnYXhPNU82R1dWVnIrMlNqcTRHaG0yRE9kRlMrZXJhUUdDcWd4K3NnTVNza1UwMWlKSXRUSkZPUHM0cVJBUlJTQXIrb3NmRUh1cDh2YlhtUlR2SFBsekE5TGkrby93YjhiZWllTy9GRFZzdExXbUp0NFlORkJwNnFDeW1Ya3JsNTdITkU0bnpNOTdLM0lIbTNvTUYyZzlvaSthZGpVZkVwYkNEcVo5amxnR09yTnVIY2xUQXMwT0JJcE1DbWNhVGFGSDB4b2o4c2ZMRTROQUFVbnBCYmkvVGFUc1ZGZ0NtUmd6ZHRLTmpUM0ZZa3R4S1lzUmNYUGtXc2dHeDJ4Kyt1RGRKU3lmOW9Kd0c0T2d3S1A1cEZHU2tZaFU5Q0pKSkpYZ09vMCt0OHZhYldKY0c2UHA3VVh1YzNWWlBSQVNaZVBQd29XeHFodzF1R21sbEcvVEJUdExHek4yRzlUQVR2U1I5dVFmSzJCRVYzanpqQXNaRERMSUQzTWJqdjZMMnlRUVhmMWNuZlVHWjNheGlJWTNwRUgvRTdWdkozaUI4ZDBNTjBqeHhzTmZrMDBCZ1N1MVIiLCJtYWMiOiJkYmZlY2UwOTI0MjVmZjI0NmZhYmVlYjdhNmI3MWY2YTk3ZjQ0ZDJkNzY3MjIyMzM5ZmU3NzZlMjAwOGUzNmUwIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ik0ySkZkOUw3d1dwUTBFYWRrLzd2Q0E9PSIsInZhbHVlIjoidGRNazlZVm8yL21wMnlKQTN4a0VRQWtoTEtnWUpYTHAwUzAyek9CT25iQzdSUkpodlRlVkFQUDhyZUMvVDZteXc0L2NzTzdJdnk3amYxOWdCQ214VXpXUUIyZUJVRGVqMzcySkdsanU0aXV0S2oxdmlkTGJxSTZ3WlpIV3dLRldyWS9VN2xldTRKalZTVUpoeUVFWEI5eWpiMHJ5QTBzS0h6MmxuY1JCUEdxYjI3RngwbXpiNzc3V3h5MmtPcm9aU2d6ZUlZZVc3emNLU216R1ZZOFBnSnNYNlFGRml1eFBBRURxUGVmSXdFRXIvUy9ZYzlLcjh3eGc3czFmcWVkc011TkloVWNEbTRldE5kL0hPR3NjZnpwVDRhUE5BOGk3azZJTDVKM3dKZ0FNaEI0QzJHcjVoZWJ6d0pmWGFETnd5NFJibEZGeFlJSGFIeHg1a01SdHNyTUN1TERPT3paVXRFTGtIdkJESGJ4MFNxMWxTMXZWZFYzYURJWUlYUXRycmNVZHUvU3UwbTB0c1RaWWJ0NlNOc3ZpeFMxbkZQRGZRb0NxYUtJZVBEQWlSSUVuRzJ0cnQ0TlcvT2xRUkxPcWlkY0ExbE0wUHdRVXF1MFpBK3U1NlVyNCtEZVZPZk9ueCtVNXoxek45enM5OWc0YkpiNjhjM2d0enhBMnQ2ZTkiLCJtYWMiOiJmYTNiNjdmYzFkMjgyNDcwNjhlYWQ4NzMxYWI2MWE5MDU4OTQyYjliZmE5MzNkNzI2OGU1NTU2ZmI4ZDcwMjc1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-297625929\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1281845160 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">COI6IIrAbucReABHBN7o3ZaIzRbwBCI1gfwrcfCo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1281845160\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-47970426 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:42:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im90TEVkTzUzaU9SK3NsSEdIQ3VRZkE9PSIsInZhbHVlIjoiMjVqMm1uSGdDSzdpazVHeXpYVElhTnRsekErRkRHTlQyRGt5OStsNFZQajExVklxV3dzaWpFUmgvSmhZckJvLzNKQzB3NyswbW5NOW9XQXJ2Y2hvdHlUZ0t1SVZPVWVTYXJ6VXFDY0s3RTJ0Z2hsdFhPTnIrVDZqREovSUhOVHJsMVZheXpIbDFReTlwRGpaNzJoejdjeGhneTAxMmlXaXZWbGJlRXViOHBWbnFDV2dqMWxmNjF4ZWxpYjAyR2xwSXZURVlxa0dwU1IzVVdyRWdCaGc1R1QwVjIvZmxXQXpqZTVpeUVPKy9TZWNuL1NCVjZOZk13c3dFbU1pVkxQMWpoNHJNRzJyZDBHT0tRWlU0WXQvSzA0N2NNOVpRNmczSXJsNUo0T2x2R3RnSEIrNmFNaDVGc0p1WW9uVmsxdWhTYmQzN3lxNWMzZkJMNUp3eEc2RzRHaVNZL2JTSUYyZkNiTjRSUzBLbzE0RDUrSjg1SlZIcThVTWEzL0oxRzh0VDV0NzVIR2lNb3gzeWRRckJKbkRXUzZiWXVUc0loU1lVMmEwdXEyZXc5b2Zwc1M3VmFOS1Zic0tuVDJJUCtubllEQ2FGZEJBangvVU51U2VKTUxUWEsvVFllcWpJaE9tTDlWd2ZJUTNYY04yRjlQN1dpTmt4R2U3MUZyRy92ZTEiLCJtYWMiOiJlNWI3ZTc5OWNiNDAxNTgwMjFkMWIyNGRiMWMwNzI3NDZiOThhNDVjMDg1NDQxNTgwMDFkMjVjODExMmVmNmEwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:42:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ii90cHFlUG9IdG1icGJLUFZUTllSWXc9PSIsInZhbHVlIjoiclRtbmhoZ1J1Z3owelB3dVQ5VVZhSmhhMFJLRHNDbml0YVBHUzhuQWdTTW9RaE1YUXlHRW1LeXp1dmlsVmRveTNWWTMzUDhpTHBnd3Y3aXl1Q0Fld2dxckdTOEpmVXZqVExSRWQza0JMVkFoWll6azVxM2hYaXM0cERMNEpFQ2l0U3krTTZ0aVZla0xrWElmNWdseWxiSHNqS09BaGhKemdVKzl2VXZUN051UWcvd0FsV2NWcktVMjRpSDBOYTUxMkFyekdoRVZ1WnVScGIvWVA4NnJQMFNxSlU2dW9qeXloeWFPZE9qc1VIU2RwZ21WR3FkZEhzUkNwS3hXK3RrbG84Nk1CVlFISy9PclI3S0JVWjhGVEh5RkJXVjlFQTM4UVVDME9GelZDbGIvSFdIeU8yRGM3RWM5djJBd25TSXBKbXNZNEdjOHlVb0JJY3J6SVlrcUZibkRpVkoraXlzencyRU9nRUZSOFhBRUtXcllvMFM1cXlXUmhodzdOdVFVMDNVRDlnVGUxWCtuNmcyWUl1YjRpWHh6QzRlekRHNDBzNjhOM05vM3ZvRmVuempwSE9ZUkMxT3E1VjdQU2JHYjVDdVVLYkxuQ0h6MlNXWjFHQXA0akxnem9OemxRMEZNVXowbW5SOFdSbFJTcEZ6b3dtWUQyMEVGTzVLaGJteHoiLCJtYWMiOiI5MzJhZTg4Y2ZjYmE1M2ZmZTRjYTU4Y2QwYmMxNTlmYzk0OTc4YWM5YmMwOTAyMzEwODY4ZTI0MjM0NmQ1YzE0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:42:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im90TEVkTzUzaU9SK3NsSEdIQ3VRZkE9PSIsInZhbHVlIjoiMjVqMm1uSGdDSzdpazVHeXpYVElhTnRsekErRkRHTlQyRGt5OStsNFZQajExVklxV3dzaWpFUmgvSmhZckJvLzNKQzB3NyswbW5NOW9XQXJ2Y2hvdHlUZ0t1SVZPVWVTYXJ6VXFDY0s3RTJ0Z2hsdFhPTnIrVDZqREovSUhOVHJsMVZheXpIbDFReTlwRGpaNzJoejdjeGhneTAxMmlXaXZWbGJlRXViOHBWbnFDV2dqMWxmNjF4ZWxpYjAyR2xwSXZURVlxa0dwU1IzVVdyRWdCaGc1R1QwVjIvZmxXQXpqZTVpeUVPKy9TZWNuL1NCVjZOZk13c3dFbU1pVkxQMWpoNHJNRzJyZDBHT0tRWlU0WXQvSzA0N2NNOVpRNmczSXJsNUo0T2x2R3RnSEIrNmFNaDVGc0p1WW9uVmsxdWhTYmQzN3lxNWMzZkJMNUp3eEc2RzRHaVNZL2JTSUYyZkNiTjRSUzBLbzE0RDUrSjg1SlZIcThVTWEzL0oxRzh0VDV0NzVIR2lNb3gzeWRRckJKbkRXUzZiWXVUc0loU1lVMmEwdXEyZXc5b2Zwc1M3VmFOS1Zic0tuVDJJUCtubllEQ2FGZEJBangvVU51U2VKTUxUWEsvVFllcWpJaE9tTDlWd2ZJUTNYY04yRjlQN1dpTmt4R2U3MUZyRy92ZTEiLCJtYWMiOiJlNWI3ZTc5OWNiNDAxNTgwMjFkMWIyNGRiMWMwNzI3NDZiOThhNDVjMDg1NDQxNTgwMDFkMjVjODExMmVmNmEwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:42:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ii90cHFlUG9IdG1icGJLUFZUTllSWXc9PSIsInZhbHVlIjoiclRtbmhoZ1J1Z3owelB3dVQ5VVZhSmhhMFJLRHNDbml0YVBHUzhuQWdTTW9RaE1YUXlHRW1LeXp1dmlsVmRveTNWWTMzUDhpTHBnd3Y3aXl1Q0Fld2dxckdTOEpmVXZqVExSRWQza0JMVkFoWll6azVxM2hYaXM0cERMNEpFQ2l0U3krTTZ0aVZla0xrWElmNWdseWxiSHNqS09BaGhKemdVKzl2VXZUN051UWcvd0FsV2NWcktVMjRpSDBOYTUxMkFyekdoRVZ1WnVScGIvWVA4NnJQMFNxSlU2dW9qeXloeWFPZE9qc1VIU2RwZ21WR3FkZEhzUkNwS3hXK3RrbG84Nk1CVlFISy9PclI3S0JVWjhGVEh5RkJXVjlFQTM4UVVDME9GelZDbGIvSFdIeU8yRGM3RWM5djJBd25TSXBKbXNZNEdjOHlVb0JJY3J6SVlrcUZibkRpVkoraXlzencyRU9nRUZSOFhBRUtXcllvMFM1cXlXUmhodzdOdVFVMDNVRDlnVGUxWCtuNmcyWUl1YjRpWHh6QzRlekRHNDBzNjhOM05vM3ZvRmVuempwSE9ZUkMxT3E1VjdQU2JHYjVDdVVLYkxuQ0h6MlNXWjFHQXA0akxnem9OemxRMEZNVXowbW5SOFdSbFJTcEZ6b3dtWUQyMEVGTzVLaGJteHoiLCJtYWMiOiI5MzJhZTg4Y2ZjYmE1M2ZmZTRjYTU4Y2QwYmMxNTlmYzk0OTc4YWM5YmMwOTAyMzEwODY4ZTI0MjM0NmQ1YzE0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:42:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-47970426\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-550132511 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-550132511\", {\"maxDepth\":0})</script>\n"}}