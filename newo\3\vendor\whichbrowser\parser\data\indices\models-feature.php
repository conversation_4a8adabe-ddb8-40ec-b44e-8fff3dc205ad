<?php

namespace WhichBrowser\Data;

DeviceModels::$FEATURE_INDEX = array (
  '@AM' => 
  array (
    0 => 'Amstrad Gamma',
  ),
  '@BE' => 
  array (
    0 => 'Benefon Q',
  ),
  '@CK' => 
  array (
    0 => 'CK13[ai]?$!',
    1 => 'CK15[ai]?$!',
  ),
  '@CO' => 
  array (
    0 => 'COCOON',
  ),
  '@EF' => 
  array (
    0 => 'EF71',
  ),
  '@EX' => 
  array (
    0 => 'EX115',
    1 => 'EX118',
    2 => 'EX119',
    3 => 'EX128',
    4 => 'EX225',
    5 => 'EX226',
    6 => 'EX430',
  ),
  '@F1' => 
  array (
    0 => 'F100[ai]?$!',
  ),
  '@GM' => 
  array (
    0 => 'GM360',
  ),
  '@GR' => 
  array (
    0 => 'GR700',
  ),
  '@GS' => 
  array (
    0 => 'GS290',
    1 => 'GS500',
  ),
  '@GT' => 
  array (
    0 => 'GT500!',
    1 => 'GT550',
    2 => 'GT-B2710',
    3 => 'GT-B3210',
    4 => 'GT-B3313',
    5 => 'GT-C3200',
    6 => 'GT-C3222',
    7 => 'GT-C3322',
    8 => 'GT-C3500',
    9 => 'GT-C5010!',
    10 => 'GT-E1282!',
    11 => 'GT-E2152',
    12 => 'GT-E2220',
    13 => 'GT-E2222',
    14 => 'GT-E2202',
    15 => 'GT-E2250',
    16 => 'GT-E2252',
    17 => 'GT-E3213',
    18 => 'GT-E3309I',
    19 => 'GT-M8910',
    20 => 'GT-S3332',
    21 => 'GT-S33(50|53)!',
    22 => 'GT-S35(70|72)!',
    23 => 'GT-S5229',
    24 => 'GT-S5270!',
    25 => 'GT-S5610!',
    26 => 'GT-I6410!',
    27 => 'GT-I8320!',
    28 => 'GT-I8330!',
  ),
  '@J1' => 
  array (
    0 => 'J105[ai]?$!',
    1 => 'J108[ai]?$!',
    2 => 'J10(i2?)?$!',
  ),
  '@J2' => 
  array (
    0 => 'J20[ai]?$!',
  ),
  '@K6' => 
  array (
    0 => 'K612',
  ),
  '@KC' => 
  array (
    0 => 'KC910i',
  ),
  '@KP' => 
  array (
    0 => 'KP500!',
    1 => 'KP570!',
  ),
  '@KU' => 
  array (
    0 => 'KU990i',
  ),
  '@LG' => 
  array (
    0 => 'LG-KU380',
    1 => 'LG-KU580',
    2 => 'LG-KU990',
  ),
  '@LI' => 
  array (
    0 => 'Lightpipe',
  ),
  '@M3' => 
  array (
    0 => 'm3510c',
  ),
  '@MO' => 
  array (
    0 => 'MOTOQA1',
  ),
  '@P7' => 
  array (
    0 => 'P7000',
  ),
  '@P9' => 
  array (
    0 => 'P9020',
    1 => 'P9050',
  ),
  '@PL' => 
  array (
    0 => 'PLS6600KJ',
  ),
  '@PM' => 
  array (
    0 => 'PM-8200',
  ),
  '@S7' => 
  array (
    0 => 'S7350',
  ),
  '@SA' => 
  array (
    0 => 'sam-r560',
  ),
  '@SC' => 
  array (
    0 => 'SCP-5300',
    1 => 'SCP-5500',
    2 => 'SCP-6600',
    3 => 'SCH-W169',
    4 => 'SCH-W279',
  ),
  '@SE' => 
  array (
    0 => 'Sendo Wap',
  ),
  '@SG' => 
  array (
    0 => 'SGH-A667',
    1 => 'SGH-A697',
    2 => 'SGH-A877',
    3 => 'SGH-A927',
    4 => 'SGH-A997',
    5 => 'SGH-D880',
    6 => 'SGH-E250i',
    7 => 'SGH-E250V',
    8 => 'SGH-G600',
    9 => 'SGH-J700i',
    10 => 'SGH-J700V',
    11 => 'SGH-M200',
    12 => 'SGH-S150G',
    13 => 'SGH-S390G',
    14 => 'SGH-T189N',
    15 => 'SGHX660V',
    16 => 'SGH-Z107!',
    17 => 'SGH-Z130!',
    18 => 'SGH-Z500!',
  ),
  '@SM' => 
  array (
    0 => 'SM-B313E',
    1 => 'SM-B350E',
    2 => 'SM-B360E',
    3 => 'SM-B780(A|W)!',
  ),
  '@TE' => 
  array (
    0 => 'tecnot36',
  ),
  '@U1' => 
  array (
    0 => 'U100[ai]?$!',
    1 => 'U10[ai]?$!',
  ),
  '@VO' => 
  array (
    0 => 'Vodafone 575',
  ),
  '@W1' => 
  array (
    0 => 'W100i?$!',
    1 => 'W150i?$!',
  ),
  '@W2' => 
  array (
    0 => 'W20i?$!',
  ),
  '@WI' => 
  array (
    0 => 'WIDETEL WCX150',
  ),
  '@WT' => 
  array (
    0 => 'WT13i$!',
  ),
  '@X5' => 
  array (
    0 => 'X5i$!',
  ),
);
