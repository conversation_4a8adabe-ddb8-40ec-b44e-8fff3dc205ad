{"__meta": {"id": "X2093f63ed14357828f030a1fb2c8201b", "datetime": "2025-06-08 15:29:58", "utime": **********.656823, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749396597.815001, "end": **********.656852, "duration": 0.841850996017456, "duration_str": "842ms", "measures": [{"label": "Booting", "start": 1749396597.815001, "relative_start": 0, "end": **********.55935, "relative_end": **********.55935, "duration": 0.7443490028381348, "duration_str": "744ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.559366, "relative_start": 0.7443649768829346, "end": **********.656855, "relative_end": 3.0994415283203125e-06, "duration": 0.0974891185760498, "duration_str": "97.49ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45484384, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00581, "accumulated_duration_str": "5.81ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.6172159, "duration": 0.00359, "duration_str": "3.59ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 61.79}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.63761, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 61.79, "width_percent": 16.351}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.643789, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "ty", "start_percent": 78.141, "width_percent": 21.859}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-518027261 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-518027261\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-961330521 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-961330521\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-858844266 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-858844266\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1885889371 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwl%7C0%7C1982; _clsk=o6pnpa%7C1749396587596%7C2%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlRrMWhKc1pzeXBGWDFQcWVHSHhzcXc9PSIsInZhbHVlIjoiS1hsdlMyeDRFRTM5RzZ0YnpxVHM0SmZ2aWRRZGtnd2RCL2lsU0cxTXVGRGFpcGFIdU5Bb0RaUDBRd1d4ajJtOUU0RlRjY3BTcGd0cnVOOVBOOGFLemNBbURQM2drZmc3MGo3L092UC9VdlF6WjMvNWJWSWJGL2g5aTh3aU9IdTVFeHFleGRGemJ4RTNWWU1nOTc0WDVxaWtiV3BLL3BPN2hGU1FrNHg5RVRDbzFVS3ZTNEtlN3J4bVQ2SWM4TU1KOEFEWW9vdnVKbWZCT1hmRnBqQXBvTHVEUnBSdC92RTF4YWd0TkpMSDBMbG5zMmd6Zkp5TkV3Szd3RVBrZzBiSnZCVlRVMmJsZnlzS2xuMTNOSDJCQkNaWDRoWEZUalZoa016Si9MTmhvUWVFNExqK2IvMVJSeC9BR3MvRmVoR3EyYmMwa3owbVZTUzVpanY3YThzaXNZb3RWUVpGL0U3NjNHcVh5VmN4MTVjdmYxTUVLZXBDUE1aa0VVcnFseUF2TFBNaDlWYXRpOHJlM0tReWpjakxnMGZhSDRYaFovZkxHZG5IRndLOUFJZmx5Y2RGYzVOT3UvNmFCMUl4dDBPZ3Z5ckhyczgwOXE0S0x3WjE4aTkvWWc2N1EwblI5QTR0dnZ4bTZHVzhhM1JwcDltQklWQlI0MFIvMGtFL254LzQiLCJtYWMiOiI3N2JmZWRkODgxOGE5NzZiNGZhZmM0NDlkMmE4NzU2MTk0YjA0YjAxOTdlODI3ZWNkMmNjZTQ5NDRjYTZlOTlhIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImZoTWlOZk9QeDFyazRBYXhUTUM4Y0E9PSIsInZhbHVlIjoidXNKNG8xa3cxU3FhaFdvazQ1UkVhRmhORVVBdlNkWndhM3VzaGRwMk9wckp4eHR6Y3RKNS8xTjhUVTZNWVBCM2RZNzRtNDNaazQzV0NRY1diNkh4S0I3UG8rQW5JMkJuaEkvSHlOYStZeXZFcXZhT0ZPaUw4U2tVaDFmdVNZNHllVTF0Y282WmN6RW55ampjSXYybUxjQ3EzbEkvSXhSa3FpdmFqZnRiUlVvWGNVbFlwLzQyR2VSQzhPakg4Ti9oaElUcGhhc2tZd3VZakY5ZUQxdE9WdTE3eHgrb0syTWZIWnRDajhidnQrTXNIc2ZRNCtJZ1RCZGdVdWVIeXA1UnZiejlXVEtrZEVTeTFNaVpWRWE1L2NodFBtU2tPRXF2VUJxbXVYOEx0TDVnMGdWSEF1cVl4WlpqaWRRaXZ2aHBidGk5aTdzdWw4MzF4M29IWUpHKzBRalFzS01acmdMZGF5enhFaDRxcGxZNWozOTVSSmcwbjhFdnNDbTFFbVA0SXZhWEFJd3QvSVc2MWpCbFZybnpJNUFRNGVyTWp2SFNXeG5kU2ZySjRDbGVZcmlPemtmK2Q2NUFSdlk1VGx2YzJWQThNa3JuTEtDYVdvUUVnOGVHSy9zRHVtSU80K1RqTGlPUjZieEZEZnc2Z0xKc241eFhEclplVVpTUHllZ3YiLCJtYWMiOiJlNzIzNWE3ZTFjNTM4MmE5NzAxMTM2OGJlYmIwYTE2NjYwMDFhOGMzYTFmMDg0NzU2ZTg1MmQyNDk5ODQ0MGYwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1885889371\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1760929790 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">COI6IIrAbucReABHBN7o3ZaIzRbwBCI1gfwrcfCo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1760929790\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1621240945 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:29:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjRYT1kyS1FyTHNSNTY5czk5bTRENGc9PSIsInZhbHVlIjoiaDBRWG5LaUhVT0VNcDRONUpPQ0VhQ0h4TEdCWkxSeCtzVUlVblZMSzcwYitsNGtSbkdaWDVlYm1veGNKUFh1SXBDYldQVDBwTzhpT0k4MkRzZktTYlM3eU05NkM0ZStEZ25OdlQyVGtqSm9YUHVzZWlzcWYrSFA3Q1p1SCsyeFpTc0pJT1Nra2lXYXJZM1R0OWZ1Wk9iTWRKNUtFbUJ6WU9Ndmx2b1k5alRFOUFhbTdpd3IrUVBrSm91cGFDdDJQUzBwRzBFOG5JWlFCTUNqRnBOMzRhd1AvMGpSSXRpM2djdG85Vnd5NjBpNHlGblVmaDZubS9SdmhoeXo1d3MvT00raFRjYVliclh5akpyNXBhZDFyWFFzdHJyalRySkYwbW9DaDYvam9TRldiN0lLLzAwenVZVzVqRHIxTDBnR2k3VTU1TEgxQnRkN2dCNG0wQlRSRncyV2JNZFR1L09ZSWQ5Tm9iZ0dwY2s1a1NIdkhqY2dFbHVCRHZVR3pKdUl1bzlaMUlvQTEyOXMxMUhhRFJoWVFMS200cUw0TW9tRXJVOVJEcWlUZkJpbGZSTVljOTMxMkhiKzB5cG9MZ2duQUg1TVJqR1Fha1ZHdGJiNzhKWWIzRUYvaTNvWkRwZGJoQmpZS1ducW8xakhyMDlxb2VMdHNQSEEyZkk4cHpmbmQiLCJtYWMiOiI2NGM1ZjU4NTQ5YTdjNzg1N2Y4ZjkzMTVkOTM4MDZmMjZhMTBmNTkwM2IyOGRlNjgxMWU3OTFhN2MxOWRhYTQxIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:29:58 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjA0YnZNOUltKzBNL0s3NlBpTncrVXc9PSIsInZhbHVlIjoiYzk0V04xNkpyTzc3amRKRE1sTldEc2kxUysxcU5sdjJjakxyS29WaHR0SkQ4bmJsT1l1ejgxU0p3NGpFT2pkdkx6L000a1pYd3NPbGNLYlFwWjYwWFJqVlg5QjQ4QTBLMDdCbVE0bVJDN29rQ2pUSE1KejlKeDRPd3VIZkVCcytCMk5MUDNrNWVyaGxwSUtQL1NJSnh4dWFOQjljN0lpbU5yZzI2bDNtSFROUmdjVXFjNHhITmNSR2FOOE9RSlhmc0svWDE2eXhYR2d5SXhPS2pDZmZVeWE0RlN5K2JXTTdLakJDOWZSS1o3Sy9xOEdTdm1DYmdrVFJrL1V0b0xRSnpnNWpiSUtpckNRWTl6R1dFV0xRMityaG5PczFOYlZydEhnR0REY2dnNnZHRU1oYkJoVmZoY3IzZUgzY0ZNZEliTFBFLzJYYi9MTVpGeG5pejR2S0k0aDhENmtXVDJPOHlNeDN0Ym8yOFdhdXVmUUt6aWtYcU9WcmFrQ09vb244Z1VBYWdsamtuNUI5YURWSVFQRVh1enRxcDNNay9abFFUMGdXbGFBTkJya21ucXlWTmJ1MzBoS1ovTWxEYmhoS2o2VUdMU0hsWUdSK3FQczg5RVl2T3JUdEFINXJoZ0RJUUJUNjVmSGJYZDVZZkNCamN2N1NOdWxqVmlhQXVYVnciLCJtYWMiOiJhNjFlNTg4MjAyZWQwOGE0YTM5MGRmMjQ5MTM4NzYzNmQ0ODdkZWM2ZTNhYjUzZGFjMGExNDBhMGFmMDE4YTdjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:29:58 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjRYT1kyS1FyTHNSNTY5czk5bTRENGc9PSIsInZhbHVlIjoiaDBRWG5LaUhVT0VNcDRONUpPQ0VhQ0h4TEdCWkxSeCtzVUlVblZMSzcwYitsNGtSbkdaWDVlYm1veGNKUFh1SXBDYldQVDBwTzhpT0k4MkRzZktTYlM3eU05NkM0ZStEZ25OdlQyVGtqSm9YUHVzZWlzcWYrSFA3Q1p1SCsyeFpTc0pJT1Nra2lXYXJZM1R0OWZ1Wk9iTWRKNUtFbUJ6WU9Ndmx2b1k5alRFOUFhbTdpd3IrUVBrSm91cGFDdDJQUzBwRzBFOG5JWlFCTUNqRnBOMzRhd1AvMGpSSXRpM2djdG85Vnd5NjBpNHlGblVmaDZubS9SdmhoeXo1d3MvT00raFRjYVliclh5akpyNXBhZDFyWFFzdHJyalRySkYwbW9DaDYvam9TRldiN0lLLzAwenVZVzVqRHIxTDBnR2k3VTU1TEgxQnRkN2dCNG0wQlRSRncyV2JNZFR1L09ZSWQ5Tm9iZ0dwY2s1a1NIdkhqY2dFbHVCRHZVR3pKdUl1bzlaMUlvQTEyOXMxMUhhRFJoWVFMS200cUw0TW9tRXJVOVJEcWlUZkJpbGZSTVljOTMxMkhiKzB5cG9MZ2duQUg1TVJqR1Fha1ZHdGJiNzhKWWIzRUYvaTNvWkRwZGJoQmpZS1ducW8xakhyMDlxb2VMdHNQSEEyZkk4cHpmbmQiLCJtYWMiOiI2NGM1ZjU4NTQ5YTdjNzg1N2Y4ZjkzMTVkOTM4MDZmMjZhMTBmNTkwM2IyOGRlNjgxMWU3OTFhN2MxOWRhYTQxIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:29:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjA0YnZNOUltKzBNL0s3NlBpTncrVXc9PSIsInZhbHVlIjoiYzk0V04xNkpyTzc3amRKRE1sTldEc2kxUysxcU5sdjJjakxyS29WaHR0SkQ4bmJsT1l1ejgxU0p3NGpFT2pkdkx6L000a1pYd3NPbGNLYlFwWjYwWFJqVlg5QjQ4QTBLMDdCbVE0bVJDN29rQ2pUSE1KejlKeDRPd3VIZkVCcytCMk5MUDNrNWVyaGxwSUtQL1NJSnh4dWFOQjljN0lpbU5yZzI2bDNtSFROUmdjVXFjNHhITmNSR2FOOE9RSlhmc0svWDE2eXhYR2d5SXhPS2pDZmZVeWE0RlN5K2JXTTdLakJDOWZSS1o3Sy9xOEdTdm1DYmdrVFJrL1V0b0xRSnpnNWpiSUtpckNRWTl6R1dFV0xRMityaG5PczFOYlZydEhnR0REY2dnNnZHRU1oYkJoVmZoY3IzZUgzY0ZNZEliTFBFLzJYYi9MTVpGeG5pejR2S0k0aDhENmtXVDJPOHlNeDN0Ym8yOFdhdXVmUUt6aWtYcU9WcmFrQ09vb244Z1VBYWdsamtuNUI5YURWSVFQRVh1enRxcDNNay9abFFUMGdXbGFBTkJya21ucXlWTmJ1MzBoS1ovTWxEYmhoS2o2VUdMU0hsWUdSK3FQczg5RVl2T3JUdEFINXJoZ0RJUUJUNjVmSGJYZDVZZkNCamN2N1NOdWxqVmlhQXVYVnciLCJtYWMiOiJhNjFlNTg4MjAyZWQwOGE0YTM5MGRmMjQ5MTM4NzYzNmQ0ODdkZWM2ZTNhYjUzZGFjMGExNDBhMGFmMDE4YTdjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:29:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1621240945\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-958784516 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-958784516\", {\"maxDepth\":0})</script>\n"}}