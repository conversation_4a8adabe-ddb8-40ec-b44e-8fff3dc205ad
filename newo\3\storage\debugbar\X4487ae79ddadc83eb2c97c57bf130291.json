{"__meta": {"id": "X4487ae79ddadc83eb2c97c57bf130291", "datetime": "2025-06-08 15:29:25", "utime": **********.775021, "method": "GET", "uri": "/users/17/edit", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.085906, "end": **********.775054, "duration": 0.68914794921875, "duration_str": "689ms", "measures": [{"label": "Booting", "start": **********.085906, "relative_start": 0, "end": **********.588456, "relative_end": **********.588456, "duration": 0.5025498867034912, "duration_str": "503ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.58847, "relative_start": 0.5025639533996582, "end": **********.775058, "relative_end": 4.0531158447265625e-06, "duration": 0.18658804893493652, "duration_str": "187ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 51558360, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x user.edit", "param_count": null, "params": [], "start": **********.745752, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/user/edit.blade.phpuser.edit", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Fuser%2Fedit.blade.php&line=1", "ajax": false, "filename": "edit.blade.php", "line": "?"}, "render_count": 1, "name_original": "user.edit"}, {"name": "3x components.required", "param_count": null, "params": [], "start": **********.763725, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/components/required.blade.phpcomponents.required", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Fcomponents%2Frequired.blade.php&line=1", "ajax": false, "filename": "required.blade.php", "line": "?"}, "render_count": 3, "name_original": "components.required"}]}, "route": {"uri": "GET users/{user}/edit", "middleware": "web, verified, auth, XSS, revalidate", "as": "users.edit", "controller": "App\\Http\\Controllers\\UserController@edit", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FUserController.php&line=235\" onclick=\"\">app/Http/Controllers/UserController.php:235-250</a>"}, "queries": {"nb_statements": 10, "nb_failed_statements": 0, "accumulated_duration": 0.01569, "accumulated_duration_str": "15.69ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.643193, "duration": 0.00392, "duration_str": "3.92ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 24.984}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.6616251, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 24.984, "width_percent": 7.584}, {"sql": "select * from `roles` where `created_by` = 15 and `name` != 'client'", "type": "query", "params": [], "bindings": ["15", "client"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\UserController.php", "line": 238}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.668335, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "UserController.php:238", "source": "app/Http/Controllers/UserController.php:238", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FUserController.php&line=238", "ajax": false, "filename": "UserController.php", "line": "238"}, "connection": "ty", "start_percent": 32.569, "width_percent": 5.417}, {"sql": "select * from `warehouses` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\UserController.php", "line": 239}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.674569, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "UserController.php:239", "source": "app/Http/Controllers/UserController.php:239", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FUserController.php&line=239", "ajax": false, "filename": "UserController.php", "line": "239"}, "connection": "ty", "start_percent": 37.986, "width_percent": 5.672}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.7039618, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 43.658, "width_percent": 8.094}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.708532, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 51.753, "width_percent": 5.29}, {"sql": "select * from `users` where `users`.`id` = '17' limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\UserController.php", "line": 241}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7165499, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "UserController.php:241", "source": "app/Http/Controllers/UserController.php:241", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FUserController.php&line=241", "ajax": false, "filename": "UserController.php", "line": "241"}, "connection": "ty", "start_percent": 57.043, "width_percent": 7.075}, {"sql": "select `custom_field_values`.`value`, `custom_fields`.`id` from `custom_field_values` inner join `custom_fields` on `custom_field_values`.`field_id` = `custom_fields`.`id` where `custom_fields`.`module` = 'user' and `record_id` = 17", "type": "query", "params": [], "bindings": ["user", "17"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/CustomField.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\CustomField.php", "line": 63}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\UserController.php", "line": 242}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.721569, "duration": 0.003, "duration_str": "3ms", "memory": 0, "memory_str": null, "filename": "CustomField.php:63", "source": "app/Models/CustomField.php:63", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FCustomField.php&line=63", "ajax": false, "filename": "CustomField.php", "line": "63"}, "connection": "ty", "start_percent": 64.117, "width_percent": 19.12}, {"sql": "select * from `custom_fields` where `created_by` = 15 and `module` = 'user'", "type": "query", "params": [], "bindings": ["15", "user"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\UserController.php", "line": 243}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.728715, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "UserController.php:243", "source": "app/Http/Controllers/UserController.php:243", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FUserController.php&line=243", "ajax": false, "filename": "UserController.php", "line": "243"}, "connection": "ty", "start_percent": 83.238, "width_percent": 5.481}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` = 17 and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["17", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "user.edit", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/user/edit.blade.php", "line": 100}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.766391, "duration": 0.00177, "duration_str": "1.77ms", "memory": 0, "memory_str": null, "filename": "user.edit:100", "source": "view::user.edit:100", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Fuser%2Fedit.blade.php&line=100", "ajax": false, "filename": "edit.blade.php", "line": "100"}, "connection": "ty", "start_percent": 88.719, "width_percent": 11.281}]}, "models": {"data": {"Spatie\\Permission\\Models\\Role": {"value": 9, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\warehouse": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2Fwarehouse.php&line=1", "ajax": false, "filename": "warehouse.php", "line": "?"}}}, "count": 13, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => edit user, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1815115176 data-indent-pad=\"  \"><span class=sf-dump-note>edit user</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">edit user</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1815115176\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.71582, "xdebug_link": null}]}, "session": {"_token": "fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/users/17/edit", "status_code": "<pre class=sf-dump id=sf-dump-450229406 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-450229406\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-613822981 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-613822981\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1521624158 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1521624158\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-176622352 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=xh9goj%7C1749396559064%7C9%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Imw1UVpTYWNOWkhyWVQrSWFRN3Vka3c9PSIsInZhbHVlIjoiR3NUVmlscVdOOTNNWkxsdGtVSVppUzhUUkdiVlNpN2F2Wi8xSHBxaDBlQ2pTUEE5M000Uk5NU2dnNTV6RGk2aGpPcEs2ajhUaWtacExUcE1yMXJDY25XMGNXb1Z6bWwvWm40TkNQNVVlZzdGc2E2V2RuL2ZZWmhmcGhINTdWb3poQzB3ZmY4WEZ4TnhxV1RtRW85K2IwTWNGWXlSRVVzdnFIWVljajBOdDZ4cnkrTENQWXJuSGcxSFNGdHhPd29aZWg3SzYxTEZCQ2lSQzR0ZmxDVVd1V3lZYW8zZksxaDRobXZ0S1NBRkhvRDhiMmVNRVpCVXJvcUt0dDB0NHFGYlVSdmdNTGcvYXpIb2hvekg2eU12ZzJadVB4NmZUb1hneGxmdS9GTDlLVmVpT2l6Ry81WkE0ZVQvOVVLbjgrWGNjbjNSTHNQUDJXeGVEaTViMEx3c2x0N1RyZWx0T21hYlk2Y3h4OHJRWkhPY3A1OU9iWmd2T2V1THlOMkdSRkVaeGIvQjV5UlpaZ1ZIYllkTDNielkrYUhzNkQwdTdtcXprOGJocmIrdDFXSlhpZXdTNWF5TDVmSnJ6djhYMHU2TXhUSnZ5WnY5eFFodU9EdWhjSWc4UUZTUzNib1hHeXMwejZEY0EyMVh2bHF5d0pzTkl5N1FCdG9yWFFQcUQyWTUiLCJtYWMiOiI1ZmYwZDE0ZTJiOGYwYzU4YTNlOGRlMjQzNDE3NzRiNmUwNzBjYWE1ZmZlMmZkMDVhNmU0ZmMxMDJjYzU4OTI0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IndKRk1oNmZzMEN0WGJJS1NlTmVlZFE9PSIsInZhbHVlIjoiYzVSeHZueE9YQzF6WTFzUXNEbWgwUVBSTFdnWUo5OXdmdmxxQ3g2VDhKdDhJTlREbE95TldWcDZ1YzBnMVJOQ2FFTkJ1bkJjK2Jzd3BRaGpYcTZXbkdRR1Y1ZlZvcWJXYkxIWG51WjlpMmhieWt5dXlUZ1QvSzdHR0x5SWdOZ3p6aXFPNkdnL283UkI4ZHM5TlhycC9XbGdncktkaWg1czA0T3lXMlFsbnZMa1VYcW8ra1V0SFFJL2RMWWRyY2x3SEhFZm9SZzd6SnZMbWVEMnBlSTJ1MGU0aE1oZWpiQkNjd2V2WnFHVmZKcjROMUFJZHZEVk1YTW9yRDQzMTdzTm5MM0pQbzh5N3k1OTc5cGFBcTh2QjNFeWVLeGc3Y000WktCeGlMbm11NUZGa2NRa3JYcHV1N1BzbzhtenpJcUtIckZSSklUc0s3TzNQcE4vbHViOEZYUUVJWExpTVcxUHpQemk5RlROUEV3Ukx5SEJIUmpVRjNJYjVVajZKMC8wdXBadFQ4OHNla0NxMmhoL1ZNTkhhb0gwK2xxLzdHdUtyZFRFVHhzZnpmNkxtR3RWY05wdlROLzNJMHhtdUxTOUUrZzBVbnFBcUdDQzNRdHg2UlRKNWF5bktsZERCdWlJWmN0SEFOMXB4Tm5hcHViVTZybmhUNjh6aU1NL3lVU00iLCJtYWMiOiI3MmEwYzA3MTYzZWQ5M2IyMTg2YjVkOWY4NTIyYTBlMjhlNWUyOTY1NmEyMjA4NzAxYWJmYjFjYjIwZDU4MmYxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-176622352\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1640947192 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">l7xz5q4Zkpyo7Zd0rhHsE8M6pi3wac4XFWhsi0lQ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1640947192\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:29:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkgvTkdFbnJ5VWtCYlAvZlNJaytUaXc9PSIsInZhbHVlIjoiZ3c5WFRNYXpiM2E4VElVektva08xbURUWUV2QUVrSXh0cThkOHorYmlRNmRlZCtpNUt3bXFmYXdybVVYT1Vvc1JwdGltWHVoSWJqdmM1MTFJaG5sMFZTQXJEbTExWVU0bkc1Rm5XMWlnakJOUVN1Q3QxdWdJNmVYTDhFN0pLcmdleTBEREczTHpIdkNxUmlpMElQc1Qya1ZJZ2N3WGk1Skl2QzJPNkZzOVpjSWF4ZUlmaForanNTZXk0VU11cXNwaHZFVEQvNDVQTkZpUHVhR2NKdFUzK3BlcW1OZjl5c2RJdHhCb0dZaGpURzRKbkJ4ZkF5ZDNWaklNdjR3M2dxZ291ZXZxNCtkbHhETmRCVmRhaVVKT0swZ2dPTmwrOHRzM1RvOUZ2WFMvbUFwQk81ZkltYzVySFA4dnptS1J1REo5R2g5OGFoQWhGU2E0Y0JEbThMeGd4Z2tBOHBadmhkS0VrTXordXFLMVFpSkEweS81djR1OGNrOWlVV0IvNXpmWGpwVEp5UDBadjJZUUk1SC93c1ZLZkljdHlrdG0xdXYxYndPajZCOGFtTzczODlDTWtERE4rNjVuZnVMajdzT1YxMVBveC9OYmFnQlQweW1PdjYvd0dPVjdOS0lONEhvNEJOZW1wVmpHSDRxWGkzSjlXTnZ0Vk1hRzg3UVBqRi8iLCJtYWMiOiJhZjFmYTBhOTBhODdiM2YxYTJjMTE4MjFhNjFmMWYyYzQwM2RlMTNiMThjNzRlOGNmNDk5YTE0MTY0Y2ZhYTlmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:29:25 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkJLVDBnYU5jMGtMMERtY1Aycm0zNFE9PSIsInZhbHVlIjoiUUg1cGhFcUx6S2Z0TFlCSFh2eEtQbVlRdmdqd09KUk5HZFdGb3l6OEZ0L1pqZi9nNVlsWEhGb0l2TVk1K2tHcm5tSCtPTDZjR3d5emlFNGtpanJhZ1Z4L0Q4a1h4cmRHUlFwMFBtL05halNsUWsrVjlER0lwdHJKdXhQRGx1Qm9NNDcxYzRKbnpFbUVtYzZUM3gyT2hackVVY3hIM3M1U3NzWnBsb2FFL1U1aGRsM1pFTlg5RE5kRHdrNlpZWjhXNFZWZFUzem42cHBWUHNqckN2NUxpRng1UHdJY2g1U3FDZTFTM1Q0QWkzQTlOMFl2aFplK29sS0lqRTducGJ0M3RCQUhtTVBKUVlvSzJTamRiRmtZVE52c1VDNzdGTjRZU1BNSDZyZzQwdDVnZmpJL2NML2dKQUE0dFMrTjFPTUl1YnBqZThOQVM5dG5SYzJlVGx5U1d0WUFucFNtZG5jNE1TeHJreGVzWHNkSGhtVTFYM2JrNUw0Y2lMYzNHWXV1bUh0ZVJEdkthTmJ3bk50NVhsYTdQSEZIT3Z5SVpzcWFPOGlOaHBtRE1JbHo5a2ZWdDdCckM1ZVhvUmVPMFAzUnJORytBQVYwdy9OM0R4Q3ByTTFnRmphVzVUQktsa1RFcSsrQzVJU3BhcENadmtFRGptcFJZS2pQSlpyb293a1oiLCJtYWMiOiI0ODYxMDQ0YzJmMjU1OWUxYTZkZDQ0ZGU5MmVkODE5NGI3ZTg2OTgxNjhiZGRjNGZjNTg1NThmMWNiY2RhYWUzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:29:25 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkgvTkdFbnJ5VWtCYlAvZlNJaytUaXc9PSIsInZhbHVlIjoiZ3c5WFRNYXpiM2E4VElVektva08xbURUWUV2QUVrSXh0cThkOHorYmlRNmRlZCtpNUt3bXFmYXdybVVYT1Vvc1JwdGltWHVoSWJqdmM1MTFJaG5sMFZTQXJEbTExWVU0bkc1Rm5XMWlnakJOUVN1Q3QxdWdJNmVYTDhFN0pLcmdleTBEREczTHpIdkNxUmlpMElQc1Qya1ZJZ2N3WGk1Skl2QzJPNkZzOVpjSWF4ZUlmaForanNTZXk0VU11cXNwaHZFVEQvNDVQTkZpUHVhR2NKdFUzK3BlcW1OZjl5c2RJdHhCb0dZaGpURzRKbkJ4ZkF5ZDNWaklNdjR3M2dxZ291ZXZxNCtkbHhETmRCVmRhaVVKT0swZ2dPTmwrOHRzM1RvOUZ2WFMvbUFwQk81ZkltYzVySFA4dnptS1J1REo5R2g5OGFoQWhGU2E0Y0JEbThMeGd4Z2tBOHBadmhkS0VrTXordXFLMVFpSkEweS81djR1OGNrOWlVV0IvNXpmWGpwVEp5UDBadjJZUUk1SC93c1ZLZkljdHlrdG0xdXYxYndPajZCOGFtTzczODlDTWtERE4rNjVuZnVMajdzT1YxMVBveC9OYmFnQlQweW1PdjYvd0dPVjdOS0lONEhvNEJOZW1wVmpHSDRxWGkzSjlXTnZ0Vk1hRzg3UVBqRi8iLCJtYWMiOiJhZjFmYTBhOTBhODdiM2YxYTJjMTE4MjFhNjFmMWYyYzQwM2RlMTNiMThjNzRlOGNmNDk5YTE0MTY0Y2ZhYTlmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:29:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkJLVDBnYU5jMGtMMERtY1Aycm0zNFE9PSIsInZhbHVlIjoiUUg1cGhFcUx6S2Z0TFlCSFh2eEtQbVlRdmdqd09KUk5HZFdGb3l6OEZ0L1pqZi9nNVlsWEhGb0l2TVk1K2tHcm5tSCtPTDZjR3d5emlFNGtpanJhZ1Z4L0Q4a1h4cmRHUlFwMFBtL05halNsUWsrVjlER0lwdHJKdXhQRGx1Qm9NNDcxYzRKbnpFbUVtYzZUM3gyT2hackVVY3hIM3M1U3NzWnBsb2FFL1U1aGRsM1pFTlg5RE5kRHdrNlpZWjhXNFZWZFUzem42cHBWUHNqckN2NUxpRng1UHdJY2g1U3FDZTFTM1Q0QWkzQTlOMFl2aFplK29sS0lqRTducGJ0M3RCQUhtTVBKUVlvSzJTamRiRmtZVE52c1VDNzdGTjRZU1BNSDZyZzQwdDVnZmpJL2NML2dKQUE0dFMrTjFPTUl1YnBqZThOQVM5dG5SYzJlVGx5U1d0WUFucFNtZG5jNE1TeHJreGVzWHNkSGhtVTFYM2JrNUw0Y2lMYzNHWXV1bUh0ZVJEdkthTmJ3bk50NVhsYTdQSEZIT3Z5SVpzcWFPOGlOaHBtRE1JbHo5a2ZWdDdCckM1ZVhvUmVPMFAzUnJORytBQVYwdy9OM0R4Q3ByTTFnRmphVzVUQktsa1RFcSsrQzVJU3BhcENadmtFRGptcFJZS2pQSlpyb293a1oiLCJtYWMiOiI0ODYxMDQ0YzJmMjU1OWUxYTZkZDQ0ZGU5MmVkODE5NGI3ZTg2OTgxNjhiZGRjNGZjNTg1NThmMWNiY2RhYWUzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:29:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}