{"__meta": {"id": "Xec6ea90cf3deaebdcf39a84cd5bfed5e", "datetime": "2025-06-08 15:42:57", "utime": 1749397377.016606, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.338626, "end": 1749397377.016628, "duration": 0.678002119064331, "duration_str": "678ms", "measures": [{"label": "Booting", "start": **********.338626, "relative_start": 0, "end": **********.921808, "relative_end": **********.921808, "duration": 0.5831820964813232, "duration_str": "583ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.921823, "relative_start": 0.5831971168518066, "end": 1749397377.016631, "relative_end": 2.86102294921875e-06, "duration": 0.09480786323547363, "duration_str": "94.81ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45152152, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.018269999999999998, "accumulated_duration_str": "18.27ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.963872, "duration": 0.01672, "duration_str": "16.72ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 91.516}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.992718, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 91.516, "width_percent": 3.777}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (17) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": 1749397377.003522, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 95.293, "width_percent": 4.707}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-257514075 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-257514075\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-194425545 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-194425545\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1095512343 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1095512343\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2069837329 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwl%7C0%7C1982; _clsk=o6pnpa%7C1749397371372%7C14%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlN1MG1YeFQ4NmgvRkhSdHN5bE52Tmc9PSIsInZhbHVlIjoiMVVrblRUZWZ3STEwVy9ZMENOK1paK0NzVnFaV2h3Z1hENGhIY2FTRzVLL2tOV3NTQUNmUkRNNmYrZHRTMDdVRkl4QmJkTUNNTHE2ZldzY0xFczh0Ykt4eUs1b3ZGVklyOXJjSHU2MTFRRzNhQlhGUjZ0MXYyQUdVSEZUYXFRSW0xa0NBU1pVQlVEMnRUMGF6TlBHT3g5Z1FLaCtGYnF6WTl2YXNMeGlIeHJucTZBbUxNYzFGVVVmeUxpck5hUE54SkhsNkRkSTVabFoxblUzN293Qmw1T1diMi9ORWNldEVuQlZUZTJWeldmd2pLakdkYjQzQ1RTcVlvVXBFVm8weVJldVROUCtoM3ViOVF2RTNhT29FRW01WWpNMzJUUVkxcGxQSHJRWCt1cnlvNjdqSnovaEhHaHpXZkQ3ampVb0tDUjJ1RnFUYUxJWU1XcE9IdDdERUxyb0lWWWRUa2RhcWJPSlNqb040QWl3RTd1WWIwZSthNk1CREdlYzZvZzR3OGhINERKNGJYcEpURXdIYkJHYktPM2JCYkZvNXdud0JpZU1LRklwdEtZbDZPVzVuQWdGei94SXhHcldXOGk4R0drcXZOQ2RUUUdqdnUwUHFPb0xiVzlkOVRPaHhoRjl0RmVJUkJQNTNFck05OFJ3MVpnaVhQNllieHZDNEtrVzkiLCJtYWMiOiI0YTcxMmEwNGMwZmNkZjAzNzRjNGVhZTE1MjRiYzE1ZGM4YzliNGFjZDQyMmI5ZDY0NTI0ZjE3NmJiNTBiOGZiIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlBldTJrd0I0NmczemY3SDN1bVZVU2c9PSIsInZhbHVlIjoiTGxiODFzbWs3K20rNHNpd3RMUmdNUGh1bDlUaTBNTjE2elNMOWVLUTNjTENnK1JSajhoMThIalMzYWpBT3pJTUlvYUxhQVZMZG1vNWZGM3h6eGxsWnluVVdPaU5pSENTNDdsS2F3N1VGS1JycDJqUUJ4aTdNYnNBV3laK1JwQWpaUEFualErckJNeEgxSEJSUlk1VkYwU2wvZkdHcnJnWXVkQ0IzZXVDRTRzL0pxTWxKYkxDcmJQVUU5NTNHWlk5V3VNZlR2N0U3MXQ3enJjNlVrMVZMd21GRjFNOGZ3UVJpZmc1dC9uWnQ5NW4yQ2l4Ym80ZDJvYWdyM1R1UE54YmZQNmpETDZuT3ZTSm5PdkJPdUhvaklYMHRlUUlNUzM1azhXQ2E4ZlVoZ2tZMXVaTXh2OVQvaVJzeHVKTnVQd0swbGRNd0RrRVhYOTVlckpTclJGL3RWNEFZZ0oyRXIrTHpBWkpZM1pISzNBaVhqdTFnMVA5bFVKQllTQ1dBZHB4Y2NFbmtwYTNQYW1MRUp2WTdwdUVCdW1uWkhUckN1UVBIWnpWOEpYUXU0QXpnSEU2eG9Ld095R25MczZIbDdOMlRSVVFYcVVUNHZqWWUrQ0xDYlhOOTlWcjFka21ERWVJNjk1Ri8rVHFqcndlbW9tOFkwb3VVNnJIMm53TGZjRFUiLCJtYWMiOiI1NzBlYzRkNDA3M2E5YWU1MDI2MzdiNDUyMzVjZWNjYmRkMWYzZWUxNTNiZGVjMmM3NDY3NGViZWNhMDkzZTcwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2069837329\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2081375461 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">COI6IIrAbucReABHBN7o3ZaIzRbwBCI1gfwrcfCo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2081375461\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:42:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjBaUXhCNkFhNmxyaTVNWFJNRGRNL1E9PSIsInZhbHVlIjoiQmtwaWFEem4zVHRyc1hOMGZSZVp0eUs0Rkduc21vcERvWXFmdDFzVitETVpsRUkyQjVmRGtiMGJ4NzVTLzRXdml3TjJkSlVYTWprcVZwVVEwQk9iY0RMM3VGRGIzcnoyaVNJQ2hHUHdIanduSnZXYWdnaWtRaWx3Wm1RbEE0VExubEErYUYvamxzZHFCY1BFRlRlSENmT29UREdVaG82bHRDY1N0Umc0UVF6MHBQQXZqVEMzWndWMmQ1V0JzTmRCejVWYnFLcmRuRWp5MnFmVW14S3RRRmJDamxQbTc5cTVIOEZPZ0duK05PY05lTWlpRE1sUldZWlhJRWZGdisvcFNFZWdrV2haeU1LTkoveUxOa1FVMXhRbG9ZQldCUEl2eTdYSHZsbDZXZmNKZ2dvdllrNE8rczJEVi9Rc2ZuWGZMUGhMK0FSYVRZZzBhajdpREhZNGgxMms5Tzl0cVY2K3pLU3dUcHlKMXZ6NmhrdDNqcGVUT0F4UVdpNjMzcFYwRWhYeVpSQ09WL2RvOEJ6UlIvQUdkVWpwQ0tkNWVsVHBidDhZcXNNYmh2anlYdGNDN3pqN1htM3ZMbFV5MXdCanVsMjFIMWlOWExreDNOZ1lwanRrNEdvMUhwclBabHc0YXh4dTBJcXJZMXdmQUtEMUdWTTJ3aDlaRUV5MHhxK00iLCJtYWMiOiJlNmIzMWRjMWVjNDc1ZWFjYzY4NDI2NWRmZjEwODhlMGNkMjgxZDAxZGNlYjVlNGMwOWFiOTY1OGZjNmUzNmY2IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:42:57 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IitsbnptbXcxNFVEN0ZYVWc0SEIxS1E9PSIsInZhbHVlIjoiRC9IMnF6cDgyWENmSlVTRHFsME9nZE5waUh6QzhHSGM5dytUdmxsK0RIZFgxcGYvZkk3ZFJpMzA3UHFzOWVpRDNGZEhvOGtKa3I4NlJqMzViSUpReTZkQTJhSFZsNjFubWFWTklsU2VSRUtxcFRmalpCRWhHMFl3dGFqRTFDUUtROEhpQXVPSGh3c3dob2ZIRytrclVWVEdscHMrOHp2c1ZCVVM0a1N2b3JTWXhuWU5IbjQ0L2JiazYxb0NtWTE4RGNGa1IvSWQvdVptalMwUkMrM0N0S3hSeE16VXVOdElGVS9JN2pVek9PcFJST1RmejdCdXFtczduWFFEWVJhVnV1NW5xaGVHaHRPak80eWZwUFJ4R1NYUVY0UGJNQjNmNTVzRzNFd21zM1htYnVJNEkvVFFSL251U1M2eEdZOUtxMWkzZ1AxZmpxZXBodGpBZGNQZFBpMVdzREtWTG0vWis0aFhhWm91bStQRUVnQnA5MGs0c0t0Q1FSdmFseWt0TEk2MnJ5R1dEbW5ERzFLTlF3MUpCb0pMaDZaL0QreVF4R0wrQXdlVTdoK1Y0U0xJL1dBNllPUlRhb25QUm85TWxmeUhqTysrcE5vYXMySFBFc0xRRXRHUHdvQXJRdWdNNE9NMkNlVEwwQklUanpHeCtBNGJXeVF6b3NibENCZFgiLCJtYWMiOiI5ZTc4MTVkNjRiYjIzMTk4OGM2ZTFlOWJiODljNzQyOWQ5YTIyNTU4YWYwMTUxOTRhMGNiNjI1NTYzNTI0YzQwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:42:57 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjBaUXhCNkFhNmxyaTVNWFJNRGRNL1E9PSIsInZhbHVlIjoiQmtwaWFEem4zVHRyc1hOMGZSZVp0eUs0Rkduc21vcERvWXFmdDFzVitETVpsRUkyQjVmRGtiMGJ4NzVTLzRXdml3TjJkSlVYTWprcVZwVVEwQk9iY0RMM3VGRGIzcnoyaVNJQ2hHUHdIanduSnZXYWdnaWtRaWx3Wm1RbEE0VExubEErYUYvamxzZHFCY1BFRlRlSENmT29UREdVaG82bHRDY1N0Umc0UVF6MHBQQXZqVEMzWndWMmQ1V0JzTmRCejVWYnFLcmRuRWp5MnFmVW14S3RRRmJDamxQbTc5cTVIOEZPZ0duK05PY05lTWlpRE1sUldZWlhJRWZGdisvcFNFZWdrV2haeU1LTkoveUxOa1FVMXhRbG9ZQldCUEl2eTdYSHZsbDZXZmNKZ2dvdllrNE8rczJEVi9Rc2ZuWGZMUGhMK0FSYVRZZzBhajdpREhZNGgxMms5Tzl0cVY2K3pLU3dUcHlKMXZ6NmhrdDNqcGVUT0F4UVdpNjMzcFYwRWhYeVpSQ09WL2RvOEJ6UlIvQUdkVWpwQ0tkNWVsVHBidDhZcXNNYmh2anlYdGNDN3pqN1htM3ZMbFV5MXdCanVsMjFIMWlOWExreDNOZ1lwanRrNEdvMUhwclBabHc0YXh4dTBJcXJZMXdmQUtEMUdWTTJ3aDlaRUV5MHhxK00iLCJtYWMiOiJlNmIzMWRjMWVjNDc1ZWFjYzY4NDI2NWRmZjEwODhlMGNkMjgxZDAxZGNlYjVlNGMwOWFiOTY1OGZjNmUzNmY2IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:42:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IitsbnptbXcxNFVEN0ZYVWc0SEIxS1E9PSIsInZhbHVlIjoiRC9IMnF6cDgyWENmSlVTRHFsME9nZE5waUh6QzhHSGM5dytUdmxsK0RIZFgxcGYvZkk3ZFJpMzA3UHFzOWVpRDNGZEhvOGtKa3I4NlJqMzViSUpReTZkQTJhSFZsNjFubWFWTklsU2VSRUtxcFRmalpCRWhHMFl3dGFqRTFDUUtROEhpQXVPSGh3c3dob2ZIRytrclVWVEdscHMrOHp2c1ZCVVM0a1N2b3JTWXhuWU5IbjQ0L2JiazYxb0NtWTE4RGNGa1IvSWQvdVptalMwUkMrM0N0S3hSeE16VXVOdElGVS9JN2pVek9PcFJST1RmejdCdXFtczduWFFEWVJhVnV1NW5xaGVHaHRPak80eWZwUFJ4R1NYUVY0UGJNQjNmNTVzRzNFd21zM1htYnVJNEkvVFFSL251U1M2eEdZOUtxMWkzZ1AxZmpxZXBodGpBZGNQZFBpMVdzREtWTG0vWis0aFhhWm91bStQRUVnQnA5MGs0c0t0Q1FSdmFseWt0TEk2MnJ5R1dEbW5ERzFLTlF3MUpCb0pMaDZaL0QreVF4R0wrQXdlVTdoK1Y0U0xJL1dBNllPUlRhb25QUm85TWxmeUhqTysrcE5vYXMySFBFc0xRRXRHUHdvQXJRdWdNNE9NMkNlVEwwQklUanpHeCtBNGJXeVF6b3NibENCZFgiLCJtYWMiOiI5ZTc4MTVkNjRiYjIzMTk4OGM2ZTFlOWJiODljNzQyOWQ5YTIyNTU4YWYwMTUxOTRhMGNiNjI1NTYzNTI0YzQwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:42:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-253199599 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-253199599\", {\"maxDepth\":0})</script>\n"}}