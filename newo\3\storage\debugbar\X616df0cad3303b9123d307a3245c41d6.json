{"__meta": {"id": "X616df0cad3303b9123d307a3245c41d6", "datetime": "2025-06-08 15:30:45", "utime": **********.834231, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.083125, "end": **********.834256, "duration": 0.7511308193206787, "duration_str": "751ms", "measures": [{"label": "Booting", "start": **********.083125, "relative_start": 0, "end": **********.725369, "relative_end": **********.725369, "duration": 0.6422438621520996, "duration_str": "642ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.725385, "relative_start": 0.6422598361968994, "end": **********.834259, "relative_end": 3.0994415283203125e-06, "duration": 0.10887408256530762, "duration_str": "109ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45373936, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1671\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1671-1681</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.0256, "accumulated_duration_str": "25.6ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.779201, "duration": 0.024550000000000002, "duration_str": "24.55ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 95.898}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.819814, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 95.898, "width_percent": 4.102}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-1671391127 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1671391127\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-597644276 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-597644276\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1751956202 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1751956202\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1619327746 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749396510751%7C76%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkNyK2dJY3JoQjZ2VmV0aEFIek9DbWc9PSIsInZhbHVlIjoiOHQxVEp6NUNvbnpkVFlIMkhwREJ5VkxmT2JNZU1HYm55M21aMW5XS2I3bmhGell2d1BDdnRVcCtPRzVLUk11cWtoaE1yU3JnNDMycktmb0lCcnU4QUhWeGwvUUh3WFAyOXpFbFVmdTErUUF0cEI3eUE4elA0RTMvME95WlRlRWUwLytRNGljdjBKZWdGY1BVM1RDNWNzZklSSlplZEx5bHRjNkM2QnI4WUNhMlZ5bHAySWtEYWhCSURURW5nRFp5NU1RSkJ4dUttTmR2MHhYRW02RVJGRWZaVGJmRk9PS3dYVVFzVkVsZ2RTRFVzdmVjeHo3cC9MRjBvZmFMb1ZwNXpEMFluWS9PWUtGdktTRjZXdUJ0K2EwS3RDbS95S29qRno5M05qUWdXdkNZUXNLb3NUcmR2aHVzYkpPUC9IQTJVZUIwTFpBZjJWUEw5dytzNEx1aUJuYVlOalFsbEtmZjIzbWZRRkVYNUVLcUVzSHpZZXhOSVREdW91MitsTnFNWTQ0SXJSU1kwOXBxQ1dDSW1pYmpjWFIxZTErTXNTelJHcWJpQnNFM0FZbVIvY1lGWjBhazZYTitLUDNkQnNqMnZreGxza2lHTkRqZ1dIaDlESVJGRWZmV2gxbWJjOUZpcWYvZTZNZkpsOVpXaUJpY0J6MGtiTlRHU1Y0K3RIL1YiLCJtYWMiOiJlMWE2NjZmNjJlMzlmODRiOTJlYThkNGJlNTIyOTQyOGUzZDk4M2FiZjc5Nzk0NTEyNWJlMWU5ZTM1NzY5NWMzIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InlBTktaK0ZmWG9pSWlZYlN3d0RDb3c9PSIsInZhbHVlIjoiYlA1K292V0RDZkhZTjhRL2llMFNKT3QzNzBoWStPaGlkRTFUMk5yMUVhK0FEdDl0L0dJTTlVUzdocmI5TndKNVl2QVZoT0V1WndiaEFNTUxEQitsS1JZWkpkQzM5ejE0OGF1b2oxNTk4YUFjZXp2Mys1a3h3VkFxQmdJSmUyNnA3dUtZRDN5RndQRFU4TSszTUVGU3JUNGtISm9hRDd0b1llbU80b1Z4V2ptWHdQRU9NeVBVdW9oZ1VwUmRGOFZYazdIcDRJbWovV3RaR3pQbXh6WlJVQm93RzVOc3VyM1ZMY204T0M1elhRNnBXRmZxK0lsSlJZVWR5eCtoMjNMajVVem90T2R6V2E3UlNBWElwdnM5ajF6dys1YkNMTWV4Q1MyWGZQZEVhejJTMmh3Y3NOTG1zOVVyUHl1UThlZVltQ3JOQ1FCeklCYXJjRWpQVERmYkRXU0NLY2VwNVhJVzJsQjB2cW9DUGM1VHNUbTRSYVplbXpCcmpOZmppdDlsNHFwVS9PME9NT0w3WmF4TlJ1YTg0YmtXS1NQaFY1Q0V5Z1poWGloMHI3Yit6MXEydlA5eFFwZEZKdjhkMW1NaEI4ZzV2VFhWd1VHOUNOU2NNcW54ZGN5eVA1Y1pRREpNYUY4MHRRRlJxMG1FWHBzOFE5aTA4WkJZU1dxRjdEdksiLCJtYWMiOiJjNTg1ZTQ1YmRlOGQwNmE4NjRlYjY4ZGM2ZDI2NWI1Y2Q1MTUxYTljZmY5Y2UzMWMwMmQ4NmNjZGI0MDQzNjZiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1619327746\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1256335181 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xNV7SvMX3AgbiuDVO62eNXfryfZD4KbWr3rEIkXt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1256335181\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:30:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjZaUnM2a2I4ZGh1czJNN1hIVzdFZkE9PSIsInZhbHVlIjoiVjM5WjFxbzZ0ZzdKWFZQUTJGNUNYUUdUZGNuR3F4aFpxck5EaFlZc0NDVk9NVmNPT1FpVXlkcEwydE5LYTZnaWY4VkpWb1ZVMURTRXBsVHJrNG16UDdDOWhSK1VhTFE4UXZ3a2loY3hWeWdNcjl2SmNiTkFYUWFmakpLWnhqcjc1eWJtS2FCeiswOUNyMGljUStVTklraXRhaEFVcHQwdmVIajdxQ2pabmZxOFZiZHZpMDh2WjRiRHZlbG1UdWVEZUd1bDJyVEkvTmtqMW44bCswVWIxQWp4ZHc5dlNMcHQzVVk1TTZKZzFLZ05ZaEdpY1hhUkE3RDZZY0E0ZUNFVlY3WCtjVDlyZ0ZYNzhEZjZaUmtUNHlZL3F6QWRQdms2R3NrSll2eTcvY1B3QmlsL3NWbmlMdDVyeW5yeVkxc0NFalh0cXNOaTBKYVFsNkR0SDAveVhNWHJjVFhiVW53K0hWYUtoeGl3TDh2Sm5JQW5ISFJhYm9RTzJGbTNtYXhtbjA5SnMwcWp3ZW81MkFpOWxxSFN4TzVGeEZnU2tyS2FBNjlIZ1NlUWd3MlZQSFhCK0pCdDBEVWFIVDBJYVh4UlNKWHBZZHdZZDRoVkFVenFmUXVNb01LOXRVR1FzdGN4ZVhPcS9iMmRUREtaUWdqMEsvNGk5OTBtWlpuWE8zazQiLCJtYWMiOiJkYzU3OWE1ZGFkOTMxZjhhOTUwYmZiNGU1ODVmYmFlYzQyZTA5ZmY1YmQwMjg5M2FmMjY5NzRkZjJjYzdiYzAzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:30:45 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Im02KzhKVGhHanpnTlg0V1kybEdRWmc9PSIsInZhbHVlIjoiSlVndnpMNGpFTnhMRi9oem1zaEVsUk42V2RsU0xRVUFHaWFlZzRlUVlBL0NUd3FOUFdaRnJEempHQ3VXTG9LNTREWkNiVDFPdWFoa1R2Z3FzZkFVRTN0NitoUXhzUVZ0WmlOblpHNmN2MXF2TFp0TjUrYnJrOXVOdDlUSjlDRDFnK1pWSnJhSUk2a01jblQzNlh2M1FSM0MzbEJNOGJwYithU3dMSUNaa1hROGhXeWthZnJINDV4L3czRWMxczJ0dUdRQkRPTjJZVURYVG5vOXI3WWFxMkZyK1ZDa0wzK2RSMzVhQ3NwVEZtb1o3S2E2SjFvdUxVYUNNMnNjbVpoOTlXZEZvMzB4S0oxczJWZ0dvcGRMVVJtcEN4N0tUTTFYNnVIYUs0UWhFdWZFTS9JRnJpd2dsMzJma0ZqeXB2dVhwVzg0Nk91Y3gxZEh1NFlrSGZHaDRKYWI0NDEzejZ2VFdOak91K3kzMy8vei8wTUhpZDF4elRvSVE5VGthbXJiSUxyS0Jyd3Y2QWszL21ydGcyUHc3eEVycWtteTBWQW5uT295Yk12UmUrN3NOLzdjY3lkNzg5Zy9lMTJaNUR5TDJOUWIxNFJQMS9xQ1I4eVdFbXBmb0hFRnpIYXpHUWtMNHpBMHNUa3N1cXVKSFdweEtaRWcyalQrSGJjeENlam4iLCJtYWMiOiJlYTU0MGI2Zjg0ODhlZjYwYjBjMDI3N2UzMGFhNWM3NWVhY2ZjMThjY2IzMzljMDFkNWM4NWVlMDUxYTI1NGNhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:30:45 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjZaUnM2a2I4ZGh1czJNN1hIVzdFZkE9PSIsInZhbHVlIjoiVjM5WjFxbzZ0ZzdKWFZQUTJGNUNYUUdUZGNuR3F4aFpxck5EaFlZc0NDVk9NVmNPT1FpVXlkcEwydE5LYTZnaWY4VkpWb1ZVMURTRXBsVHJrNG16UDdDOWhSK1VhTFE4UXZ3a2loY3hWeWdNcjl2SmNiTkFYUWFmakpLWnhqcjc1eWJtS2FCeiswOUNyMGljUStVTklraXRhaEFVcHQwdmVIajdxQ2pabmZxOFZiZHZpMDh2WjRiRHZlbG1UdWVEZUd1bDJyVEkvTmtqMW44bCswVWIxQWp4ZHc5dlNMcHQzVVk1TTZKZzFLZ05ZaEdpY1hhUkE3RDZZY0E0ZUNFVlY3WCtjVDlyZ0ZYNzhEZjZaUmtUNHlZL3F6QWRQdms2R3NrSll2eTcvY1B3QmlsL3NWbmlMdDVyeW5yeVkxc0NFalh0cXNOaTBKYVFsNkR0SDAveVhNWHJjVFhiVW53K0hWYUtoeGl3TDh2Sm5JQW5ISFJhYm9RTzJGbTNtYXhtbjA5SnMwcWp3ZW81MkFpOWxxSFN4TzVGeEZnU2tyS2FBNjlIZ1NlUWd3MlZQSFhCK0pCdDBEVWFIVDBJYVh4UlNKWHBZZHdZZDRoVkFVenFmUXVNb01LOXRVR1FzdGN4ZVhPcS9iMmRUREtaUWdqMEsvNGk5OTBtWlpuWE8zazQiLCJtYWMiOiJkYzU3OWE1ZGFkOTMxZjhhOTUwYmZiNGU1ODVmYmFlYzQyZTA5ZmY1YmQwMjg5M2FmMjY5NzRkZjJjYzdiYzAzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:30:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Im02KzhKVGhHanpnTlg0V1kybEdRWmc9PSIsInZhbHVlIjoiSlVndnpMNGpFTnhMRi9oem1zaEVsUk42V2RsU0xRVUFHaWFlZzRlUVlBL0NUd3FOUFdaRnJEempHQ3VXTG9LNTREWkNiVDFPdWFoa1R2Z3FzZkFVRTN0NitoUXhzUVZ0WmlOblpHNmN2MXF2TFp0TjUrYnJrOXVOdDlUSjlDRDFnK1pWSnJhSUk2a01jblQzNlh2M1FSM0MzbEJNOGJwYithU3dMSUNaa1hROGhXeWthZnJINDV4L3czRWMxczJ0dUdRQkRPTjJZVURYVG5vOXI3WWFxMkZyK1ZDa0wzK2RSMzVhQ3NwVEZtb1o3S2E2SjFvdUxVYUNNMnNjbVpoOTlXZEZvMzB4S0oxczJWZ0dvcGRMVVJtcEN4N0tUTTFYNnVIYUs0UWhFdWZFTS9JRnJpd2dsMzJma0ZqeXB2dVhwVzg0Nk91Y3gxZEh1NFlrSGZHaDRKYWI0NDEzejZ2VFdOak91K3kzMy8vei8wTUhpZDF4elRvSVE5VGthbXJiSUxyS0Jyd3Y2QWszL21ydGcyUHc3eEVycWtteTBWQW5uT295Yk12UmUrN3NOLzdjY3lkNzg5Zy9lMTJaNUR5TDJOUWIxNFJQMS9xQ1I4eVdFbXBmb0hFRnpIYXpHUWtMNHpBMHNUa3N1cXVKSFdweEtaRWcyalQrSGJjeENlam4iLCJtYWMiOiJlYTU0MGI2Zjg0ODhlZjYwYjBjMDI3N2UzMGFhNWM3NWVhY2ZjMThjY2IzMzljMDFkNWM4NWVlMDUxYTI1NGNhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:30:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}