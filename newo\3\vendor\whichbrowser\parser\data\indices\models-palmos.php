<?php

namespace WhichBrowser\Data;

DeviceModels::$PALMOS_INDEX = array (
  '@AC' => 
  array (
    0 => 'acer-momo',
    1 => 'acer-coco',
    2 => 'Acea-MZ01',
    3 => 'Acea-PD01',
  ),
  '@GR' => 
  array (
    0 => 'grmn-3200',
    1 => 'grmn-3600',
    2 => 'grmn-3700',
  ),
  '@GS' => 
  array (
    0 => 'gsRl-zicn',
    1 => 'gsRl-zcn2',
    2 => 'gsRl-zcn5',
  ),
  '@HS' => 
  array (
    0 => 'hspr-H101',
    1 => 'hspr-H102',
  ),
  '@KW' => 
  array (
    0 => 'kwc.-7135',
  ),
  '@PA' => 
  array (
    0 => 'Palm-D061',
    1 => 'Palm-D062',
    2 => 'Palm-TunX',
    3 => 'Palm-stuj',
    4 => 'Palm-hbbs',
    5 => 'Palm-trnd',
    6 => 'Palm-ecty',
    7 => 'Palm-lith',
    8 => 'Palm-sky1',
    9 => 'Palm-D053',
    10 => 'Palm-D052',
    11 => 'Palm-D060',
    12 => 'Palm-MT64',
    13 => 'Palm-Cct1',
    14 => 'Palm-Zir4',
    15 => 'Palm-Frg1',
    16 => 'Palm-Frg2',
    17 => 'Palm-Arz1',
    18 => 'Palm-TnT5',
    19 => 'Palm-atc1',
    20 => 'Palm-D050',
    21 => 'Palm-Cubs',
    22 => 'Palm-Zi21',
    23 => 'Palm-D051',
    24 => 'Palm-Zi22',
    25 => 'Palm-Zpth',
    26 => 'Palm-Zi72',
  ),
  '@QC' => 
  array (
    0 => 'qcom-qc20',
  ),
  '@SM' => 
  array (
    0 => 'smsn-phix',
    1 => 'smsn-Phx2',
    2 => 'smsn-blch',
    3 => 'smsn-BLFM',
    4 => 'smsn-glxy',
  ),
  '@SO' => 
  array (
    0 => 'sony-ystn',
    1 => 'sony-ysmt',
    2 => 'sony-ysm2',
    3 => 'sony-nsca',
    4 => 'sony-nsc2',
    5 => 'sony-vnce',
    6 => 'sony-mdna',
    7 => 'sony-npls',
    8 => 'sony-rdwd',
    9 => 'sony-crdb',
    10 => 'sony-tldo',
    11 => 'sony-mdrd',
    12 => 'sony-grnd',
    13 => 'sony-frta',
    14 => 'sony-cocs',
    15 => 'sony-glps',
    16 => 'sony-mcnd',
    17 => 'sony-vrna',
    18 => 'sony-atom',
    19 => 'sony-goha',
    20 => 'sony-leia',
    21 => 'sony-hwai',
    22 => 'sony-goku',
    23 => 'sony-luke',
    24 => 'sony-amno',
    25 => 'sony-prmr',
    26 => 'sony-ancy',
  ),
  '@TP' => 
  array (
    0 => 'Tpwv-Rdog',
  ),
  '@TR' => 
  array (
    0 => 'trgp-trg1',
    1 => 'trgp-trg2',
  ),
);
