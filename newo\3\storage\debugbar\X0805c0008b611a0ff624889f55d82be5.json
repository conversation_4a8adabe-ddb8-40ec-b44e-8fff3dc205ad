{"__meta": {"id": "X0805c0008b611a0ff624889f55d82be5", "datetime": "2025-06-08 15:28:41", "utime": 1749396521.230964, "method": "GET", "uri": "/users", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749396520.254264, "end": 1749396521.230992, "duration": 0.9767279624938965, "duration_str": "977ms", "measures": [{"label": "Booting", "start": 1749396520.254264, "relative_start": 0, "end": 1749396521.177709, "relative_end": 1749396521.177709, "duration": 0.9234449863433838, "duration_str": "923ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1749396521.177724, "relative_start": 0.9234597682952881, "end": 1749396521.230995, "relative_end": 2.86102294921875e-06, "duration": 0.05327105522155762, "duration_str": "53.27ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 42122168, "peak_usage_str": "40MB"}, "exceptions": {"count": 1, "exceptions": [{"type": "Illuminate\\Auth\\AuthenticationException", "message": "Unauthenticated.", "code": 0, "file": "vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php", "line": 102, "stack_trace": null, "stack_trace_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:52</span> [<samp data-depth=1 class=sf-dump-expanded>\n  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>88</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">unauthenticated</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Auth\\Middleware\\Authenticate</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref253 title=\"50 occurrences\">#53</a><samp data-depth=4 id=sf-dump-**********-ref253 class=sf-dump-compact>\n        +<span class=sf-dump-public title=\"Public property\">attributes</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\ParameterBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>ParameterBag</span> {<a class=sf-dump-ref>#58</a><samp data-depth=5 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: []\n        </samp>}\n        +<span class=sf-dump-public title=\"Public property\">request</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\InputBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>InputBag</span> {<a class=sf-dump-ref>#54</a><samp data-depth=5 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: []\n        </samp>}\n        +<span class=sf-dump-public title=\"Public property\">query</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\InputBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>InputBag</span> {<a class=sf-dump-ref>#61</a><samp data-depth=5 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: []\n        </samp>}\n        +<span class=sf-dump-public title=\"Public property\">server</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\ServerBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>ServerBag</span> {<a class=sf-dump-ref>#56</a><samp data-depth=5 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: <span class=sf-dump-note>array:45</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n            \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n            \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n            \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n            \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n            \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n            \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n            \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n            \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n            \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n            \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n            \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n            \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n            \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/customer</span>\"\n            \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n            \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n            \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; XSRF-TOKEN=eyJpdiI6IjdSSkRnc1QvZXY5eGYrZGlEV3ZmK2c9PSIsInZhbHVlIjoiODRYQmg5RHMza1RkU2ZTcEwrMEM4cWczNDZDU3kxV01XNld6Y2F2ZThZYk1WMktJeXpZRkVLbUoxVU5rZVlXWkhRWkhjK2tOQVRxR0lrcjhQZzBtMzVERThpQys1ODdTRmZQVzV5Z3o2UXJxeHlyVzhuMkhEWHkvWHJXS3FzRmNidjdrUGo1ZVNGbGMvbmZ1SVZOWVRCQUdZL3BNVmwvK011Rit5VmpWdDBEMXZrUmRWNXVSZ1ZraWNPMG9jTUxDSkw0U3ZrVitwR3VWR2RhZU1YUVNzNVgxM0VlQ2hoTWtQUm9ucENFM05jUlpvWFo5S2J5MUEvUXBBMzhKUXIvd3BiRjJLTHA5YmVmellneG10VDV5aExYSzhMV0dWeVllZFY2aVZScHRXY3BkbVR4cGlaZ2o0RFhyTlB2TDU2cm5tSDQ1NjYvYmt3TjBtaWRFaG9LVHdsZzdGTHJ2WFJJSGdFQkhDazQxTFVFMjBvQ1Q3TkNpMTRFZDQyTEpRMHRXd1N1UVVqTmVqYWFFdTcyZ2RNMnZpakgrWWJuaTBGT29vekIzeS93WmxkV0FSQ0JEb1RZZiswaTBZdmNqVzVRSTRqNzBBZklvODBzNDdITm03ZGNyRjlGQ3lLSHpramJ2K0w3MGR3RXpXNyttMGZteHd2WUlZMUVDL1BxZ21DODciLCJtYWMiOiI5NzRhOTk2M2MyZjE4YzZjNzZlNjI3NWEyODM1NzE4OGI1YTZhNGFiNDAwYmYzOWViYjhlZWVmZmExZmUxZjg3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InJCN2huSmlZUzQyRDNCZ0xqbVBTeUE9PSIsInZhbHVlIjoiRVZVeHlaMU5URldrcGgvandYVmhMVmYrRll2enlQSDVkUEpRM3BwaktVeGN2UFJCclQzNzhrdnR4bmY2S2p1d2pScVVZSUt4RHVRYnNQTGZPNzZGVGluNU1pUnZCaFlXODV1WlpKYThNbXFJUTliaEorZlZEWE5SOStJRTQ4eGZ4TXArNkF1UVVqa0xIK0FOeitzdkowVklrQm1TNVY5ZjlHM29UMFpmekwwbTdEWXhsL1YrRGpaa2twZ2NqSG93dVdRRUlWckxUNTE1THY4Z3Y2RGhjd29YZmNhSjZ5M2ZzaEQreCtrczlvL003ZjNHRnhINk5VT21ZbkdJamZkNnNiU0xDZXJrUWNlWjVpdytTdjFNTm1EbTdEaGJLSk1ZZ0s2UXQ5WHk5NjVwdUdCYjlONXJOSlZ1QVVJZFlUSWp5ZGcrSHY4Mis4VHgwSk8xZjk2T1JHOC9iNlN0cnJ6dnZKK2x5S3gweFMzblVpUllZOGV5QkhSbVNSYXJTeFJkcnR4WVhCb1QxY1hpdTM0UE92UGVGR3RDQitxd2QvUWM2a3ZFVThpUmRtdmpObm9IcXljU2NlbkZqczZhVmd0WnZTVG8zZldIVVRUVjlHN0kyeU1XZTNFV0xOejZQZTFlbU84L1drL3dUUTN0UUNxTnFxYVVXV2huUHV3LzhnVFQiLCJtYWMiOiIxZjdhOWRkYjA1OTMxMmY5NTRlNzU0Y2UzZGY2NmM2MzAwZWEzZWMwODcwNjQ1OTg1MWYyMjk0OWNlMWZlYTcxIiwidGFnIjoiIn0%3D; _clsk=xh9goj%7C1749396266838%7C4%7C1%7Ck.clarity.ms%2Fcollect</span>\"\n            \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"792 characters\">C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\Trae\\bin;C:\\laragon\\bin\\composer;C:\\laragon\\bin\\git\\bin;C:\\laragon\\bin\\git\\cmd;C:\\laragon\\bin\\git\\mingw64\\bin;C:\\laragon\\bin\\git\\usr\\bin;C:\\laragon\\bin\\mysql\\mysql-8.4.3-winx64\\bin;C:\\laragon\\bin\\ngrok;C:\\laragon\\bin\\nodejs\\node-v22;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\laragon\\bin\\python\\python-3.13;C:\\laragon\\bin\\python\\python-3.13\\Scripts;C:\\laragon\\usr\\bin;C:\\Users\\<USER>\\AppData\\Local\\Yarn\\config\\global\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Program Files\\JetBrains\\WebStorm 2025.1.1\\bin;</span>\"\n            \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n            \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\WINDOWS\\system32\\cmd.exe</span>\"\n            \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n            \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n            \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"\"\n            \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Apache/2.4.62 (Win64) OpenSSL/3.0.15 PHP/8.3.16</span>\"\n            \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n            \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"3 characters\">::1</span>\"\n            \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"\n            \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"3 characters\">::1</span>\"\n            \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"24 characters\">C:/laragon/www/to/newo/3</span>\"\n            \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"4 characters\">http</span>\"\n            \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n            \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"24 characters\">C:/laragon/www/to/newo/3</span>\"\n            \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n            \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"41 characters\">C:/laragon/www/to/newo/3/public/index.php</span>\"\n            \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">54861</span>\"\n            \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"6 characters\">/users</span>\"\n            \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n            \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n            \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n            \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"\"\n            \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"6 characters\">/users</span>\"\n            \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"17 characters\">/public/index.php</span>\"\n            \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"17 characters\">/public/index.php</span>\"\n            \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1749396520.2543</span>\n            \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1749396520</span>\n          </samp>]\n        </samp>}\n        +<span class=sf-dump-public title=\"Public property\">files</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\FileBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>FileBag</span> {<a class=sf-dump-ref>#60</a><samp data-depth=5 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: []\n        </samp>}\n        +<span class=sf-dump-public title=\"Public property\">cookies</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\InputBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>InputBag</span> {<a class=sf-dump-ref>#59</a><samp data-depth=5 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">parameters</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n            \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BJuMSlnG5Xc84hQpzCBfZadVHL6kh5OEk3auNDNe</span>\"\n            \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n          </samp>]\n        </samp>}\n        +<span class=sf-dump-public title=\"Public property\">headers</span>: <span class=sf-dump-note title=\"Symfony\\Component\\HttpFoundation\\HeaderBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\HttpFoundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>HeaderBag</span> {<a class=sf-dump-ref>#55</a><samp data-depth=5 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">headers</span>: <span class=sf-dump-note>array:16</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/customer</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n            </samp>]\n            \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; XSRF-TOKEN=eyJpdiI6IjdSSkRnc1QvZXY5eGYrZGlEV3ZmK2c9PSIsInZhbHVlIjoiODRYQmg5RHMza1RkU2ZTcEwrMEM4cWczNDZDU3kxV01XNld6Y2F2ZThZYk1WMktJeXpZRkVLbUoxVU5rZVlXWkhRWkhjK2tOQVRxR0lrcjhQZzBtMzVERThpQys1ODdTRmZQVzV5Z3o2UXJxeHlyVzhuMkhEWHkvWHJXS3FzRmNidjdrUGo1ZVNGbGMvbmZ1SVZOWVRCQUdZL3BNVmwvK011Rit5VmpWdDBEMXZrUmRWNXVSZ1ZraWNPMG9jTUxDSkw0U3ZrVitwR3VWR2RhZU1YUVNzNVgxM0VlQ2hoTWtQUm9ucENFM05jUlpvWFo5S2J5MUEvUXBBMzhKUXIvd3BiRjJLTHA5YmVmellneG10VDV5aExYSzhMV0dWeVllZFY2aVZScHRXY3BkbVR4cGlaZ2o0RFhyTlB2TDU2cm5tSDQ1NjYvYmt3TjBtaWRFaG9LVHdsZzdGTHJ2WFJJSGdFQkhDazQxTFVFMjBvQ1Q3TkNpMTRFZDQyTEpRMHRXd1N1UVVqTmVqYWFFdTcyZ2RNMnZpakgrWWJuaTBGT29vekIzeS93WmxkV0FSQ0JEb1RZZiswaTBZdmNqVzVRSTRqNzBBZklvODBzNDdITm03ZGNyRjlGQ3lLSHpramJ2K0w3MGR3RXpXNyttMGZteHd2WUlZMUVDL1BxZ21DODciLCJtYWMiOiI5NzRhOTk2M2MyZjE4YzZjNzZlNjI3NWEyODM1NzE4OGI1YTZhNGFiNDAwYmYzOWViYjhlZWVmZmExZmUxZjg3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InJCN2huSmlZUzQyRDNCZ0xqbVBTeUE9PSIsInZhbHVlIjoiRVZVeHlaMU5URldrcGgvandYVmhMVmYrRll2enlQSDVkUEpRM3BwaktVeGN2UFJCclQzNzhrdnR4bmY2S2p1d2pScVVZSUt4RHVRYnNQTGZPNzZGVGluNU1pUnZCaFlXODV1WlpKYThNbXFJUTliaEorZlZEWE5SOStJRTQ4eGZ4TXArNkF1UVVqa0xIK0FOeitzdkowVklrQm1TNVY5ZjlHM29UMFpmekwwbTdEWXhsL1YrRGpaa2twZ2NqSG93dVdRRUlWckxUNTE1THY4Z3Y2RGhjd29YZmNhSjZ5M2ZzaEQreCtrczlvL003ZjNHRnhINk5VT21ZbkdJamZkNnNiU0xDZXJrUWNlWjVpdytTdjFNTm1EbTdEaGJLSk1ZZ0s2UXQ5WHk5NjVwdUdCYjlONXJOSlZ1QVVJZFlUSWp5ZGcrSHY4Mis4VHgwSk8xZjk2T1JHOC9iNlN0cnJ6dnZKK2x5S3gweFMzblVpUllZOGV5QkhSbVNSYXJTeFJkcnR4WVhCb1QxY1hpdTM0UE92UGVGR3RDQitxd2QvUWM2a3ZFVThpUmRtdmpObm9IcXljU2NlbkZqczZhVmd0WnZTVG8zZldIVVRUVjlHN0kyeU1XZTNFV0xOejZQZTFlbU84L1drL3dUUTN0UUNxTnFxYVVXV2huUHV3LzhnVFQiLCJtYWMiOiIxZjdhOWRkYjA1OTMxMmY5NTRlNzU0Y2UzZGY2NmM2MzAwZWEzZWMwODcwNjQ1OTg1MWYyMjk0OWNlMWZlYTcxIiwidGFnIjoiIn0%3D; _clsk=xh9goj%7C1749396266838%7C4%7C1%7Ck.clarity.ms%2Fcollect</span>\"\n            </samp>]\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">cacheControl</span>: []\n        </samp>}\n        #<span class=sf-dump-protected title=\"Protected property\">content</span>: <span class=sf-dump-const>null</span>\n        #<span class=sf-dump-protected title=\"Protected property\">languages</span>: <span class=sf-dump-const>null</span>\n        #<span class=sf-dump-protected title=\"Protected property\">charsets</span>: <span class=sf-dump-const>null</span>\n        #<span class=sf-dump-protected title=\"Protected property\">encodings</span>: <span class=sf-dump-const>null</span>\n        #<span class=sf-dump-protected title=\"Protected property\">acceptableContentTypes</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">text/html</span>\"\n          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"21 characters\">application/xhtml+xml</span>\"\n          <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"10 characters\">image/avif</span>\"\n          <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"10 characters\">image/webp</span>\"\n          <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">image/apng</span>\"\n          <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"15 characters\">application/xml</span>\"\n          <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n          <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"27 characters\">application/signed-exchange</span>\"\n        </samp>]\n        #<span class=sf-dump-protected title=\"Protected property\">pathInfo</span>: \"<span class=sf-dump-str title=\"6 characters\">/users</span>\"\n        #<span class=sf-dump-protected title=\"Protected property\">requestUri</span>: \"<span class=sf-dump-str title=\"6 characters\">/users</span>\"\n        #<span class=sf-dump-protected title=\"Protected property\">baseUrl</span>: \"\"\n        #<span class=sf-dump-protected title=\"Protected property\">basePath</span>: <span class=sf-dump-const>null</span>\n        #<span class=sf-dump-protected title=\"Protected property\">method</span>: \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n        #<span class=sf-dump-protected title=\"Protected property\">format</span>: <span class=sf-dump-const>null</span>\n        #<span class=sf-dump-protected title=\"Protected property\">session</span>: <span class=sf-dump-note title=\"Illuminate\\Session\\SymfonySessionDecorator\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Session</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>SymfonySessionDecorator</span> {<a class=sf-dump-ref>#3508</a><samp data-depth=5 class=sf-dump-compact>\n          +<span class=sf-dump-public title=\"Public property\">store</span>: <span class=sf-dump-note title=\"Illuminate\\Session\\Store\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Session</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Store</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2332 title=\"2 occurrences\">#332</a><samp data-depth=6 id=sf-dump-**********-ref2332 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">id</span>: \"<span class=sf-dump-str title=\"40 characters\">BJuMSlnG5Xc84hQpzCBfZadVHL6kh5OEk3auNDNe</span>\"\n            #<span class=sf-dump-protected title=\"Protected property\">name</span>: \"<span class=sf-dump-str title=\"12 characters\">test_session</span>\"\n            #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WEgVgdfObfw6rNC2frmzIt58FwRoAm7QcDJjfmq1</span>\"\n              \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n              </samp>]\n              \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>old</span>\" => []\n                \"<span class=sf-dump-key>new</span>\" => []\n              </samp>]\n              \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">handler</span>: <span class=sf-dump-note title=\"Illuminate\\Session\\FileSessionHandler\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Session</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>FileSessionHandler</span> {<a class=sf-dump-ref>#331</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">files</span>: <span class=sf-dump-note title=\"Illuminate\\Filesystem\\Filesystem\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Filesystem</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Filesystem</span> {<a class=sf-dump-ref>#210</a>}\n              #<span class=sf-dump-protected title=\"Protected property\">path</span>: \"<span class=sf-dump-str title=\"51 characters\">C:\\laragon\\www\\to\\newo\\3\\storage\\framework/sessions</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">minutes</span>: \"<span class=sf-dump-str title=\"3 characters\">120</span>\"\n            </samp>}\n            #<span class=sf-dump-protected title=\"Protected property\">serialization</span>: \"<span class=sf-dump-str title=\"3 characters\">php</span>\"\n            #<span class=sf-dump-protected title=\"Protected property\">started</span>: <span class=sf-dump-const>true</span>\n          </samp>}\n        </samp>}\n        #<span class=sf-dump-protected title=\"Protected property\">locale</span>: <span class=sf-dump-const>null</span>\n        #<span class=sf-dump-protected title=\"Protected property\">defaultLocale</span>: \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n        -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\Request`\">preferredFormat</span>: <span class=sf-dump-const>null</span>\n        -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\Request`\">isHostValid</span>: <span class=sf-dump-const>true</span>\n        -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\Request`\">isForwardedValid</span>: <span class=sf-dump-const>true</span>\n        -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\Request`\">isSafeContentPreferred</span>: <span class=sf-dump-const title=\"Uninitialized property\">? bool</span>\n        -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\Request`\">trustedValuesCache</span>: []\n        -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpFoundation\\Request`\">isIisRewrite</span>: <span class=sf-dump-const>false</span>\n        #<span class=sf-dump-protected title=\"Protected property\">json</span>: <span class=sf-dump-const>null</span>\n        #<span class=sf-dump-protected title=\"Protected property\">convertedFiles</span>: <span class=sf-dump-const>null</span>\n        #<span class=sf-dump-protected title=\"Protected property\">userResolver</span>: <span class=sf-dump-note>Closure($guard = null)</span> {<a class=sf-dump-ref>#3369</a><samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Auth\\AuthServiceProvider\n35 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Auth</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>AuthServiceProvider</span>\"\n          <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Auth\\AuthServiceProvider\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Auth</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>AuthServiceProvider</span> {<a class=sf-dump-ref>#84</a> &#8230;}\n          <span class=sf-dump-meta>use</span>: {<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-meta>$app</span>: <span class=sf-dump-note title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Application</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref27 title=\"2 occurrences\">#7</a> &#8230;44}\n          </samp>}\n          <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthServiceProvider.php\n93 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\to\\newo\\3\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Auth\\AuthServiceProvider.php</span>\"\n          <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"8 characters\">88 to 90</span>\"\n        </samp>}\n        #<span class=sf-dump-protected title=\"Protected property\">routeResolver</span>: <span class=sf-dump-note>Closure()</span> {<a class=sf-dump-ref>#3380</a><samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Routing\\Router\n25 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Router</span>\"\n          <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Router\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Router</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref242 title=\"3 occurrences\">#42</a> &#8230;}\n          <span class=sf-dump-meta>use</span>: {<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-meta>$route</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Route</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22545 title=\"4 occurrences\">#2545</a><samp data-depth=7 id=sf-dump-**********-ref22545 class=sf-dump-compact>\n              +<span class=sf-dump-public title=\"Public property\">uri</span>: \"<span class=sf-dump-str title=\"5 characters\">users</span>\"\n              +<span class=sf-dump-public title=\"Public property\">methods</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">HEAD</span>\"\n              </samp>]\n              +<span class=sf-dump-public title=\"Public property\">action</span>: <span class=sf-dump-note>array:7</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>middleware</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=9 class=sf-dump-compact>\n                  <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n                  <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">verified</span>\"\n                  <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"4 characters\">auth</span>\"\n                  <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"3 characters\">XSS</span>\"\n                  <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">revalidate</span>\"\n                </samp>]\n                \"<span class=sf-dump-key>as</span>\" => \"<span class=sf-dump-str title=\"11 characters\">users.index</span>\"\n                \"<span class=sf-dump-key>uses</span>\" => \"<span class=sf-dump-str title=\"41 characters\">App\\Http\\Controllers\\UserController@index</span>\"\n                \"<span class=sf-dump-key>controller</span>\" => \"<span class=sf-dump-str title=\"41 characters\">App\\Http\\Controllers\\UserController@index</span>\"\n                \"<span class=sf-dump-key>namespace</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>prefix</span>\" => \"\"\n                \"<span class=sf-dump-key>where</span>\" => []\n              </samp>]\n              +<span class=sf-dump-public title=\"Public property\">isFallback</span>: <span class=sf-dump-const>false</span>\n              +<span class=sf-dump-public title=\"Public property\">controller</span>: <span class=sf-dump-note title=\"App\\Http\\Controllers\\UserController\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Http\\Controllers</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>UserController</span> {<a class=sf-dump-ref>#3364</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">middleware</span>: []\n              </samp>}\n              +<span class=sf-dump-public title=\"Public property\">defaults</span>: []\n              +<span class=sf-dump-public title=\"Public property\">wheres</span>: []\n              +<span class=sf-dump-public title=\"Public property\">parameters</span>: []\n              +<span class=sf-dump-public title=\"Public property\">parameterNames</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">originalParameters</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withTrashedBindings</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">lockSeconds</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">waitSeconds</span>: <span class=sf-dump-const>null</span>\n              +<span class=sf-dump-public title=\"Public property\">computedMiddleware</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">verified</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"4 characters\">auth</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"3 characters\">XSS</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">revalidate</span>\"\n              </samp>]\n              +<span class=sf-dump-public title=\"Public property\">compiled</span>: <span class=sf-dump-note title=\"Symfony\\Component\\Routing\\CompiledRoute\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Symfony\\Component\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>CompiledRoute</span> {<a class=sf-dump-ref>#3492</a><samp data-depth=8 class=sf-dump-compact>\n                -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\Routing\\CompiledRoute`\">staticPrefix</span>: \"<span class=sf-dump-str title=\"6 characters\">/users</span>\"\n                -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\Routing\\CompiledRoute`\">regex</span>: \"<span class=sf-dump-str title=\"13 characters\">{^/users$}sDu</span>\"\n                -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\Routing\\CompiledRoute`\">tokens</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=10 class=sf-dump-compact>\n                    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">text</span>\"\n                    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"6 characters\">/users</span>\"\n                  </samp>]\n                </samp>]\n                -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\Routing\\CompiledRoute`\">pathVariables</span>: []\n                -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\Routing\\CompiledRoute`\">hostRegex</span>: <span class=sf-dump-const>null</span>\n                -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\Routing\\CompiledRoute`\">hostTokens</span>: []\n                -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\Routing\\CompiledRoute`\">hostVariables</span>: []\n                -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\Routing\\CompiledRoute`\">variables</span>: []\n              </samp>}\n              #<span class=sf-dump-protected title=\"Protected property\">router</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Router\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Router</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref242 title=\"3 occurrences\">#42</a> &#8230;}\n              #<span class=sf-dump-protected title=\"Protected property\">container</span>: <span class=sf-dump-note title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Application</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref27 title=\"2 occurrences\">#7</a> &#8230;44}\n              #<span class=sf-dump-protected title=\"Protected property\">bindingFields</span>: []\n            </samp>}\n          </samp>}\n          <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php\n83 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\to\\newo\\3\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Routing\\Router.php</span>\"\n          <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">781 to 781</span>\"\n        </samp>}\n        <span class=sf-dump-meta>basePath</span>: \"\"\n        <span class=sf-dump-meta>format</span>: \"<span class=sf-dump-str title=\"4 characters\">html</span>\"\n      </samp>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>62</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">authenticate</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Auth\\Middleware\\Authenticate</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref253 title=\"50 occurrences\">#53</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Auth\\Middleware\\Authenticate</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref253 title=\"50 occurrences\">#53</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref23499 title=\"2 occurrences\">#3499</a><samp data-depth=4 id=sf-dump-**********-ref23499 class=sf-dump-compact>\n        <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Routing\\Pipeline\n27 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Pipeline</span>\"\n        <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Pipeline\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Pipeline</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref23387 title=\"7 occurrences\">#3387</a> &#8230;}\n        <span class=sf-dump-meta>use</span>: {<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-meta>$stack</span>: <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref>#3498</a> &#8230;}\n          <span class=sf-dump-meta>$pipe</span>: \"<span class=sf-dump-str title=\"48 characters\">Illuminate\\Routing\\Middleware\\SubstituteBindings</span>\"\n        </samp>}\n        <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php\n86 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\to\\newo\\3\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Pipeline\\Pipeline.php</span>\"\n        <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">159 to 190</span>\"\n      </samp>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"86 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>88</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref253 title=\"50 occurrences\">#53</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref253 title=\"50 occurrences\">#53</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref23500 title=\"2 occurrences\">#3500</a><samp data-depth=4 id=sf-dump-**********-ref23500 class=sf-dump-compact>\n        <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Routing\\Pipeline\n27 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Pipeline</span>\"\n        <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Pipeline\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Pipeline</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref23387 title=\"7 occurrences\">#3387</a> &#8230;}\n        <span class=sf-dump-meta>use</span>: {<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-meta>$stack</span>: <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref23499 title=\"2 occurrences\">#3499</a>}\n          <span class=sf-dump-meta>$pipe</span>: \"<span class=sf-dump-str title=\"32 characters\">App\\Http\\Middleware\\Authenticate</span>\"\n        </samp>}\n        <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php\n86 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\to\\newo\\3\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Pipeline\\Pipeline.php</span>\"\n        <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">159 to 190</span>\"\n      </samp>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref253 title=\"50 occurrences\">#53</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\View\\Middleware\\ShareErrorsFromSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref253 title=\"50 occurrences\">#53</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref23501 title=\"2 occurrences\">#3501</a><samp data-depth=4 id=sf-dump-**********-ref23501 class=sf-dump-compact>\n        <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Routing\\Pipeline\n27 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Pipeline</span>\"\n        <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Pipeline\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Pipeline</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref23387 title=\"7 occurrences\">#3387</a> &#8230;}\n        <span class=sf-dump-meta>use</span>: {<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-meta>$stack</span>: <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref23500 title=\"2 occurrences\">#3500</a>}\n          <span class=sf-dump-meta>$pipe</span>: \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\ValidateCsrfToken</span>\"\n        </samp>}\n        <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php\n86 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\to\\newo\\3\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Pipeline\\Pipeline.php</span>\"\n        <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">159 to 190</span>\"\n      </samp>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>121</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref253 title=\"50 occurrences\">#53</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>64</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"21 characters\">handleStatefulRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref253 title=\"50 occurrences\">#53</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note title=\"Illuminate\\Session\\Store\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Session</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Store</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2332 title=\"2 occurrences\">#332</a>}\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref23502 title=\"3 occurrences\">#3502</a><samp data-depth=4 id=sf-dump-**********-ref23502 class=sf-dump-compact>\n        <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Routing\\Pipeline\n27 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Pipeline</span>\"\n        <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Pipeline\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Pipeline</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref23387 title=\"7 occurrences\">#3387</a> &#8230;}\n        <span class=sf-dump-meta>use</span>: {<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-meta>$stack</span>: <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref23501 title=\"2 occurrences\">#3501</a>}\n          <span class=sf-dump-meta>$pipe</span>: \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\View\\Middleware\\ShareErrorsFromSession</span>\"\n        </samp>}\n        <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php\n86 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\to\\newo\\3\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Pipeline\\Pipeline.php</span>\"\n        <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">159 to 190</span>\"\n      </samp>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref253 title=\"50 occurrences\">#53</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref23502 title=\"3 occurrences\">#3502</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>37</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref253 title=\"50 occurrences\">#53</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref253 title=\"50 occurrences\">#53</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref23503 title=\"2 occurrences\">#3503</a><samp data-depth=4 id=sf-dump-**********-ref23503 class=sf-dump-compact>\n        <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Routing\\Pipeline\n27 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Pipeline</span>\"\n        <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Pipeline\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Pipeline</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref23387 title=\"7 occurrences\">#3387</a> &#8230;}\n        <span class=sf-dump-meta>use</span>: {<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-meta>$stack</span>: <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref23502 title=\"3 occurrences\">#3502</a>}\n          <span class=sf-dump-meta>$pipe</span>: \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n        </samp>}\n        <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php\n86 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\to\\newo\\3\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Pipeline\\Pipeline.php</span>\"\n        <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">159 to 190</span>\"\n      </samp>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>75</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref253 title=\"50 occurrences\">#53</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Cookie\\Middleware\\EncryptCookies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref253 title=\"50 occurrences\">#53</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref23504 title=\"2 occurrences\">#3504</a><samp data-depth=4 id=sf-dump-**********-ref23504 class=sf-dump-compact>\n        <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Routing\\Pipeline\n27 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Pipeline</span>\"\n        <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Pipeline\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Pipeline</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref23387 title=\"7 occurrences\">#3387</a> &#8230;}\n        <span class=sf-dump-meta>use</span>: {<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-meta>$stack</span>: <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref23503 title=\"2 occurrences\">#3503</a>}\n          <span class=sf-dump-meta>$pipe</span>: \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse</span>\"\n        </samp>}\n        <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php\n86 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\to\\newo\\3\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Pipeline\\Pipeline.php</span>\"\n        <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">159 to 190</span>\"\n      </samp>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>75</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref253 title=\"50 occurrences\">#53</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Cookie\\Middleware\\EncryptCookies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref253 title=\"50 occurrences\">#53</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref>#3505</a><samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Routing\\Pipeline\n27 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Pipeline</span>\"\n        <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Pipeline\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Pipeline</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref23387 title=\"7 occurrences\">#3387</a> &#8230;}\n        <span class=sf-dump-meta>use</span>: {<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-meta>$stack</span>: <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref23504 title=\"2 occurrences\">#3504</a>}\n          <span class=sf-dump-meta>$pipe</span>: \"<span class=sf-dump-str title=\"34 characters\">App\\Http\\Middleware\\EncryptCookies</span>\"\n        </samp>}\n        <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php\n86 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\to\\newo\\3\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Pipeline\\Pipeline.php</span>\"\n        <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">159 to 190</span>\"\n      </samp>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>119</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref253 title=\"50 occurrences\">#53</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>807</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>Closure($request)</span> {<a class=sf-dump-ref>#3493</a><samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Routing\\Router\n25 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Router</span>\"\n        <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Router\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Router</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref242 title=\"3 occurrences\">#42</a> &#8230;}\n        <span class=sf-dump-meta>use</span>: {<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-meta>$route</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Route</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22545 title=\"4 occurrences\">#2545</a>}\n        </samp>}\n        <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php\n83 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\to\\newo\\3\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Routing\\Router.php</span>\"\n        <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">807 to 809</span>\"\n      </samp>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>786</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">runRouteWithinStack</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Route</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22545 title=\"4 occurrences\">#2545</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref253 title=\"50 occurrences\">#53</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>750</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">runRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref253 title=\"50 occurrences\">#53</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note title=\"Illuminate\\Routing\\Route\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Route</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22545 title=\"4 occurrences\">#2545</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>739</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">dispatchToRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref253 title=\"50 occurrences\">#53</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>201</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref253 title=\"50 occurrences\">#53</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>144</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Foundation\\Http\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref253 title=\"50 occurrences\">#53</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref253 title=\"50 occurrences\">#53</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref253 title=\"50 occurrences\">#53</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref23236 title=\"2 occurrences\">#3236</a><samp data-depth=4 id=sf-dump-**********-ref23236 class=sf-dump-compact>\n        <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Routing\\Pipeline\n27 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Pipeline</span>\"\n        <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Pipeline\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Pipeline</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2107 title=\"11 occurrences\">#107</a> &#8230;}\n        <span class=sf-dump-meta>use</span>: {<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-meta>$destination</span>: <span class=sf-dump-note>Closure($request)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref23267 title=\"2 occurrences\">#3267</a><samp data-depth=6 id=sf-dump-**********-ref23267 class=sf-dump-compact>\n            <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Foundation\\Http\\Kernel\n33 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Foundation\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Kernel</span>\"\n            <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Foundation\\Http\\Kernel\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Kernel</span> {<a class=sf-dump-ref>#47</a> &#8230;}\n            <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php\n91 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\to\\newo\\3\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Foundation\\Http\\Kernel.php</span>\"\n            <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">198 to 202</span>\"\n          </samp>}\n        </samp>}\n        <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php\n86 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\to\\newo\\3\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Pipeline\\Pipeline.php</span>\"\n        <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">142 to 148</span>\"\n      </samp>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"50 characters\">app/Http/Middleware/RemoveInjectedVerifyScript.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>18</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref253 title=\"50 occurrences\">#53</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"46 characters\">App\\Http\\Middleware\\RemoveInjectedVerifyScript</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref253 title=\"50 occurrences\">#53</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref23224 title=\"2 occurrences\">#3224</a><samp data-depth=4 id=sf-dump-**********-ref23224 class=sf-dump-compact>\n        <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Routing\\Pipeline\n27 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Pipeline</span>\"\n        <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Pipeline\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Pipeline</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2107 title=\"11 occurrences\">#107</a> &#8230;}\n        <span class=sf-dump-meta>use</span>: {<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-meta>$stack</span>: <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref23236 title=\"2 occurrences\">#3236</a>}\n          <span class=sf-dump-meta>$pipe</span>: \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n        </samp>}\n        <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php\n86 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\to\\newo\\3\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Pipeline\\Pipeline.php</span>\"\n        <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">159 to 190</span>\"\n      </samp>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref253 title=\"50 occurrences\">#53</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref253 title=\"50 occurrences\">#53</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref23206 title=\"3 occurrences\">#3206</a><samp data-depth=4 id=sf-dump-**********-ref23206 class=sf-dump-compact>\n        <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Routing\\Pipeline\n27 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Pipeline</span>\"\n        <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Pipeline\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Pipeline</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2107 title=\"11 occurrences\">#107</a> &#8230;}\n        <span class=sf-dump-meta>use</span>: {<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-meta>$stack</span>: <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref23224 title=\"2 occurrences\">#3224</a>}\n          <span class=sf-dump-meta>$pipe</span>: \"<span class=sf-dump-str title=\"46 characters\">App\\Http\\Middleware\\RemoveInjectedVerifyScript</span>\"\n        </samp>}\n        <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php\n86 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\to\\newo\\3\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Pipeline\\Pipeline.php</span>\"\n        <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">159 to 190</span>\"\n      </samp>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref253 title=\"50 occurrences\">#53</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref23206 title=\"3 occurrences\">#3206</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>110</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref253 title=\"50 occurrences\">#53</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref253 title=\"50 occurrences\">#53</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref23111 title=\"2 occurrences\">#3111</a><samp data-depth=4 id=sf-dump-**********-ref23111 class=sf-dump-compact>\n        <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Routing\\Pipeline\n27 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Pipeline</span>\"\n        <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Pipeline\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Pipeline</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2107 title=\"11 occurrences\">#107</a> &#8230;}\n        <span class=sf-dump-meta>use</span>: {<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-meta>$stack</span>: <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref23206 title=\"3 occurrences\">#3206</a>}\n          <span class=sf-dump-meta>$pipe</span>: \"<span class=sf-dump-str title=\"31 characters\">App\\Http\\Middleware\\TrimStrings</span>\"\n        </samp>}\n        <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php\n86 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\to\\newo\\3\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Pipeline\\Pipeline.php</span>\"\n        <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">159 to 190</span>\"\n      </samp>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>58</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref253 title=\"50 occurrences\">#53</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref253 title=\"50 occurrences\">#53</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2270 title=\"2 occurrences\">#270</a><samp data-depth=4 id=sf-dump-**********-ref2270 class=sf-dump-compact>\n        <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Routing\\Pipeline\n27 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Pipeline</span>\"\n        <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Pipeline\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Pipeline</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2107 title=\"11 occurrences\">#107</a> &#8230;}\n        <span class=sf-dump-meta>use</span>: {<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-meta>$stack</span>: <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref23111 title=\"2 occurrences\">#3111</a>}\n          <span class=sf-dump-meta>$pipe</span>: \"<span class=sf-dump-str title=\"52 characters\">App\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n        </samp>}\n        <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php\n86 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\to\\newo\\3\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Pipeline\\Pipeline.php</span>\"\n        <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">159 to 190</span>\"\n      </samp>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref253 title=\"50 occurrences\">#53</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"96 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>31</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref253 title=\"50 occurrences\">#53</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2271 title=\"3 occurrences\">#271</a><samp data-depth=4 id=sf-dump-**********-ref2271 class=sf-dump-compact>\n        <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Routing\\Pipeline\n27 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Pipeline</span>\"\n        <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Pipeline\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Pipeline</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2107 title=\"11 occurrences\">#107</a> &#8230;}\n        <span class=sf-dump-meta>use</span>: {<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-meta>$stack</span>: <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2270 title=\"2 occurrences\">#270</a>}\n          <span class=sf-dump-meta>$pipe</span>: \"<span class=sf-dump-str title=\"32 characters\">App\\Http\\Middleware\\TrustProxies</span>\"\n        </samp>}\n        <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php\n86 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\to\\newo\\3\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Pipeline\\Pipeline.php</span>\"\n        <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">159 to 190</span>\"\n      </samp>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref253 title=\"50 occurrences\">#53</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2271 title=\"3 occurrences\">#271</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref253 title=\"50 occurrences\">#53</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref253 title=\"50 occurrences\">#53</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref23293 title=\"3 occurrences\">#3293</a><samp data-depth=4 id=sf-dump-**********-ref23293 class=sf-dump-compact>\n        <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Routing\\Pipeline\n27 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Pipeline</span>\"\n        <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Pipeline\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Pipeline</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2107 title=\"11 occurrences\">#107</a> &#8230;}\n        <span class=sf-dump-meta>use</span>: {<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-meta>$stack</span>: <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2271 title=\"3 occurrences\">#271</a>}\n          <span class=sf-dump-meta>$pipe</span>: \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n        </samp>}\n        <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php\n86 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\to\\newo\\3\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Pipeline\\Pipeline.php</span>\"\n        <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">159 to 190</span>\"\n      </samp>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref253 title=\"50 occurrences\">#53</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref23293 title=\"3 occurrences\">#3293</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>40</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref253 title=\"50 occurrences\">#53</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>41</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Http\\Middleware\\ValidatePostSize</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref253 title=\"50 occurrences\">#53</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref23295 title=\"2 occurrences\">#3295</a><samp data-depth=4 id=sf-dump-**********-ref23295 class=sf-dump-compact>\n        <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Routing\\Pipeline\n27 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Pipeline</span>\"\n        <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Pipeline\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Pipeline</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2107 title=\"11 occurrences\">#107</a> &#8230;}\n        <span class=sf-dump-meta>use</span>: {<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-meta>$stack</span>: <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref23293 title=\"3 occurrences\">#3293</a>}\n          <span class=sf-dump-meta>$pipe</span>: \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n        </samp>}\n        <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php\n86 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\to\\newo\\3\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Pipeline\\Pipeline.php</span>\"\n        <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">159 to 190</span>\"\n      </samp>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>42</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>110</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref253 title=\"50 occurrences\">#53</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>43</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref253 title=\"50 occurrences\">#53</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref23289 title=\"2 occurrences\">#3289</a><samp data-depth=4 id=sf-dump-**********-ref23289 class=sf-dump-compact>\n        <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Routing\\Pipeline\n27 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Pipeline</span>\"\n        <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Pipeline\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Pipeline</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2107 title=\"11 occurrences\">#107</a> &#8230;}\n        <span class=sf-dump-meta>use</span>: {<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-meta>$stack</span>: <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref23295 title=\"2 occurrences\">#3295</a>}\n          <span class=sf-dump-meta>$pipe</span>: \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Http\\Middleware\\ValidatePostSize</span>\"\n        </samp>}\n        <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php\n86 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\to\\newo\\3\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Pipeline\\Pipeline.php</span>\"\n        <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">159 to 190</span>\"\n      </samp>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>44</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref253 title=\"50 occurrences\">#53</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>45</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref253 title=\"50 occurrences\">#53</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref23063 title=\"2 occurrences\">#3063</a><samp data-depth=4 id=sf-dump-**********-ref23063 class=sf-dump-compact>\n        <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Routing\\Pipeline\n27 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Pipeline</span>\"\n        <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Pipeline\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Pipeline</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2107 title=\"11 occurrences\">#107</a> &#8230;}\n        <span class=sf-dump-meta>use</span>: {<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-meta>$stack</span>: <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref23289 title=\"2 occurrences\">#3289</a>}\n          <span class=sf-dump-meta>$pipe</span>: \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n        </samp>}\n        <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php\n86 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\to\\newo\\3\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Pipeline\\Pipeline.php</span>\"\n        <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">159 to 190</span>\"\n      </samp>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>46</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>58</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref253 title=\"50 occurrences\">#53</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>47</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref253 title=\"50 occurrences\">#53</a>}\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref>#3298</a><samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-meta>class</span>: \"<span class=sf-dump-str title=\"Illuminate\\Routing\\Pipeline\n27 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span>Pipeline</span>\"\n        <span class=sf-dump-meta>this</span>: <span class=sf-dump-note title=\"Illuminate\\Routing\\Pipeline\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Pipeline</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2107 title=\"11 occurrences\">#107</a> &#8230;}\n        <span class=sf-dump-meta>use</span>: {<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-meta>$stack</span>: <span class=sf-dump-note>Closure($passable)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref23063 title=\"2 occurrences\">#3063</a>}\n          <span class=sf-dump-meta>$pipe</span>: \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n        </samp>}\n        <span class=sf-dump-meta>file</span>: \"<span class=sf-dump-str title=\"C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php\n86 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\laragon\\www\\to\\newo\\3\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span>src\\Illuminate\\Pipeline\\Pipeline.php</span>\"\n        <span class=sf-dump-meta>line</span>: \"<span class=sf-dump-str title=\"10 characters\">159 to 190</span>\"\n      </samp>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>48</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>119</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref253 title=\"50 occurrences\">#53</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>49</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>176</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>Closure($request)</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref23267 title=\"2 occurrences\">#3267</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>50</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>145</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">sendRequestThroughRouter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref253 title=\"50 occurrences\">#53</a>}\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>51</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"16 characters\">public/index.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Illuminate\\Http\\Request\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Request</span> {<a class=sf-dump-ref href=#sf-dump-**********-ref253 title=\"50 occurrences\">#53</a>}\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "surrounding_lines": ["     */\n", "    protected function unauthenticated($request, array $guards)\n", "    {\n", "        throw new AuthenticationException(\n", "            'Unauthenticated.',\n", "            $guards,\n", "            $request->expectsJson() ? null : $this->redirectTo($request),\n"], "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FMiddleware%2FAuthenticate.php&line=102", "ajax": false, "filename": "Authenticate.php", "line": "102"}}]}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET users", "middleware": "web, verified, auth, XSS, revalidate", "as": "users.index", "controller": "App\\Http\\Controllers\\UserController@index", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FUserController.php&line=32\" onclick=\"\">app/Http/Controllers/UserController.php:32-49</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "WEgVgdfObfw6rNC2frmzIt58FwRoAm7QcDJjfmq1", "url": "array:1 [\n  \"intended\" => \"http://localhost/users\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/users", "status_code": "<pre class=sf-dump id=sf-dump-968349907 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-968349907\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1601933896 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1601933896\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-488335431 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-488335431\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/customer</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; XSRF-TOKEN=eyJpdiI6IjdSSkRnc1QvZXY5eGYrZGlEV3ZmK2c9PSIsInZhbHVlIjoiODRYQmg5RHMza1RkU2ZTcEwrMEM4cWczNDZDU3kxV01XNld6Y2F2ZThZYk1WMktJeXpZRkVLbUoxVU5rZVlXWkhRWkhjK2tOQVRxR0lrcjhQZzBtMzVERThpQys1ODdTRmZQVzV5Z3o2UXJxeHlyVzhuMkhEWHkvWHJXS3FzRmNidjdrUGo1ZVNGbGMvbmZ1SVZOWVRCQUdZL3BNVmwvK011Rit5VmpWdDBEMXZrUmRWNXVSZ1ZraWNPMG9jTUxDSkw0U3ZrVitwR3VWR2RhZU1YUVNzNVgxM0VlQ2hoTWtQUm9ucENFM05jUlpvWFo5S2J5MUEvUXBBMzhKUXIvd3BiRjJLTHA5YmVmellneG10VDV5aExYSzhMV0dWeVllZFY2aVZScHRXY3BkbVR4cGlaZ2o0RFhyTlB2TDU2cm5tSDQ1NjYvYmt3TjBtaWRFaG9LVHdsZzdGTHJ2WFJJSGdFQkhDazQxTFVFMjBvQ1Q3TkNpMTRFZDQyTEpRMHRXd1N1UVVqTmVqYWFFdTcyZ2RNMnZpakgrWWJuaTBGT29vekIzeS93WmxkV0FSQ0JEb1RZZiswaTBZdmNqVzVRSTRqNzBBZklvODBzNDdITm03ZGNyRjlGQ3lLSHpramJ2K0w3MGR3RXpXNyttMGZteHd2WUlZMUVDL1BxZ21DODciLCJtYWMiOiI5NzRhOTk2M2MyZjE4YzZjNzZlNjI3NWEyODM1NzE4OGI1YTZhNGFiNDAwYmYzOWViYjhlZWVmZmExZmUxZjg3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InJCN2huSmlZUzQyRDNCZ0xqbVBTeUE9PSIsInZhbHVlIjoiRVZVeHlaMU5URldrcGgvandYVmhMVmYrRll2enlQSDVkUEpRM3BwaktVeGN2UFJCclQzNzhrdnR4bmY2S2p1d2pScVVZSUt4RHVRYnNQTGZPNzZGVGluNU1pUnZCaFlXODV1WlpKYThNbXFJUTliaEorZlZEWE5SOStJRTQ4eGZ4TXArNkF1UVVqa0xIK0FOeitzdkowVklrQm1TNVY5ZjlHM29UMFpmekwwbTdEWXhsL1YrRGpaa2twZ2NqSG93dVdRRUlWckxUNTE1THY4Z3Y2RGhjd29YZmNhSjZ5M2ZzaEQreCtrczlvL003ZjNHRnhINk5VT21ZbkdJamZkNnNiU0xDZXJrUWNlWjVpdytTdjFNTm1EbTdEaGJLSk1ZZ0s2UXQ5WHk5NjVwdUdCYjlONXJOSlZ1QVVJZFlUSWp5ZGcrSHY4Mis4VHgwSk8xZjk2T1JHOC9iNlN0cnJ6dnZKK2x5S3gweFMzblVpUllZOGV5QkhSbVNSYXJTeFJkcnR4WVhCb1QxY1hpdTM0UE92UGVGR3RDQitxd2QvUWM2a3ZFVThpUmRtdmpObm9IcXljU2NlbkZqczZhVmd0WnZTVG8zZldIVVRUVjlHN0kyeU1XZTNFV0xOejZQZTFlbU84L1drL3dUUTN0UUNxTnFxYVVXV2huUHV3LzhnVFQiLCJtYWMiOiIxZjdhOWRkYjA1OTMxMmY5NTRlNzU0Y2UzZGY2NmM2MzAwZWEzZWMwODcwNjQ1OTg1MWYyMjk0OWNlMWZlYTcxIiwidGFnIjoiIn0%3D; _clsk=xh9goj%7C1749396266838%7C4%7C1%7Ck.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-600943861 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8GgviBvWEHdkNu7UwTSn8obXEzPtOyVrO3GdFYWq</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BJuMSlnG5Xc84hQpzCBfZadVHL6kh5OEk3auNDNe</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-600943861\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2132441346 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:28:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik1Wd2RoKzNyNFBCTDRxTk02TlFVdnc9PSIsInZhbHVlIjoiVkwwMlR3d1pqYy8xd0sxejBwcEhWVHJ0bVJwRW1PUnE4MFRBYndibk41OWVvUjdIZEp3SWhSd0RpaXo5VWUwMEp3Z1o2OFFLUU9HMXhzZXQ1dUY2b1YzMlVzZktFRzB6eXZMVHU0cGpxVXd2TDNua1g2cERoQ0ZZbXBaSzN1M3I0eFczVUg2b2ZoeVoyelRlbDBlS1FTemxmL2d6TTluYTl3UkRGME5McW4zcExjTDJvQUdKcVlvWCtIeHBzeEt5OGVlbmNlZ0diVkt2K1h3YUFFMEpyVGhMem0rNGZRbEZjbWVZRGtrQWU1cklrN2kxeC9YcUIxM3NiblloRWhCMWp6SkFibUZFaTcvNURzL2lNS2Rtc1dOaGptcDRSUGVmcmdGYkEvQ3pabDRicHRoRlZuTUk0clMrb1p5YWpQb1g4aFpBN3o5bmlzVU9rMW9nczYzak9ya3ZTTkR4bXpkNEMvZVlOOUViVit5dWpqbmhWY1ZRSlZoaitaY25uK0FsT3pDY1Q0Qi84aHFCQ0YwSXhBVUFzSGFmUVhWOXlqMzFEaEJDaFpRbmduWnNvUWt2UVdrSkp5THhjVlBqcWtINFRwUEwrYktyemVWRXRaVTFydmNHMDFFd1Z3dDFlamNEWkt2d1hiWnhxS0NIZTlnVXZrTEpsdUZKazFGTFFVR1UiLCJtYWMiOiI5YjhjMjNlMWJiNWYwOWQ4ODNkMWNjYjM5YTJjYTZhMDlkYTExYTg4OTEzNzNjNDlmNmEwNzNkY2I5YTNhYjljIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:28:41 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IklkTnBldUNSb0E5RjlKUlJiTDR1eHc9PSIsInZhbHVlIjoiOEY2MWNxN3FzYTFsR3p2MEkvelhGcERtM21aOUJzdk84QTl3bWNzazM0bEg2ZUJMNmtIYnFLQUgwODJjbVJsK092ZE84eTIzMHpJNmxXSVpUR2tqOThQNkpERWs4NHhPVTRRTjYvZnVJZmxDRlVkNksrbjZnbTBPR2NQalk5NGpRMFFKVE5hNE9rTUhzL1YxcjNqeFZ5R3plbkUyZkthcG5VVHFPNkNQV3NZQ1l2SjNTOFJTWE5xUGYyQmpFNmVlakROclhkTnRub0M2SVUyREpEN0kybHJ6bjBBS2NBNFBuTFpLR2k3Q1pIa1R4Z1ZWOVRiTXoxUHhCd1RONkkrYVNVVFBpTmdDeGVTdGgrTnFwNDRocTZnT2lYenRXbHNENXNVTWFvUFZXQUZ3WUl3RVl2TDNSSzhoL2NpOTlnZjdhOW54QzJwMFlXcnpldkpNbHVhRmVJZ29zU3kwVksvekdrZ2Z0Y1NPMElHVDNvMHdBS01KbFUrQm9DdzI3dmRTamxXeWRaa1NsRWZFNU9iMFBBTzI4K3RhWGx3YUxKRWoyUUNkSnNhZUlmWTRveXhwOGRBM2Rud0xlZDI3eVA0S0hWZlNrUHBac0FOZTFUTlYxQkFqdDVraXBTUXh1ek9DL0pBcCtXZXhIZlo4TmQ0UDREUjFFY0k0MFhmMjNaSEoiLCJtYWMiOiJkM2ZkNzUzMzRmNjEwMjM1ODI1NThmMWZmYjA2NTk0NGQ4ZDA5ODA1YjY4ZTA5ODhkYTFlODgyMTZiYTU2M2Y0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:28:41 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik1Wd2RoKzNyNFBCTDRxTk02TlFVdnc9PSIsInZhbHVlIjoiVkwwMlR3d1pqYy8xd0sxejBwcEhWVHJ0bVJwRW1PUnE4MFRBYndibk41OWVvUjdIZEp3SWhSd0RpaXo5VWUwMEp3Z1o2OFFLUU9HMXhzZXQ1dUY2b1YzMlVzZktFRzB6eXZMVHU0cGpxVXd2TDNua1g2cERoQ0ZZbXBaSzN1M3I0eFczVUg2b2ZoeVoyelRlbDBlS1FTemxmL2d6TTluYTl3UkRGME5McW4zcExjTDJvQUdKcVlvWCtIeHBzeEt5OGVlbmNlZ0diVkt2K1h3YUFFMEpyVGhMem0rNGZRbEZjbWVZRGtrQWU1cklrN2kxeC9YcUIxM3NiblloRWhCMWp6SkFibUZFaTcvNURzL2lNS2Rtc1dOaGptcDRSUGVmcmdGYkEvQ3pabDRicHRoRlZuTUk0clMrb1p5YWpQb1g4aFpBN3o5bmlzVU9rMW9nczYzak9ya3ZTTkR4bXpkNEMvZVlOOUViVit5dWpqbmhWY1ZRSlZoaitaY25uK0FsT3pDY1Q0Qi84aHFCQ0YwSXhBVUFzSGFmUVhWOXlqMzFEaEJDaFpRbmduWnNvUWt2UVdrSkp5THhjVlBqcWtINFRwUEwrYktyemVWRXRaVTFydmNHMDFFd1Z3dDFlamNEWkt2d1hiWnhxS0NIZTlnVXZrTEpsdUZKazFGTFFVR1UiLCJtYWMiOiI5YjhjMjNlMWJiNWYwOWQ4ODNkMWNjYjM5YTJjYTZhMDlkYTExYTg4OTEzNzNjNDlmNmEwNzNkY2I5YTNhYjljIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:28:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IklkTnBldUNSb0E5RjlKUlJiTDR1eHc9PSIsInZhbHVlIjoiOEY2MWNxN3FzYTFsR3p2MEkvelhGcERtM21aOUJzdk84QTl3bWNzazM0bEg2ZUJMNmtIYnFLQUgwODJjbVJsK092ZE84eTIzMHpJNmxXSVpUR2tqOThQNkpERWs4NHhPVTRRTjYvZnVJZmxDRlVkNksrbjZnbTBPR2NQalk5NGpRMFFKVE5hNE9rTUhzL1YxcjNqeFZ5R3plbkUyZkthcG5VVHFPNkNQV3NZQ1l2SjNTOFJTWE5xUGYyQmpFNmVlakROclhkTnRub0M2SVUyREpEN0kybHJ6bjBBS2NBNFBuTFpLR2k3Q1pIa1R4Z1ZWOVRiTXoxUHhCd1RONkkrYVNVVFBpTmdDeGVTdGgrTnFwNDRocTZnT2lYenRXbHNENXNVTWFvUFZXQUZ3WUl3RVl2TDNSSzhoL2NpOTlnZjdhOW54QzJwMFlXcnpldkpNbHVhRmVJZ29zU3kwVksvekdrZ2Z0Y1NPMElHVDNvMHdBS01KbFUrQm9DdzI3dmRTamxXeWRaa1NsRWZFNU9iMFBBTzI4K3RhWGx3YUxKRWoyUUNkSnNhZUlmWTRveXhwOGRBM2Rud0xlZDI3eVA0S0hWZlNrUHBac0FOZTFUTlYxQkFqdDVraXBTUXh1ek9DL0pBcCtXZXhIZlo4TmQ0UDREUjFFY0k0MFhmMjNaSEoiLCJtYWMiOiJkM2ZkNzUzMzRmNjEwMjM1ODI1NThmMWZmYjA2NTk0NGQ4ZDA5ODA1YjY4ZTA5ODhkYTFlODgyMTZiYTU2M2Y0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:28:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2132441346\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-56374116 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WEgVgdfObfw6rNC2frmzIt58FwRoAm7QcDJjfmq1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-56374116\", {\"maxDepth\":0})</script>\n"}}