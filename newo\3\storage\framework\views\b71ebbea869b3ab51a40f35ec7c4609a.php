<?php
    $settings = Utility::settings();

    // الحصول على شعار الشركة من إعدادات العلامة التجارية
    $logo = \App\Models\Utility::get_file('uploads/logo');
    $company_logo_dark = !empty($settings['company_logo_dark']) ? $settings['company_logo_dark'] : 'logo-dark.png';
    $company_logo = $logo . '/' . $company_logo_dark;

    // تحقق من وجود مكتبة QR Code
    $qr_enabled = true; // تفعيل QR Code دائمًا

    // الحصول على بيانات المنتجات من الجلسة
    $sess = session()->get('pos') ?? [];
?>
<style>
    @media print {
        @page {
            size: 80mm 297mm; /* تحديد حجم ثابت للطابعة الحرارية */
            margin: 0;
        }

        body {
            width: 74mm; /* عرض ثابت مع هامش للتأكد من عدم قص المحتوى */
            font-family: 'Courier New', monospace;
            font-size: 10px;
            margin: 0 auto;
            padding: 3mm;
            direction: rtl;
            overflow: hidden; /* منع ظهور أشرطة التمرير */
        }

        .thermal-print {
            width: 100%;
            text-align: center;
        }

        .header, .footer, .items, .totals {
            width: 100%;
            text-align: center;
        }

        .logo-container {
            text-align: center;
            margin-bottom: 5px;
        }

        .logo-img {
            max-width: 90%;
            max-height: 20mm;
            margin-bottom: 5px;
        }

        .company-name {
            font-weight: bold;
            font-size: 12px;
            margin-bottom: 3px;
        }

        .invoice-type {
            font-size: 10px;
            font-weight: bold;
            margin: 5px 0;
        }

        .receipt-info {
            font-size: 9px;
            margin-bottom: 5px;
            text-align: right;
        }

        .receipt-info div {
            margin-bottom: 4px;
        }

        .bilingual {
            display: flex;
            flex-direction: column;
        }

        .ar {
            font-weight: bold;
            margin-bottom: 1px;
        }

        .en {
            font-size: 8px;
            color: #555;
            margin-bottom: 2px;
        }

        .divider {
            border-top: 1px dashed #000;
            margin: 6px 0;
        }

        .solid-divider {
            border-top: 1px solid #000;
            margin: 6px 0;
        }

        table {
            width: 100%;
            font-size: 9px;
            border-collapse: collapse;
            margin-top: 5px;
            table-layout: fixed; /* للتكيف مع أي عرض */
            max-width: 74mm; /* تأكد من عدم تجاوز عرض الصفحة */
        }

        th, td {
            padding: 2px 2px; /* تقليل التباعد */
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap; /* منع التفاف النص */
        }

        th:nth-child(1), td:nth-child(1) { text-align: right; width: 35%; }
        th:nth-child(2), td:nth-child(2) { text-align: center; width: 10%; }
        th:nth-child(3), td:nth-child(3) { text-align: center; width: 15%; direction: ltr; }
        th:nth-child(4), td:nth-child(4) { text-align: center; width: 15%; direction: ltr; }
        th:nth-child(5), td:nth-child(5) { text-align: left; width: 25%; direction: ltr; }

        .totals div {
            display: flex;
            justify-content: space-between;
            padding: 2px 0;
            font-size: 9px;
        }

        .totals span:last-child {
            font-weight: bold;
            direction: ltr; /* لضمان عرض الأرقام بشكل صحيح */
            display: inline-block;
            min-width: 60px;
            text-align: left;
        }

        .total-final {
            font-weight: bold;
            border-top: 1px solid #000;
            margin-top: 5px;
            padding-top: 3px;
        }

        .total-final span:last-child {
            font-size: 11px;
        }

        .thank-you {
            margin-top: 8px;
            font-size: 9px;
            text-align: center;
        }

        .qr-code {
            text-align: center;
            margin-top: 10px;
            margin-bottom: 10px;
            display: block;
            width: 100%;
        }

        .qr-code img {
            width: 50mm;
            height: auto;
            max-width: 90%;
            margin: 0 auto;
            display: block;
        }

        /* تحسينات للطابعات الحرارية المختلفة */
        @media screen and (max-width: 58mm) {
            body { font-size: 8px; }
            .company-name { font-size: 10px; }
            .receipt-info { font-size: 8px; }
            table { font-size: 8px; }
            .totals div { font-size: 8px; }
        }

        /* إخفاء الكود والسكريبت من الطباعة والعرض */
        script, .modal-footer {
            display: none !important;
        }
    }

    /* إخفاء الكود والسكريبت من العرض العادي أيضاً - لكن نبقي modal-footer مرئي */
    @media screen {
        script {
            display: none !important;
        }

        /* تحسين مظهر أزرار الطباعة الحرارية */
        .modal-footer {
            display: flex !important; /* إظهار أزرار الطباعة */
            justify-content: center !important;
            align-items: center !important;
            flex-wrap: wrap !important;
            gap: 10px !important;
            padding: 1.5rem !important;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
            border-top: 3px solid #dee2e6 !important;
            border-radius: 0 0 0.375rem 0.375rem !important;
        }

        #thermal-print-modal, #thermal-print-latest, #open-thermal-tab {
            background: linear-gradient(135deg, #28a745, #20c997) !important;
            border: none !important;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3) !important;
            transition: all 0.3s ease !important;
            font-weight: 600 !important;
            display: inline-flex !important;
            align-items: center !important;
            justify-content: center !important;
        }

        #thermal-print-modal:hover, #thermal-print-latest:hover, #open-thermal-tab:hover {
            background: linear-gradient(135deg, #20c997, #28a745) !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4) !important;
        }

        #thermal-print-modal i, #thermal-print-latest i, #open-thermal-tab i {
            margin-right: 5px !important;
            font-size: 1.1em !important;
        }

        #open-thermal-tab {
            background: linear-gradient(135deg, #17a2b8, #138496) !important;
        }

        #open-thermal-tab:hover {
            background: linear-gradient(135deg, #138496, #17a2b8) !important;
        }
    }
</style>

<!-- تنبيه بصري لتوجيه المستخدم للطباعة الحرارية -->
<div class="alert alert-info alert-dismissible fade show mb-3" role="alert" style="border-left: 4px solid #17a2b8;">
    <div class="d-flex align-items-center">
        <i class="ti ti-info-circle me-2" style="font-size: 1.2em; color: #17a2b8;"></i>
        <div>
            <strong><?php echo e(__('Thermal Printing Available')); ?></strong><br>
            <small><?php echo e(__('استخدم أزرار الطباعة الحرارية أسفل الفاتورة لفتح صفحة الطباعة في نافذة أو تبويب جديد')); ?></small>
        </div>
    </div>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>

<div class="thermal-print" id="printarea">
    <!-- شعار واسم الشركة -->
    <div class="header">
        <div class="logo-container">
            <img src="<?php echo e($company_logo); ?>" alt="<?php echo e($settings['company_name']); ?>" class="logo-img">
        </div>
        <div class="company-name"><?php echo e($settings['company_name'] ?? 'شركتي'); ?></div>

        <div class="receipt-info">
            <div class="bilingual">
                <div class="ar">العنوان: <?php echo e($settings['company_address'] ?? 'المملكة العربية السعودية'); ?></div>
                <div class="en">Address: <?php echo e($settings['company_address'] ?? 'Saudi Arabia'); ?></div>
            </div>
            <div class="bilingual">
                <div class="ar">الرقم الضريبي: <?php echo e($settings['vat_number'] ?? '123456789012345'); ?></div>
                <div class="en">Tax Number: <?php echo e($settings['vat_number'] ?? '123456789012345'); ?></div>
            </div>
        </div>

        <!-- تم نقل الباركود ورمز QR إلى مواضع أخرى -->
        <?php
            // إنشاء رمز الباركود للفاتورة
            $barcode_text = $details['pos_id'];
            $barcode_image = DNS1D::getBarcodePNG($barcode_text, 'C128', 1.2, 30);

            // حساب المجموع الفرعي (قبل الضريبة) إذا لم يكن موجودًا
            if (!isset($subtotal)) {
                // استخدام القيم المحسوبة من PosController
                $subtotal = isset($sales['subtotal']) ? $sales['subtotal'] :
                           (isset($sales['sub_total']) ? floatval(preg_replace('/[^0-9.]/', '', $sales['sub_total'])) :
                           (isset($sales['total']) && isset($sales['tax']) ? ($sales['total'] - $sales['tax']) : $sales['total']));

                $tax = isset($sales['tax']) ? $sales['tax'] : '0.00';
                $total = isset($sales['total']) ? $sales['total'] : ($subtotal + $tax);

                // تحويل القيم المنسقة إلى أرقام إذا لزم الأمر
                if (is_string($subtotal) && !is_numeric($subtotal)) {
                    $subtotal = floatval(preg_replace('/[^0-9.]/', '', $subtotal));
                }

                if (is_string($tax) && !is_numeric($tax)) {
                    $tax = floatval(preg_replace('/[^0-9.]/', '', $tax));
                }

                if (is_string($total) && !is_numeric($total)) {
                    $total = floatval(preg_replace('/[^0-9.]/', '', $total));
                }
            }

            // تبسيط محتوى رمز QR لضمان عمله بشكل صحيح
            $qr_text = "Invoice: " . $details['pos_id'] . "\n";
            $qr_text .= "Company: " . ($settings['company_name'] ?? 'شركتي') . "\n";
            $qr_text .= "Date: " . (isset($details['date']) ? $details['date'] : date('Y-m-d')) . "\n";
            $qr_text .= "Subtotal: " . number_format((float)$subtotal, 2) . " SAR\n";
            $qr_text .= "VAT (15%): " . number_format((float)$tax, 2) . " SAR\n";
            $qr_text .= "Total: " . number_format((float)$total, 2) . " SAR\n";
            $qr_text .= "VAT Number: " . ($settings['vat_number'] ?? '123456789012345');

            // استخدام الدالة المباشرة لإنشاء رمز QR
            $qr_image = DNS2D::getBarcodePNG($qr_text, 'QRCODE', 6, 6);
        ?>
    </div>

    <div class="solid-divider"></div>

    <!-- نوع الفاتورة -->
    <div class="invoice-type">
        <div class="bilingual">
            <div class="ar">فاتورة مبسطة</div>
            <div class="en">Simplified Invoice</div>
        </div>
    </div>

    <div class="divider"></div>

    <!-- بيانات الفاتورة -->
    <div class="receipt-info">
        <?php if(isset($details['date'])): ?>
        <div class="bilingual">
            <div class="ar">التاريخ: <?php echo e($details['date']); ?></div>
            <div class="en">Date: <?php echo e($details['date']); ?></div>
        </div>
        <?php endif; ?>
        <div class="bilingual">
            <div class="ar">رقم الفاتورة: <?php echo e($details['pos_id']); ?></div>
            <div class="en">Invoice No: <?php echo e($details['pos_id']); ?></div>
        </div>
        <div class="bilingual">
            <div class="ar">الكاشير: <?php echo e($details['user']['name'] ?? ''); ?></div>
            <div class="en">Cashier: <?php echo e($details['user']['name'] ?? ''); ?></div>
        </div>
        <?php if(isset($details['customer']['name'])): ?>
        <div class="bilingual">
            <div class="ar">العميل: <?php echo e($details['customer']['name']); ?></div>
            <div class="en">Customer: <?php echo e($details['customer']['name']); ?></div>
        </div>
        <?php endif; ?>
    </div>

    <div class="solid-divider"></div>

    <!-- جدول المنتجات -->
    <table>
        <thead>
            <tr>
                <th>
                    <div class="bilingual">
                        <div class="ar">الصنف</div>
                        <div class="en">Item</div>
                    </div>
                </th>
                <th>
                    <div class="bilingual">
                        <div class="ar">الكمية</div>
                        <div class="en">Qty</div>
                    </div>
                </th>
                <th>
                    <div class="bilingual">
                        <div class="ar">السعر</div>
                        <div class="en">Price</div>
                    </div>
                </th>
                <th>
                    <div class="bilingual">
                        <div class="ar">الضريبة</div>
                        <div class="en">VAT</div>
                    </div>
                </th>
                <th>
                    <div class="bilingual">
                        <div class="ar">الإجمالي</div>
                        <div class="en">Total</div>
                    </div>
                </th>
            </tr>
        </thead>
        <tbody>
            <?php $__currentLoopData = $sales['data']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <tr>
                <td><?php echo e($value['name']); ?></td>
                <td><?php echo e($value['quantity']); ?></td>
                <td>
                    <?php
                        // استخراج السعر الأصلي (غير المنسق) من البيانات
                        $originalPrice = 0;
                        if (isset($sess[$key])) {
                            $originalPrice = $sess[$key]['price'];
                        } else {
                            // استخراج السعر من النص المنسق كحل بديل
                            $originalPrice = isset($value['price']) ? (is_string($value['price']) ? floatval(preg_replace('/[^0-9.]/', '', $value['price'])) : $value['price']) : 0;
                        }
                        echo number_format((float)$originalPrice, 2) . ' ﷼';
                    ?>
                </td>
                <td><?php echo e(isset($value['tax_rate']) ? $value['tax_rate'] : (isset($value['tax']) && is_numeric($value['tax']) ? number_format((float)$value['tax'], 2) : '15%')); ?></td>
                <td>
                    <?php
                        // حساب الإجمالي الصحيح (السعر × الكمية)
                        $quantity = isset($value['quantity']) ? $value['quantity'] : 1;
                        $itemTotal = $originalPrice * $quantity;
                        echo number_format((float)$itemTotal, 2) . ' ﷼';
                    ?>
                </td>
            </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </tbody>
    </table>

    <div class="solid-divider"></div>

    <!-- الباركود في منتصف الصفحة -->
    <div class="barcode-middle" style="text-align: center; margin: 10px auto;">
        <img src="data:image/png;base64,<?php echo e($barcode_image); ?>" alt="Barcode" style="max-width: 90%; height: 30px; margin: 0 auto;">
        <div style="font-size: 8px; margin-top: 2px; color: #555;"><?php echo e($details['pos_id']); ?></div>
    </div>

    <div class="solid-divider"></div>

    <!-- الإجماليات -->
    <div class="totals">
        <?php
            // حساب المجموع الفرعي (قبل الضريبة)
            $subtotal = isset($sales['subtotal']) ? $sales['subtotal'] :
                       (isset($sales['sub_total']) ? floatval(preg_replace('/[^0-9.]/', '', $sales['sub_total'])) :
                       (isset($sales['total']) && isset($sales['tax']) ? ($sales['total'] - $sales['tax']) : $sales['total']));

            // حساب الضريبة
            $tax = isset($sales['tax']) ? $sales['tax'] : '0.00';

            // تحويل القيم المنسقة إلى أرقام إذا لزم الأمر
            if (is_string($subtotal) && !is_numeric($subtotal)) {
                $subtotal = floatval(preg_replace('/[^0-9.]/', '', $subtotal));
            }

            if (is_string($tax) && !is_numeric($tax)) {
                $tax = floatval(preg_replace('/[^0-9.]/', '', $tax));
            }

            // حساب الإجمالي (بعد الضريبة)
            $discount = isset($sales['discount']) ? (is_string($sales['discount']) ? floatval(preg_replace('/[^0-9.]/', '', $sales['discount'])) : $sales['discount']) : 0;
            $total = $subtotal + $tax - $discount;

            if (is_string($total) && !is_numeric($total)) {
                $total = floatval(preg_replace('/[^0-9.]/', '', $total));
            }
        ?>
        <div>
            <span class="bilingual">
                <span class="ar">المجموع الفرعي:</span>
                <span class="en">Subtotal:</span>
            </span>
            <span><?php echo e(number_format((float)$subtotal, 2)); ?> ﷼</span>
        </div>
        <div>
            <span class="bilingual">
                <span class="ar">ضريبة القيمة المضافة (15%):</span>
                <span class="en">Value Added Tax (15%):</span>
            </span>
            <span><?php echo e(number_format((float)$tax, 2)); ?> ﷼</span>
        </div>
        <?php if(isset($sales['discount']) && floatval(preg_replace('/[^0-9.]/', '', $sales['discount'])) > 0): ?>
        <div>
            <span class="bilingual">
                <span class="ar">الخصم:</span>
                <span class="en">Discount:</span>
            </span>
            <span><?php echo e(number_format((float)$discount, 2)); ?> ﷼</span>
        </div>
        <?php endif; ?>
        <div class="total-final">
            <span class="bilingual">
                <span class="ar">الإجمالي:</span>
                <span class="en">Total:</span>
            </span>
            <span><?php echo e(number_format((float)$total, 2)); ?> ﷼</span>
        </div>
    </div>

    <div class="divider"></div>

    <!-- الشكر -->
    <div class="thank-you">
        <div class="bilingual">
            <div class="ar">شكراً للتسوق معنا. نرجو زيارتكم مرة أخرى.</div>
            <div class="en">Thank You For Shopping With Us. Please visit again.</div>
        </div>
    </div>

    <!-- رمز QR في أسفل الفاتورة -->
    <div class="qr-code" style="text-align: center; margin: 10px auto;">
        <img src="data:image/png;base64,<?php echo e($qr_image); ?>" alt="QR Code" style="width: 60mm; height: auto; max-width: 90%; margin: 0 auto; display: block;">
    </div>
</div>

<!-- تحسين أزرار النافذة المنبثقة مع إضافة زر الطباعة الحرارية المحسن -->
<div class="justify-content-center pt-3 modal-footer" style="background-color: #f8f9fa; border-top: 2px solid #dee2e6;">
    <!-- زر الطباعة العادية -->
    <a href="#" id="print" class="btn btn-primary btn-sm me-2" style="min-width: 120px;">
        <i class="ti ti-printer me-1"></i> <?php echo e(__('Print')); ?>

    </a>

    <!-- أزرار الطباعة الحرارية - تظهر دائماً مع تحسينات بصرية -->
    <?php if(isset($pos_id) && $pos_id): ?>
    <!-- زر الطباعة الحرارية مع pos_id محدد -->
    <a href="<?php echo e(route('pos.thermal.print', $pos_id)); ?>" target="_blank"
       class="btn btn-success btn-sm me-2 thermal-print-btn-enhanced"
       id="thermal-print-modal"
       style="min-width: 150px; background: linear-gradient(45deg, #28a745, #20c997); border: none; box-shadow: 0 2px 4px rgba(0,0,0,0.1); display: inline-flex !important; align-items: center; justify-content: center;">
        <i class="ti ti-device-mobile me-1"></i> <?php echo e(__('🖨️ Thermal Print')); ?>

    </a>
    <?php endif; ?>

    <!-- زر الطباعة الحرارية للفاتورة الأحدث - يظهر دائماً -->
    <button type="button"
            class="btn btn-success btn-sm me-2 thermal-print-btn-enhanced"
            id="thermal-print-latest"
            style="min-width: 150px; background: linear-gradient(45deg, #28a745, #20c997); border: none; box-shadow: 0 2px 4px rgba(0,0,0,0.1); display: inline-flex !important; align-items: center; justify-content: center;">
        <i class="ti ti-device-mobile me-1"></i> <?php echo e(__('🖨️ Latest Invoice')); ?>

    </button>

    <!-- زر إضافي لفتح الطباعة الحرارية في تبويب جديد -->
    <button type="button"
            class="btn btn-info btn-sm me-2"
            id="open-thermal-tab"
            style="min-width: 140px; display: inline-flex !important; align-items: center; justify-content: center;"
            title="<?php echo e(__('فتح الطباعة الحرارية في تبويب جديد')); ?>">
        <i class="ti ti-external-link me-1"></i> <?php echo e(__('Open Thermal')); ?>

    </button>

    <!-- زر الإغلاق -->
    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal" style="min-width: 100px;">
        <i class="ti ti-x me-1"></i> <?php echo e(__('Close')); ?>

    </button>
</div>

<!-- إضافة CSS للتحسينات البصرية المحسنة -->
<style>
/* تحسينات أزرار الطباعة الحرارية */
.thermal-print-btn-enhanced {
    position: relative;
    overflow: hidden;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.thermal-print-btn-enhanced:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2) !important;
    background: linear-gradient(45deg, #20c997, #28a745) !important;
}

.thermal-print-btn-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.thermal-print-btn-enhanced:hover::before {
    left: 100%;
}

/* تحسينات النافذة المنبثقة */
.modal-footer {
    padding: 1.5rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-top: 3px solid #dee2e6;
    border-radius: 0 0 0.375rem 0.375rem;
}

.modal-footer .btn {
    transition: all 0.3s ease;
    font-weight: 500;
    border-radius: 0.375rem;
    position: relative;
}

.modal-footer .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 6px rgba(0,0,0,0.15);
}

/* زر فتح التبويب الجديد */
#open-thermal-tab {
    background: linear-gradient(45deg, #17a2b8, #138496);
    border: none;
    position: relative;
    overflow: hidden;
}

#open-thermal-tab:hover {
    background: linear-gradient(45deg, #138496, #17a2b8);
}

#open-thermal-tab::after {
    content: '🔗';
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 0.8em;
    opacity: 0.7;
}

/* تحسينات التنبيه */
.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    border: 1px solid #b6d4da;
    animation: slideDown 0.5s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تأثيرات إضافية للأزرار */
.btn-primary {
    background: linear-gradient(45deg, #007bff, #0056b3);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #0056b3, #007bff);
}

.btn-secondary {
    background: linear-gradient(45deg, #6c757d, #545b62);
    border: none;
}

.btn-secondary:hover {
    background: linear-gradient(45deg, #545b62, #6c757d);
}

/* تحسين مظهر الأيقونات */
.ti {
    font-size: 1.1em;
    vertical-align: middle;
}

/* تأثير نبضة للأزرار المهمة */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
    }
}

.thermal-print-btn-enhanced.pulse {
    animation: pulse 2s infinite;
}

/* CSS إضافي قوي لضمان ظهور أزرار الطباعة الحرارية */
.modal-footer {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    z-index: 9999 !important;
}

#thermal-print-modal, #thermal-print-latest, #open-thermal-tab {
    display: inline-flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    z-index: 10000 !important;
}

/* تأكيد إضافي لظهور الأزرار */
.btn.thermal-print-btn-enhanced {
    display: inline-flex !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* إزالة أي CSS قد يخفي الأزرار */
.modal-footer * {
    display: initial !important;
    visibility: visible !important;
    opacity: 1 !important;
}
</style>

<script>
    // تحسين طريقة الطباعة للطابعات الحرارية
    $("#print").click(function () {
        var print_div = document.getElementById("printarea");

        // إخفاء العناصر غير المطلوبة للطباعة
        $('.row').addClass('d-none');
        $('.toast').addClass('d-none');
        $('#print').addClass('d-none');

        // إنشاء نافذة طباعة جديدة للحصول على معاينة أفضل
        var printWindow = window.open('', '_blank', 'width=400,height=800');

        // إضافة محتوى الفاتورة إلى نافذة الطباعة
        printWindow.document.write('<html dir="rtl"><head><title>فاتورة مبسطة</title>');
        printWindow.document.write('<meta charset="UTF-8">');
        printWindow.document.write('<style>');

        // نسخ CSS من الصفحة الأصلية
        var styles = document.getElementsByTagName('style');
        for (var i = 0; i < styles.length; i++) {
            printWindow.document.write(styles[i].outerHTML);
        }

        // إضافة CSS إضافي لتحسين الطباعة - إخفاء كود CSS
        printWindow.document.write(`
            <style type="text/css">
            body {
                width: 74mm;
                margin: 0 auto;
                padding: 3mm;
                direction: rtl;
                font-family: 'Courier New', monospace;
                font-size: 10px;
                overflow: hidden; /* منع ظهور أشرطة التمرير */
            }

            @media print {
                @page {
                    size: 80mm 297mm;
                    margin: 0;
                }

                body {
                    width: 74mm;
                }

                .thermal-print {
                    width: 100%;
                    overflow: hidden;
                }

                table {
                    width: 100%;
                    table-layout: fixed;
                }

                th:nth-child(1), td:nth-child(1) { width: 35%; }
                th:nth-child(2), td:nth-child(2) { width: 10%; }
                th:nth-child(3), td:nth-child(3) { width: 15%; }
                th:nth-child(4), td:nth-child(4) { width: 15%; }
                th:nth-child(5), td:nth-child(5) { width: 25%; }

                .qr-code img {
                    width: 50mm;
                    height: auto;
                    margin: 0 auto;
                    display: block;
                }

                /* إخفاء كود CSS والسكريبت */
                script, .modal-footer, pre, code {
                    display: none !important;
                }

                * {
                    overflow: hidden !important;
                }
            }
            </style>
        `);

        printWindow.document.write('</style></head><body>');
        printWindow.document.write(print_div.innerHTML);
        printWindow.document.write('</body></html>');

        // إغلاق الكتابة في النافذة
        printWindow.document.close();

        // انتظار تحميل المحتوى ثم طباعة
        printWindow.onload = function() {
            // تأكد من أن رمز QR مرئي عند الطباعة
            setTimeout(function() {
                // إخفاء كود CSS والسكريبت في نافذة الطباعة
                var style = printWindow.document.createElement('style');
                style.textContent = 'script, .modal-footer, pre, code, style { display: none !important; visibility: hidden !important; }';
                printWindow.document.head.appendChild(style);

                // إزالة عناصر السكريبت من نافذة الطباعة
                var scripts = printWindow.document.querySelectorAll('script');
                scripts.forEach(function(script) {
                    script.remove();
                });

                // إزالة عناصر الستايل الإضافية
                var extraStyles = printWindow.document.querySelectorAll('style:not(:first-child)');
                extraStyles.forEach(function(styleEl) {
                    if (styleEl !== style) {
                        styleEl.remove();
                    }
                });

                printWindow.focus();
                printWindow.print();

                // إغلاق نافذة الطباعة بعد الانتهاء
                printWindow.onafterprint = function() {
                    printWindow.close();

                    // إظهار العناصر المخفية
                    $('.row').removeClass('d-none');
                    $('#print').removeClass('d-none');
                    $('.toast').removeClass('d-none');

                    // إفراغ السلة بعد الطباعة
                    emptyCartAfterPrint();
                };

                // في حالة عدم دعم onafterprint
                setTimeout(function() {
                    printWindow.close();

                    // إظهار العناصر المخفية
                    $('.row').removeClass('d-none');
                    $('#print').removeClass('d-none');
                    $('.toast').removeClass('d-none');

                    // إفراغ السلة بعد الطباعة
                    emptyCartAfterPrint();
                }, 2000);
            }, 500);
        };
    });

    // دالة لإفراغ السلة بعد الطباعة
    function emptyCartAfterPrint() {
        // إرسال طلب AJAX لإفراغ السلة
        $.ajax({
            url: '<?php echo e(route("empty-cart")); ?>',
            type: 'POST',
            headers: {
                'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
            },
            data: {
                session_key: 'pos'
            },
            success: function(response) {
                // عرض رسالة نجاح
                console.log("Cart emptied successfully");
                // إعادة توجيه المستخدم إلى صفحة نقاط البيع الرئيسية بعد تأخير قصير
                setTimeout(function() {
                    window.location.href = '<?php echo e(route("poses.index")); ?>';
                }, 500);
            },
            error: function(xhr, status, error) {
                console.error("Error emptying cart:", error);
                // إعادة توجيه المستخدم إلى صفحة نقاط البيع الرئيسية حتى في حالة الخطأ
                setTimeout(function() {
                    window.location.href = '<?php echo e(route("poses.index")); ?>';
                }, 1000);
            }
        });
    }

    // معالج النقر لزر الطباعة الحرارية في المودال (محسن)
    $(document).on('click', '#thermal-print-modal', function(e) {
        e.preventDefault();

        // إظهار رسالة تأكيد مع تحسينات بصرية
        if (confirm('<?php echo e(__("هل تريد طباعة الفاتورة الحرارية؟")); ?>\n<?php echo e(__("سيتم فتح نافذة جديدة للطباعة")); ?>')) {
            var printUrl = $(this).attr('href');

            // فتح نافذة جديدة للطباعة الحرارية مع إعدادات محسنة
            var printWindow = window.open(printUrl, '_blank', 'width=450,height=850,scrollbars=yes,resizable=yes,menubar=no,toolbar=no,location=no,status=no');

            // التأكد من أن النافذة فتحت بنجاح
            if (printWindow) {
                // إغلاق المودال الحالي
                $('#commonModal').modal('hide');

                // عرض رسالة نجاح مع تعليمات
                show_toastr('success', '<?php echo e(__("تم فتح نافذة الطباعة الحرارية")); ?><br><?php echo e(__("استخدم Ctrl+P للطباعة")); ?>', 'success');

                // التركيز على النافذة الجديدة
                printWindow.focus();
            } else {
                // في حالة فشل فتح النافذة (مثل حظر النوافذ المنبثقة)
                show_toastr('error', '<?php echo e(__("فشل في فتح نافذة الطباعة. يرجى السماح بالنوافذ المنبثقة.")); ?><br><?php echo e(__("تحقق من إعدادات المتصفح")); ?>', 'error');
            }
        }
    });

    // معالج النقر للزر الجديد "Open Thermal" - فتح في تبويب جديد
    $(document).on('click', '#open-thermal-tab', function(e) {
        e.preventDefault();

        var thermalUrl = '';

        // تحديد URL الطباعة الحرارية
        <?php if(isset($pos_id) && $pos_id): ?>
            thermalUrl = '<?php echo e(route("pos.thermal.print", $pos_id)); ?>';
        <?php else: ?>
            // إذا لم يكن pos_id متوفر، نحصل على آخر فاتورة
            $.ajax({
                url: '<?php echo e(route("pos.get-latest")); ?>',
                type: 'GET',
                success: function(response) {
                    if (response.success && response.pos_id) {
                        thermalUrl = '<?php echo e(route("pos.thermal.print", ":pos_id")); ?>';
                        thermalUrl = thermalUrl.replace(':pos_id', response.pos_id);
                        openThermalTab(thermalUrl);
                    } else {
                        show_toastr('error', '<?php echo e(__("لا توجد فواتير للطباعة")); ?>', 'error');
                    }
                },
                error: function() {
                    show_toastr('error', '<?php echo e(__("حدث خطأ أثناء البحث عن الفاتورة")); ?>', 'error');
                }
            });
            return;
        <?php endif; ?>

        if (thermalUrl) {
            openThermalTab(thermalUrl);
        }
    });

    // دالة لفتح الطباعة الحرارية في تبويب جديد
    function openThermalTab(url) {
        // فتح في تبويب جديد بدلاً من نافذة منبثقة
        var thermalTab = window.open(url, '_blank');

        if (thermalTab) {
            // إغلاق المودال الحالي
            $('#commonModal').modal('hide');

            // عرض رسالة نجاح
            show_toastr('success', '<?php echo e(__("تم فتح الطباعة الحرارية في تبويب جديد")); ?><br><?php echo e(__("يمكنك التبديل إلى التبويب الجديد للطباعة")); ?>', 'success');

            // التركيز على التبويب الجديد
            thermalTab.focus();
        } else {
            show_toastr('error', '<?php echo e(__("فشل في فتح التبويب الجديد. يرجى السماح بالنوافذ المنبثقة.")); ?>', 'error');
        }
    }

    // دالة لإظهار زر الطباعة الحرارية بعد حفظ الفاتورة
    function showThermalPrintButton(posId) {
        if (posId) {
            var thermalPrintUrl = '<?php echo e(route("pos.thermal.print", ":pos_id")); ?>';
            thermalPrintUrl = thermalPrintUrl.replace(':pos_id', posId);

            // إخفاء الزر المؤقت وإظهار الزر الحقيقي
            $('#thermal-print-after-save').hide();

            // إنشاء زر جديد مع الرابط الصحيح
            var newThermalBtn = '<a href="' + thermalPrintUrl + '" target="_blank" class="btn btn-success btn-sm me-2" id="thermal-print-modal-new">' +
                '<i class="ti ti-device-mobile"></i> <?php echo e(__("🖨️ Thermal Print")); ?></a>';

            // إضافة الزر الجديد
            $('#thermal-print-after-save').after(newThermalBtn);

            // إضافة معالج النقر للزر الجديد
            $("#thermal-print-modal-new").click(function(e) {
                e.preventDefault();

                if (confirm('<?php echo e(__("هل تريد طباعة الفاتورة الحرارية؟")); ?>')) {
                    var printUrl = $(this).attr('href');
                    var printWindow = window.open(printUrl, '_blank', 'width=400,height=800,scrollbars=yes,resizable=yes');

                    if (printWindow) {
                        $('#commonModal').modal('hide');
                        show_toastr('success', '<?php echo e(__("تم فتح نافذة الطباعة الحرارية")); ?>', 'success');
                    } else {
                        show_toastr('error', '<?php echo e(__("فشل في فتح نافذة الطباعة. يرجى السماح بالنوافذ المنبثقة.")); ?>', 'error');
                    }
                }
            });
        }
    }

    // معالج النقر لزر الطباعة الحرارية للفاتورة الأحدث
    $(document).on('click', '#thermal-print-latest', function(e) {
        e.preventDefault();

        // إظهار رسالة تأكيد
        if (confirm('<?php echo e(__("هل تريد طباعة الفاتورة الحرارية؟")); ?>')) {
            // إرسال طلب AJAX للحصول على آخر فاتورة
            $.ajax({
                url: '<?php echo e(route("pos.get-latest")); ?>',
                type: 'GET',
                success: function(response) {
                    if (response.success && response.pos_id) {
                        var thermalPrintUrl = '<?php echo e(route("pos.thermal.print", ":pos_id")); ?>';
                        thermalPrintUrl = thermalPrintUrl.replace(':pos_id', response.pos_id);

                        // فتح نافذة جديدة للطباعة الحرارية
                        var printWindow = window.open(thermalPrintUrl, '_blank', 'width=400,height=800,scrollbars=yes,resizable=yes');

                        if (printWindow) {
                            $('#commonModal').modal('hide');
                            show_toastr('success', '<?php echo e(__("تم فتح نافذة الطباعة الحرارية")); ?>', 'success');
                        } else {
                            show_toastr('error', '<?php echo e(__("فشل في فتح نافذة الطباعة. يرجى السماح بالنوافذ المنبثقة.")); ?>', 'error');
                        }
                    } else {
                        show_toastr('error', '<?php echo e(__("لا توجد فواتير للطباعة")); ?>', 'error');
                    }
                },
                error: function() {
                    show_toastr('error', '<?php echo e(__("حدث خطأ أثناء البحث عن الفاتورة")); ?>', 'error');
                }
            });
        }
    });

    // التحقق من وجود pos_id في البيانات المرسلة من الخادم
    <?php if(isset($pos_id) && $pos_id): ?>
        // إذا كان pos_id موجود، لا نحتاج لفعل شيء إضافي
        console.log('POS ID available: <?php echo e($pos_id); ?>');
    <?php else: ?>
        // إذا لم يكن pos_id موجود، نحتاج للانتظار حتى يتم حفظ الفاتورة
        console.log('POS ID not available, waiting for save...');

        // يمكن استدعاء هذه الدالة من الكود الذي يحفظ الفاتورة
        window.showThermalPrintButton = showThermalPrintButton;
    <?php endif; ?>

    // تحسينات بصرية عند تحميل النافذة
    $(document).ready(function() {
        // التأكد من ظهور أزرار الطباعة الحرارية
        function ensureThermalButtonsVisible() {
            // إظهار modal-footer بقوة
            $('.modal-footer').css({
                'display': 'flex !important',
                'visibility': 'visible !important',
                'opacity': '1 !important'
            });

            // إظهار أزرار الطباعة الحرارية بقوة
            $('#thermal-print-modal, #thermal-print-latest, #open-thermal-tab').css({
                'display': 'inline-flex !important',
                'visibility': 'visible !important',
                'opacity': '1 !important'
            });

            console.log('Thermal print buttons visibility ensured');
        }

        // تشغيل الدالة فوراً
        ensureThermalButtonsVisible();

        // تشغيل الدالة مرة أخرى بعد تأخير قصير
        setTimeout(ensureThermalButtonsVisible, 500);

        // إضافة تأثير النبضة لأزرار الطباعة الحرارية
        setTimeout(function() {
            $('.thermal-print-btn-enhanced').addClass('pulse');

            // إزالة تأثير النبضة بعد 10 ثوان
            setTimeout(function() {
                $('.thermal-print-btn-enhanced').removeClass('pulse');
            }, 10000);
        }, 1000);

        // إضافة تلميحات للأزرار
        $('#thermal-print-modal').attr('title', '<?php echo e(__("اضغط لفتح الطباعة الحرارية للفاتورة الحالية")); ?>');
        $('#thermal-print-latest').attr('title', '<?php echo e(__("اضغط لفتح الطباعة الحرارية لآخر فاتورة")); ?>');
        $('#open-thermal-tab').attr('title', '<?php echo e(__("اضغط لفتح الطباعة الحرارية في تبويب جديد")); ?>');

        // تفعيل tooltips إذا كان Bootstrap متوفر
        if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[title]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }

        // إضافة تأثير تركيز بصري على أزرار الطباعة الحرارية
        $('.thermal-print-btn-enhanced, #open-thermal-tab').on('mouseenter', function() {
            $(this).css('transform', 'scale(1.05) translateY(-2px)');
        }).on('mouseleave', function() {
            $(this).css('transform', 'scale(1) translateY(0)');
        });

        // إضافة معالج لإعادة إظهار الأزرار في حالة إخفائها
        setInterval(function() {
            if ($('.modal-footer').is(':hidden') || $('.modal-footer').css('display') === 'none') {
                ensureThermalButtonsVisible();
            }
        }, 2000);
    });
</script>
<?php /**PATH C:\laragon\www\to\newo\3\resources\views/pos/printview.blade.php ENDPATH**/ ?>