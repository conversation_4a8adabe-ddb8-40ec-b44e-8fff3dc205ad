{"name": "whichbrowser/parser", "description": "Useragent sniffing library for PHP", "keywords": ["useragent", "browser", "sniffing", "ua"], "homepage": "http://whichbrowser.net", "license": "MIT", "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "require": {"php": ">=5.4.0", "psr/cache": "^1.0 || ^2.0 || ^3.0"}, "autoload": {"psr-4": {"WhichBrowser\\": ["src/"]}}, "suggest": {"cache/array-adapter": "Allows testing of the caching functionality"}, "require-dev": {"symfony/yaml": "~3.4 || ~4.0", "phpunit/php-code-coverage": "^5.0 || ^7.0", "phpunit/phpunit": "^6.0 || ^8.0", "php-coveralls/php-coveralls": "^2.0", "icomefromthenet/reverse-regex": "*******", "squizlabs/php_codesniffer": "^3.5", "cache/array-adapter": "^1.1"}, "autoload-dev": {"psr-4": {"WhichBrowserTest\\": "tests/unit", "WhichBrowser\\": ["tests/src/"]}, "files": ["tests/src/polyfills.php"]}}