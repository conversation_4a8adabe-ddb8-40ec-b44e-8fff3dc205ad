# [YooKassa API SDK](../home.md)

# Class: \YooKassa\Request\Deals\CreateDealRequestSerializer
### Namespace: [\YooKassa\Request\Deals](../namespaces/yookassa-request-deals.md)
---
**Summary:**

Класс, представляющий модель CreateDealRequestSerializer.

**Description:**

Класс объекта осуществляющего сериализацию объекта запроса к API на создание сделки.

---
### Constants
* No constants found

---
### Methods
| Visibility | Name | Flag | Summary |
| ----------:| ---- | ---- | ------- |
| public | [serialize()](../classes/YooKassa-Request-Deals-CreateDealRequestSerializer.md#method_serialize) |  | Формирует ассоциативный массив данных из объекта запроса. |

---
### Details
* File: [lib/Request/Deals/CreateDealRequestSerializer.php](../../lib/Request/Deals/CreateDealRequestSerializer.php)
* Package: YooKassa\Request
* Class Hierarchy:
  * \YooKassa\Request\Deals\CreateDealRequestSerializer

* See Also:
  * [](https://yookassa.ru/developers/api)

---
### Tags
| Tag | Version | Description |
| --- | ------- | ----------- |
| category |  | Class |
| author |  | <EMAIL> |

---
## Methods
<a name="method_serialize" class="anchor"></a>
#### public serialize() : array

```php
public serialize(\YooKassa\Request\Deals\CreateDealRequestInterface $request) : array
```

**Summary**

Формирует ассоциативный массив данных из объекта запроса.

**Details:**
* Inherited From: [\YooKassa\Request\Deals\CreateDealRequestSerializer](../classes/YooKassa-Request-Deals-CreateDealRequestSerializer.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">\YooKassa\Request\Deals\CreateDealRequestInterface</code> | request  | Объект запроса |

**Returns:** array - Массив данных для дальнейшего кодирования в JSON



---

### Top Namespaces

* [\YooKassa](../namespaces/yookassa.md)

---

### Reports
* [Errors - 0](../reports/errors.md)
* [Markers - 0](../reports/markers.md)
* [Deprecated - 25](../reports/deprecated.md)

---

This document was automatically generated from source code comments on 2024-07-02 using [phpDocumentor](http://www.phpdoc.org/)

&copy; 2024 YooMoney