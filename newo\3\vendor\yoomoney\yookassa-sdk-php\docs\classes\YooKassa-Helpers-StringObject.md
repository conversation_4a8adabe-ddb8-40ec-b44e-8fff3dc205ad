# [YooKassa API SDK](../home.md)

# Class: \YooKassa\Helpers\StringObject
### Namespace: [\YooKassa\Helpers](../namespaces/yookassa-helpers.md)
---
**Summary:**

Класс, представляющий модель StringObject.

**Description:**

Класс объекта, преобразуемого в строку, используется только в тестах.

---
### Constants
* No constants found

---
### Methods
| Visibility | Name | Flag | Summary |
| ----------:| ---- | ---- | ------- |
| public | [__construct()](../classes/YooKassa-Helpers-StringObject.md#method___construct) |  | StringObject constructor. |
| public | [__toString()](../classes/YooKassa-Helpers-StringObject.md#method___toString) |  | Возвращает строковое значение текущего объекта. |

---
### Details
* File: [lib/Helpers/StringObject.php](../../lib/Helpers/StringObject.php)
* Package: YooKassa\Helpers
* Class Hierarchy:
  * \YooKassa\Helpers\StringObject

* See Also:
  * [](https://yookassa.ru/developers/api)

---
### Tags
| Tag | Version | Description |
| --- | ------- | ----------- |
| category |  | Class |
| author |  | <EMAIL> |

---
## Methods
<a name="method___construct" class="anchor"></a>
#### public __construct() : mixed

```php
public __construct(string $value) : mixed
```

**Summary**

StringObject constructor.

**Details:**
* Inherited From: [\YooKassa\Helpers\StringObject](../classes/YooKassa-Helpers-StringObject.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string</code> | value  |  |

**Returns:** mixed - 


<a name="method___toString" class="anchor"></a>
#### public __toString() : string

```php
public __toString() : string
```

**Summary**

Возвращает строковое значение текущего объекта.

**Details:**
* Inherited From: [\YooKassa\Helpers\StringObject](../classes/YooKassa-Helpers-StringObject.md)

**Returns:** string - Строковое представление объекта



---

### Top Namespaces

* [\YooKassa](../namespaces/yookassa.md)

---

### Reports
* [Errors - 0](../reports/errors.md)
* [Markers - 0](../reports/markers.md)
* [Deprecated - 25](../reports/deprecated.md)

---

This document was automatically generated from source code comments on 2024-07-02 using [phpDocumentor](http://www.phpdoc.org/)

&copy; 2024 YooMoney