{"__meta": {"id": "X089629db62cf3964eb4eeadb8d003dd3", "datetime": "2025-06-08 15:34:45", "utime": **********.496601, "method": "GET", "uri": "/roles/21/edit", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749396884.598298, "end": **********.496621, "duration": 0.8983228206634521, "duration_str": "898ms", "measures": [{"label": "Booting", "start": 1749396884.598298, "relative_start": 0, "end": **********.275816, "relative_end": **********.275816, "duration": 0.6775178909301758, "duration_str": "678ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.275829, "relative_start": 0.6775310039520264, "end": **********.496623, "relative_end": 2.1457672119140625e-06, "duration": 0.2207939624786377, "duration_str": "221ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 54007456, "peak_usage_str": "52MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "1x role.edit", "param_count": null, "params": [], "start": **********.431668, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/role/edit.blade.phprole.edit", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Frole%2Fedit.blade.php&line=1", "ajax": false, "filename": "edit.blade.php", "line": "?"}, "render_count": 1, "name_original": "role.edit"}, {"name": "1x components.required", "param_count": null, "params": [], "start": **********.446661, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/components/required.blade.phpcomponents.required", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Fcomponents%2Frequired.blade.php&line=1", "ajax": false, "filename": "required.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.required"}]}, "route": {"uri": "GET roles/{role}/edit", "middleware": "web, verified, auth, XSS, revalidate", "as": "roles.edit", "controller": "App\\Http\\Controllers\\RoleController@edit", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FRoleController.php&line=99\" onclick=\"\">app/Http/Controllers/RoleController.php:99-127</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.024679999999999997, "accumulated_duration_str": "24.68ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.321029, "duration": 0.01376, "duration_str": "13.76ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 55.754}, {"sql": "select * from `roles` where `id` = '21' limit 1", "type": "query", "params": [], "bindings": ["21"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 961}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 42}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 64}], "start": **********.340743, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "ImplicitRouteBinding.php:61", "source": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FRouting%2FImplicitRouteBinding.php&line=61", "ajax": false, "filename": "ImplicitRouteBinding.php", "line": "61"}, "connection": "ty", "start_percent": 55.754, "width_percent": 3.485}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.352449, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 59.238, "width_percent": 3.606}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.377039, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 62.844, "width_percent": 5.146}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.381398, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 67.99, "width_percent": 5.146}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` = 2", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 114}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.389769, "duration": 0.00438, "duration_str": "4.38ms", "memory": 0, "memory_str": null, "filename": "RoleController.php:114", "source": "app/Http/Controllers/RoleController.php:114", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FRoleController.php&line=114", "ajax": false, "filename": "RoleController.php", "line": "114"}, "connection": "ty", "start_percent": 73.136, "width_percent": 17.747}, {"sql": "select * from `plans` where `plans`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Plan.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Plan.php", "line": 65}, {"index": 21, "namespace": "view", "name": "role.edit", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/role/edit.blade.php", "line": 2}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.43659, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "Plan.php:65", "source": "app/Models/Plan.php:65", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FPlan.php&line=65", "ajax": false, "filename": "Plan.php", "line": "65"}, "connection": "ty", "start_percent": 90.883, "width_percent": 5.632}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` = 21", "type": "query", "params": [], "bindings": ["21"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/konekt/html/src/FormBuilder.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\konekt\\html\\src\\FormBuilder.php", "line": 996}, {"index": 24, "namespace": null, "name": "vendor/konekt/html/src/FormBuilder.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\konekt\\html\\src\\FormBuilder.php", "line": 938}, {"index": 25, "namespace": null, "name": "vendor/konekt/html/src/FormBuilder.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\konekt\\html\\src\\FormBuilder.php", "line": 911}, {"index": 26, "namespace": null, "name": "vendor/konekt/html/src/FormBuilder.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\konekt\\html\\src\\FormBuilder.php", "line": 888}, {"index": 27, "namespace": null, "name": "vendor/konekt/html/src/FormBuilder.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\konekt\\html\\src\\FormBuilder.php", "line": 851}], "start": **********.448615, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "FormBuilder.php:996", "source": "vendor/konekt/html/src/FormBuilder.php:996", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fkonekt%2Fhtml%2Fsrc%2FFormBuilder.php&line=996", "ajax": false, "filename": "FormBuilder.php", "line": "996"}, "connection": "ty", "start_percent": 96.515, "width_percent": 3.485}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 515, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Plan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FPlan.php&line=1", "ajax": false, "filename": "Plan.php", "line": "?"}}}, "count": 519, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => edit role, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1327626147 data-indent-pad=\"  \"><span class=sf-dump-note>edit role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">edit role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1327626147\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.388788, "xdebug_link": null}]}, "session": {"_token": "fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/roles\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/roles/21/edit", "status_code": "<pre class=sf-dump id=sf-dump-1327753593 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1327753593\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1961300688 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1961300688\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-28999877 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-28999877\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-29081887 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=xh9goj%7C1749396875457%7C14%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im5hUjJ1cEovaVY2Lys3SHowaXE4R0E9PSIsInZhbHVlIjoiRzZVT1NFN2pQMnprdmRVWm1QckhUL0QxRFAwY21nY1V6MlFTc1E4UVJkcTN4eXVrUW5WaU9NMzlMbkhQbkdjanFqSXZNT3pKTnB6OXlDNnZmRXgreUNLVS9xbEJYRHJab0NNSVhGQTZQWmYzNFR3Nk9XUGtZNTlkOUV4dVNSVWRVSE5PNWdjZUdNSWJaY1FucXNDc2daQWFuSENIajRQU3ZYb2VZZENHTlVnMEUrNG5aVXkyUjZVQ3duZmd0M2xOakJ6OFpyWkFDTXBQbFJYQ21KRlVPY3hmVkVyK3pHZ0wvVCsyeGZvMnZ4TmE3ZUxhZmIvcFhSdXlVbVQwT2w0ZEZaTTRCVEV1QTFaOEhRNHErdjhKdUlvcXBtNW41ZC81YVBZSFBWMlUxMndRSnhrU2hHSnE4UXR6Sk5ZRm1ZMUdJMDNsMEFsU3ZkdjRQR2pJWVBnbUtYdU9wWW11WXZ3ZitPTWNncEdqZlprYW5GZkJHOGRvNVJZajNGanFsZENmeDIxai9UdlBhMFBPSTQrSDBGdEZRMjJXdngrZnhiL01UY2Y4akxPalBFU0hKOTF2clNSM0dYSnVKbk1CTENEOWNhNi9UdXVVVWpCbENKOG8yQVpscFBuUWhyUW43Q1d4WEo3dE94K1R3QjZzaHRjY1NseVVlWmNNcXJJRE1OV2oiLCJtYWMiOiIwZTJhYzUzNTMyZjBkYmQ2ZmVkOTYwOTNkMjM0YWYyNmE3MWI5OTZkOWEzMTY3OTYxNDcyYjE1M2JiMGRjNjE3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImxDeEN0L3BETE9uZDVUZWhXSFFwemc9PSIsInZhbHVlIjoiRTk0WFZndVhoSXJDSFZGMnZnbXpZTVo5eStCNVoveS9ieEhlSmRuY2p6aGVJYWthWURmMlN0SVU3ZUZGc2M2ME1YMXprOXAvZlhKMC9ZdFM3S0ZZeHQzZGhrRVV4bUlkRkdRSlVuQTFqWVluUityRXVtaHhxZDAxNWI0Tm1FdzhpUDBoVnJEMVJlWlpTVFIxOFVjQnRjUll6allBSExUaVEzNWpjRkM3K1YveW10ZkJQMjdhVGxNOGhFQkt6ejhCaWx0a0lVZmNZd0lVWGh2R1dQWkNnUCt4aGQ2U3g5Z2hYZDVpWjllS0lRNkJkQVYzanJKTzcyTjZGVE9PaXhFNGM3VEsyWWJTakpiNzBoZVpobHVnbm1QbE5rTC8rWnNiMWZNS3E2a1hWRjVXWEsyVnozQTU0OVUyVDhHQ2VpcmMyZ3QyalBuZHVoRTFKcFJqTjBWMGJsQXBIS2FJVDIrTzNEU29PU1VSRkhZZDU0SDN2US83aC9oaFpMYkZKcG1DeVRINlhOTUorOFNhL3RudUhVbWlDcm1VdHltakkyVWp0TU1BWVpZRDZGeHZYMmR3UXd0U1NJM1NBZXVFdFZidFJNYndGWHdYRzlGcnhmcDlwUVFzc2dxV24xcG82ZnVqOThUK3RUS1c5Z2g3RXNnVXJFeXdRSlc2K3NmUm1uK0UiLCJtYWMiOiI3MWY5YjIzMjJkZTIwZDJjYThmZDg4ZWFhMDVlZDVlMmQzZmRkZGYyOTQwYzNhNzRjNTNkOWIyNjY3Y2RjZTJmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-29081887\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-624335845 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">l7xz5q4Zkpyo7Zd0rhHsE8M6pi3wac4XFWhsi0lQ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-624335845\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-849320539 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:34:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InFMQ0RmSEJHcmtLL2lGcnBUK0pMMEE9PSIsInZhbHVlIjoicXorZjgzUGJvenM0WVg0YytGdjM1SVE4NkRSbkl5d3FCWitXSG82T1VHa2FVb1cxWXRTbFRhSFc4ZnV0ckJpQzlLdnBGbkFvRkRTTy9YSWxoZU9xSjBkSXcvRXMxaW1nK2dndUJJa0NjUGQ1eERpdHBjUjFDRXBYMHpNWGVUd0lDeWswU3N6V3phZFNkVXZtaUtGL2w3cFZkT1d3TzVUbVA2QVI3QlowdlVCWEhYZk0xcjFyN0ExSy9aMHdRN05Wa05rQnZWaGdERUd0YitxSDM5MW51NTFWK1U3azJDWW1jVGQwd3dNSnEzSFhMMXp2VHRKTWRxTFZpTzRSVjd4VTNpVDc0RGdOZ3JqbjMxN3M3ZjJ3bGVxY0JrdE94eGRNbFk2Yi8xc0lPZWIxWmJXbnNOVlN5d0JBcjBqbTdaelc0T3F1NitUWDI3aXowMUJadFE5TkRvRG1lU2IxbnMxWnZRSktuRnhTTTlzR0VUSFY3MGI2ZDFteVFBSXh0VmJ2VHBBb3R1bmZRVUhsOWNFN3l4R2NtMlJFbnA4bDVkU0F2eXp4TGVNNXN0ZUFMaDBYUHdZb3Y4dmhsNFVYSlg2ZDAvWGdncS9ib3dFeTJmWXBVZUdsa2ZGVUcvZUxvTnhVT2hsUkZDWkQycE44MEhTcU9rZU51YWdWaTk3ekxVd2siLCJtYWMiOiI2YzQ3ZDk2YzRiNDgzNjc2MTE2YTQwYjI2ZTQ3YTQ5NTU3MTQ1YmM1NWY0MmMxMmVlYTA5NjcyMWIwODgwMTQ0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:34:45 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ii9LKzBuTDFENEVYNUZGYjg4bVVQYmc9PSIsInZhbHVlIjoiNjVRS0xXNVEyWmxhTENYUmlBVllpaTd2K2FiS1c3aVNYcmRoSVlkNlE4YWJaQnRNNWcwWUllM2Q0TkY5UkFRRld1V3FuaFdzbllwcUtnYVY3ZUVhK3hlNjlua0FoaDJQaEJZZlBxbnZyK0hMRmVUUDMwODkvSFRnYXZocmtQS1l4YlY4Q0drOEY1dDBZZENlL2c2TnRtaDZjZm5VMTRXcVdTT01hc2FrenNVTUFKM3JqVjFFREkzYjJMSmFBTFJRTlgrUVNjNkxGSUVDbnhBajNLTnVSNTQzbzdjc3BEeHRraE9zcVplTk4wd3RwUEkvWXVjb0EydG01anBQeER4SGRQWXBQdi9yTGQwQ1NpRVJXcUgyVXVScnBremlmUmpsWUZlMkt6Uzg4VTdmQVFWZDkvUXk5YUlkdlFxUmVCZTdLdGthQnFwN0huTTdaeGNNUFR4bXRPM1l6OTRDTGo0QVdBWG5VajlXc0o4QWh6R2k3UStQQytqSTc0eVJLU1E4RHcwUDZhci9iVEZzTXN6RG4wMlFQUXhlZXNmQTk0RmtZQ0NhZlBENTB2Um50dkx4Wmd5Wk1WZlNlQlN3Y3A4L2M4NW41RUlDTkZ6aXlVL3lZL0l0MUhVQ2x6Z0ZORUVkUEtnc05oRzlFYnpCUUoxQTI0RXM0dEtQbG42ZTNkZ0wiLCJtYWMiOiI5OWI0MDI1ZmVhZmQzNzhhNGI1MjQyZjliMmQ2M2EyMjg3ZjYyZjgzNmU2MDIzZTcxODQ4Yjk4NjcxZjgzMDE5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:34:45 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InFMQ0RmSEJHcmtLL2lGcnBUK0pMMEE9PSIsInZhbHVlIjoicXorZjgzUGJvenM0WVg0YytGdjM1SVE4NkRSbkl5d3FCWitXSG82T1VHa2FVb1cxWXRTbFRhSFc4ZnV0ckJpQzlLdnBGbkFvRkRTTy9YSWxoZU9xSjBkSXcvRXMxaW1nK2dndUJJa0NjUGQ1eERpdHBjUjFDRXBYMHpNWGVUd0lDeWswU3N6V3phZFNkVXZtaUtGL2w3cFZkT1d3TzVUbVA2QVI3QlowdlVCWEhYZk0xcjFyN0ExSy9aMHdRN05Wa05rQnZWaGdERUd0YitxSDM5MW51NTFWK1U3azJDWW1jVGQwd3dNSnEzSFhMMXp2VHRKTWRxTFZpTzRSVjd4VTNpVDc0RGdOZ3JqbjMxN3M3ZjJ3bGVxY0JrdE94eGRNbFk2Yi8xc0lPZWIxWmJXbnNOVlN5d0JBcjBqbTdaelc0T3F1NitUWDI3aXowMUJadFE5TkRvRG1lU2IxbnMxWnZRSktuRnhTTTlzR0VUSFY3MGI2ZDFteVFBSXh0VmJ2VHBBb3R1bmZRVUhsOWNFN3l4R2NtMlJFbnA4bDVkU0F2eXp4TGVNNXN0ZUFMaDBYUHdZb3Y4dmhsNFVYSlg2ZDAvWGdncS9ib3dFeTJmWXBVZUdsa2ZGVUcvZUxvTnhVT2hsUkZDWkQycE44MEhTcU9rZU51YWdWaTk3ekxVd2siLCJtYWMiOiI2YzQ3ZDk2YzRiNDgzNjc2MTE2YTQwYjI2ZTQ3YTQ5NTU3MTQ1YmM1NWY0MmMxMmVlYTA5NjcyMWIwODgwMTQ0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:34:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ii9LKzBuTDFENEVYNUZGYjg4bVVQYmc9PSIsInZhbHVlIjoiNjVRS0xXNVEyWmxhTENYUmlBVllpaTd2K2FiS1c3aVNYcmRoSVlkNlE4YWJaQnRNNWcwWUllM2Q0TkY5UkFRRld1V3FuaFdzbllwcUtnYVY3ZUVhK3hlNjlua0FoaDJQaEJZZlBxbnZyK0hMRmVUUDMwODkvSFRnYXZocmtQS1l4YlY4Q0drOEY1dDBZZENlL2c2TnRtaDZjZm5VMTRXcVdTT01hc2FrenNVTUFKM3JqVjFFREkzYjJMSmFBTFJRTlgrUVNjNkxGSUVDbnhBajNLTnVSNTQzbzdjc3BEeHRraE9zcVplTk4wd3RwUEkvWXVjb0EydG01anBQeER4SGRQWXBQdi9yTGQwQ1NpRVJXcUgyVXVScnBremlmUmpsWUZlMkt6Uzg4VTdmQVFWZDkvUXk5YUlkdlFxUmVCZTdLdGthQnFwN0huTTdaeGNNUFR4bXRPM1l6OTRDTGo0QVdBWG5VajlXc0o4QWh6R2k3UStQQytqSTc0eVJLU1E4RHcwUDZhci9iVEZzTXN6RG4wMlFQUXhlZXNmQTk0RmtZQ0NhZlBENTB2Um50dkx4Wmd5Wk1WZlNlQlN3Y3A4L2M4NW41RUlDTkZ6aXlVL3lZL0l0MUhVQ2x6Z0ZORUVkUEtnc05oRzlFYnpCUUoxQTI0RXM0dEtQbG42ZTNkZ0wiLCJtYWMiOiI5OWI0MDI1ZmVhZmQzNzhhNGI1MjQyZjliMmQ2M2EyMjg3ZjYyZjgzNmU2MDIzZTcxODQ4Yjk4NjcxZjgzMDE5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:34:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-849320539\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1766840351 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1766840351\", {\"maxDepth\":0})</script>\n"}}