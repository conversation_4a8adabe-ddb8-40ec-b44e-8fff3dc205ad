{"__meta": {"id": "X2bb5b204e7bdf3b42b8fdf82e8a0adf2", "datetime": "2025-06-08 16:23:57", "utime": 1749399837.009279, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.337379, "end": 1749399837.0093, "duration": 0.6719210147857666, "duration_str": "672ms", "measures": [{"label": "Booting", "start": **********.337379, "relative_start": 0, "end": **********.905709, "relative_end": **********.905709, "duration": 0.5683300495147705, "duration_str": "568ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.905723, "relative_start": 0.5683441162109375, "end": 1749399837.009303, "relative_end": 3.0994415283203125e-06, "duration": 0.10357999801635742, "duration_str": "104ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45369976, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1671\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1671-1681</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.02608, "accumulated_duration_str": "26.08ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.954055, "duration": 0.02505, "duration_str": "25.05ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 96.051}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.995229, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 96.051, "width_percent": 3.949}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-240987354 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-240987354\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1512497038 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1512497038\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-284343698 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-284343698\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=u1db3a%7C1749399485619%7C2%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjhSbWsxMm1uZWhJVE1rWEc4UURYZEE9PSIsInZhbHVlIjoiRWprWEdiYVA0akZCR1plWTZxVkY5MnpvSUFCTWJwVS8rY3ZzYU1Fd05kUUUyTnUvVERLUExraW1ZUFVma1N1Y3hyYVdtbFFpbytEc3NyS1E3YUdCellJbjk3d1lncUlJdytTR1ZVSEJmU0VOSEZtRVdXdHQxSnZXK1I4cVNkVE1jRGJQbVQyZGJKcjd0YXdnSDhwK2ZqUTdwNGdPbUVrelEvdlFtcmZRWStyZmtYZE05K0FPTWZUMnFEUjhuYk1BZXhPU29QOHpNRWRGWGJkM1hrdFgvR0pnNHZleHlUMk9SMGFMeUdaQVF6OWhaV0FwaEQ5T1h1aUszV0NpN2tMZnRLMnV4UDdONDcwR24xNnQ4UmlaT1NOd2NvNVRGTi9iRWFrVUtvQzQ1MG53K2duSmJkQkx1bFRTbUpIRCtoS2V2NFlDeXIzQjh4ZE9BS3loK2c3ZzRoWnNsTDBLM3JxYVNlMXIwVU9XSWZ3ZDFYSFlkUkw3TEdqREZPL2dxT3lSY0tJSC8vNWJFK2lkY1lqWWxZT0pIVk5sMUxueHB6a2tNak12NFJ6YVVBNmdpZ09IRHVLNXNXcWdZb3JFdVdQVStVNGdyM3VzbzV5MS9DWnJ0KzNONm1mSGx5T2dMTFVMSjJqdHFzdTFXUHo4d1ViaWptS3NzYzQvNk5xOU5qQTEiLCJtYWMiOiI0MzA5NzhhMTNmMWRkNGIyMWZlMmY4OGI3NjFkYjA0NzAxMDBhOWRhNzEyZTgwMzJlY2Y2MzY4MDlmOTc2M2Y3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ik5MZElUNlIwN2FJMUxIR3Qva0J5bGc9PSIsInZhbHVlIjoiNysxWTFHVWFqMjZuci9KWS9lM0xrT1N0a1o5c3RLdHR1R3hMMEwwZjBTTGJpVWxNZnhVNE9lVE5UOWJ0aFlWdXJ1eWU5YzRXR2V5b1pCNFZ5Z2VpYXJPd1BUVVBBZThkcWdyblFlR3RtSThpSjJUaUJMdFI0YnVGSE9SYWY5RFVHVTVRYVFSOUswdlJ6L3hrUHJ4VUlMWWRRVE9tZjZHVi9BTkVwM3NZVjVpUGJMTHRlYmJCRlI2WElYRXBzenUzN0t2eWV2NWlWd0U0VFVTVlBraTdWMDR6L25tZXVzSDRnRHhROFNoVFErS1pwYmNHdmE5MHBpOHhRQlNtaEhFUldEQ25DNmtjbC9SeEU0eDJIWHZuQU5JQkt3N1JvTkdQS005eEc3Y0Q2bnlmNzRLMDNBRWo2dkhDMndOMmViRDlrSjRwUmJBWmtZb2FCemRreVMxeFJtYWhMVlVNNlhMdmdKRlRuWjdJU09SMnhDaGRlREQ5enhSZk1ReUJvcFVSZUFBZUdUYVhXeUpIY3lQbW1jNWYrZHNkUG1ROTZ3RDE4U1o2M3hCOEhUbThQQW9OcTZPeklpZ1FNK1hxNUp0bFVCSzVYSVA2Z0pxVkR1WVh5b0cwWWxnUDhMWUpSM1dNbWN3VUtFdUVrYnAwVjkwb2h5dkdlL1c1WVFKY2FJd3QiLCJtYWMiOiI2MjVjNTgyNTUwYTA5OWIzZjNjZDcyNzZiODg1MzljMjhmZDllNDNhMTBhNGQyN2NlMDhmZDZhNDg2NjFlYjZkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xNV7SvMX3AgbiuDVO62eNXfryfZD4KbWr3rEIkXt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-665433038 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 16:23:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlR6NS93UGdDNzlicGNtYXU1Q2F2eEE9PSIsInZhbHVlIjoiM2EzV2I5TUJ4TG5ab0s2RXZSWWZFTFlZVTNZQ3hUYU56Ym1hSVcyYnFLRWdRdnJsMmFMZFJJUHVJVkE2Qi80WGZpdDJCWUtaUlhteTh1cW9FcTZaaEZrZTlNWFdMNmhBdWlQUEM0M0FCWnMrdm5mYzZ2OHY1K3VrNW1TYW1IbE5ESUhBVVB3ZXVxZDlPWm9Wa1U4MGd0OHo0NXpSMTFHTEIrdFFKaHAzV3BvZnRVUExOcGZvNkxKLzBXMDRYZ1pFM0owTjdZL1hoSXB1QW1YS3BOYVBWaWhhdFVLMndsd2pLUlgwZWhRVmlYOXQwYUhEZngwNzJ4cXpTN3FSOW0wUklVV2VFQzNreUtPZ3hWWG5WQmtNenJMc1BuREZ0WkRPRjBoVlp0Q21IUWFtVkE0MEVOMDB4dVJleDROYjFRdzBiU0VrUUsyeFdldWFjWFhKaytiZ20rbkxtZzhaaUx5eHdsa2RnYi9pRXlXU3VzQ1FhK2ZJa0ppR2MyZWJLdnlPU2pHbDJqSXJCeHhUblZPWmRSV1VSNGZ3dTNRMEoxYVJwc1N1cjlKZzJTZWV5UDd6eWZuMFBmbVgxZjd4aVR4Q1psbnR1K2xsWk9aS2lGVnBzeGlRRGVUS1JETnQwYXh2NVdZYmNiWGdCUm80UCs5ck1FU3ZXOVVLanFBcW55SXgiLCJtYWMiOiJiNTNhOTQ1YzQxYzI4NWU2NjM1YzAzNzM0NThhZmYyZmFkNzg4YjQ3ZGNjNTE2ZDQ2ZDAxNDcxZTdiZTc4MThjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 18:23:57 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IllBUXExM3NGcXc1ZllvQVZRMkFkUFE9PSIsInZhbHVlIjoiV2VVS2FjbEhUeWRCaHhYeC9wb3AxWnlVcHJldHl2ZVYvbmNWVWRQWG4vMUdOWW8yaGM0QkRYdFZiUVVTRzFsZWh5ZUtPSnZSWkkrSTROcWJKcnlPb2MrNUNwTTZSVTlhU0ZQdEtMaFdoMHljM0tTQ25PbXg0d0hENXdjcTl5aVNTNlkwTG5DZE5TWkRrTm9NWEpCSnhPUkZwOGIvbW55YTcwT3Q1TVBFbnpDaUU1aEdVVHphbXczYUNaam82NTBkVDU0U3phbzFmV2UvU2VwZXNMd0ZZWmk5VmF6YStiSU9rUTdnS0tGeHkvdVFsSC9ZTjhCZ01HZUN3REhqWGpudlBOS00zWVFzcW5VaExvcmZjY2JrcGEzRTJZZVRBSVBPeERYZjA2MC9qWjJzQTJHZHdHTGhBTW5ybzYralJ6U2kwOTh5T3Noc0l5V2JVSEF1NHlQUmM1Y1NWM1JFODFjMmtITm1PUVFCa0tvM3pmc2NVOWVRc1lSZzhMYjAvRkQ3eXl2ZmlaUjFIS3pveW11MW9aZENGZWcweEc5WGtCNFE4d3Z1aDc4NVJGK2NPY09YWWVnaWNTUlArOTY2aS9OTUlKS2MyK1RJMVhGQk9MNXErNWtFQVVCODJvVnhWQkY5OXUzc0JZUkF2UW5jNXlCSE02WHZTM3pqNEhMT2NIL20iLCJtYWMiOiI5NzhjYmJjZjBlMzYxMjYzNDE2NWVhY2E5MjIyMjYwYjY2MzE4MTNkODEzOTVmODZkZWZjZjc5MWIxOGViMDg2IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 18:23:57 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlR6NS93UGdDNzlicGNtYXU1Q2F2eEE9PSIsInZhbHVlIjoiM2EzV2I5TUJ4TG5ab0s2RXZSWWZFTFlZVTNZQ3hUYU56Ym1hSVcyYnFLRWdRdnJsMmFMZFJJUHVJVkE2Qi80WGZpdDJCWUtaUlhteTh1cW9FcTZaaEZrZTlNWFdMNmhBdWlQUEM0M0FCWnMrdm5mYzZ2OHY1K3VrNW1TYW1IbE5ESUhBVVB3ZXVxZDlPWm9Wa1U4MGd0OHo0NXpSMTFHTEIrdFFKaHAzV3BvZnRVUExOcGZvNkxKLzBXMDRYZ1pFM0owTjdZL1hoSXB1QW1YS3BOYVBWaWhhdFVLMndsd2pLUlgwZWhRVmlYOXQwYUhEZngwNzJ4cXpTN3FSOW0wUklVV2VFQzNreUtPZ3hWWG5WQmtNenJMc1BuREZ0WkRPRjBoVlp0Q21IUWFtVkE0MEVOMDB4dVJleDROYjFRdzBiU0VrUUsyeFdldWFjWFhKaytiZ20rbkxtZzhaaUx5eHdsa2RnYi9pRXlXU3VzQ1FhK2ZJa0ppR2MyZWJLdnlPU2pHbDJqSXJCeHhUblZPWmRSV1VSNGZ3dTNRMEoxYVJwc1N1cjlKZzJTZWV5UDd6eWZuMFBmbVgxZjd4aVR4Q1psbnR1K2xsWk9aS2lGVnBzeGlRRGVUS1JETnQwYXh2NVdZYmNiWGdCUm80UCs5ck1FU3ZXOVVLanFBcW55SXgiLCJtYWMiOiJiNTNhOTQ1YzQxYzI4NWU2NjM1YzAzNzM0NThhZmYyZmFkNzg4YjQ3ZGNjNTE2ZDQ2ZDAxNDcxZTdiZTc4MThjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 18:23:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IllBUXExM3NGcXc1ZllvQVZRMkFkUFE9PSIsInZhbHVlIjoiV2VVS2FjbEhUeWRCaHhYeC9wb3AxWnlVcHJldHl2ZVYvbmNWVWRQWG4vMUdOWW8yaGM0QkRYdFZiUVVTRzFsZWh5ZUtPSnZSWkkrSTROcWJKcnlPb2MrNUNwTTZSVTlhU0ZQdEtMaFdoMHljM0tTQ25PbXg0d0hENXdjcTl5aVNTNlkwTG5DZE5TWkRrTm9NWEpCSnhPUkZwOGIvbW55YTcwT3Q1TVBFbnpDaUU1aEdVVHphbXczYUNaam82NTBkVDU0U3phbzFmV2UvU2VwZXNMd0ZZWmk5VmF6YStiSU9rUTdnS0tGeHkvdVFsSC9ZTjhCZ01HZUN3REhqWGpudlBOS00zWVFzcW5VaExvcmZjY2JrcGEzRTJZZVRBSVBPeERYZjA2MC9qWjJzQTJHZHdHTGhBTW5ybzYralJ6U2kwOTh5T3Noc0l5V2JVSEF1NHlQUmM1Y1NWM1JFODFjMmtITm1PUVFCa0tvM3pmc2NVOWVRc1lSZzhMYjAvRkQ3eXl2ZmlaUjFIS3pveW11MW9aZENGZWcweEc5WGtCNFE4d3Z1aDc4NVJGK2NPY09YWWVnaWNTUlArOTY2aS9OTUlKS2MyK1RJMVhGQk9MNXErNWtFQVVCODJvVnhWQkY5OXUzc0JZUkF2UW5jNXlCSE02WHZTM3pqNEhMT2NIL20iLCJtYWMiOiI5NzhjYmJjZjBlMzYxMjYzNDE2NWVhY2E5MjIyMjYwYjY2MzE4MTNkODEzOTVmODZkZWZjZjc5MWIxOGViMDg2IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 18:23:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-665433038\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1150940084 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1150940084\", {\"maxDepth\":0})</script>\n"}}