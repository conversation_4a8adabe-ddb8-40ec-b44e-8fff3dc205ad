# [YooKassa API SDK](../home.md)

# Class: \YooKassa\Helpers\UUID
### Namespace: [\YooKassa\Helpers](../namespaces/yookassa-helpers.md)
---
**Summary:**

Класс, представляющий модель UUID.

**Description:**

Класс для получения UUID.

---
### Constants
* No constants found

---
### Methods
| Visibility | Name | Flag | Summary |
| ----------:| ---- | ---- | ------- |
| public | [v4()](../classes/YooKassa-Helpers-UUID.md#method_v4) |  |  |

---
### Details
* File: [lib/Helpers/UUID.php](../../lib/Helpers/UUID.php)
* Package: YooKassa\Helpers
* Class Hierarchy:
  * \YooKassa\Helpers\UUID

* See Also:
  * [](https://yookassa.ru/developers/api)

---
### Tags
| Tag | Version | Description |
| --- | ------- | ----------- |
| category |  | Class |
| author |  | <EMAIL> |

---
## Methods
<a name="method_v4" class="anchor"></a>
#### public v4() : string

```php
Static public v4() : string
```

**Details:**
* Inherited From: [\YooKassa\Helpers\UUID](../classes/YooKassa-Helpers-UUID.md)

**Returns:** string - 



---

### Top Namespaces

* [\YooKassa](../namespaces/yookassa.md)

---

### Reports
* [Errors - 0](../reports/errors.md)
* [Markers - 0](../reports/markers.md)
* [Deprecated - 25](../reports/deprecated.md)

---

This document was automatically generated from source code comments on 2024-07-02 using [phpDocumentor](http://www.phpdoc.org/)

&copy; 2024 YooMoney