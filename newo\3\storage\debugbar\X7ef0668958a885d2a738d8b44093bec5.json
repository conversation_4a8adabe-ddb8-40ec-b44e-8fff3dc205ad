{"__meta": {"id": "X7ef0668958a885d2a738d8b44093bec5", "datetime": "2025-06-08 15:43:08", "utime": 1749397388.050995, "method": "POST", "uri": "/empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.461507, "end": 1749397388.051015, "duration": 0.5895078182220459, "duration_str": "590ms", "measures": [{"label": "Booting", "start": **********.461507, "relative_start": 0, "end": **********.945733, "relative_end": **********.945733, "duration": 0.4842259883880615, "duration_str": "484ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.945744, "relative_start": 0.4842369556427002, "end": 1749397388.051018, "relative_end": 3.0994415283203125e-06, "duration": 0.10527396202087402, "duration_str": "105ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47488728, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@emptyCart", "namespace": null, "prefix": "", "where": [], "as": "empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1655\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1655-1669</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.01816, "accumulated_duration_str": "18.16ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.983792, "duration": 0.014539999999999999, "duration_str": "14.54ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 80.066}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": 1749397388.0104861, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 80.066, "width_percent": 4.405}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": 1749397388.032152, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 84.471, "width_percent": 8.976}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": 1749397388.036485, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 93.447, "width_percent": 6.553}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 16,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-78610678 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-78610678\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1749397388.042967, "xdebug_link": null}]}, "session": {"_token": "WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "error": "Cart is empty!", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-200433105 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-200433105\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-764354083 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-764354083\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1913017152 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1913017152\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1324666889 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749396510751%7C76%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IllNSnYvVVd2NjRKZVJTYXJGR1R5V1E9PSIsInZhbHVlIjoiRDR0VnNSclg1REExa2FVR09MalJlNnJ2di80ZkUvRnhkNW9oR0htNUFmK0JZOWt5RjgrZ0orRXlPQzBMbHlJWHNOZS9sKzFtbnlZTG1SOUFGbXJPMnNJQUJmL2VZd0V4RGkzTjlEMytZUmo1VHVTNzAwYlUwMStaeUFDZUFjbHJ0aU5TU29kOUtneklBWndWRXJiSzB1YkNBVEJIbmFMeUphRkxNSjE1WUF6T2NISG5CS094SnBwdExUTVhLREFZV2RXbWk1b2l4TGU2WkUyL3dQRndBWWhEZHR2OUNWUVV6TWpOQW1oZ3FVTFhPUWVjWXdXWmdpMkpXR20xY1YzNEwweGorWGFuVGpMdHBUSXVQeVlqbXlCc2FEeGVyZkhJMUJBTVpGWDVpMmc5Tk9KaWdBWlhMVSs0MzJFVkRQdDJ2dUl2Qk5UNTRLeWRyZWdFVnZDQWNZVndMc1hjRSt0WGtRNjVUSG5FbW9iYkJQUUhTVURGWUdSRmR5MHpRbDhNakNxTHgvRnhVbWY2NE1qelkxczVqdDdMekJkWjh2MHJINkxlR1lUU0V1bFNrV0hZTmZpdkN2OHRiWU82Wlh5SVM5REp4c1ZveTUwOHl5bmEvWExybG5hSExlbmFwQm0wSEVKL1l1dzljLzkzZXVLOEdOWk5EMlZzSnlmc2JDUGciLCJtYWMiOiJiN2UyNmNiMWZhYzcxMTM5MmViNGM5MWQ5MmZiODE0NWUwMzFjZTU3OWNiMWUyMzQ1MGJkMmM0NTdiMWJmYjM3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImFXN1JqM3BVT2E5TldBNHdtZ2E4MkE9PSIsInZhbHVlIjoiQnBCWGZvc21rVS9VY1VTYXJseUhzV09rQXZaaHAzaVBuNktieE9mL09RZ2l0Q05PRzFKTkE2dWJrMVVneGJMWHdKQkFCZVNLaVhIWFNoamlzSmkxMDdMYTlRSXI3L0VtRklDVUJzTGNzeDQreVk2VG1naVVBWUdjN3gyUU5rVHNBOFA2c1p5MVFMMW55aFZtMzhaWlJrSGlXZXc3THA0SkkvRWVyZTJsaXJvdDRNZmRWUkdESEpXZit1Q21yTzNOT0hnejllT2FWdnBXSlhSbTRHRy9XK21FdmJrNTJ0L0JKQ0ZWVXJMdGdacjlSelIwdFRlUU5iMWpvLzE0c015SW1tU0MzdWNMcTVoQWQ3RjJSbHRYRkMxZ0Z0K1JybzcrWlAxamR4bVpRZjdndWhzRE5PWXVyTWxLQnZRbGpESW5oZVBmcTNjVnozeENGOGYrZStHdC9XZHI1ZW5VajQ2SnpxRGpXR0wzZ2dwdlg4d0dFbVZZRHB6N0tqRk53aXhWV2pGVFdaSTZWUkw5NTVBSlFQNEc0K3BTV0VkdjIxTFlsdmhOWW0ySFRoaUlPbk5CajEwekdJNVR3SDhzeHFpU2FVeEN6Q2p4TmlBekhBSUoyRm45TFpZTisxZVQzb0hLY2c2bGZRd2hXbS95UzJsNnArVjN4R2FHMWlhN0phb0kiLCJtYWMiOiI1M2Y0NzYyMmYyYTdkYjJmMzNkNWQyZmM4NTgxZjA5ZjMzZjE1NTU0M2UyMjU4NTA1YjU4OGQ4NTcwNzE4NWM4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1324666889\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-981179879 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xNV7SvMX3AgbiuDVO62eNXfryfZD4KbWr3rEIkXt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-981179879\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1202330076 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:43:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im5ubGJRYlB5cjdqY1ZzQy9VQkxFaUE9PSIsInZhbHVlIjoiWm5ZOW5ycUZoT1lJV3IyWDJKQ2JpTU9xNjVWSDRCa1EwUVlqb1lIWUQxQ1NaZzk0TTgxRTZrTC9RR2JmVnpMeVk3QkxDa3V0ckxsc3V2YUhHOWRpak16L0hwY05mQ2tST3hHaERsbnVKWDdpMzRNeTJOTklPV1pqdnF3b0JBWEtHaCt1TWc4UDlGd3ZEYWVFUTVoSHE5SXVDYko4aEJtcGgwWWpxK2JWU0RRcTlmb2lKTlorM1lRM2loTkxKOUxoYXBtazUvVC9IYzFrb0ZtMUg1TWJQRnpQYzc2dWJvMFJZMTMzd09ZbE13TmIvQ3RkK24rNVlEMU5MRzgyaDgxVURwT0ZZb2s5dERZRklsWEVCREJxRWtPRFJaU216RUJPUjRQK1RTUDJMSk1yb2dSZ1BvZytBT0ZwTlZFeTQrK3h3ejgzNjA1V1Y1dEhVUzhlNXdmcndyTDd4VGE4WUlEZ1VjR1dZV1FVOW9DN09uRzhFdzczYTZYaEpKTnlLams3L1I2YWt2K3lBaGdLU0pBRDQxU1JNb3VGRHgzVmdCbFdzQ3QvdjFDTGJQRW1aa3RWbDhqbFFHR3VOUzN2bk1JV1pYeERqZjZyNE81djc4bkVwUFp1TEJwTmJwYTFaRzBSNEdlZFo1K2VRdEkrWCtDVElrclU2cjlSN0xha0ZhR0YiLCJtYWMiOiI4OWNjMzM0ZTNhMTMxMzNmZmU4MzdhODkwYmUxZWIyYWM5ZTA5ZjgwYjU3NDFkNGM4ZGE1ZDRjYWU3YzRkN2ZjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:43:08 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlVIYzN1NUEySnduQVljeTVGMWxMRVE9PSIsInZhbHVlIjoiRmdCR0VuVmxjdUNQZjFpSDQycGR4NWNVYlc5dmtKa2JvSjNJSjErOUxzQUlmSnNNbTVFTXNzWk9vQVp4bTNwY2VBeUxUU1VnY2JqdWY1SFJMT3VWTDd4czhjeElQM3FQWUVyK1VNOVBWNkcvT203WURrdkxud1htK2dYa3JodDE4cFU0TjV3blowR1h3c2FucXd3cm5GM1R5ZVVzQUF0cCtQL2NGYjVKbW43LzFEM3RyUVBsY25QQ1gwWmx3T09lT01RdndVV2p4VDRrZEprTC9TeDJVWXlRZXA5eW9mS3JFS2lqQk1XQmhPL1laclZwMTJMRjc1aUlDRGIrOFhhS1RrR2lOc0loOEkzRzJxY0p2WUhpS1NXd3JmZW9oMitRL0FEcTc0RjlIQWdYblkxcGQzM1ladFRtalRDdTJjaFBOamQ0ME95R3VhQXlzOVI4Z05lOG1oTC9SMW1WSm1LdVB4TVZrZEZPaUJCeE5sZ0dUb3B1eUkvWERYSUNydXpPSFppRE8ycFZwQW1KcnM1aEpkMmljRzlobzdvQnNraFhBbXNsZ1N1Q3V1SHBIWVlJSGNyclQxVWtTR1lMUGdrb0R4SVRWOWtNdTZESGNMYkFxOG9OVHNCOGxzc3REcFlxQ0t3WUJ3Z1lSR1ZQckZ0NUN3R3dKZVJBZUlEeVVaRXciLCJtYWMiOiI2YzUwMzY2NzEyMDc2NGMxNDA2MWRhNWEyNDNlNTUwNzQ1MDU5Njg4ZGZjNzczYWJhNDA5MzFmYjQwZjZhNGZjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:43:08 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im5ubGJRYlB5cjdqY1ZzQy9VQkxFaUE9PSIsInZhbHVlIjoiWm5ZOW5ycUZoT1lJV3IyWDJKQ2JpTU9xNjVWSDRCa1EwUVlqb1lIWUQxQ1NaZzk0TTgxRTZrTC9RR2JmVnpMeVk3QkxDa3V0ckxsc3V2YUhHOWRpak16L0hwY05mQ2tST3hHaERsbnVKWDdpMzRNeTJOTklPV1pqdnF3b0JBWEtHaCt1TWc4UDlGd3ZEYWVFUTVoSHE5SXVDYko4aEJtcGgwWWpxK2JWU0RRcTlmb2lKTlorM1lRM2loTkxKOUxoYXBtazUvVC9IYzFrb0ZtMUg1TWJQRnpQYzc2dWJvMFJZMTMzd09ZbE13TmIvQ3RkK24rNVlEMU5MRzgyaDgxVURwT0ZZb2s5dERZRklsWEVCREJxRWtPRFJaU216RUJPUjRQK1RTUDJMSk1yb2dSZ1BvZytBT0ZwTlZFeTQrK3h3ejgzNjA1V1Y1dEhVUzhlNXdmcndyTDd4VGE4WUlEZ1VjR1dZV1FVOW9DN09uRzhFdzczYTZYaEpKTnlLams3L1I2YWt2K3lBaGdLU0pBRDQxU1JNb3VGRHgzVmdCbFdzQ3QvdjFDTGJQRW1aa3RWbDhqbFFHR3VOUzN2bk1JV1pYeERqZjZyNE81djc4bkVwUFp1TEJwTmJwYTFaRzBSNEdlZFo1K2VRdEkrWCtDVElrclU2cjlSN0xha0ZhR0YiLCJtYWMiOiI4OWNjMzM0ZTNhMTMxMzNmZmU4MzdhODkwYmUxZWIyYWM5ZTA5ZjgwYjU3NDFkNGM4ZGE1ZDRjYWU3YzRkN2ZjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:43:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlVIYzN1NUEySnduQVljeTVGMWxMRVE9PSIsInZhbHVlIjoiRmdCR0VuVmxjdUNQZjFpSDQycGR4NWNVYlc5dmtKa2JvSjNJSjErOUxzQUlmSnNNbTVFTXNzWk9vQVp4bTNwY2VBeUxUU1VnY2JqdWY1SFJMT3VWTDd4czhjeElQM3FQWUVyK1VNOVBWNkcvT203WURrdkxud1htK2dYa3JodDE4cFU0TjV3blowR1h3c2FucXd3cm5GM1R5ZVVzQUF0cCtQL2NGYjVKbW43LzFEM3RyUVBsY25QQ1gwWmx3T09lT01RdndVV2p4VDRrZEprTC9TeDJVWXlRZXA5eW9mS3JFS2lqQk1XQmhPL1laclZwMTJMRjc1aUlDRGIrOFhhS1RrR2lOc0loOEkzRzJxY0p2WUhpS1NXd3JmZW9oMitRL0FEcTc0RjlIQWdYblkxcGQzM1ladFRtalRDdTJjaFBOamQ0ME95R3VhQXlzOVI4Z05lOG1oTC9SMW1WSm1LdVB4TVZrZEZPaUJCeE5sZ0dUb3B1eUkvWERYSUNydXpPSFppRE8ycFZwQW1KcnM1aEpkMmljRzlobzdvQnNraFhBbXNsZ1N1Q3V1SHBIWVlJSGNyclQxVWtTR1lMUGdrb0R4SVRWOWtNdTZESGNMYkFxOG9OVHNCOGxzc3REcFlxQ0t3WUJ3Z1lSR1ZQckZ0NUN3R3dKZVJBZUlEeVVaRXciLCJtYWMiOiI2YzUwMzY2NzEyMDc2NGMxNDA2MWRhNWEyNDNlNTUwNzQ1MDU5Njg4ZGZjNzczYWJhNDA5MzFmYjQwZjZhNGZjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:43:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1202330076\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-555846620 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Cart is empty!</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-555846620\", {\"maxDepth\":0})</script>\n"}}