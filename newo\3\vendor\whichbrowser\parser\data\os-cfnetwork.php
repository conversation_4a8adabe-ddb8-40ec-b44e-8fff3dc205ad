<?php

namespace WhichBrowser\Data;

CFNetwork::$OSX = [
    '1.1'           => [ 'value' => '10.2' ],
    '1.2.1'         => [ 'value' => '10.3.2' ],
    '1.2.2'         => [ 'value' => '10.3.9' ],
    '1.2.6'         => [ 'value' => '10.3.9' ],
    '128'           => [ 'value' => '10.4' ],
    '128.2'         => [ 'value' => '10.4.2' ],
    '129.5'         => [ 'value' => '10.4.3' ],
    '129.9'         => [ 'value' => '10.4.4' ],
    '129.10'        => [ 'value' => '10.4.4' ],
    '129.13'        => [ 'value' => '10.4.6' ],
    '129.16'        => [ 'value' => '10.4.7' ],
    '129.18'        => [ 'value' => '10.4.8' ],
    '129.20'        => [ 'value' => '10.4.9' ],
    '129.21'        => [ 'value' => '10.4.10' ],
    '129.22'        => [ 'value' => '10.4.11' ],
    '217'           => [ 'value' => '10.5' ],
    '220'           => [ 'value' => '10.5.1' ],
    '221.5'         => [ 'value' => '10.5.2' ],
    '330'           => [ 'value' => '10.5.3' ],
    '330.4'         => [ 'value' => '10.5.4' ],
    '339.5'         => [ 'value' => '10.5.5' ],
    '422.11'        => [ 'value' => '10.5.6' ],
    '438.12'        => [ 'value' => '10.5.7' ],
    '438.14'        => [ 'value' => '10.5.8' ],
    '454.4'         => [ 'value' => '10.6.0' ],
    '454.5'         => [ 'value' => '10.6.2' ],
    '454.9.4'       => [ 'value' => '10.6.3' ],
    '454.9.7'       => [ 'value' => '10.6.4' ],
    '454.11.5'      => [ 'value' => '10.6.5' ],
    '454.11.12'     => [ 'value' => '10.6.7' ],
    '454.12.4'      => [ 'value' => '10.6.8' ],
    '520.0.13'      => [ 'value' => '10.7.1' ],
    '520.2.5'       => [ 'value' => '10.7.2' ],
    '520.3.2'       => [ 'value' => '10.7.3' ],
    '520.4.3'       => [ 'value' => '10.7.4' ],
    '520.5.1'       => [ 'value' => '10.7.5' ],
    '596.1'         => [ 'value' => '10.8.1' ],
    '596.2.3'       => [ 'value' => '10.8.2' ],
    '596.3.3'       => [ 'value' => '10.8.3' ],
    '596.4.3'       => [ 'value' => '10.8.4' ],
    '596.5'         => [ 'value' => '10.8.5' ],
    '673.0.3'       => [ 'value' => '10.9' ],
    '673.2.1'       => [ 'value' => '10.9.2' ],
    '673.4'         => [ 'value' => '10.9.3' ],
    '720.0.9'       => [ 'value' => '10.10' ],
    '720.1.1'       => [ 'value' => '10.10.1' ],
    '720.2.4'       => [ 'value' => '10.10.2' ],
    '720.3.13'      => [ 'value' => '10.10.3' ],
    '720.4.4'       => [ 'value' => '10.10.4' ],
    '720.5.7'       => [ 'value' => '10.10.5' ],
    '760.0.5'       => [ 'value' => '10.11' ],
    '760.1.2'       => [ 'value' => '10.11.1' ],
    '760.2.6'       => [ 'value' => '10.11.2' ],
    '760.4.2'       => [ 'value' => '10.11.4' ],
    '760.5'         => [ 'value' => '10.11.4' ],
    '760.5.1'       => [ 'value' => '10.11.5' ],
];

CFNetwork::$IOS = [
    '459'           => [ 'value' => '3.1.3' ],
    '467.12'        => [ 'value' => '3.2' ],
    '485.2'         => [ 'value' => '4.0' ],
    '485.10.2'      => [ 'value' => '4.1' ],
    '485.12.7'      => [ 'value' => '4.2.1' ],
    '485.12.30'     => [ 'value' => '4.2.8' ],
    '485.13.9'      => [ 'value' => '4.3' ],
    '548.0.3'       => [ 'value' => '5.0' ],
    '548.0.4'       => [ 'value' => '5.0.1' ],
    '548.1.4'       => [ 'value' => '5.1' ],
    '602'           => [ 'value' => '6.0' ],
    '609'           => [ 'value' => '6.0' ],
    '609.1.4'       => [ 'value' => '6.1.2' ],
    '671'           => [ 'value' => '7.0' ],
    '672.0.2'       => [ 'value' => '7.0' ],
    '672.0.8'       => [ 'value' => '7.0.3' ],
    '672.1.13'      => [ 'value' => '7.1' ],
    '672.1.14'      => [ 'value' => '7.1.1' ],
    '672.1.15'      => [ 'value' => '7.1.2' ],
    '711.0.6'       => [ 'value' => '8.0' ],
    '711.1.12'      => [ 'value' => '8.1' ],
    '711.1.16'      => [ 'value' => '8.1.1' ],
    '711.2.23'      => [ 'value' => '8.2' ],
    '711.3.18'      => [ 'value' => '8.3' ],
    '711.4.6'       => [ 'value' => '8.4' ],
    '711.5.6'       => [ 'value' => '8.4.1' ],
    '758.0.2'       => [ 'value' => '9.0' ],
    '758.1.6'       => [ 'value' => '9.1' ],
    '758.2.7'       => [ 'value' => '9.2' ],
    '758.2.8'       => [ 'value' => '9.2' ],
    '758.3.15'      => [ 'value' => '9.3' ],
    '758.4.3'       => [ 'value' => '9.3.2' ],
];
