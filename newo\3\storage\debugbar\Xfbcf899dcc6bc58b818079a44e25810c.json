{"__meta": {"id": "Xfbcf899dcc6bc58b818079a44e25810c", "datetime": "2025-06-08 15:42:44", "utime": **********.18611, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749397363.541389, "end": **********.186132, "duration": 0.6447429656982422, "duration_str": "645ms", "measures": [{"label": "Booting", "start": 1749397363.541389, "relative_start": 0, "end": **********.105262, "relative_end": **********.105262, "duration": 0.5638730525970459, "duration_str": "564ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.105274, "relative_start": 0.563884973526001, "end": **********.186135, "relative_end": 3.0994415283203125e-06, "duration": 0.08086109161376953, "duration_str": "80.86ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45137888, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.006869999999999999, "accumulated_duration_str": "6.87ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1434631, "duration": 0.00522, "duration_str": "5.22ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 75.983}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.161675, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 75.983, "width_percent": 10.917}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (17) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.172385, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 86.9, "width_percent": 13.1}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/eyJpdiI6InRWOGJ2eHRKM1Fpc2NXaGVRbGREekE9PSIsInZhbHVlIjoiZjN3YzRMbnc0K3FRanU5bW1yTnVhZz09IiwibWFjIjoiZjE4NTMxYjk4YWZhMzQxNWZlNDMwYjNhZmVlZDNjY2EwODMzNzQzMDk3MTU0NWQ5NTM3MTFmNmY3MjVkMDlmOCIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1666785040 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1666785040\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1176564194 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1176564194\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-465327229 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-465327229\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6InRWOGJ2eHRKM1Fpc2NXaGVRbGREekE9PSIsInZhbHVlIjoiZjN3YzRMbnc0K3FRanU5bW1yTnVhZz09IiwibWFjIjoiZjE4NTMxYjk4YWZhMzQxNWZlNDMwYjNhZmVlZDNjY2EwODMzNzQzMDk3MTU0NWQ5NTM3MTFmNmY3MjVkMDlmOCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwl%7C0%7C1982; _clsk=o6pnpa%7C1749397360704%7C12%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlBxdkhRUVhXRW5oZlJvbkkxYkRVcnc9PSIsInZhbHVlIjoiNFF4cm43SkkzakU3T3k1ODlOelduTjI2VHVHT3NNVlh2eG9iWE1qek8vN0ZxemZjWk15NXNtdkxZNzluZFBzK1VqM1hBbkZLcEJnUXZWUkRQS1VZWkxERDZPVUtobXl6OTEyT0Vqc0RPRVZ4WjdYK1NiWG1SL1Rka1JBZ1MxbzVmOVJ0SkVQb1JYamtsYzFSUmFEYVdTKzhLUEczVnRtUURVQ3hJVVB1L2NmRHhqNWI2MkpwUW5yNlVDL3Iyd3BPUzJWUVBibVI2Z3hTeWdrN082SmpVN0N5bXNEUVdYd0dyTVRINFRHWGU3WW5TcHJoY3NUUVBzaHIyeXFSLzA0Nkw4ajlrNjNUZ2JaQ0tLc0hNRG82WXBWZitubGI1ekNtOFRWdG5LTURMY25pN3ExQnk1MTRud1Zsc1VuTHNFdmtXbUZ2VlhiYkdBRThJeXFlenVoREhHQkFrOVJVakJFaXZwckgzM1hRSnFaUjFFY2NWcHdKWG92U0VWQ0hrejBlUHFibEgxdWl6eHpGSTNmMnFSVFRJUk14Q1BLaFZxdWZqQnU1d1BLM2dIUEgyOXJDUk9QTWp0SHZTL2xiNnd1RXdYVWJNV09XMCt5c2paWFhMSWZSdzJ0dHdmaUdtVGxrb3ZtQ1NWM0ZhTGFWQUhuZFc0RFlKQzlXbzJUUTJ6R2QiLCJtYWMiOiI0ZGJhNmY4ZDIyZDBjY2I3ODI0ZTQwODQxMWVkNTRhNDRjNmU0ZTYxOWNmNDk5OWQwZmM0Y2FjNGRkMDk3NjdmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImZNNUx2bURIM1l3OFc4dTFSYlJ5clE9PSIsInZhbHVlIjoiaHhnbVEwc0w0RnhPSlNmRU5zcEVDK1d0REpobkFQcGxxYjFMRG1OOEc2bDdaTzFXU2ZSZkhrU095Q25qWm5qU1RxMDBrSFFtRkx6UDF0M3V4Qm1WV0REb1RHS3RRb0F1WWhBSjM0K0ZlL0d4ejdUSGZza1Z1bzAxQk9ENkgxKzBXaUMzanpyN3dTcC9FY1N0UU8xNU5YbHhva0lNWTFwUG0vTmdhSTBSbmphMHBabG5NZFZBTWVKaFhGSTBTMkZaNUhvNHJJVWphQzk3Tzdna0JtcVlza3JJdGM2SWpTMUNTaEdGRGNLNFJUSnRsMHdublFVT3Fxb213aGVjbDM4NXN6bE1JWHliRlVlK1l2WGlnclpyQlpmdTJ1TWtaaWlIYzg5R05wTjdMSk1IU2xCNjZTWlk2WWRpS3JEVlVTZktpOEhESGFUMEpGR05ZeGtYa1NtTlptRFFiamxXN3ZScHdwOVp4RzJiWU5XTS9WMEx0eEJrVlhiN0Z1VUNZVWkyazQwK0x2QUw3R284SnZrTkJMNEU5Tzc1aVdiUGRqWEEwaDVtWmd0QUVwSVJ6enFTZ0htbzlnM1RpM0FKVlBwa2lzNXh2VS83b3AvZ255cW4xdU5TbWVyMVJyZytuYzVTb0tZOFZnaU54ckl6eW9BYUEwdmFINE5ETFNSTkxvQS8iLCJtYWMiOiJmNGY3YzUzMmM4MTIzYTM2MDZmZTdhZmNjNTI1MmJkY2IyNTE0NTdjZmNkNGJjODNlY2I0YThkNmMyNGIyN2Q4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">COI6IIrAbucReABHBN7o3ZaIzRbwBCI1gfwrcfCo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2147051111 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:42:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InR4cDJ3ai8wNW1nOFB6YnZGVjVXQ3c9PSIsInZhbHVlIjoiMlFPbGNKaExCYTVlc0h0eVVEdFZWUnJSNG5LNUJZZ3h1ZjE3L09aWmdsYUd5eWxFZHVZMjNJeDljckxHdlhqQ1A5d3dwbHhIeENmc3dkT2JvODJ2ckw5cmZDNWwyT1hvUWJCRTlWWHg3cjJxSHBibFp2WVB4UjFEamIwenk1ZFJ4cWNITWpSaTVKTUFFcFZmckMxUzVLaU40bGVQc0dWSHF1SDBYL2R6TUVyRFIwKyswcjE3V3ZhVDg2VC90QmVtTjh0VjZQSUNnN1NYTFpPNjhmNFhPVkRUMkNuajFSQ05WTUFsTWJoRUkwaGVudm04YmkwMkptNk54eFhoRUdCbHNRd1RtdGpQQlVxVTdBWUV4SmlrRTRBbGttanZmSjAzeUF5ak9kU3k0Y1pkdzc2SHVEVXpIeW00ajExUUMwQURQK1p5WTIrSEhpdnlTWEM0VVBBdzBCSmZyelhhTllQTmNXV2pucDlxaDFzMGYwamNBdFB4NWdEazhQdE1mRTJWYzFPc05PMXZ0NWlnU3BEdmU0bmphZnl5bFhCNDUyQkhrVHYwRHlxR3p5TENDd3hzbzlzaWNtaFJtejh1cVhuMlRQK3ZNM2YvR2dqVUMwN2VIS0ZJQnBOK09HZzZhb2YzcW1Wb25BUXZNeVdoYVpIMkVoeVZmZkgxc0VHdUg4b0QiLCJtYWMiOiIzM2NhMDZkMWZkNDUwOTQyOWQzMTNjNjg0OTk5ZDcyZTFiYTQ1Y2RmZDNlNjYxZjNlMWUxZDZhM2EyNjhkM2QyIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:42:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImVqMTBya25yTmplUHowdE1lb0NRS1E9PSIsInZhbHVlIjoidzk2OVVCSzZEQ2wvRnMyZ21ZVm1rUE5lWjdCNHpkbElnNElXa0lKZzhVNStIZHA4STVxWDhqUktCUlZkNjI0Sm9mSjV3TFU0cVlmTzB3WkdQMFJYalpmcWJSSStGQ3R3bVY4eEFWYkpkR0pjczRwTVhLNGpyUHVORnlMbFU4dmpNM1hYNU8rWENsenZBVWlWVzFKVWNrUVV2ZjE2M2Z6TG9iSTBLQ2FwSFl3V1JXc2R3MStsR3locU5VWGNTeWRaV1dUUDJwdXFiNnpsYm9rMlhsc2piZmpZTG90UEQ4MXJkMExYWmRzaXlVei9iNVgwRDVGNUpuV1FUcWxGL3FJVjBobFpOZHVLdVBZYlZ5S1F1QnVqWHdtMUcvaDgvVTMyOXNwYzlTSmVSTTg4L05SemdQNG1HMzRGMnY3R2NNSjNZcGRrdVo2bEQxb1Z1ZlFHWmM1K2F3Wkg0bjF1c1lPNWl0N1o3TXp0TExsMW1SYmNaNStEMytDclhkYVN6WVNjMWxTZTlqdUd5UEJhdGpENmQrdlQ5eTgvald2S0JQYjlHUjBHejZwdWp2d0FtNlFmdkRjNjFUaXlCMjVyTjllS0R2VTZWVWF1WmhPOHBaa2FidEdhWThFNFpTdlFzRU5tSDhmbFhVRExLRzljNkh1dHBHMTNBRGpETlVDRmx1enIiLCJtYWMiOiI1Y2Y0MThiZThiNDhhNjgyMWI1YTYxOGI3ZjQwZDM5YjczNTE2NzJlZjQwMzE2MzMzZjZiZmZkYmU4ZDY3Y2M1IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:42:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InR4cDJ3ai8wNW1nOFB6YnZGVjVXQ3c9PSIsInZhbHVlIjoiMlFPbGNKaExCYTVlc0h0eVVEdFZWUnJSNG5LNUJZZ3h1ZjE3L09aWmdsYUd5eWxFZHVZMjNJeDljckxHdlhqQ1A5d3dwbHhIeENmc3dkT2JvODJ2ckw5cmZDNWwyT1hvUWJCRTlWWHg3cjJxSHBibFp2WVB4UjFEamIwenk1ZFJ4cWNITWpSaTVKTUFFcFZmckMxUzVLaU40bGVQc0dWSHF1SDBYL2R6TUVyRFIwKyswcjE3V3ZhVDg2VC90QmVtTjh0VjZQSUNnN1NYTFpPNjhmNFhPVkRUMkNuajFSQ05WTUFsTWJoRUkwaGVudm04YmkwMkptNk54eFhoRUdCbHNRd1RtdGpQQlVxVTdBWUV4SmlrRTRBbGttanZmSjAzeUF5ak9kU3k0Y1pkdzc2SHVEVXpIeW00ajExUUMwQURQK1p5WTIrSEhpdnlTWEM0VVBBdzBCSmZyelhhTllQTmNXV2pucDlxaDFzMGYwamNBdFB4NWdEazhQdE1mRTJWYzFPc05PMXZ0NWlnU3BEdmU0bmphZnl5bFhCNDUyQkhrVHYwRHlxR3p5TENDd3hzbzlzaWNtaFJtejh1cVhuMlRQK3ZNM2YvR2dqVUMwN2VIS0ZJQnBOK09HZzZhb2YzcW1Wb25BUXZNeVdoYVpIMkVoeVZmZkgxc0VHdUg4b0QiLCJtYWMiOiIzM2NhMDZkMWZkNDUwOTQyOWQzMTNjNjg0OTk5ZDcyZTFiYTQ1Y2RmZDNlNjYxZjNlMWUxZDZhM2EyNjhkM2QyIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:42:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImVqMTBya25yTmplUHowdE1lb0NRS1E9PSIsInZhbHVlIjoidzk2OVVCSzZEQ2wvRnMyZ21ZVm1rUE5lWjdCNHpkbElnNElXa0lKZzhVNStIZHA4STVxWDhqUktCUlZkNjI0Sm9mSjV3TFU0cVlmTzB3WkdQMFJYalpmcWJSSStGQ3R3bVY4eEFWYkpkR0pjczRwTVhLNGpyUHVORnlMbFU4dmpNM1hYNU8rWENsenZBVWlWVzFKVWNrUVV2ZjE2M2Z6TG9iSTBLQ2FwSFl3V1JXc2R3MStsR3locU5VWGNTeWRaV1dUUDJwdXFiNnpsYm9rMlhsc2piZmpZTG90UEQ4MXJkMExYWmRzaXlVei9iNVgwRDVGNUpuV1FUcWxGL3FJVjBobFpOZHVLdVBZYlZ5S1F1QnVqWHdtMUcvaDgvVTMyOXNwYzlTSmVSTTg4L05SemdQNG1HMzRGMnY3R2NNSjNZcGRrdVo2bEQxb1Z1ZlFHWmM1K2F3Wkg0bjF1c1lPNWl0N1o3TXp0TExsMW1SYmNaNStEMytDclhkYVN6WVNjMWxTZTlqdUd5UEJhdGpENmQrdlQ5eTgvald2S0JQYjlHUjBHejZwdWp2d0FtNlFmdkRjNjFUaXlCMjVyTjllS0R2VTZWVWF1WmhPOHBaa2FidEdhWThFNFpTdlFzRU5tSDhmbFhVRExLRzljNkh1dHBHMTNBRGpETlVDRmx1enIiLCJtYWMiOiI1Y2Y0MThiZThiNDhhNjgyMWI1YTYxOGI3ZjQwZDM5YjczNTE2NzJlZjQwMzE2MzMzZjZiZmZkYmU4ZDY3Y2M1IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:42:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2147051111\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-810462896 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6InRWOGJ2eHRKM1Fpc2NXaGVRbGREekE9PSIsInZhbHVlIjoiZjN3YzRMbnc0K3FRanU5bW1yTnVhZz09IiwibWFjIjoiZjE4NTMxYjk4YWZhMzQxNWZlNDMwYjNhZmVlZDNjY2EwODMzNzQzMDk3MTU0NWQ5NTM3MTFmNmY3MjVkMDlmOCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-810462896\", {\"maxDepth\":0})</script>\n"}}