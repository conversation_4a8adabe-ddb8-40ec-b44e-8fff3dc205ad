{"__meta": {"id": "Xd9274a440a2a2cd9baaecb46cd1bc2cf", "datetime": "2025-06-08 15:29:45", "utime": **********.253129, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749396584.498625, "end": **********.253152, "duration": 0.7545268535614014, "duration_str": "755ms", "measures": [{"label": "Booting", "start": 1749396584.498625, "relative_start": 0, "end": **********.110401, "relative_end": **********.110401, "duration": 0.6117758750915527, "duration_str": "612ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.110417, "relative_start": 0.6117918491363525, "end": **********.253154, "relative_end": 2.1457672119140625e-06, "duration": 0.14273715019226074, "duration_str": "143ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46077968, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.193793, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": **********.203706, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x landingpage::layouts.buttons", "param_count": null, "params": [], "start": **********.238862, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.phplandingpage::layouts.buttons", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2FModules%2FLandingPage%2FResources%2Fviews%2Flayouts%2Fbuttons.blade.php&line=1", "ajax": false, "filename": "buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "landingpage::layouts.buttons"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.244062, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.017699999999999997, "accumulated_duration_str": "17.7ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.15877, "duration": 0.00379, "duration_str": "3.79ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 21.412}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'ty' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.167459, "duration": 0.00696, "duration_str": "6.96ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "ty", "start_percent": 21.412, "width_percent": 39.322}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.178737, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "ty", "start_percent": 60.734, "width_percent": 3.39}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.194656, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 64.124, "width_percent": 4.915}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.php", "line": 10}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.204958, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 69.04, "width_percent": 6.328}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4081}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4123}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.221368, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4081", "source": "app/Models/Utility.php:4081", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4081", "ajax": false, "filename": "Utility.php", "line": "4081"}, "connection": "ty", "start_percent": 75.367, "width_percent": 7.74}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4082}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4123}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.227818, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4082", "source": "app/Models/Utility.php:4082", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4082", "ajax": false, "filename": "Utility.php", "line": "4082"}, "connection": "ty", "start_percent": 83.107, "width_percent": 7.571}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.232436, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 90.678, "width_percent": 4.802}, {"sql": "select * from `landing_page_settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "Modules/LandingPage/Entities/LandingPageSetting.php", "file": "C:\\laragon\\www\\to\\newo\\3\\Modules\\LandingPage\\Entities\\LandingPageSetting.php", "line": 27}, {"index": 19, "namespace": "view", "name": "landingpage::layouts.buttons", "file": "C:\\laragon\\www\\to\\newo\\3\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.240461, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "LandingPageSetting.php:27", "source": "Modules/LandingPage/Entities/LandingPageSetting.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2FModules%2FLandingPage%2FEntities%2FLandingPageSetting.php&line=27", "ajax": false, "filename": "LandingPageSetting.php", "line": "27"}, "connection": "ty", "start_percent": 95.48, "width_percent": 4.52}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "wWkRFw77ieFixKV8JTSg4Jm7iJrLf6hsqY7pEPcp", "_previous": "array:1 [\n  \"url\" => \"http://localhost/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-1642079136 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1642079136\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-221522258 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-221522258\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-358950298 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-358950298\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1158112989 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwl%7C0%7C1982; _clsk=o6pnpa%7C1749396259916%7C1%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlVrYmZsemd0dUNyeWRHMCtCTDA5UWc9PSIsInZhbHVlIjoibVF0NHhQWVdzZy8rWDlHWDBaU2htT0MzTVFhdnROcGx2MFJGQ3JIa004cHNrQjBZR2o2Zms3OVROTVlaL2pGVk40UVc1enU2M05xVHhIN3FMUEIvVTFyemlWWExYaGJOSUkwUmZsTTBMRlM2a2RnN3IzQjFzUzl0MSt2UXZjWkRKeTRCRk5odXJmYTlFTlU2NG5BYm43K205dGJBUVVXSWdmRmpVWGVlOVRsZEg1QjVCYnY4SVFSQWQrUEQzWE1oZitzYVNRdWZKOGhydEczVk00RElWYjZSNkoxVXZwNDg1TXRGVE9kN28wSG5mS0pCYWsyajJMSEtkWnYxUk1qWGdtUGtDQlovY0tUZG9ZaSsvVHFPcVY4NjdBRDVwZE1NVzBzenZsS05RVkYxYUlrMjl1ZFlBazFrUFVBeVIyMlJyR2JQYjkxU1VhYWRCNkJWYlFFLzBqc0pSU2NnZHhueWFsdm9hMERUWVAwNVI2aTdldTQyZlZIME50K2svU1JXQXk2ZGl2VjJDbWEwMEY3YnZqVFlxei8wcndRWStLc1FPYU93WWZienNuNGtRN0xNdmxYWmR5K2ZtT2hjTzZ3c1RkZk50UnJPOUgranczRWxmOFFEZmRDemwzb3lDMGh1eXRuUjFQM0VTQU9rZk5kM3JvS3hCck9GYkl3VXZXalQiLCJtYWMiOiIyNDY3NDE0ZDBjNjdhMTUwOGQ0NmNhYThiNDE4MDRjNjE3YmE3M2I3YzY5Yjc3OGQ3Mjc1NjgwMzg0MGEzOWJmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjJHbDNtcGh5NlhRT0FRM1U5NS9YTlE9PSIsInZhbHVlIjoiNEZGbGNhZWFtbUxicEEzc3o1c2tFbU84ckZOQ0lqdHJISUF5V2ZKL2FSd1R2TDFaVUxhY0JiNFNZemhRUDcySHQ5ZzlCbmRaT2pIamh3V09hTFlTVkVmZXYvOGpvV2tLZ2g3QlI0QnI1NG1Ra0JPNVU3QkRpRVBxbmlsWHcxSjFZbmtzVUtpM1ZudkFmQ0ZoZHVlK1RyNTMyeVdDbkZuTEcwazEvVTFYSFdjcXhOaFA1clJ4VFVXOTRZSVVSc0ZOUUlocjI3SjlwL0ZBcVRWOUZSWVhhMXZZSy9lbkZ2OHR4YUJGQmpuak9QVitIekh3WHlQOWlkVXRKQUJ2dlhJQ3RaSjIxc1hnMllSQmZadGt6VTFlVlptQ1NTQVNtOXdwOVJyUmR4VEhhV3l3ZFN4ajJDckw0d2J6ZkNFdG5DbnNRVWtFYlNCVm1DVmhtZkllbitoV1JhWDZUa1dYVTQvaXN2RDFxbEV5OUZackhxR2FrK0RaR2NFNTJsL1ZCZ1p0S21IUlVIbjRhZUdVd3Y4RUh5V2FNN3ExUEVLMUZkVGxOZWJCYjZSUWlsdFJuaEd5UnZQSFk1MXo5UG55WjJ5eTlCRVZhVS9YdkxTN0hvaHBHblBLWjJNc2ZQWFlEZXo1RFgxaXV6SlFZK2hZRDdXcDFNWVEzMnV4b0IwUERuS2siLCJtYWMiOiI2MWM2NGUzOWZiYjEwM2M1ZDRmY2Q3MTRmYWIxZWM4ZjE5YzQ0NDNjZGUwNjliMmVhMjE5NzFkNDY4NjZjZGU2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1158112989\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1493231641 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wWkRFw77ieFixKV8JTSg4Jm7iJrLf6hsqY7pEPcp</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9Hl42KK3LIJZIc1GjeFvdAvqwA62mqMM6ouBBVUW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1493231641\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1491622735 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:29:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkkwaXljNjk4Y29BZG5ZK0pvYk1vZEE9PSIsInZhbHVlIjoiOGlKRXZmTSttN0FzbExFeHgvT0ZvclpXa3BlVWpHekZJRG1TOEN3N1dySVAxd1J4UVFXQjBHZlJGMmhJMnpURE5NUi9hdnZ5ZjZaVWg0Y3M4L25zRUVUYzcyVkIzL2FnM1hXd0ZCa3pHU1Z3alpoVENnN2pRd2RrYTgrNTFqOW5Cc0VRNkdhekZ1cFM5Y2FZRkVYUVcwRzVmMGk5V3o5VjJYTVlVOUdPUnROdVRLMklBSTNLWDY5TkdwTlB2bVhiekNEeEpTZ1BuZWlIaEY4OVlaZGVjZTZyQ21iRVBHMENYcDVnWlRwMkpjK2w2WE0vZkR1eEpCZnhPU1VJejhoVGdwalFUS0NWRDJiaEtIMHRSMUJSOWkvTHdtK2FiR2JBVDZERThDYmhES0tZLzIrRkE2T3BFWjQ5aFRmdmd4empSdWJ4RzBPamVhYWFMaGJmQjNoRVY0c3hDM0wxczM3THNscENwcG93MEV1OUxLakV5NTByU3VqNFd6aHpuRW96Y1J0STUwYlhaRkZKdTlYUTlxT3ZXa3hhSDByNzFORTA3cFRUOUtIYjVjWVhlTlVLTTdOWWJpSXRmSC8zZTVSbE5lam9lNXdlTTJQaDBZblg3UnB6VzBvSDUzSTlIaFFycHpyaFBXUlVIdUp3eFFNSmF3U2pFWFFjSGhWajZ2bzUiLCJtYWMiOiIxN2Y1MDVjZjM4Njc1YzFiMmYxMDVhM2FkNDUyYjE0MzFmZGJiMDMzOTU5YzY5NjQ3YTUzMTM4MTEyYzI0NmNlIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:29:45 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkdUUFArTnRqdGtueCtmUU1UdkRkS2c9PSIsInZhbHVlIjoiYmFQUkFJb0RBSC9FN1cyOXFYTU5aSzVMeWZJVjVSWFpBeTZDMER3bUN3STJETjRYdTBUSmY1UERLRVdVa01GRWtMMmxpdDBZY1Bya1FjeElUYnIydE5TRXAydFV1am5OZEcvelNPT3pPRUVnWmVYNFZocGVudkJNV2VCcVdmTWh4bjgvOFRETlNUTUE5S2IvSjh0N0JxbXBhWjhRSnFmY001VGVaK3JxZ1F6cndYejZiWURUSGROMGVRaXBEeUJvV054SmNYUHpMbUtYK2s3L0I0c3MzQTlEaVNuMG9JZVRwUHZtMzBDQm80ZDJsTU5sK1E1TUY3UEgvc2NPZFUvUHZYZFNlbElxMjhSc0tHdmVXUGc0b3hOYnM2VWliRGJ1N2VxUkg0RCtKK25Gdkkra3hpMlVsY0JLbnFKQ2JaV1E3U2pIeE1ad2ZYU1IxcnhKSzV5dzB2c1JzcHFaOXdMRXJHM1NUK0lUWmxUbVhPZkIrdHpmREgwOW5BOU5hMzBrT3lvZlA4M1Jla3UwYVpLdklaeGltWjlTMzlUb0dUdzRwbEdyMlhoT0NqbVhNeTNtTnpZWlpYMGVneTdtOVVxT1Q0aGo1c2lPajIyZGhtQUNEaXdmZXhjQW84clpjWll4Q20wMEZkTFY4S0xKc0tidG9Fc3Z3V2ZLdUFQZFkvOUEiLCJtYWMiOiJlMDZjYWQxZDc0N2JlYTAzMTgwY2QzNTBlNWQyNGJkMmQ4MzA3MmNkZTMwODQ0NjhjY2E4NjVmNTE3NzcyZTI1IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:29:45 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkkwaXljNjk4Y29BZG5ZK0pvYk1vZEE9PSIsInZhbHVlIjoiOGlKRXZmTSttN0FzbExFeHgvT0ZvclpXa3BlVWpHekZJRG1TOEN3N1dySVAxd1J4UVFXQjBHZlJGMmhJMnpURE5NUi9hdnZ5ZjZaVWg0Y3M4L25zRUVUYzcyVkIzL2FnM1hXd0ZCa3pHU1Z3alpoVENnN2pRd2RrYTgrNTFqOW5Cc0VRNkdhekZ1cFM5Y2FZRkVYUVcwRzVmMGk5V3o5VjJYTVlVOUdPUnROdVRLMklBSTNLWDY5TkdwTlB2bVhiekNEeEpTZ1BuZWlIaEY4OVlaZGVjZTZyQ21iRVBHMENYcDVnWlRwMkpjK2w2WE0vZkR1eEpCZnhPU1VJejhoVGdwalFUS0NWRDJiaEtIMHRSMUJSOWkvTHdtK2FiR2JBVDZERThDYmhES0tZLzIrRkE2T3BFWjQ5aFRmdmd4empSdWJ4RzBPamVhYWFMaGJmQjNoRVY0c3hDM0wxczM3THNscENwcG93MEV1OUxLakV5NTByU3VqNFd6aHpuRW96Y1J0STUwYlhaRkZKdTlYUTlxT3ZXa3hhSDByNzFORTA3cFRUOUtIYjVjWVhlTlVLTTdOWWJpSXRmSC8zZTVSbE5lam9lNXdlTTJQaDBZblg3UnB6VzBvSDUzSTlIaFFycHpyaFBXUlVIdUp3eFFNSmF3U2pFWFFjSGhWajZ2bzUiLCJtYWMiOiIxN2Y1MDVjZjM4Njc1YzFiMmYxMDVhM2FkNDUyYjE0MzFmZGJiMDMzOTU5YzY5NjQ3YTUzMTM4MTEyYzI0NmNlIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:29:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkdUUFArTnRqdGtueCtmUU1UdkRkS2c9PSIsInZhbHVlIjoiYmFQUkFJb0RBSC9FN1cyOXFYTU5aSzVMeWZJVjVSWFpBeTZDMER3bUN3STJETjRYdTBUSmY1UERLRVdVa01GRWtMMmxpdDBZY1Bya1FjeElUYnIydE5TRXAydFV1am5OZEcvelNPT3pPRUVnWmVYNFZocGVudkJNV2VCcVdmTWh4bjgvOFRETlNUTUE5S2IvSjh0N0JxbXBhWjhRSnFmY001VGVaK3JxZ1F6cndYejZiWURUSGROMGVRaXBEeUJvV054SmNYUHpMbUtYK2s3L0I0c3MzQTlEaVNuMG9JZVRwUHZtMzBDQm80ZDJsTU5sK1E1TUY3UEgvc2NPZFUvUHZYZFNlbElxMjhSc0tHdmVXUGc0b3hOYnM2VWliRGJ1N2VxUkg0RCtKK25Gdkkra3hpMlVsY0JLbnFKQ2JaV1E3U2pIeE1ad2ZYU1IxcnhKSzV5dzB2c1JzcHFaOXdMRXJHM1NUK0lUWmxUbVhPZkIrdHpmREgwOW5BOU5hMzBrT3lvZlA4M1Jla3UwYVpLdklaeGltWjlTMzlUb0dUdzRwbEdyMlhoT0NqbVhNeTNtTnpZWlpYMGVneTdtOVVxT1Q0aGo1c2lPajIyZGhtQUNEaXdmZXhjQW84clpjWll4Q20wMEZkTFY4S0xKc0tidG9Fc3Z3V2ZLdUFQZFkvOUEiLCJtYWMiOiJlMDZjYWQxZDc0N2JlYTAzMTgwY2QzNTBlNWQyNGJkMmQ4MzA3MmNkZTMwODQ0NjhjY2E4NjVmNTE3NzcyZTI1IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:29:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1491622735\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1209706525 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wWkRFw77ieFixKV8JTSg4Jm7iJrLf6hsqY7pEPcp</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1209706525\", {\"maxDepth\":0})</script>\n"}}