{"__meta": {"id": "X24d0ad21c46ee02c6902f2c137bf1ccc", "datetime": "2025-06-08 16:17:53", "utime": **********.908804, "method": "POST", "uri": "/pos-delevery-pay", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.229035, "end": **********.908825, "duration": 0.6797900199890137, "duration_str": "680ms", "measures": [{"label": "Booting", "start": **********.229035, "relative_start": 0, "end": **********.69491, "relative_end": **********.69491, "duration": 0.46587514877319336, "duration_str": "466ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.694923, "relative_start": 0.46588802337646484, "end": **********.908827, "relative_end": 2.1457672119140625e-06, "duration": 0.21390414237976074, "duration_str": "214ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52332648, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST pos-delevery-pay", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\FinancialRecordController@finacialdeleveryBill", "namespace": null, "prefix": "", "where": [], "as": "pos.delevery.bill", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=348\" onclick=\"\">app/Http/Controllers/FinancialRecordController.php:348-415</a>"}, "queries": {"nb_statements": 16, "nb_failed_statements": 0, "accumulated_duration": 0.04743999999999999, "accumulated_duration_str": "47.44ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.736672, "duration": 0.02089, "duration_str": "20.89ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 44.035}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.771135, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 44.035, "width_percent": 1.834}, {"sql": "select count(*) as aggregate from `pos` where `id` = '41'", "type": "query", "params": [], "bindings": ["41"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 938}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 909}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}], "start": **********.795331, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "ty", "start_percent": 45.868, "width_percent": 1.497}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 17 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["17", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.829041, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 47.365, "width_percent": 1.539}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (17) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.832487, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 48.904, "width_percent": 2.213}, {"sql": "select * from `shifts` where `closed_at` is null and `warehouse_id` = 8 and `shifts`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Services\\FinancialRecordService.php", "line": 167}, {"index": 17, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Services\\FinancialRecordService.php", "line": 59}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 361}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.842575, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "FinancialRecordService.php:167", "source": "app/Services/FinancialRecordService.php:167", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FServices%2FFinancialRecordService.php&line=167", "ajax": false, "filename": "FinancialRecordService.php", "line": "167"}, "connection": "ty", "start_percent": 51.117, "width_percent": 2.087}, {"sql": "select * from `financial_records` where `shift_id` = 2 and `financial_records`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Services\\FinancialRecordService.php", "line": 173}, {"index": 17, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Services\\FinancialRecordService.php", "line": 59}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 361}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.846752, "duration": 0.00225, "duration_str": "2.25ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordService.php:173", "source": "app/Services/FinancialRecordService.php:173", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FServices%2FFinancialRecordService.php&line=173", "ajax": false, "filename": "FinancialRecordService.php", "line": "173"}, "connection": "ty", "start_percent": 53.204, "width_percent": 4.743}, {"sql": "select * from `financial_records` where (`id` = 2) and `financial_records`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Services\\FinancialRecordService.php", "line": 184}, {"index": 22, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Services\\FinancialRecordService.php", "line": 59}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 361}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.852474, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "FinancialRecordService.php:184", "source": "app/Services/FinancialRecordService.php:184", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FServices%2FFinancialRecordService.php&line=184", "ajax": false, "filename": "FinancialRecordService.php", "line": "184"}, "connection": "ty", "start_percent": 57.947, "width_percent": 1.497}, {"sql": "update `financial_records` set `delivery_cash` = 22, `total_cash` = 1298, `deficit` = 22, `financial_records`.`updated_at` = '2025-06-08 16:17:53' where `id` = 2", "type": "query", "params": [], "bindings": ["22", "1298", "22", "2025-06-08 16:17:53", "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Services\\FinancialRecordService.php", "line": 184}, {"index": 21, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Services\\FinancialRecordService.php", "line": 59}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 361}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.855946, "duration": 0.00296, "duration_str": "2.96ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordService.php:184", "source": "app/Services/FinancialRecordService.php:184", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FServices%2FFinancialRecordService.php&line=184", "ajax": false, "filename": "FinancialRecordService.php", "line": "184"}, "connection": "ty", "start_percent": 59.444, "width_percent": 6.239}, {"sql": "select * from `shifts` where `closed_at` is null and `warehouse_id` = 8 and `shifts`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Services\\FinancialRecordService.php", "line": 230}, {"index": 17, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Services\\FinancialRecordService.php", "line": 65}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 361}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.862536, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "FinancialRecordService.php:230", "source": "app/Services/FinancialRecordService.php:230", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FServices%2FFinancialRecordService.php&line=230", "ajax": false, "filename": "FinancialRecordService.php", "line": "230"}, "connection": "ty", "start_percent": 65.683, "width_percent": 1.918}, {"sql": "select * from `delivery_financial_records` where `shift_id` = 2 and `created_by` = 17 and `delivery_financial_records`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2", "17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Services\\FinancialRecordService.php", "line": 237}, {"index": 17, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Services\\FinancialRecordService.php", "line": 65}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 361}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.867854, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordService.php:237", "source": "app/Services/FinancialRecordService.php:237", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FServices%2FFinancialRecordService.php&line=237", "ajax": false, "filename": "FinancialRecordService.php", "line": "237"}, "connection": "ty", "start_percent": 67.601, "width_percent": 2.846}, {"sql": "select * from `delivery_financial_records` where (`id` = 1) and `delivery_financial_records`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Services\\FinancialRecordService.php", "line": 249}, {"index": 22, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Services\\FinancialRecordService.php", "line": 65}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 361}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.872081, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordService.php:249", "source": "app/Services/FinancialRecordService.php:249", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FServices%2FFinancialRecordService.php&line=249", "ajax": false, "filename": "FinancialRecordService.php", "line": "249"}, "connection": "ty", "start_percent": 70.447, "width_percent": 2.171}, {"sql": "update `delivery_financial_records` set `delivery_cash` = 22, `delivery_financial_records`.`updated_at` = '2025-06-08 16:17:53' where `id` = 1", "type": "query", "params": [], "bindings": ["22", "2025-06-08 16:17:53", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Services\\FinancialRecordService.php", "line": 249}, {"index": 21, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Services\\FinancialRecordService.php", "line": 65}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 361}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.8759122, "duration": 0.0038, "duration_str": "3.8ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordService.php:249", "source": "app/Services/FinancialRecordService.php:249", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FServices%2FFinancialRecordService.php&line=249", "ajax": false, "filename": "FinancialRecordService.php", "line": "249"}, "connection": "ty", "start_percent": 72.618, "width_percent": 8.01}, {"sql": "update `pos` set `is_payment_set` = 1, `pos`.`updated_at` = '2025-06-08 16:17:53' where `id` = '41'", "type": "query", "params": [], "bindings": ["1", "2025-06-08 16:17:53", "41"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Services\\FinancialRecordService.php", "line": 73}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 361}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.883565, "duration": 0.0045899999999999995, "duration_str": "4.59ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordService.php:73", "source": "app/Services/FinancialRecordService.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FServices%2FFinancialRecordService.php&line=73", "ajax": false, "filename": "FinancialRecordService.php", "line": "73"}, "connection": "ty", "start_percent": 80.628, "width_percent": 9.675}, {"sql": "select * from `shifts` where `closed_at` is null and `warehouse_id` = 8 and `shifts`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/FinancialTransactionService.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Services\\FinancialTransactionService.php", "line": 21}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 395}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.891428, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "FinancialTransactionService.php:21", "source": "app/Services/FinancialTransactionService.php:21", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FServices%2FFinancialTransactionService.php&line=21", "ajax": false, "filename": "FinancialTransactionService.php", "line": "21"}, "connection": "ty", "start_percent": 90.304, "width_percent": 2.508}, {"sql": "insert into `financial_transactions` (`shift_id`, `transaction_type`, `cash_amount`, `created_by`, `payment_method`, `updated_at`, `created_at`) values (2, 'sale', '22.00', 17, 'cash', '2025-06-08 16:17:53', '2025-06-08 16:17:53')", "type": "query", "params": [], "bindings": ["2", "sale", "22.00", "17", "cash", "2025-06-08 16:17:53", "2025-06-08 16:17:53"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/FinancialTransactionService.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Services\\FinancialTransactionService.php", "line": 27}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 395}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.896339, "duration": 0.0034100000000000003, "duration_str": "3.41ms", "memory": 0, "memory_str": null, "filename": "FinancialTransactionService.php:27", "source": "app/Services/FinancialTransactionService.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FServices%2FFinancialTransactionService.php&line=27", "ajax": false, "filename": "FinancialTransactionService.php", "line": "27"}, "connection": "ty", "start_percent": 92.812, "width_percent": 7.188}]}, "models": {"data": {"App\\Models\\Shift": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FShift.php&line=1", "ajax": false, "filename": "Shift.php", "line": "?"}}, "App\\Models\\FinancialRecord": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FFinancialRecord.php&line=1", "ajax": false, "filename": "FinancialRecord.php", "line": "?"}}, "App\\Models\\DeliveryFinancialRecord": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FDeliveryFinancialRecord.php&line=1", "ajax": false, "filename": "DeliveryFinancialRecord.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 9, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => manage delevery, result => true, user => 17, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1075672840 data-indent-pad=\"  \"><span class=sf-dump-note>manage delevery</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage delevery</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1075672840\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.838446, "xdebug_link": null}]}, "session": {"_token": "SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/eyJpdiI6IkFRYWh6YzFtM3ZKUitTR0xaMzNPL3c9PSIsInZhbHVlIjoiTTQ3YTVSVCs2WjlicXFPditOMVY3dz09IiwibWFjIjoiOWZhOWU0ZTY4ZmJmMGYwM2JhN2E5OWZiNDNlMzZjYjg2MTlkZjU4Yjk2NWEwNmE2MDVmMjkwMmY4NGY0ZmRiMSIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17", "success": "Delivery has been paid successfully", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/pos-delevery-pay", "status_code": "<pre class=sf-dump id=sf-dump-1904468425 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1904468425\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1321969796 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1321969796\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1657898483 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>payment_type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cash</span>\"\n  \"<span class=sf-dump-key>pos_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">41</span>\"\n  \"<span class=sf-dump-key>select_payment</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cash</span>\"\n  \"<span class=sf-dump-key>total_price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">22.00</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1657898483\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1045499404 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">113</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6IkFRYWh6YzFtM3ZKUitTR0xaMzNPL3c9PSIsInZhbHVlIjoiTTQ3YTVSVCs2WjlicXFPditOMVY3dz09IiwibWFjIjoiOWZhOWU0ZTY4ZmJmMGYwM2JhN2E5OWZiNDNlMzZjYjg2MTlkZjU4Yjk2NWEwNmE2MDVmMjkwMmY4NGY0ZmRiMSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwl%7C0%7C1982; _clsk=1y4k55%7C1749399469579%7C2%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InhobG9iRXo3bGZnUVp6UDRHSzYvN1E9PSIsInZhbHVlIjoidzErcDFKMDNwc2ZUak9EbVlMRGVvZDhLVmN4UlRmSjdnNmdKM2FWanRrdExtcVJWVEVIM3VkSXJ5UWZEZjFmcTk3ajZwR3R4ajNhMllrSURuSUlPb2FrYnUyN0drM3BkcTl4U3huK2s5Y2R4cXNRQ0dWZGNaUHhlbWkvYXM3Qk84K0FZejdyL3dVSnZ2Q2VvVHBzckt2RUxteFptVmhZUm43dHMvNi94dnQ0Wks1TFJ5ZE5FeGZoVmJSTXE1S2ZhWjVYNGU0SWVLOE54alVyUFRvNWFYSkJJWkNUVjVYSUk3ckFKcVFDQzEzY1IrZ3hkZlBWTFMrNy9qa3ZSMFZveDJ3VEFGNVp5ZGVGaTBISmdacVJtSm1oeEVmMlFPZFVxTDNGZEdwSVFCZjM3eXh3b3Z6cVVPM1RwTXV5M0JwV1lOQjFKU25JRzUyQUZNK1IxWXdPL0FEdHBZQ24rUEZDVm16YjI0NWJ4ZE9xWlVwWUl0TnJmQXRoTHpOL3BWZkNjRTFraUhnQW5wa3VBR2dWTVRqYi9vVFRYblFteEI0MCtrV05CWHRza0Jxb2hGczBDV05sVTk2VnVvUU9sYU45by9oRnNXVzZsNzRiemtxVGNybG9JMlA3Njg4amlrQzBHVm5ONTJUL3Fsb3g2eldCRWVNMll3YjFiWTI3OHV3cDgiLCJtYWMiOiJlNTQ4MjVmZWIzNzU0MmI3NDE0NWMzY2I2MWNiMzVmNzRkMmEwMDgyYTE5NTliOGRmYmZiYjZlODY2NjRmNWE3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InI1YXZJRGRNODNWTi9DOTg3WEsxY0E9PSIsInZhbHVlIjoicm1XSTJtSjUvRmJwSjBEK2R2WTBVRWtWRTZmVVd3R3V5ZVVWNmc2alZpeUxlVmRlWWxwb3hxakx3dWdTMGFMUWVvcFZGWmpsVUd5d2FRR0gwaWhCcnVOdmNCZ3UvNjJJOXpHamQ3a3MwSGJaY1lMMzVlM1FxbWV3SnI5cVZvTS9QVWVRcHN3ZzRJMDlWWDRpNmhGeWo5MjVRUUdiYnNQdENWeHNTdEZBelRSQWhGSkh4aUh1RUgySXdPckF3ZUswQ3RNdW4zc2hRM1JvQTFNMkYzd0pyd1YzUXJkMVhtd1diVkk3Z2xEcmluUjhoUjhDUzdKbGYyMytsRElCQWNsbS9pYm9Vd0tmMjZ1eTN4MmJhOUFtbXNOcFZFaStnZS8rL01kc0JlOTA4bkNGRlYwMkpmME0xRStJSVlFVmlWMEVCYkM4Z3pDb1Y0ODUzU0VwdGVrSVhPaUFxcW5VMmJnOUFuUU1iY1plUk05R2VJeGI4cWFraWFSdzNqYXErczVHTklQUXhwaE5SaERMY01yMHdsVWNoSUFzbFUzMUFBSjNjVGFnWE84ZXorYVRoOVc4aHQwS3FUekpIWkVKbCsxOGdxT24rVkxJQ0lTd0JFeHVBaWEzaG85bGhHMEpUaHJOb3ZFQjFXaldCYmhUdWgrQWlkSG8zdy9vbWtiYU8vZm4iLCJtYWMiOiIzZDJmNmUzN2VjNTBmNDc1MjRmNzQ3ZjUzMjdiMjYzODRhNzk1Njk4YzYyNTNmZTFjN2ZlYWYyNTIxMDY4ZGVlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1045499404\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">COI6IIrAbucReABHBN7o3ZaIzRbwBCI1gfwrcfCo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1862617516 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 16:17:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6IkFRYWh6YzFtM3ZKUitTR0xaMzNPL3c9PSIsInZhbHVlIjoiTTQ3YTVSVCs2WjlicXFPditOMVY3dz09IiwibWFjIjoiOWZhOWU0ZTY4ZmJmMGYwM2JhN2E5OWZiNDNlMzZjYjg2MTlkZjU4Yjk2NWEwNmE2MDVmMjkwMmY4NGY0ZmRiMSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InlIb1duRmtDWmtqVjZyZzI1WnpZakE9PSIsInZhbHVlIjoickdTNkoyb2xuL1RhbU5OSkVVZHNxbnlrejZIb0hTamNQSHE3TENJWXB0RUFTTVRCUEhDVGt3TzR3MjhMRVdYc2xROVl1U3hEQVhvb2JMSkJpNUQvYVd6R0RvMUkxMW9tbTRyUUNZd3hZZk9YbjgyckM1UWlVTW1oTFpMRmF3dWpzL3NKcWlzYkhyVVNTUHVwL0xlSVB0elpwNFVDa1R2OFlPOGtyQU12K2h2R2JKUGdCb01MSWhvYTZUYTFINnFBc29YOSt3eHNiVDcySFZEMmV4VHAzbG5mNS8wRDFCajBUUlBvaEdBbmRQK2N4bDFla3RuU09xUnR4R0VGUm5YNmxWQUVYZUFhYTVYYkNaSHNGQm5kVDA1eG5FcFFRRTlQdXZzcFBPVXRpT0tCYTNKdURYblMzMHRzSFVCeWdLWHAzSU9SUVZyeHJKalF3bUFCWjRkOTJKR3NOb0xHdWxaM2cwbUMxUXlLZlVINzVad3JPb0svVkNOc0ZVUGlEZmVBbnZVL25VN0ZZWkh3cnM5a2hDUEtmaDNaeVI0WTJBM2YvNk56SjA1bG80c05uL3ltR1NyNUltSVE4U0s1Nm1XWVdhL0NCNEJuWkMzZWNsQnVtdGdEdlFyTjQwYnhmN3EwZW83YkhEaCtHYlM2KzY1UUpIOVZGSEJkUkhBRmNNL0wiLCJtYWMiOiIxOGRiNDBmNmRhZWJkOWVlYzAzYmZiYmU3MTYwYzY4YWQwNDhlM2QxMDM0ZjQ3ZWQzOTVkY2RhZDgzYjUxZDFjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 18:17:53 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InZxUHRxTCtYd01YUEZtaXZKQjdGZWc9PSIsInZhbHVlIjoicEpCK0xKcWcwbG41NDJYb1QzMkUvMTBJOTVqVjlzZzhWMXB4dmcyMlZtZ0w3akdlaFRZRmRGTnB2Sk5TMmpqbXBJb1RyZFgzbUhkNW9tQ2NnTU5zTGYxUXpzSnIyRGMvVmtpSW5wQ3BMakgxaFRHS1dWOTdLWnNsMjZHdWZRb0tsV2d2NTExeG5WM2hqR3lMMlFSTjJoV3F6YmM2MTkwOVBuMGRWbGwyeHlyOFBxWFhiQ1lVcmRXL3hBMDJQVllhOFJOcjdoeERxWFhLbXFzQktobyt6ZWlGWVlyUEp1ZTMwMm5SVVorcWJNNDRzdU92TVFnQyswbFl6em4zYk9qY1k0eTBPTUhRMDJjYVVuS094c0ZtY2F0Sk5IemxTaUtMUHFtZ1FWYUNlQnhFemN5RHFjaEE2QmUrNkh5eTgxZXpPajJ1V3gyMHVSZUxIVkYyK0l2eEpyU2V5WjlLVWJBenZHa20zVVRCNVVMQ3JKTHN4dldKWDdXR3hKMTJoNWdTcWNnU21DQ2kyQjJHeXhNR1pJazE4ZzJRRGdSSzJKZTVzMlNFY25ZWmdYNCt4dFhUQmVMK1ZyV3ZMZDk2ZVFNVXk0WVlrdWRiRFF6cWFISS90SFNHUTd5TlpiRjQ1czBoUDhVcVhKbTI4WEhScnUxanc1dDhpRTkzeUxvQjNJUXoiLCJtYWMiOiI5ZjM2ZWIwZWRmOTAyNWExNWZkMzBmMWY3ZjBiNDc2NDlkN2QyOTBlYWYzYjA2Nzc1NzljNTRlMzk2YTBmMjQ4IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 18:17:53 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InlIb1duRmtDWmtqVjZyZzI1WnpZakE9PSIsInZhbHVlIjoickdTNkoyb2xuL1RhbU5OSkVVZHNxbnlrejZIb0hTamNQSHE3TENJWXB0RUFTTVRCUEhDVGt3TzR3MjhMRVdYc2xROVl1U3hEQVhvb2JMSkJpNUQvYVd6R0RvMUkxMW9tbTRyUUNZd3hZZk9YbjgyckM1UWlVTW1oTFpMRmF3dWpzL3NKcWlzYkhyVVNTUHVwL0xlSVB0elpwNFVDa1R2OFlPOGtyQU12K2h2R2JKUGdCb01MSWhvYTZUYTFINnFBc29YOSt3eHNiVDcySFZEMmV4VHAzbG5mNS8wRDFCajBUUlBvaEdBbmRQK2N4bDFla3RuU09xUnR4R0VGUm5YNmxWQUVYZUFhYTVYYkNaSHNGQm5kVDA1eG5FcFFRRTlQdXZzcFBPVXRpT0tCYTNKdURYblMzMHRzSFVCeWdLWHAzSU9SUVZyeHJKalF3bUFCWjRkOTJKR3NOb0xHdWxaM2cwbUMxUXlLZlVINzVad3JPb0svVkNOc0ZVUGlEZmVBbnZVL25VN0ZZWkh3cnM5a2hDUEtmaDNaeVI0WTJBM2YvNk56SjA1bG80c05uL3ltR1NyNUltSVE4U0s1Nm1XWVdhL0NCNEJuWkMzZWNsQnVtdGdEdlFyTjQwYnhmN3EwZW83YkhEaCtHYlM2KzY1UUpIOVZGSEJkUkhBRmNNL0wiLCJtYWMiOiIxOGRiNDBmNmRhZWJkOWVlYzAzYmZiYmU3MTYwYzY4YWQwNDhlM2QxMDM0ZjQ3ZWQzOTVkY2RhZDgzYjUxZDFjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 18:17:53 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InZxUHRxTCtYd01YUEZtaXZKQjdGZWc9PSIsInZhbHVlIjoicEpCK0xKcWcwbG41NDJYb1QzMkUvMTBJOTVqVjlzZzhWMXB4dmcyMlZtZ0w3akdlaFRZRmRGTnB2Sk5TMmpqbXBJb1RyZFgzbUhkNW9tQ2NnTU5zTGYxUXpzSnIyRGMvVmtpSW5wQ3BMakgxaFRHS1dWOTdLWnNsMjZHdWZRb0tsV2d2NTExeG5WM2hqR3lMMlFSTjJoV3F6YmM2MTkwOVBuMGRWbGwyeHlyOFBxWFhiQ1lVcmRXL3hBMDJQVllhOFJOcjdoeERxWFhLbXFzQktobyt6ZWlGWVlyUEp1ZTMwMm5SVVorcWJNNDRzdU92TVFnQyswbFl6em4zYk9qY1k0eTBPTUhRMDJjYVVuS094c0ZtY2F0Sk5IemxTaUtMUHFtZ1FWYUNlQnhFemN5RHFjaEE2QmUrNkh5eTgxZXpPajJ1V3gyMHVSZUxIVkYyK0l2eEpyU2V5WjlLVWJBenZHa20zVVRCNVVMQ3JKTHN4dldKWDdXR3hKMTJoNWdTcWNnU21DQ2kyQjJHeXhNR1pJazE4ZzJRRGdSSzJKZTVzMlNFY25ZWmdYNCt4dFhUQmVMK1ZyV3ZMZDk2ZVFNVXk0WVlrdWRiRFF6cWFISS90SFNHUTd5TlpiRjQ1czBoUDhVcVhKbTI4WEhScnUxanc1dDhpRTkzeUxvQjNJUXoiLCJtYWMiOiI5ZjM2ZWIwZWRmOTAyNWExNWZkMzBmMWY3ZjBiNDc2NDlkN2QyOTBlYWYzYjA2Nzc1NzljNTRlMzk2YTBmMjQ4IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 18:17:53 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1862617516\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-791830503 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6IkFRYWh6YzFtM3ZKUitTR0xaMzNPL3c9PSIsInZhbHVlIjoiTTQ3YTVSVCs2WjlicXFPditOMVY3dz09IiwibWFjIjoiOWZhOWU0ZTY4ZmJmMGYwM2JhN2E5OWZiNDNlMzZjYjg2MTlkZjU4Yjk2NWEwNmE2MDVmMjkwMmY4NGY0ZmRiMSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Delivery has been paid successfully</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-791830503\", {\"maxDepth\":0})</script>\n"}}