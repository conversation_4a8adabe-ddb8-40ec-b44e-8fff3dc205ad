# [YooKassa API SDK](../home.md)

# Class: \YooKassa\Common\Exceptions\EmptyPropertyValueException
### Namespace: [\YooKassa\Common\Exceptions](../namespaces/yookassa-common-exceptions.md)
---

---
### Constants
* No constants found

---
### Methods
| Visibility | Name | Flag | Summary |
| ----------:| ---- | ---- | ------- |
| public | [__construct()](../classes/YooKassa-Common-Exceptions-InvalidPropertyException.md#method___construct) |  | InvalidValueException constructor. |
| public | [getProperty()](../classes/YooKassa-Common-Exceptions-InvalidPropertyException.md#method_getProperty) |  |  |

---
### Details
* File: [lib/Common/Exceptions/EmptyPropertyValueException.php](../../lib/Common/Exceptions/EmptyPropertyValueException.php)
* Package: Default
* Class Hierarchy:  
  * [\InvalidArgumentException](\InvalidArgumentException)
  * [\YooKassa\Common\Exceptions\InvalidPropertyException](../classes/YooKassa-Common-Exceptions-InvalidPropertyException.md)
  * \YooKassa\Common\Exceptions\EmptyPropertyValueException

---
## Methods
<a name="method___construct" class="anchor"></a>
#### public __construct() : mixed

```php
public __construct(string $message = &#039;&#039;, int $code, string $property = &#039;&#039;) : mixed
```

**Summary**

InvalidValueException constructor.

**Details:**
* Inherited From: [\YooKassa\Common\Exceptions\InvalidPropertyException](../classes/YooKassa-Common-Exceptions-InvalidPropertyException.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string</code> | message  |  |
| <code lang="php">int</code> | code  |  |
| <code lang="php">string</code> | property  |  |

**Returns:** mixed - 


<a name="method_getProperty" class="anchor"></a>
#### public getProperty() : string

```php
public getProperty() : string
```

**Details:**
* Inherited From: [\YooKassa\Common\Exceptions\InvalidPropertyException](../classes/YooKassa-Common-Exceptions-InvalidPropertyException.md)

**Returns:** string - 



---

### Top Namespaces

* [\YooKassa](../namespaces/yookassa.md)

---

### Reports
* [Errors - 0](../reports/errors.md)
* [Markers - 0](../reports/markers.md)
* [Deprecated - 25](../reports/deprecated.md)

---

This document was automatically generated from source code comments on 2024-07-02 using [phpDocumentor](http://www.phpdoc.org/)

&copy; 2024 YooMoney