{"__meta": {"id": "X428075e66abe3dd161a0aa9df229cbac", "datetime": "2025-06-08 16:25:21", "utime": **********.728659, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.167534, "end": **********.728679, "duration": 0.5611448287963867, "duration_str": "561ms", "measures": [{"label": "Booting", "start": **********.167534, "relative_start": 0, "end": **********.65015, "relative_end": **********.65015, "duration": 0.48261594772338867, "duration_str": "483ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.650161, "relative_start": 0.48262691497802734, "end": **********.728681, "relative_end": 2.1457672119140625e-06, "duration": 0.07852005958557129, "duration_str": "78.52ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45691304, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.014639999999999999, "accumulated_duration_str": "14.64ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.684479, "duration": 0.013, "duration_str": "13ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 88.798}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.708693, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 88.798, "width_percent": 6.216}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.717022, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 95.014, "width_percent": 4.986}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-272345130 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-272345130\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-645535375 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-645535375\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2058576592 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2058576592\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-492642389 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=u1db3a%7C1749399903410%7C5%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjNKL0RyL2FWVFJKS2c3bVN5M3BUbGc9PSIsInZhbHVlIjoiMXpmTXI1aVJJYWpWZWxJbGx0TzV5MHgwS0VhZzRsNHZnY1Vlc3hoZUQ3NExMcG0yaTE2NE9qSkdHM2hLaDVCbjVWNE0xaXdhejlkbGRqUVpKcFhDN0VvNW5KZ1FISHh4T1Z6aVdxR1k4Y3c1MXpYdTRLend4eW5XSFY5ZU9XRlVDTCs2ZUlieEdISnROb3FKYzBSSEczL0ZWZ0RTejdORkJLTHJmV1BnTVRnNjVTZkIzY2RjaElYYzcyamd1RkNLT2prNnZrckNkRkV2RDRBNllCVWVXd2gvMnNnbnFma2xjRGdQbGNMNjZRc2N4MDdyK3BoTGNyOWhJSnRONnM5OXRGek1rZGZ1VnlTKzNBM1JEU0NJcHl6Tkd0bWxlUnFJYUJzZlczLzJQTzVza05Rc0xhU2JPZTl1TGJOL21jbW1xYXhRRUJwKzB4MTVsU1VjbEJvWEtCOHp2d0YxbkJGa2FFWHpSaTNFKzNEMlFxR01vSHFsYWowKzhjL0toc3A5WWRSYVlYMUMxV2NJVGJ1NklWWFg0QitpejNtN0dRdm50cS8zK29lNC9CbGQzZHVTMDk3ZUlPOEhhVTJaU2Q3K3BZVzh4bGZtWGRQTy9sTGgzdUlyNjFPT1draVJGMWRCS1JPeUNEODJJR1A4YitOVEdDYkVhKys4cVlNeTdINHEiLCJtYWMiOiJkMGY2NGUwOWNhNDAxYTQ3NTgxNDIwMzE2YTg0ZDAwNmY3MWY2MzdlNjU3ZjlkMTRkMjNmYjViMDNlNDNhNTYwIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImJvWXVpM2UrYVk3dDRuNTBLekRDaEE9PSIsInZhbHVlIjoicUsxM1JCT3I3cDFTV3B1ekV4eHJTYkk0NTdqeUttUTN2TU5IWFJNRXJXakpuQXZHY0d5Rm1MTnNyOFNWWEJwUDV6c0FLNzV2NlV0RUFGTFJlSTlyamdNQnQyR1Z5ajBBVGFEM0EybEVVc2ltNzJJQWhIWHFqaHlNTHE1QjY2V2wvZFQwSTIwR1hkRzk3TWh0WW9ITk42bnhHZ3g1bkNsaUdZZCttWkVBM25nSjB3TVQrMFlVSVIzbjlGbjl1M0NTZVhmVWJtRHZ3WUsvRDBkSXZPbkovZGNnaHhKaXFEL1R1cHBid2xvVTVyUXBhdTdBZW5RaWdpeXZ6UVdIOGZWMURSb3B4VHBUaFBXQWRRd2dISFFnVmQ1UlJldVJPZVZWQjVrVU1Db1JFMnN0UDg2MnR2aVp3a0hodjZ0T0tqTWVOMnhTYyt2N1R2VTgvMHJ6VGRXN3F3L3dVVU9HZVVxNnNjbjVBcmVEVkZQbjdLNFVoTHdoMTVpNFNuaHJURkwzWm8zVE42L2JLa3NxVnJlOWFtTW15em1WR3E2em1DM3FCelVsU2hyNklPeFNDV2t4QkREVTFFNW9qczlYOU8rcTRGOUpjZEpud2ZBc2lMLzg1b0lkQW5BdEdIODBjV01GUzh6UzhRNDd1b2ZscHZvUlg4eStsYzJEOUcrdUJGeGsiLCJtYWMiOiI1NGE5YTllZDEzMGE0NDE4NjI1N2NhZDdlMzQ0MDUyYzhkYTBjYTlkYzYxMTI5YmZiZWE0NTkzZWI1MThlYmQzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-492642389\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1617832934 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xNV7SvMX3AgbiuDVO62eNXfryfZD4KbWr3rEIkXt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1617832934\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-861293310 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 16:25:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImV6cmVxNG9EaWc3ZmhseUthbkd0d2c9PSIsInZhbHVlIjoiOHZ5RlVNSUJlOGlzU1lDT0tiZXVMWGNzWjhkUGo2NHZWL2oyYzArRk9ocU5HNkdVam1JcTlzY3JwTmtXU1BqVnFpOTIvS0hzcXltMDRjdk9xb1BndkFLUUJqZzM3dFhmMFNHRncxSmxidG9pSEZ2d2pJMUFNanEvU1hkNVNDclEwU01HTmcvc0ZHZ1YwZWZSekY2bU42SXJ6b1FmY0JOMTVnTnZYZTE0Tk1ZcTE2aU1teWJLSVNKaSsvVzdUR0J4Z3JoQStReTBrMDYvUUFZNmNNajVKa3NtQVYyNDZXc0UzZmpzcUFVOXZ3bmJiNkk2ak0yNDlFeFFEWGppVlpqbXFtRlBaRXZkTWRITG5kOEt1ZGZjc0lYbXovNHhyMGJiUUlQNG9HeFNuNW94MnRzZThQV3NiMzBFUlB6T2d6R1k0VDVFMkl6TjdSd1ovN2ZiaElDa3lyeDZCWjQ0ajNpMXhIbzdVeWRZK3g4Ukg1QnJWdVdoVER1bEVneFNmN1BpM29hcXhueXVzWEpVQ1BIYk1Kb0FRZ0R4RmgwTnJ2Mis3c3puN2xQelVqdUk5Z3VYQnpQWUI0YjVQdi9wVmUxMGdudDB1aTNiOWkxckpaY2czdUxld3RNZzJvTUkwYUxsS3hUcVRKYnQzT0xDRFdrOXVFQTJkS2Y2TjJkOFoxaFciLCJtYWMiOiJlZTUyNDcxYzE4YmE1NTY5OGU5OGNkY2IwYzU3YjdkMWRjMTJmOWViNjUyY2Q2OGY1OWEwZDgwNmViOWFmY2Q2IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 18:25:21 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjROV1JNODdlL0tWejBzOWFGbVFVZXc9PSIsInZhbHVlIjoiNW9KZGtkQWxHbVdZN0VQenVzQm5xRzhocVdCY2FaaU5PNjdpelhxbkVmWktNTjltWWtDczdaYTRQQXlhZ0FtdGFyWVBBZStITDFGMU1wS2d6S1hzMDlLNDN1K0hUVkZraTJQajRWaGJpNVJ4UXVITGQxNUpFSEdFTm9LNjFWV2NvMDJJM1FUZmUyY0RzdU12dGZvMTRIUnFjM28yVlk3OWRXKzdhQTJqc0lPY3Bxc0thL3FaaGwzRFhNSzFvZ1lZR0E1bjMzWlloOFp3YVJLS1hhTCs4MUhPVVVIbFBjbUhXbWFKYmoyZVZ2VE0xVUJvSWJSblgyMEloeVVIbGNGSGc5VXZqL05nWGE2a3BSYWlIMkpRSTRmbkZwMjlxSWdnSVFsM21idUR0elZYVnhtTFptSXltSmxsRDdOb1dHWlUyQlhzVmdSVi8wbmdjcHZpZk0yNFJhZjg3VW5jZ3Q3ajhqano5NmFqT0U2YjVYM0RKQ2puUWJTRmhhV2h0RnhyRG1aQjhRZnRqZmtxLzZuQU9qZEhlc0dnMW54ZWgvSGJZc0UrcnVwWjFROW0vaE0xUXlIMldZTGN3SGVSdHhnN3dURVBBbG95bmlSelh5YXFNMjh6b0cxTnZWNnY3eXJmNm9kemlSbkp6bFhXTVBOemx4VGMrU0NNazdjQWgwaEciLCJtYWMiOiI5ZGU1ODgwOWI1NDBkOGI1ZTFiMGNlNTJjNjU4ZmE0NTZlZTE0MDg1NzViMGQxMGYzOTNlM2I5MDZhYWY0MzAxIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 18:25:21 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImV6cmVxNG9EaWc3ZmhseUthbkd0d2c9PSIsInZhbHVlIjoiOHZ5RlVNSUJlOGlzU1lDT0tiZXVMWGNzWjhkUGo2NHZWL2oyYzArRk9ocU5HNkdVam1JcTlzY3JwTmtXU1BqVnFpOTIvS0hzcXltMDRjdk9xb1BndkFLUUJqZzM3dFhmMFNHRncxSmxidG9pSEZ2d2pJMUFNanEvU1hkNVNDclEwU01HTmcvc0ZHZ1YwZWZSekY2bU42SXJ6b1FmY0JOMTVnTnZYZTE0Tk1ZcTE2aU1teWJLSVNKaSsvVzdUR0J4Z3JoQStReTBrMDYvUUFZNmNNajVKa3NtQVYyNDZXc0UzZmpzcUFVOXZ3bmJiNkk2ak0yNDlFeFFEWGppVlpqbXFtRlBaRXZkTWRITG5kOEt1ZGZjc0lYbXovNHhyMGJiUUlQNG9HeFNuNW94MnRzZThQV3NiMzBFUlB6T2d6R1k0VDVFMkl6TjdSd1ovN2ZiaElDa3lyeDZCWjQ0ajNpMXhIbzdVeWRZK3g4Ukg1QnJWdVdoVER1bEVneFNmN1BpM29hcXhueXVzWEpVQ1BIYk1Kb0FRZ0R4RmgwTnJ2Mis3c3puN2xQelVqdUk5Z3VYQnpQWUI0YjVQdi9wVmUxMGdudDB1aTNiOWkxckpaY2czdUxld3RNZzJvTUkwYUxsS3hUcVRKYnQzT0xDRFdrOXVFQTJkS2Y2TjJkOFoxaFciLCJtYWMiOiJlZTUyNDcxYzE4YmE1NTY5OGU5OGNkY2IwYzU3YjdkMWRjMTJmOWViNjUyY2Q2OGY1OWEwZDgwNmViOWFmY2Q2IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 18:25:21 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjROV1JNODdlL0tWejBzOWFGbVFVZXc9PSIsInZhbHVlIjoiNW9KZGtkQWxHbVdZN0VQenVzQm5xRzhocVdCY2FaaU5PNjdpelhxbkVmWktNTjltWWtDczdaYTRQQXlhZ0FtdGFyWVBBZStITDFGMU1wS2d6S1hzMDlLNDN1K0hUVkZraTJQajRWaGJpNVJ4UXVITGQxNUpFSEdFTm9LNjFWV2NvMDJJM1FUZmUyY0RzdU12dGZvMTRIUnFjM28yVlk3OWRXKzdhQTJqc0lPY3Bxc0thL3FaaGwzRFhNSzFvZ1lZR0E1bjMzWlloOFp3YVJLS1hhTCs4MUhPVVVIbFBjbUhXbWFKYmoyZVZ2VE0xVUJvSWJSblgyMEloeVVIbGNGSGc5VXZqL05nWGE2a3BSYWlIMkpRSTRmbkZwMjlxSWdnSVFsM21idUR0elZYVnhtTFptSXltSmxsRDdOb1dHWlUyQlhzVmdSVi8wbmdjcHZpZk0yNFJhZjg3VW5jZ3Q3ajhqano5NmFqT0U2YjVYM0RKQ2puUWJTRmhhV2h0RnhyRG1aQjhRZnRqZmtxLzZuQU9qZEhlc0dnMW54ZWgvSGJZc0UrcnVwWjFROW0vaE0xUXlIMldZTGN3SGVSdHhnN3dURVBBbG95bmlSelh5YXFNMjh6b0cxTnZWNnY3eXJmNm9kemlSbkp6bFhXTVBOemx4VGMrU0NNazdjQWgwaEciLCJtYWMiOiI5ZGU1ODgwOWI1NDBkOGI1ZTFiMGNlNTJjNjU4ZmE0NTZlZTE0MDg1NzViMGQxMGYzOTNlM2I5MDZhYWY0MzAxIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 18:25:21 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-861293310\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1333888025 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1333888025\", {\"maxDepth\":0})</script>\n"}}