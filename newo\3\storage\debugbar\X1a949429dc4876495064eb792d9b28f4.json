{"__meta": {"id": "X1a949429dc4876495064eb792d9b28f4", "datetime": "2025-06-08 15:31:12", "utime": **********.759737, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749396671.923655, "end": **********.75976, "duration": 0.8361048698425293, "duration_str": "836ms", "measures": [{"label": "Booting", "start": 1749396671.923655, "relative_start": 0, "end": **********.670963, "relative_end": **********.670963, "duration": 0.7473080158233643, "duration_str": "747ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.670976, "relative_start": 0.7473208904266357, "end": **********.759763, "relative_end": 3.0994415283203125e-06, "duration": 0.08878707885742188, "duration_str": "88.79ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45152752, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.016700000000000003, "accumulated_duration_str": "16.7ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.714004, "duration": 0.01486, "duration_str": "14.86ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 88.982}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.740143, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 88.982, "width_percent": 6.168}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (17) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.749357, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 95.15, "width_percent": 4.85}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/eyJpdiI6ImNiYzkwakxlbFl5M0hmWXd6cnhmZWc9PSIsInZhbHVlIjoiaWxjU3hBK1J4cjlkSXBwVGZ2eXNWZz09IiwibWFjIjoiYmJkM2U2NzNiY2EzMmQyY2VlY2YyNjdhNmQxNjY4ZGM2NTgxOWJlMmVmMWJjZWUzNDYzYzA3MTViZjAzYzRhNCIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1152377547 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1152377547\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-509467312 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-509467312\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-899899878 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-899899878\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-558009790 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6ImNiYzkwakxlbFl5M0hmWXd6cnhmZWc9PSIsInZhbHVlIjoiaWxjU3hBK1J4cjlkSXBwVGZ2eXNWZz09IiwibWFjIjoiYmJkM2U2NzNiY2EzMmQyY2VlY2YyNjdhNmQxNjY4ZGM2NTgxOWJlMmVmMWJjZWUzNDYzYzA3MTViZjAzYzRhNCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwl%7C0%7C1982; _clsk=o6pnpa%7C1749396668812%7C4%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im4yWllydjBDQlJFb0FBK3hYc1gybmc9PSIsInZhbHVlIjoidld4R2FJTnplUjdjeWRhUGMrakNIWU1XZ1JRbW1qeWVwTmJralNiaHpGdE53dFN6ckFnTGYrWDRBQ3RqcVR6T0ZaWHpGOGRDQWIrTGo4MDZKQnVkaGJEanRFU24yczVvdkN6blhIcDFMWGhhRWZxeEhJNHkxZS9aUjZkMDlTaW9YUTdGL3VLcTQyTEN0WTBDSWRSZ0RhbHR5QVNOSlV0S0U4aGhvVDgyUkozRVdEU0tYc1VITUR6YlREYkRwbElzcjJrYXVaK0NVRXRFNld5N1JNd3RtaGFPVGtWc2hKdllyeU80NE5ibUVtU3k4MTJGc1A5U2ZFM0s4ajc0M3R2Z0R2VEJ1eGpiRFcyYVkzcmdRbGdrYTNDaGwybVBSZmJmckxZVkw5ZW1PUkNvSHBSZjFFa29vRnhaZU1OTDdjbzVBZGNNWk9VMkFTTGZkdTRSd0ljUXZiRUNrZWFKVnJhWkxSYTc4NzZuYitCOTVyd1NtK2IxWlhoeU5LRlp6MUkzemFBemhwdjNJU0lXeE9rZ1plbVZSakhxamg1aDd0QmVRSy9pczFxZ0Nrc1NCbXJqcEpmbzN4eVlCYzlsdnNxSUJQaS8rbFYvdm5POFg1VTF0Ymlhb1dTWXgxOXJVRXZQREFGS3Q3U2h5MlZWUTN4dnZiTC8rTk0rWi9pTUYxM0giLCJtYWMiOiI3Y2E4NThjMjk0OThlYTQ0MDQ0MGNjOTIwYjNiZDRlNmU3ZDc4MDdkYzI0YjRjMjMyODU3NDkyNDM2YjAwNGJkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlFOd0l4ZW9nNmdKeTJZUHBuejMzMVE9PSIsInZhbHVlIjoiQzh6TmswN2J4ajlqRXhMZW80cmE0ZkV6cCt5Y1R3Nm40aUlrOXA1czNFOTZXK3FpU0JISHBUZU16VlFNb1JneGxlTy9LTUdyNVJFbGl4N1B4RFdTa2FpSkZUcEVhVmtHTkNkYjdBOFRpZHhqK2xGQ3doOHBUdUVvbEdsTVVxSmN0ZjQxS1ZwWC9LdW1Oam5lUjg5dmFndzc3RkIyL1lYcVJSa20yUVlmaGMwNEVSMkx3SkVFUGxBRFNWVHljQXR0MHNGNWtESmJtMFdHb0Q2TFJjR2phcDRYOGdua3JTYVVXSGZXRVdwU2VEVlphT3h1TW45TnUrRlZjcWFNejJJK0RzK0ZLQnFEL0dxeGhRMjIzTTBQT3lCVXNSUGI5SUhLUHVkd213LzhuUjI3bm0rYUhac1o3V0Z2WHdsTVp5ajFWQWZwa0xWMHJ2cmY5Qkc3MXBpV3VsV0VqMGJDekJSQWZZRVlsN3dOSWdVUCsyeW1JemdReDBzbk5FU2dBWDFVM1VweHZtK2pEcS9KQ1NQZExqWHZFWlZYWWgxeHNLN0ZOVlZHQzVCNmQwclB5UDRwcGlBV1Q2ZkpUV09sZkdzb3R5OXgrbGNtSUtqaTdqNjREZ2tTODJVTzFHL2YrWFJjZFFPNFg0aUkzRjJpRXo4WVhxZDhwV05TWW9YVk5oOXAiLCJtYWMiOiIwZWQ3MWQxMGY4YmE0ZTRmOTlmNTQxYTlmYWQ2ODBmMmU2ODg0Y2NmMDBlOTNlOWIyYWY5MjIyNTBjMDQ5MGRiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-558009790\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-150217600 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">COI6IIrAbucReABHBN7o3ZaIzRbwBCI1gfwrcfCo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-150217600\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1718334218 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:31:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InF5T0NVTWRpYkkzK0h6V3FOYmZZV1E9PSIsInZhbHVlIjoiR1dPRjA5ajNmZWFxM2U5ejZWRGZXbFpZbmlaRlI0UG1QVFpibkNGVlRCWTRrZVVrM1hNS2N4UU9zb0NwdW1tbGtiMGpmc3k4SlZYdFB3MEIwREFFOGJBYXcxdmlmbGRHamVPa0lVWUl2L1VMNURjYzBja2JIZmpLTVJ5cmNiM1VBMGt1M2U2bG8zTHgzRklkVk1LeTlKeEFsbzRvbVo1dFVQRG5ndUFBYnZPZ2pPR3NZTXdkRHZVM3V2eXgwMlpLbXdIWDl3VVlYNHFNRjRIa0FubTZ2endubzlKOTdFbEZ3UVRKekVabzFoLzArSWZXVUxSUUdtbmwra0VvaStBUVB4cmYxL1g4ek42aXFDQy9yd0NzNTYvbEpOWmQrUnNYWWJUZnNmRUNYaUVsQ1B5d1lJYjBScC9UTlhaanVxZTRvMVliaDlrcXRLNkd1S2FMR2RqSGFIT3hTQ0xTL2dWc3JYR2h4Z1FTeERuR0I4VTFJTTQzc0E4Y0pzNm9TS2RJdkM5QkgvNXhPMGhVTWpvQk9pa2ZHZTYxY0FCME1RRkdwOVYvTWZPS1MvcWMxa3M2T3BreEo0b2JIbms0NkZUUHVhdjFWV0RNQjdzWUJzbkNHYkc3TGxUNlZkZ0NLOGRVcDhmVXhXOThncFJXVWJsRThidmNHUFJaRGRjTHowc3EiLCJtYWMiOiI0NjI5YjYxMmVmZTE3NDFmYjI0ZDNhMmJiYjU4MTllMzE5NjYwODZkNmY2NzI2NDJhNTJmNzhjOGZmZDEyZjhiIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:31:12 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkluY0UrMjc2YzhQM2tEbGJLV1VlU0E9PSIsInZhbHVlIjoiSjJyTGZzd3VvdXJ5WjlxeUxlSGQvYzNrdEx4R09jRDRzTE80NFI0OTc3L2QwL2FNWkFGcU9iTDBZZ0VKa1QrTEdwa2toN3dzdUoya2JZbEtKd25haUJ4akR0RXc0OThQZlJiRXNFdmplcE9ub1BXV2pzN3UwdERMajVUbFhySnZaQUo5ZnBhQktxSkZQUTBQblpYbEhOT3ZPdExBQlhwcTlLZFFINHFNUHdHQTlFcC84clFMMkRwR0o4WHlMYVgrTEk4QnRnTk5qTzh4NjV0U3JPK3Bxa0dPUzYvcXovMVpacDN0dWtQS1RnWUZ2a0lJdU9WbzlmSFJTbFhJbGg0VHpKU093bVhIUmhFTks3dWdWZWcyd0hLa1VXdnJEcUR2V1ZxcTQ1TUdZWlUwOGNNNmU3SE5wdkNhZ0pZT1dPempPUG5UeldzMkVWbVZYUUVWRjlQTDZ6YittQW5nZGIrZVpvWS8ycnNtL0J5RkJPRnI4Y2Z1UWFHR3JKWW1ZN3N6SFVtc1dBUjc3N1pQSkxWMTlSVkdob0ZMZUVsQTJ5RVlIUFNsY2xkNyt4QzVaem5rQS9rbVRJR2RjSzBPVWttV3hSdXBEZjZxSFFNbFp6RjIyVUx3Zm9EY3lGcGY5bWVhRDA3Q3hWdGMySHp0L2N3NG9CUU16Wnp3NXhKeDl3S2UiLCJtYWMiOiI5YWE1YzE0NjYwNjQxNDEzOWI1ZjE1MWFjYTRkNmM1NGI4YTE5MjUzYTczZDZmMGZiMmVkOTc5ZWYyNjFiMzM2IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:31:12 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InF5T0NVTWRpYkkzK0h6V3FOYmZZV1E9PSIsInZhbHVlIjoiR1dPRjA5ajNmZWFxM2U5ejZWRGZXbFpZbmlaRlI0UG1QVFpibkNGVlRCWTRrZVVrM1hNS2N4UU9zb0NwdW1tbGtiMGpmc3k4SlZYdFB3MEIwREFFOGJBYXcxdmlmbGRHamVPa0lVWUl2L1VMNURjYzBja2JIZmpLTVJ5cmNiM1VBMGt1M2U2bG8zTHgzRklkVk1LeTlKeEFsbzRvbVo1dFVQRG5ndUFBYnZPZ2pPR3NZTXdkRHZVM3V2eXgwMlpLbXdIWDl3VVlYNHFNRjRIa0FubTZ2endubzlKOTdFbEZ3UVRKekVabzFoLzArSWZXVUxSUUdtbmwra0VvaStBUVB4cmYxL1g4ek42aXFDQy9yd0NzNTYvbEpOWmQrUnNYWWJUZnNmRUNYaUVsQ1B5d1lJYjBScC9UTlhaanVxZTRvMVliaDlrcXRLNkd1S2FMR2RqSGFIT3hTQ0xTL2dWc3JYR2h4Z1FTeERuR0I4VTFJTTQzc0E4Y0pzNm9TS2RJdkM5QkgvNXhPMGhVTWpvQk9pa2ZHZTYxY0FCME1RRkdwOVYvTWZPS1MvcWMxa3M2T3BreEo0b2JIbms0NkZUUHVhdjFWV0RNQjdzWUJzbkNHYkc3TGxUNlZkZ0NLOGRVcDhmVXhXOThncFJXVWJsRThidmNHUFJaRGRjTHowc3EiLCJtYWMiOiI0NjI5YjYxMmVmZTE3NDFmYjI0ZDNhMmJiYjU4MTllMzE5NjYwODZkNmY2NzI2NDJhNTJmNzhjOGZmZDEyZjhiIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:31:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkluY0UrMjc2YzhQM2tEbGJLV1VlU0E9PSIsInZhbHVlIjoiSjJyTGZzd3VvdXJ5WjlxeUxlSGQvYzNrdEx4R09jRDRzTE80NFI0OTc3L2QwL2FNWkFGcU9iTDBZZ0VKa1QrTEdwa2toN3dzdUoya2JZbEtKd25haUJ4akR0RXc0OThQZlJiRXNFdmplcE9ub1BXV2pzN3UwdERMajVUbFhySnZaQUo5ZnBhQktxSkZQUTBQblpYbEhOT3ZPdExBQlhwcTlLZFFINHFNUHdHQTlFcC84clFMMkRwR0o4WHlMYVgrTEk4QnRnTk5qTzh4NjV0U3JPK3Bxa0dPUzYvcXovMVpacDN0dWtQS1RnWUZ2a0lJdU9WbzlmSFJTbFhJbGg0VHpKU093bVhIUmhFTks3dWdWZWcyd0hLa1VXdnJEcUR2V1ZxcTQ1TUdZWlUwOGNNNmU3SE5wdkNhZ0pZT1dPempPUG5UeldzMkVWbVZYUUVWRjlQTDZ6YittQW5nZGIrZVpvWS8ycnNtL0J5RkJPRnI4Y2Z1UWFHR3JKWW1ZN3N6SFVtc1dBUjc3N1pQSkxWMTlSVkdob0ZMZUVsQTJ5RVlIUFNsY2xkNyt4QzVaem5rQS9rbVRJR2RjSzBPVWttV3hSdXBEZjZxSFFNbFp6RjIyVUx3Zm9EY3lGcGY5bWVhRDA3Q3hWdGMySHp0L2N3NG9CUU16Wnp3NXhKeDl3S2UiLCJtYWMiOiI5YWE1YzE0NjYwNjQxNDEzOWI1ZjE1MWFjYTRkNmM1NGI4YTE5MjUzYTczZDZmMGZiMmVkOTc5ZWYyNjFiMzM2IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:31:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1718334218\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-73720394 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6ImNiYzkwakxlbFl5M0hmWXd6cnhmZWc9PSIsInZhbHVlIjoiaWxjU3hBK1J4cjlkSXBwVGZ2eXNWZz09IiwibWFjIjoiYmJkM2U2NzNiY2EzMmQyY2VlY2YyNjdhNmQxNjY4ZGM2NTgxOWJlMmVmMWJjZWUzNDYzYzA3MTViZjAzYzRhNCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-73720394\", {\"maxDepth\":0})</script>\n"}}