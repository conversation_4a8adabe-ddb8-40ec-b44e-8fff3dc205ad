{"__meta": {"id": "X259f17069dcb2571692b1fe016ddb625", "datetime": "2025-06-08 15:29:19", "utime": **********.289604, "method": "GET", "uri": "/users/create", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749396558.459233, "end": **********.289631, "duration": 0.8303978443145752, "duration_str": "830ms", "measures": [{"label": "Booting", "start": 1749396558.459233, "relative_start": 0, "end": **********.095815, "relative_end": **********.095815, "duration": 0.6365818977355957, "duration_str": "637ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.095832, "relative_start": 0.636599063873291, "end": **********.289634, "relative_end": 3.0994415283203125e-06, "duration": 0.1938018798828125, "duration_str": "194ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 51496768, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 5, "templates": [{"name": "1x user.create", "param_count": null, "params": [], "start": **********.258118, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/user/create.blade.phpuser.create", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Fuser%2Fcreate.blade.php&line=1", "ajax": false, "filename": "create.blade.php", "line": "?"}, "render_count": 1, "name_original": "user.create"}, {"name": "4x components.required", "param_count": null, "params": [], "start": **********.282201, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/components/required.blade.phpcomponents.required", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Fcomponents%2Frequired.blade.php&line=1", "ajax": false, "filename": "required.blade.php", "line": "?"}, "render_count": 4, "name_original": "components.required"}]}, "route": {"uri": "GET users/create", "middleware": "web, verified, auth, XSS, revalidate", "as": "users.create", "controller": "App\\Http\\Controllers\\UserController@create", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FUserController.php&line=51\" onclick=\"\">app/Http/Controllers/UserController.php:51-62</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.013449999999999998, "accumulated_duration_str": "13.45ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.155839, "duration": 0.00392, "duration_str": "3.92ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 29.145}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.181231, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 29.145, "width_percent": 5.948}, {"sql": "select * from `custom_fields` where `created_by` = 15 and `module` = 'user'", "type": "query", "params": [], "bindings": ["15", "user"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\UserController.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.188535, "duration": 0.0049299999999999995, "duration_str": "4.93ms", "memory": 0, "memory_str": null, "filename": "UserController.php:54", "source": "app/Http/Controllers/UserController.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FUserController.php&line=54", "ajax": false, "filename": "UserController.php", "line": "54"}, "connection": "ty", "start_percent": 35.093, "width_percent": 36.654}, {"sql": "select * from `roles` where `created_by` = 15 and `name` != 'client'", "type": "query", "params": [], "bindings": ["15", "client"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\UserController.php", "line": 56}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1991398, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "UserController.php:56", "source": "app/Http/Controllers/UserController.php:56", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FUserController.php&line=56", "ajax": false, "filename": "UserController.php", "line": "56"}, "connection": "ty", "start_percent": 71.747, "width_percent": 9.665}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.230776, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 81.413, "width_percent": 10.483}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.237103, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 91.896, "width_percent": 8.104}]}, "models": {"data": {"Spatie\\Permission\\Models\\Role": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 9, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create user, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1489599164 data-indent-pad=\"  \"><span class=sf-dump-note>create user</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create user</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1489599164\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.245386, "xdebug_link": null}]}, "session": {"_token": "fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/users/create", "status_code": "<pre class=sf-dump id=sf-dump-995222244 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-995222244\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-223759916 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-223759916\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-756267269 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-756267269\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-76742639 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=xh9goj%7C1749396556865%7C8%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlVLK2tGVzQ2aCsyR3N0WjBiUzJhNWc9PSIsInZhbHVlIjoia3p0REROV05SMUY0ZXNXYTF5VVAvR200bkhkMVN6THk3OWxVbXNNUnZodW9hL0ZZbWhVRjJadkZHV3JMbU1WMkg2bVk2UVJyNWtIVEw3QWJPbHdqTUNmWEV6NW5wMmlwZE1yRXgyaUpDNVIwVkMzbTNOUHJKWDdCc0loWHgzMjlhMDc2dEpJWjMvSG5yRGJKMU1KVmdJVlJFS0F2bHppakNXZHJYYm93eGQra1JxcTBxM3BGN0pFT1BHcklwN1RibUZGbnNFOEdXZFBsU0pTSXJ3SWF4OEQ1U0U0MHRKNm82d3k4d0RCNHc1RFJ6SGVQOTEzUW1YNS9EeXNUR3JCc2hBLzJ1WnNxcVBWc0xaMVlxVEtTM1dlVURYeTlsOXF2RU5KMjA5YTFjTkhUQUhOZXVjRUM5MDRYemFBdjY3QkpydWpLeTJoMFhJR3RiTU1GbXM0ZForMTYzYndRNnJRT3ZDN21EZHJjNFdUbm1QQ3VvUHFyclJOSXhSakdrUGUzQWhEWVdqekJCN1FnY29FWEtRTjB1bEk1V0JQaUtZTE5neUVkNHlNL3dYSDRMTTNKV3QwTStURGhtUHQ2RUJRZksrMHVTRUJuck4zSkFCbzVLTGk3SHBuRWxmeGNwTVM5cEpwcmxPK1dvV2gyYk54QjRJNnI1aWF3ZjFmNzFLdTgiLCJtYWMiOiJlOWE2ZTE4NjU5ZGNmMmYwY2ExMDg2ZDM4ZTQwMzhlMjg1ZDQ0YjQ2Y2FkYzYxZTIyNDg2OGJkMDg1YzkxOThjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlNUVCtwTkdqeHROUEFFZy8rR0pvcWc9PSIsInZhbHVlIjoiT2FuNytuVHFkZEJ5Zk56dWVNRlc1ZHRrK0toM3l0SWo2SndJMWhzUzFVNW1PeHF2NGhSUWNTTy9LU0Z5NDI3K2J3ZDB5YkNnTnlrS3c2bFJQbEVsT0xEWmsxM3pqVUhtMUd6ZDYvZEU4cmpQdk4zbFM1eDJmdUtvcVhuNndMUUFNU2FYSnlnNE5Ycm5scjhMVzViaTVSbVJXVnpuTEhSNlNxVlc3ekZBVDBoRjUrdGVrcU1kL2xrTjBKQ2tZWnhxQmJGcm1yMm11ZDY1Z0svNTNkNWgvTVNrYjJQemtwdEJXQ3pmdlFwL203Y2hlQi9saHordzFZallzWkRUR3pycjVzdCtiR2kzZXVlUFJSNWZjd1pYOVpiV2VjSSszd056ejBFR2ZsTEFtVkVFZVdyN0Zqd2NyS2lvbWNKR09ROFRSUUtOZURPWkVwb1Z1UDVUNDluZmM5ekMvOGNUenk1YjNSZUhINVQ5UDd2UVNNUWJ1YTNtTlNGTXBPU3AxbmdkVk1SZS9HOFZYU2VQSkJiSkY1TEdNWmZxN214dXRoa0E1UkRjTnd1dVVpZEl4bU9kVWEwMDFKYU9HQ0hrMzNsUjBzMXFRTVlnR3doVUZRcnRnenRHU200amV4SFB1QnAxSCtONmNKNUtuYWVIa2pGVE9MRXBlTGM5NGEwQlR4aU0iLCJtYWMiOiJhNGM4YTBkN2ZlOGE2NjAxY2JmZGVlOTQ5MjAzMTY0MDQ4NTdmOTFkMDI5MWFmNDg2OWQ2OTdjMzhhZWU4YjcwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-76742639\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-931695878 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">l7xz5q4Zkpyo7Zd0rhHsE8M6pi3wac4XFWhsi0lQ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-931695878\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-323519482 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:29:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlM4RTBCbWlzYlpKRHFtL1JWVW9DRlE9PSIsInZhbHVlIjoickRCMGlOZnBjS2ZEMXIwNVI2UnNGQ2c4b3hrMDhqU3ZPRmd6ZmJVcjNmbko3RHdlNVZWdmppSkU2ZTJhVVdRaDE4RlMwM2N1WTIvQUFtUGxRMFBLVzlBUXd4aWdGSFh6eHMxK1JJd1IvR1Fwb0gvc2FHWHZTdXJuM0pSZ1Y3Tms0TDFWZzBFWm1vVU1TL2JLL0poRC9xQjV5TXhIb2JXYUdjRDJGUFR4WWF2UkFNKzgybzJzbzBaMEJDSjc5aWNHV2ppTEdoMXVHb1ZWcUp2cDR0QTJQL01sdHZIeUEraHMzdXIwVEx5eHB2dkI1UTd4d2RHT2V4eDZVazZZQi9aWEtvbitvdWpUWXBaZndKazNoVEU4WDg3VU40MmFGcTlBNUpEbjBjTlNHYkExcnVKZHNlOGpmY01DcHM1RXR6Z3lRTWJKdkJjMFN3NFpueVovVlVIajQ0V0x2STJ0VGY2eFFHejFLWEtlOFRlMHN6MWJubVltai9NQ1J2TmRheG9hTjdaMm9aNE8xR1p5bVNyNVhnMG1aTkU3MG1kNVVRcnB6WU1ibWFNNTlhaDNpa0xxb2N0ZUdOY055cm1YOVhqT1hSL0REVzIxSk45OFFPeHUwTERkUHpzOVhiaVBGNlFpQjNuVDBuSlZwZGVHSWRkZkppcGUxOFBuT3NzdHJSV2MiLCJtYWMiOiI0YmVmMTA0Y2U1ZmU5Y2M5Y2MyNTk1MzVjNGFhNDBjNWE0Y2EwZTM3ODkyY2Y0ZDBmOTNkYjM5ZTQ1ZWNjMzc2IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:29:19 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Im5Na3JCaEM5dnUweE1mUGJKM0sxWkE9PSIsInZhbHVlIjoibitIZHM3WDZ3Rml2dC9ZaGU2Nk1MSXA5S1BXQldqYnh4YmdlOXplTkxBWm5sTDZUQWRVNlRlZk9ZWGUzVVhNVER3UzAwclFMT1BqK1M2eTVHVW1vMlBscDl6YnhQMHUveVYrdHAxYlVCd2lFSVBta3k5N1RVeVVmbDBzYTBiRGE1Sjg1ZVJRMXVxL0FCbUFBQWxINkNXM3JRQzVsdVF6UTRiUmtiTit5eEJCc3htZkxBWEp1U3htcUlOOUxtZC9TbFF0dU1HcGpQR0tFbWk0ZXo3Wk1oK1ZpTGxhQ29oR2ZsRXIzOW9adGdGaWQvUEYrM1FLNHJLR01tNnh3MndyaWNYZzhIM2NxalpBSG5Kb1E2T2t1eDZHZ0NsVUpCTWhvVjFWR1IwYjh4T2lMUWRKZERHa2hpTDljcmJ5Z2xuMkZHeFA3Yk52REFIdFpoeWxlOFpyZzdjUHVObUhENVMySlNJcDBweVpHNjM3cWNHR3ZJOVcyR0RaV09BWWFPU2ZiMXh3VXRCcGNMZU1vUDVsREtQL01vM2xmK3lrWW5ZanBQeTQ4WmJXNW9iYU1ORlp1aXdWLzBvOXlTNzh0U3RLcVkxOVcxTnBmY2ZacEJYM1A4aGVUdXNHaVovRVFzUUpqakd3SEJ3cEU5MTFMZWxHcjAvMnV5bTIvc2Y0M1A0blUiLCJtYWMiOiI1NmI1MGE5OWMxN2RkNWU4YmNjOTM3MTQ3OWQ1NzdlZDJkM2I4YzkyZGE0ZTA3NTk2ZDNjNTkwYWIwNzJkYzJiIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:29:19 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlM4RTBCbWlzYlpKRHFtL1JWVW9DRlE9PSIsInZhbHVlIjoickRCMGlOZnBjS2ZEMXIwNVI2UnNGQ2c4b3hrMDhqU3ZPRmd6ZmJVcjNmbko3RHdlNVZWdmppSkU2ZTJhVVdRaDE4RlMwM2N1WTIvQUFtUGxRMFBLVzlBUXd4aWdGSFh6eHMxK1JJd1IvR1Fwb0gvc2FHWHZTdXJuM0pSZ1Y3Tms0TDFWZzBFWm1vVU1TL2JLL0poRC9xQjV5TXhIb2JXYUdjRDJGUFR4WWF2UkFNKzgybzJzbzBaMEJDSjc5aWNHV2ppTEdoMXVHb1ZWcUp2cDR0QTJQL01sdHZIeUEraHMzdXIwVEx5eHB2dkI1UTd4d2RHT2V4eDZVazZZQi9aWEtvbitvdWpUWXBaZndKazNoVEU4WDg3VU40MmFGcTlBNUpEbjBjTlNHYkExcnVKZHNlOGpmY01DcHM1RXR6Z3lRTWJKdkJjMFN3NFpueVovVlVIajQ0V0x2STJ0VGY2eFFHejFLWEtlOFRlMHN6MWJubVltai9NQ1J2TmRheG9hTjdaMm9aNE8xR1p5bVNyNVhnMG1aTkU3MG1kNVVRcnB6WU1ibWFNNTlhaDNpa0xxb2N0ZUdOY055cm1YOVhqT1hSL0REVzIxSk45OFFPeHUwTERkUHpzOVhiaVBGNlFpQjNuVDBuSlZwZGVHSWRkZkppcGUxOFBuT3NzdHJSV2MiLCJtYWMiOiI0YmVmMTA0Y2U1ZmU5Y2M5Y2MyNTk1MzVjNGFhNDBjNWE0Y2EwZTM3ODkyY2Y0ZDBmOTNkYjM5ZTQ1ZWNjMzc2IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:29:19 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Im5Na3JCaEM5dnUweE1mUGJKM0sxWkE9PSIsInZhbHVlIjoibitIZHM3WDZ3Rml2dC9ZaGU2Nk1MSXA5S1BXQldqYnh4YmdlOXplTkxBWm5sTDZUQWRVNlRlZk9ZWGUzVVhNVER3UzAwclFMT1BqK1M2eTVHVW1vMlBscDl6YnhQMHUveVYrdHAxYlVCd2lFSVBta3k5N1RVeVVmbDBzYTBiRGE1Sjg1ZVJRMXVxL0FCbUFBQWxINkNXM3JRQzVsdVF6UTRiUmtiTit5eEJCc3htZkxBWEp1U3htcUlOOUxtZC9TbFF0dU1HcGpQR0tFbWk0ZXo3Wk1oK1ZpTGxhQ29oR2ZsRXIzOW9adGdGaWQvUEYrM1FLNHJLR01tNnh3MndyaWNYZzhIM2NxalpBSG5Kb1E2T2t1eDZHZ0NsVUpCTWhvVjFWR1IwYjh4T2lMUWRKZERHa2hpTDljcmJ5Z2xuMkZHeFA3Yk52REFIdFpoeWxlOFpyZzdjUHVObUhENVMySlNJcDBweVpHNjM3cWNHR3ZJOVcyR0RaV09BWWFPU2ZiMXh3VXRCcGNMZU1vUDVsREtQL01vM2xmK3lrWW5ZanBQeTQ4WmJXNW9iYU1ORlp1aXdWLzBvOXlTNzh0U3RLcVkxOVcxTnBmY2ZacEJYM1A4aGVUdXNHaVovRVFzUUpqakd3SEJ3cEU5MTFMZWxHcjAvMnV5bTIvc2Y0M1A0blUiLCJtYWMiOiI1NmI1MGE5OWMxN2RkNWU4YmNjOTM3MTQ3OWQ1NzdlZDJkM2I4YzkyZGE0ZTA3NTk2ZDNjNTkwYWIwNzJkYzJiIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:29:19 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-323519482\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-849449289 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-849449289\", {\"maxDepth\":0})</script>\n"}}