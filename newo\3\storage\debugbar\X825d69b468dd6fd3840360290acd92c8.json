{"__meta": {"id": "X825d69b468dd6fd3840360290acd92c8", "datetime": "2025-06-08 16:25:21", "utime": **********.744687, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.173055, "end": **********.744707, "duration": 0.5716521739959717, "duration_str": "572ms", "measures": [{"label": "Booting", "start": **********.173055, "relative_start": 0, "end": **********.65333, "relative_end": **********.65333, "duration": 0.48027515411376953, "duration_str": "480ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.65334, "relative_start": 0.4802851676940918, "end": **********.744709, "relative_end": 1.9073486328125e-06, "duration": 0.0913689136505127, "duration_str": "91.37ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45678600, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.025990000000000003, "accumulated_duration_str": "25.99ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.688922, "duration": 0.02391, "duration_str": "23.91ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 91.997}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.723768, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 91.997, "width_percent": 3.155}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.732085, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 95.152, "width_percent": 4.848}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-846126962 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-846126962\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2056707701 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2056707701\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1408300465 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1408300465\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-10077035 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=u1db3a%7C1749399903410%7C5%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjNKL0RyL2FWVFJKS2c3bVN5M3BUbGc9PSIsInZhbHVlIjoiMXpmTXI1aVJJYWpWZWxJbGx0TzV5MHgwS0VhZzRsNHZnY1Vlc3hoZUQ3NExMcG0yaTE2NE9qSkdHM2hLaDVCbjVWNE0xaXdhejlkbGRqUVpKcFhDN0VvNW5KZ1FISHh4T1Z6aVdxR1k4Y3c1MXpYdTRLend4eW5XSFY5ZU9XRlVDTCs2ZUlieEdISnROb3FKYzBSSEczL0ZWZ0RTejdORkJLTHJmV1BnTVRnNjVTZkIzY2RjaElYYzcyamd1RkNLT2prNnZrckNkRkV2RDRBNllCVWVXd2gvMnNnbnFma2xjRGdQbGNMNjZRc2N4MDdyK3BoTGNyOWhJSnRONnM5OXRGek1rZGZ1VnlTKzNBM1JEU0NJcHl6Tkd0bWxlUnFJYUJzZlczLzJQTzVza05Rc0xhU2JPZTl1TGJOL21jbW1xYXhRRUJwKzB4MTVsU1VjbEJvWEtCOHp2d0YxbkJGa2FFWHpSaTNFKzNEMlFxR01vSHFsYWowKzhjL0toc3A5WWRSYVlYMUMxV2NJVGJ1NklWWFg0QitpejNtN0dRdm50cS8zK29lNC9CbGQzZHVTMDk3ZUlPOEhhVTJaU2Q3K3BZVzh4bGZtWGRQTy9sTGgzdUlyNjFPT1draVJGMWRCS1JPeUNEODJJR1A4YitOVEdDYkVhKys4cVlNeTdINHEiLCJtYWMiOiJkMGY2NGUwOWNhNDAxYTQ3NTgxNDIwMzE2YTg0ZDAwNmY3MWY2MzdlNjU3ZjlkMTRkMjNmYjViMDNlNDNhNTYwIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImJvWXVpM2UrYVk3dDRuNTBLekRDaEE9PSIsInZhbHVlIjoicUsxM1JCT3I3cDFTV3B1ekV4eHJTYkk0NTdqeUttUTN2TU5IWFJNRXJXakpuQXZHY0d5Rm1MTnNyOFNWWEJwUDV6c0FLNzV2NlV0RUFGTFJlSTlyamdNQnQyR1Z5ajBBVGFEM0EybEVVc2ltNzJJQWhIWHFqaHlNTHE1QjY2V2wvZFQwSTIwR1hkRzk3TWh0WW9ITk42bnhHZ3g1bkNsaUdZZCttWkVBM25nSjB3TVQrMFlVSVIzbjlGbjl1M0NTZVhmVWJtRHZ3WUsvRDBkSXZPbkovZGNnaHhKaXFEL1R1cHBid2xvVTVyUXBhdTdBZW5RaWdpeXZ6UVdIOGZWMURSb3B4VHBUaFBXQWRRd2dISFFnVmQ1UlJldVJPZVZWQjVrVU1Db1JFMnN0UDg2MnR2aVp3a0hodjZ0T0tqTWVOMnhTYyt2N1R2VTgvMHJ6VGRXN3F3L3dVVU9HZVVxNnNjbjVBcmVEVkZQbjdLNFVoTHdoMTVpNFNuaHJURkwzWm8zVE42L2JLa3NxVnJlOWFtTW15em1WR3E2em1DM3FCelVsU2hyNklPeFNDV2t4QkREVTFFNW9qczlYOU8rcTRGOUpjZEpud2ZBc2lMLzg1b0lkQW5BdEdIODBjV01GUzh6UzhRNDd1b2ZscHZvUlg4eStsYzJEOUcrdUJGeGsiLCJtYWMiOiI1NGE5YTllZDEzMGE0NDE4NjI1N2NhZDdlMzQ0MDUyYzhkYTBjYTlkYzYxMTI5YmZiZWE0NTkzZWI1MThlYmQzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-10077035\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-355109903 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xNV7SvMX3AgbiuDVO62eNXfryfZD4KbWr3rEIkXt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-355109903\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1829270104 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 16:25:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImF2RGRpZS81YUR2RXJab21CdWlVRkE9PSIsInZhbHVlIjoiT1JIcEdiYmI2Ty8rU3RaTndzYVR2N3c4aGtaTTd4N2prZEhNZUZCOGNEU2d0MHp2NHRaSVNkUWtxbGVPZEV0VmNFQzQ5NzErK2JjRDRzcnc2V2VtS3dES2t3clVuaXN6eHkxNi9rV1BYTklTTzNaTnJOY0hRcm8zWmVXbXl4aGZwYlY3a3E2aHQzV0hjRzNkL05iL0tmUEM1MW1WU0ZnYUVFSWhsVk5uK2NrMEVCT3VkU0puU1RnbjV3TzZpdTlpd2M0NjY4ZmVldzJaR0dGSTJhS2RHcExCWlJpVU5BRDNJNklzTVpZRkdWTXBKQk96K1hzK20xbkN1VDk4TWZYYUZ2c2hQZy9GblR4TFhMSlI4Lzhab29FRjI5dTJDT1VmQVdMOVJGa1B0TnV4ZkFEUFZaMWxLMmpUdmp6cEQ5Ui9BVThOYlNjcHlrekxhdE5tUWtVTDVJU1RHWjNLM0JGVGlrNEhQVFNkc0NDZ2VmMVQ0TTVOTlpraVVCdkwya0ZvUnNzelluMjQrSHVZZktrOEhpb0JPa0FFSDZ4bENVUkxqWC9UcUZnVDdkZEMvOHY4WWF6c1dBSGFlcmlVUWhzcDJHYlc0dXoreXRMa2NidFY4aEJ2dDF0Zi9RaEY5SGpza2hQVTFUbWc4emc4VWltVThVTGR0VXhqcmE0MnJOK1giLCJtYWMiOiI3MDExZTFhYWJmZjUzYzc1YmYyZTlkNzYzYzhiNjQ5ZjE4MjU0Nzk2NWZkNzFkMGQyYjQxOWE3MmFkYTFlZDEyIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 18:25:21 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlRNS3lTWjBnT1RocEo1dzQySWxNNXc9PSIsInZhbHVlIjoiYW5nKzI3bDUyZndTT25KV2wxdXkzcFNTTXYva0d3SWczNW9Da0FoZWpOVUlYUDFjQ1dxTU5pTE5KalNPbU9MS0xzN2VBL0NWSTRscFhLTFJlRkdnUjhDQklRMmFKQkd6cVhQU2ZwQ1dUSFkzVmtUL0JsdTZjalBMbTMzcEJENW1EZVpnZGZrMGlyOEI4VVVSZndTQXZFc2lYL1Vob296UFd6UUVySjNic0twWDhkUTNIRkwveEZjY0dXUkorVVZ5aWZTTlEvTS9UMmJhQk1keEI3VE4zZ3JWRUpNQSt2TktHdXM0VmZXdi9TYnI0cXRkUVFTRG9CZ0dXQVhObzVjbGdTR3RDK3p0d0NFVHJIN3dKN3FmTUZSUHRlcnBtN3lQVjVTMko4dFRIelZNNmVoemgyUzZ2eUNPQXFVN0xEbFdEMXpGKzN5VXZNV0JGM2N1R3pwMEZXM05zY0ZSdERBcGN1R1c2SVJhNjQzbm16Q3V4Tm5sa1hNa05UTzN0V3pUSWJTZzZRQUlwQ2l6cmEwUEhxUzRxQzZheU5Ud3VKSUZUdi9halVqRE1teXdFaUxGbXl5Z0ljd09ON0dlbWJoazd5djlQcTFHUmdUOEp5czE0bmFPd3V1N05rVWorWXIzVENocG5FN2NBWG1mU3NDRW5uZkVaL1ZlV3lYSzZjSW0iLCJtYWMiOiI0ZWI0YmU5ZDM5MzI3NTViNzhiZGFjNmUxYjdkOGNhYWUzY2RmOTBiOTBkZDBjZWMzODE4MGE5ZGE5ZmQzY2RmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 18:25:21 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImF2RGRpZS81YUR2RXJab21CdWlVRkE9PSIsInZhbHVlIjoiT1JIcEdiYmI2Ty8rU3RaTndzYVR2N3c4aGtaTTd4N2prZEhNZUZCOGNEU2d0MHp2NHRaSVNkUWtxbGVPZEV0VmNFQzQ5NzErK2JjRDRzcnc2V2VtS3dES2t3clVuaXN6eHkxNi9rV1BYTklTTzNaTnJOY0hRcm8zWmVXbXl4aGZwYlY3a3E2aHQzV0hjRzNkL05iL0tmUEM1MW1WU0ZnYUVFSWhsVk5uK2NrMEVCT3VkU0puU1RnbjV3TzZpdTlpd2M0NjY4ZmVldzJaR0dGSTJhS2RHcExCWlJpVU5BRDNJNklzTVpZRkdWTXBKQk96K1hzK20xbkN1VDk4TWZYYUZ2c2hQZy9GblR4TFhMSlI4Lzhab29FRjI5dTJDT1VmQVdMOVJGa1B0TnV4ZkFEUFZaMWxLMmpUdmp6cEQ5Ui9BVThOYlNjcHlrekxhdE5tUWtVTDVJU1RHWjNLM0JGVGlrNEhQVFNkc0NDZ2VmMVQ0TTVOTlpraVVCdkwya0ZvUnNzelluMjQrSHVZZktrOEhpb0JPa0FFSDZ4bENVUkxqWC9UcUZnVDdkZEMvOHY4WWF6c1dBSGFlcmlVUWhzcDJHYlc0dXoreXRMa2NidFY4aEJ2dDF0Zi9RaEY5SGpza2hQVTFUbWc4emc4VWltVThVTGR0VXhqcmE0MnJOK1giLCJtYWMiOiI3MDExZTFhYWJmZjUzYzc1YmYyZTlkNzYzYzhiNjQ5ZjE4MjU0Nzk2NWZkNzFkMGQyYjQxOWE3MmFkYTFlZDEyIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 18:25:21 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlRNS3lTWjBnT1RocEo1dzQySWxNNXc9PSIsInZhbHVlIjoiYW5nKzI3bDUyZndTT25KV2wxdXkzcFNTTXYva0d3SWczNW9Da0FoZWpOVUlYUDFjQ1dxTU5pTE5KalNPbU9MS0xzN2VBL0NWSTRscFhLTFJlRkdnUjhDQklRMmFKQkd6cVhQU2ZwQ1dUSFkzVmtUL0JsdTZjalBMbTMzcEJENW1EZVpnZGZrMGlyOEI4VVVSZndTQXZFc2lYL1Vob296UFd6UUVySjNic0twWDhkUTNIRkwveEZjY0dXUkorVVZ5aWZTTlEvTS9UMmJhQk1keEI3VE4zZ3JWRUpNQSt2TktHdXM0VmZXdi9TYnI0cXRkUVFTRG9CZ0dXQVhObzVjbGdTR3RDK3p0d0NFVHJIN3dKN3FmTUZSUHRlcnBtN3lQVjVTMko4dFRIelZNNmVoemgyUzZ2eUNPQXFVN0xEbFdEMXpGKzN5VXZNV0JGM2N1R3pwMEZXM05zY0ZSdERBcGN1R1c2SVJhNjQzbm16Q3V4Tm5sa1hNa05UTzN0V3pUSWJTZzZRQUlwQ2l6cmEwUEhxUzRxQzZheU5Ud3VKSUZUdi9halVqRE1teXdFaUxGbXl5Z0ljd09ON0dlbWJoazd5djlQcTFHUmdUOEp5czE0bmFPd3V1N05rVWorWXIzVENocG5FN2NBWG1mU3NDRW5uZkVaL1ZlV3lYSzZjSW0iLCJtYWMiOiI0ZWI0YmU5ZDM5MzI3NTViNzhiZGFjNmUxYjdkOGNhYWUzY2RmOTBiOTBkZDBjZWMzODE4MGE5ZGE5ZmQzY2RmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 18:25:21 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1829270104\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-440136023 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-440136023\", {\"maxDepth\":0})</script>\n"}}