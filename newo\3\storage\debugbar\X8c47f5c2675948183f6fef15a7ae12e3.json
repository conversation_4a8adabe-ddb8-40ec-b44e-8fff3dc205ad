{"__meta": {"id": "X8c47f5c2675948183f6fef15a7ae12e3", "datetime": "2025-06-08 15:29:58", "utime": **********.640796, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749396597.817078, "end": **********.640821, "duration": 0.8237428665161133, "duration_str": "824ms", "measures": [{"label": "Booting", "start": 1749396597.817078, "relative_start": 0, "end": **********.53947, "relative_end": **********.53947, "duration": 0.7223918437957764, "duration_str": "722ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.539484, "relative_start": 0.7224059104919434, "end": **********.640824, "relative_end": 3.0994415283203125e-06, "duration": 0.10134005546569824, "duration_str": "101ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45139448, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00859, "accumulated_duration_str": "8.59ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.588965, "duration": 0.0065, "duration_str": "6.5ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 75.669}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.613379, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 75.669, "width_percent": 12.573}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (17) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.626583, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 88.242, "width_percent": 11.758}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1082986752 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1082986752\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1142727149 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1142727149\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2122152969 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2122152969\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2029084526 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwl%7C0%7C1982; _clsk=o6pnpa%7C1749396587596%7C2%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlRrMWhKc1pzeXBGWDFQcWVHSHhzcXc9PSIsInZhbHVlIjoiS1hsdlMyeDRFRTM5RzZ0YnpxVHM0SmZ2aWRRZGtnd2RCL2lsU0cxTXVGRGFpcGFIdU5Bb0RaUDBRd1d4ajJtOUU0RlRjY3BTcGd0cnVOOVBOOGFLemNBbURQM2drZmc3MGo3L092UC9VdlF6WjMvNWJWSWJGL2g5aTh3aU9IdTVFeHFleGRGemJ4RTNWWU1nOTc0WDVxaWtiV3BLL3BPN2hGU1FrNHg5RVRDbzFVS3ZTNEtlN3J4bVQ2SWM4TU1KOEFEWW9vdnVKbWZCT1hmRnBqQXBvTHVEUnBSdC92RTF4YWd0TkpMSDBMbG5zMmd6Zkp5TkV3Szd3RVBrZzBiSnZCVlRVMmJsZnlzS2xuMTNOSDJCQkNaWDRoWEZUalZoa016Si9MTmhvUWVFNExqK2IvMVJSeC9BR3MvRmVoR3EyYmMwa3owbVZTUzVpanY3YThzaXNZb3RWUVpGL0U3NjNHcVh5VmN4MTVjdmYxTUVLZXBDUE1aa0VVcnFseUF2TFBNaDlWYXRpOHJlM0tReWpjakxnMGZhSDRYaFovZkxHZG5IRndLOUFJZmx5Y2RGYzVOT3UvNmFCMUl4dDBPZ3Z5ckhyczgwOXE0S0x3WjE4aTkvWWc2N1EwblI5QTR0dnZ4bTZHVzhhM1JwcDltQklWQlI0MFIvMGtFL254LzQiLCJtYWMiOiI3N2JmZWRkODgxOGE5NzZiNGZhZmM0NDlkMmE4NzU2MTk0YjA0YjAxOTdlODI3ZWNkMmNjZTQ5NDRjYTZlOTlhIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImZoTWlOZk9QeDFyazRBYXhUTUM4Y0E9PSIsInZhbHVlIjoidXNKNG8xa3cxU3FhaFdvazQ1UkVhRmhORVVBdlNkWndhM3VzaGRwMk9wckp4eHR6Y3RKNS8xTjhUVTZNWVBCM2RZNzRtNDNaazQzV0NRY1diNkh4S0I3UG8rQW5JMkJuaEkvSHlOYStZeXZFcXZhT0ZPaUw4U2tVaDFmdVNZNHllVTF0Y282WmN6RW55ampjSXYybUxjQ3EzbEkvSXhSa3FpdmFqZnRiUlVvWGNVbFlwLzQyR2VSQzhPakg4Ti9oaElUcGhhc2tZd3VZakY5ZUQxdE9WdTE3eHgrb0syTWZIWnRDajhidnQrTXNIc2ZRNCtJZ1RCZGdVdWVIeXA1UnZiejlXVEtrZEVTeTFNaVpWRWE1L2NodFBtU2tPRXF2VUJxbXVYOEx0TDVnMGdWSEF1cVl4WlpqaWRRaXZ2aHBidGk5aTdzdWw4MzF4M29IWUpHKzBRalFzS01acmdMZGF5enhFaDRxcGxZNWozOTVSSmcwbjhFdnNDbTFFbVA0SXZhWEFJd3QvSVc2MWpCbFZybnpJNUFRNGVyTWp2SFNXeG5kU2ZySjRDbGVZcmlPemtmK2Q2NUFSdlk1VGx2YzJWQThNa3JuTEtDYVdvUUVnOGVHSy9zRHVtSU80K1RqTGlPUjZieEZEZnc2Z0xKc241eFhEclplVVpTUHllZ3YiLCJtYWMiOiJlNzIzNWE3ZTFjNTM4MmE5NzAxMTM2OGJlYmIwYTE2NjYwMDFhOGMzYTFmMDg0NzU2ZTg1MmQyNDk5ODQ0MGYwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2029084526\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1893461295 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">COI6IIrAbucReABHBN7o3ZaIzRbwBCI1gfwrcfCo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1893461295\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1719429247 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:29:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjBkK25xUlNDRVhaYmlHWUZiUUo4bWc9PSIsInZhbHVlIjoic0FsQ0tOb1A3RjlwUk9xelhZWFBBKy9HdVI4ZUJOVlJSSGpSSGFpeTAySzkzWk1kbGlodm5FdC9GZFR6Z2lteElEb2Jselp5NG9WdU1EQlpEWFBaQ3hVdi9ieENkWGdFQjlQTy8xcDJ3NEtFUTM4L2lpN3RJRmpTeVhnZEJvZ2FUZGpyZW5SZnBRZXV0d3l2aFRDWFl3VDJnMGYvTlRIcklRQndnOUYvVElMSTlpa0M0UEJnbDV6eDhCKzNIbG9TdVI5OUVCYjJad01jbUVJZXZlWDlIUHplbVZvNTNGdEFGb3lWZHl6VVpXb2FMbjJTcyt5b1lFNHBUOUdZY2lCc1pqNTZiT2lCWTZBdFZ5eE9CNDJ3Ym12Q2NLSWNGRmVpbGJ5S3hHczJjTm5SVkN5a1VqMHZzdkh4KzhmOHE2dUdIVERCM3I5S0pqMGRUV25ta1VYdFY4M0VxRXFKdFk2V1VhakFRMFBTcGMyOVBGZmRLb0FmMlRPMlIya0x4L1ZSYThaZjRRcUZtaCsvVzVzZ2c2d0JTQU43aDVuTmtldWM0b2R2WUR3YkY5WFV5L2FVYTBIaXJMQUdXNm50Z0hhd0ZRZ0lobENhTmFBN3h6REZneVp1RlIySG80RTJkOSsxdUVwQXRZSzVFQkFWT0NaV0VRVm91TWFNRHhQdVRld0giLCJtYWMiOiIxMzdiMDE4N2NiYTBkNmE2MjZhMjgxYjcyNDk5NTg4N2MwYWE2YzVlMDE4N2VmNTgwYzdhMmJkNDJjN2IzODdjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:29:58 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InZyaE4wZ0VJQnlqM05odTZINFpZU1E9PSIsInZhbHVlIjoiWldMVU4ybUZtYmlia3M3d2lOK0Y0eGhZL2w5d1RiOEQ5NXRodUVGVS85eXNwSGtNbUg1TEZHc0tvUWVtaG1nY2kxRnFXMXR2dCtQemprdVlEdndtWlBQMEtVcUVPQ2Rxd3djWFU0Rlp5UUVVaHk5UXczMWFjbzJadGZ1T25PY280VEo0Q2x1cFhxSmhkWU9hYkVyMEw3S3BnWk9SK24xbzVCRTVUdzA5L1RqNmNhUzhnQjBNSzV0UTNlbTg2VlkvdGVXWGNvL2dRRjFEbWlSRlFpdjA3SnlBYkhrWmdrMjhtZTNPSEZMMTJTVkJXWTBQeWFMbkU0OVN3bUNzT3FHMDMrdW5MMFZ0eGJBSXVubDJtM2ZhWlJHRGtpTHVROXplNHJBUlJZeHZIWHVCZ21TTk9TNDVZUHYwTFR0MHVBRyt4VWVOOFA4c2dDNnFqdWRwOEgrN01Kc1pxOUxXSGpQVDhyU2VnbjUwL3RxQ3F0RlM0YlBTbHFTV0R0SFpmQld0YXhEdjd1TGx0R0RNNU1iUTJrWGxRUms5QTYrTzNxbW9JRzRDLys3R2daS0d5U2k4YzVMOUMrQVg5NkJnalh2RUM0cXY3WUZscG4xL1lBVTZ0WVlaeTEvNDUyN1FObW1YbGVVL2pFK252YXFvQXB3RE96TUwxUDVrdUpPVUFjOUciLCJtYWMiOiJlMzg5NDMxZTA5OGE0ZWFmZmM2NDIyN2VjNDQyMjViZGYwZTlkYjRjM2IwODczOWRjMTg1MmRiOTVkMjE5MDQ5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:29:58 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjBkK25xUlNDRVhaYmlHWUZiUUo4bWc9PSIsInZhbHVlIjoic0FsQ0tOb1A3RjlwUk9xelhZWFBBKy9HdVI4ZUJOVlJSSGpSSGFpeTAySzkzWk1kbGlodm5FdC9GZFR6Z2lteElEb2Jselp5NG9WdU1EQlpEWFBaQ3hVdi9ieENkWGdFQjlQTy8xcDJ3NEtFUTM4L2lpN3RJRmpTeVhnZEJvZ2FUZGpyZW5SZnBRZXV0d3l2aFRDWFl3VDJnMGYvTlRIcklRQndnOUYvVElMSTlpa0M0UEJnbDV6eDhCKzNIbG9TdVI5OUVCYjJad01jbUVJZXZlWDlIUHplbVZvNTNGdEFGb3lWZHl6VVpXb2FMbjJTcyt5b1lFNHBUOUdZY2lCc1pqNTZiT2lCWTZBdFZ5eE9CNDJ3Ym12Q2NLSWNGRmVpbGJ5S3hHczJjTm5SVkN5a1VqMHZzdkh4KzhmOHE2dUdIVERCM3I5S0pqMGRUV25ta1VYdFY4M0VxRXFKdFk2V1VhakFRMFBTcGMyOVBGZmRLb0FmMlRPMlIya0x4L1ZSYThaZjRRcUZtaCsvVzVzZ2c2d0JTQU43aDVuTmtldWM0b2R2WUR3YkY5WFV5L2FVYTBIaXJMQUdXNm50Z0hhd0ZRZ0lobENhTmFBN3h6REZneVp1RlIySG80RTJkOSsxdUVwQXRZSzVFQkFWT0NaV0VRVm91TWFNRHhQdVRld0giLCJtYWMiOiIxMzdiMDE4N2NiYTBkNmE2MjZhMjgxYjcyNDk5NTg4N2MwYWE2YzVlMDE4N2VmNTgwYzdhMmJkNDJjN2IzODdjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:29:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InZyaE4wZ0VJQnlqM05odTZINFpZU1E9PSIsInZhbHVlIjoiWldMVU4ybUZtYmlia3M3d2lOK0Y0eGhZL2w5d1RiOEQ5NXRodUVGVS85eXNwSGtNbUg1TEZHc0tvUWVtaG1nY2kxRnFXMXR2dCtQemprdVlEdndtWlBQMEtVcUVPQ2Rxd3djWFU0Rlp5UUVVaHk5UXczMWFjbzJadGZ1T25PY280VEo0Q2x1cFhxSmhkWU9hYkVyMEw3S3BnWk9SK24xbzVCRTVUdzA5L1RqNmNhUzhnQjBNSzV0UTNlbTg2VlkvdGVXWGNvL2dRRjFEbWlSRlFpdjA3SnlBYkhrWmdrMjhtZTNPSEZMMTJTVkJXWTBQeWFMbkU0OVN3bUNzT3FHMDMrdW5MMFZ0eGJBSXVubDJtM2ZhWlJHRGtpTHVROXplNHJBUlJZeHZIWHVCZ21TTk9TNDVZUHYwTFR0MHVBRyt4VWVOOFA4c2dDNnFqdWRwOEgrN01Kc1pxOUxXSGpQVDhyU2VnbjUwL3RxQ3F0RlM0YlBTbHFTV0R0SFpmQld0YXhEdjd1TGx0R0RNNU1iUTJrWGxRUms5QTYrTzNxbW9JRzRDLys3R2daS0d5U2k4YzVMOUMrQVg5NkJnalh2RUM0cXY3WUZscG4xL1lBVTZ0WVlaeTEvNDUyN1FObW1YbGVVL2pFK252YXFvQXB3RE96TUwxUDVrdUpPVUFjOUciLCJtYWMiOiJlMzg5NDMxZTA5OGE0ZWFmZmM2NDIyN2VjNDQyMjViZGYwZTlkYjRjM2IwODczOWRjMTg1MmRiOTVkMjE5MDQ5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:29:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1719429247\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1648613383 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1648613383\", {\"maxDepth\":0})</script>\n"}}