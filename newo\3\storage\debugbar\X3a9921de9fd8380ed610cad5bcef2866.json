{"__meta": {"id": "X3a9921de9fd8380ed610cad5bcef2866", "datetime": "2025-06-08 15:29:07", "utime": **********.846896, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749396546.709689, "end": **********.84692, "duration": 1.1372311115264893, "duration_str": "1.14s", "measures": [{"label": "Booting", "start": 1749396546.709689, "relative_start": 0, "end": **********.692767, "relative_end": **********.692767, "duration": 0.9830780029296875, "duration_str": "983ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.692807, "relative_start": 0.9831180572509766, "end": **********.846923, "relative_end": 3.0994415283203125e-06, "duration": 0.15411615371704102, "duration_str": "154ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45139712, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.030119999999999997, "accumulated_duration_str": "30.12ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7663262, "duration": 0.02819, "duration_str": "28.19ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 93.592}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.816257, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 93.592, "width_percent": 3.187}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.83095, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 96.78, "width_percent": 3.22}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2069023721 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=xh9goj%7C1749396538091%7C6%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjJUQzZYaXhaeU1HeWFORHh5VG5rOHc9PSIsInZhbHVlIjoid3o0RzllVUFoTGw5aXpKRWxUL2xCaDBTR0VjSmhRM0FTUzdNSVFwUTl5SzNKUUNjQ21kQUZhOCthQnBoWDF3N0Rta21qQUJlNG9vdUFLNG9hUXp5ZnFJMXRnT1FxcXJ3ZWFuZWRiWEcwUmFFaTQ2dHhLc2FPQm5uTFRmcTNWTkFOTGxVT2Jrc2szN0taaGZOZ0luOE04UnNweDZrc2lNVHdNbTBtOC9UMWRRR2l2QnhUTXA3ZDB5ZEtSMDVhcms5TG5FUHFjRDFpVXNMZ3NjV3YwalphOEFUdHZHK0FwV083Rm8yazR1WGN4QmhrNHRZaS9NQ2R1Yzg3Y0JDSXcvd3doZ0VDUkN1U0dxZUVIZTV2VUdsMUN3WUVhUk1PSVU3VWM2OHZ4WnVSWFVwdTllbGRQZzZmSUs4M3JIUml1M0hGOVlpVGM1Vi9uSENOVjZDR3RtRXN0cnd0TytxSmYxbDUvQmZmQnV4cVNidmQ5eXVUUlJMdXZSczNKUjlidXFaSnNPM3RRcndQVUgxbXp0MUo3cWI1c3E3dm9wRm41VDRZaWxHdVdrczgyMjMzaG9lMnA0ZDUyWmhGOG9QZForVW5icVBObjJFVmxCRUJVaGpMa29QRjdpZ05CSUNoMjBDOVpzVlJWTjhVNG45VEoxbDVDR0ZQbThrTHd3bERNK2IiLCJtYWMiOiJlMmRjNmRmM2ZmYjZiZGUxMWE5NmU5ZDNjYTFlMzUwYTFkOWYxZDcyOThkMjY0ODUxMGViN2QzOGIwZGRmNmQ1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ik1hc3BjR0VHUWx5VUlUK2NYU2NOclE9PSIsInZhbHVlIjoiVE1sNHFGZjBCclVtZGM1M0g1dkUxOHJZWWtoZkNNWjF5MTJyVXJ6aUp2Yy9GYVpEZ1pORi8rcEhGZXM1NzRtTWJ1Q2pnMG14WXRSY0Y2WDJLUkNxU0FtY0h6NitQYTdCRjVodTB5UTQ0TkRoZ2tvc1lJdzdHa3dzNVA4YURsQXlyeEhUZ3M2bm15NGM0cXRMRmtDSURZQkk5WmFkSWF2Q0tVVVlQZFEvbjBJT2NBaWtZTytJRGwwdXErOHhYdHhxbWYySzRwaUsvbkxUSTQ0RmdvTFpRUFNTNWM4akdvblcvMkJrWktvRTlRT05aUmJrZ3FWMUcyTDVHV3R4dEJwQlIzcnYvU0dhZGxRd01vNXhpREhRd3hyL3NFTkJjMVVGNDZNNitKL1oxUVZsK2JTeklrR1dZRy9BeVpVMHgweU1Tc1VCUzFzQUpUSHBIcFVUTC9FMVpZamhJTnliTUU3UXc4LzJBT3B0QTN1WTF4T2EzcVZOSjFTdUYyTGxKTVRYOFdMMDZuSWVYSmFCT1ZqNlJLU091Q3oxYzd1VGM5Y2Rzb0JJWVRBMHJaYTljS0lVRTc0Yll4Mk5xMHpYZ3dRcHpDbGRiRzB4YUxMVlRleHpROUlWWkZYdHZ4VEVUVm5MOVhxS2xtNHJOT1FkTFZERlhOaEtjNVA4eE81bGkrWnMiLCJtYWMiOiI4NDNjMGU3NzI3NmMzNDA2NTk5MzU0MWY0YzZlODg1OTA0YTc3ODBjNTFiOThmZjhiYjg0MjlmMzY4MWU4ZGVhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2069023721\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-252479149 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">l7xz5q4Zkpyo7Zd0rhHsE8M6pi3wac4XFWhsi0lQ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-252479149\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-878239622 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:29:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkszVVpwSjFnQUx5Z3ZuQTU1SXJlUEE9PSIsInZhbHVlIjoiL1VQYXExUlNBUCtZMVB2M0V5d3NHTkZTMVE5dVNwcnh5Y1BwY2ptL2ZTTkJjTm5RaTEwb0NQdHRVUG9zandaTWZYZ3oxbzRTL3VEdklCeEUyN21UWW9va3R1MUZYYldEVndRTDFaVXZTdE5NMGFIVkUzYU90RzFGaitJOUY2RVBjTjgrdmhZaVRTend0T08wVjRSTXJwVHJFMytZeTNmTEdheWFqSXZGa3FTMW1zZ3QzOXltTDRNZS94aXJrSy93bVNFR1M0TFNMVjhDWG03UjUxckFjL0krQ3ZmYkxtV3k5UG5sc25qNnNESjZKVEk1KzMydERWR2RoRUtSQlAwdjVWU3lyTyt6S1MwSlF1aENpUGdiY1pxcHpDV0RwR0V1STdRbnRJZk0ycFc1R2kwSVhQL3BocmU1V2FZYW8xZWNqZEQzUjQyQ21yZlJjUGFZeWMzdDVBanpRZFFLNXIvY2ErVHI3TFRUY3Ruamo1amVZVUdabjNvMWRHdlpXNkpITzZlNlZzeklnOGo2dk9BVjJuSGUyaXV3RlozdVh6MkJBWW9rZnBHbFczWG5pUDBRWHgvRkpiYUo2ejJXZkNERzMzajMvamZiK3c3REhuNHBncXpXb0k1d1h4dUs1d2VwMVJaaVphT2c4WFVoa01rTVNBZEVsa0RyNkJMQWIydTYiLCJtYWMiOiI5MzljMjY4NDRmMTM1MWJkYThhZGMzNWMxN2ExYjllYzFiYmEzNTEyNjcxMWE2NGMwNDkyMjFiNjNlYzk5NGY2IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:29:07 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjNsdTI1NVZBL21VYjdZKzRNL0p0OXc9PSIsInZhbHVlIjoiZERucFBaTnd4c2lqRGU2Zm1YcEhRL2RiaHhGbmp0MEE5OXNlTHE5a3hLOVJ3MFM0Nzc4ekhobW9Tdm55ZjBFQVR1QkFIZjN0NGhwOFkvRDF6eFBuTjNHVHFHQTBQeE1kcDIwVVROOFo3Skgxekt6REpCVHB1bEU3SXBNZkpPWjhVOHFFeTBLZTFURVFFeDVma1JSM29JT3Y3WG4rZjc0MlloUkdEQTE2aXE1Zld1bmZlZkhNRGVzbENqc01iOU9zSm5MLzJya2NiMUtGRWxCbm9DTEhnS2hRMDQxanFEam9ZQWhLa0l3OHZZbllBWUtxVEVtTnFJNkZpa3g2Z085bVp1WFgyQVhBeENpajJiZXo0VGkvMzVCRWkwU2lHeENVcmJvSzRnS0xKdk51YUpXWDI2WG4wRTFDMTRjZEgwZTVqNjhMb1FuTllvSUJubFBCejR4QmV3L2F4cGRyQ01RbWhVTlZLcXg1cUpWb3p2UG5JUjlmYlhsR0RBZjlFYlpqWjAxbnpTRmtUamN0U1NXU29uTnF6WTV0aTdGQ0JNbk9qcVI4VjVrTVp1K2MvVTZnRXRob0RGanBqTS80RVcyNjlIajRBdStCdVNHQ3I2NFBuc2EzQnJDaTdYV2lGNXRvSXo3QWZNN3lvcFlJWUlYbzBYTEVKaStvQ05CbTdjV0MiLCJtYWMiOiJiZDAxZjZiZDc4NTVmMGY2NzMxOTdiYmJhMGQ5ZTYxM2NkZmNiMGYzZWU1OWViZmQ3NDQ4ZTU2YzJlMTgwMDc3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:29:07 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkszVVpwSjFnQUx5Z3ZuQTU1SXJlUEE9PSIsInZhbHVlIjoiL1VQYXExUlNBUCtZMVB2M0V5d3NHTkZTMVE5dVNwcnh5Y1BwY2ptL2ZTTkJjTm5RaTEwb0NQdHRVUG9zandaTWZYZ3oxbzRTL3VEdklCeEUyN21UWW9va3R1MUZYYldEVndRTDFaVXZTdE5NMGFIVkUzYU90RzFGaitJOUY2RVBjTjgrdmhZaVRTend0T08wVjRSTXJwVHJFMytZeTNmTEdheWFqSXZGa3FTMW1zZ3QzOXltTDRNZS94aXJrSy93bVNFR1M0TFNMVjhDWG03UjUxckFjL0krQ3ZmYkxtV3k5UG5sc25qNnNESjZKVEk1KzMydERWR2RoRUtSQlAwdjVWU3lyTyt6S1MwSlF1aENpUGdiY1pxcHpDV0RwR0V1STdRbnRJZk0ycFc1R2kwSVhQL3BocmU1V2FZYW8xZWNqZEQzUjQyQ21yZlJjUGFZeWMzdDVBanpRZFFLNXIvY2ErVHI3TFRUY3Ruamo1amVZVUdabjNvMWRHdlpXNkpITzZlNlZzeklnOGo2dk9BVjJuSGUyaXV3RlozdVh6MkJBWW9rZnBHbFczWG5pUDBRWHgvRkpiYUo2ejJXZkNERzMzajMvamZiK3c3REhuNHBncXpXb0k1d1h4dUs1d2VwMVJaaVphT2c4WFVoa01rTVNBZEVsa0RyNkJMQWIydTYiLCJtYWMiOiI5MzljMjY4NDRmMTM1MWJkYThhZGMzNWMxN2ExYjllYzFiYmEzNTEyNjcxMWE2NGMwNDkyMjFiNjNlYzk5NGY2IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:29:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjNsdTI1NVZBL21VYjdZKzRNL0p0OXc9PSIsInZhbHVlIjoiZERucFBaTnd4c2lqRGU2Zm1YcEhRL2RiaHhGbmp0MEE5OXNlTHE5a3hLOVJ3MFM0Nzc4ekhobW9Tdm55ZjBFQVR1QkFIZjN0NGhwOFkvRDF6eFBuTjNHVHFHQTBQeE1kcDIwVVROOFo3Skgxekt6REpCVHB1bEU3SXBNZkpPWjhVOHFFeTBLZTFURVFFeDVma1JSM29JT3Y3WG4rZjc0MlloUkdEQTE2aXE1Zld1bmZlZkhNRGVzbENqc01iOU9zSm5MLzJya2NiMUtGRWxCbm9DTEhnS2hRMDQxanFEam9ZQWhLa0l3OHZZbllBWUtxVEVtTnFJNkZpa3g2Z085bVp1WFgyQVhBeENpajJiZXo0VGkvMzVCRWkwU2lHeENVcmJvSzRnS0xKdk51YUpXWDI2WG4wRTFDMTRjZEgwZTVqNjhMb1FuTllvSUJubFBCejR4QmV3L2F4cGRyQ01RbWhVTlZLcXg1cUpWb3p2UG5JUjlmYlhsR0RBZjlFYlpqWjAxbnpTRmtUamN0U1NXU29uTnF6WTV0aTdGQ0JNbk9qcVI4VjVrTVp1K2MvVTZnRXRob0RGanBqTS80RVcyNjlIajRBdStCdVNHQ3I2NFBuc2EzQnJDaTdYV2lGNXRvSXo3QWZNN3lvcFlJWUlYbzBYTEVKaStvQ05CbTdjV0MiLCJtYWMiOiJiZDAxZjZiZDc4NTVmMGY2NzMxOTdiYmJhMGQ5ZTYxM2NkZmNiMGYzZWU1OWViZmQ3NDQ4ZTU2YzJlMTgwMDc3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:29:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-878239622\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}