{"__meta": {"id": "X4ca2fb7ed8c546372f99651b84646b06", "datetime": "2025-06-08 15:40:02", "utime": **********.594584, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749397201.899754, "end": **********.594604, "duration": 0.694849967956543, "duration_str": "695ms", "measures": [{"label": "Booting", "start": 1749397201.899754, "relative_start": 0, "end": **********.486231, "relative_end": **********.486231, "duration": 0.5864770412445068, "duration_str": "586ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.486247, "relative_start": 0.5864930152893066, "end": **********.594606, "relative_end": 1.9073486328125e-06, "duration": 0.10835886001586914, "duration_str": "108ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45139856, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02577, "accumulated_duration_str": "25.77ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.535394, "duration": 0.02357, "duration_str": "23.57ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 91.463}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.572824, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 91.463, "width_percent": 3.57}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (17) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.582813, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 95.033, "width_percent": 4.967}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/eyJpdiI6ImNiYzkwakxlbFl5M0hmWXd6cnhmZWc9PSIsInZhbHVlIjoiaWxjU3hBK1J4cjlkSXBwVGZ2eXNWZz09IiwibWFjIjoiYmJkM2U2NzNiY2EzMmQyY2VlY2YyNjdhNmQxNjY4ZGM2NTgxOWJlMmVmMWJjZWUzNDYzYzA3MTViZjAzYzRhNCIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-583037842 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-583037842\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1006315020 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1006315020\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1045903215 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1045903215\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-488595195 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6ImNiYzkwakxlbFl5M0hmWXd6cnhmZWc9PSIsInZhbHVlIjoiaWxjU3hBK1J4cjlkSXBwVGZ2eXNWZz09IiwibWFjIjoiYmJkM2U2NzNiY2EzMmQyY2VlY2YyNjdhNmQxNjY4ZGM2NTgxOWJlMmVmMWJjZWUzNDYzYzA3MTViZjAzYzRhNCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwl%7C0%7C1982; _clsk=o6pnpa%7C1749397200163%7C6%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlBWUW03TEdRSkR1a0FJUDVsYlVRQnc9PSIsInZhbHVlIjoiWkR1ajBDQmE5aXlUSDgxZUFCYWdrK01NV2VTNVYrNk9pSmg1bzB0SnlGMFZJQkpHT1YxZ1VwbFRTTzRJSUJ0T3A0RnMzMW9KbGlMd0pyclV0aTlhZDRWQlA0U2w2STl4cFlYQzdhTkw0UFJtRUVQVnZaaVQxMWlpOWYrNGdrUlc0MWVsUlZlMU1HbndlN0dqQzBYdURjNjk3ZURLRVhRZVZtS3dZSk5xTWpEUk9uRFhhTmxsdW9uenZ3T0FxVzN1SGRQQlYzU2hlUGxhOXNrOTF1UnJaaHBtWW5RNzY5TjJyQXZWdm8rUkpzazc3WXFJdC9Ed1E1eDRWNHJIRW5Sc0xydEhiYkwyVktRUWgvM3NsanVvM08yZ2gzRzJ1YU12NVdab29LLzFlVWl2K0t5eFFKaHgraWRzdFZ1eGFrL1MyMW9ZZFFDSUxvUXRHdVQ2bTFMTVUwekxuN1FmdDJhQmt3cXZRdFZLVHFrcHRLM3pGUHRuemxTbGd0cmpqMDVwVWtKVGtVa2F2QVg1V0VxMGhteTZjRnpncm1TZ2xxMkdMMFNDSUt2NzJmeDZNUEpRN3FRUGpnalpnQ0VyaGhNcW04R1luRjRvM0I5UzNPRXpsTmtQMlFEbjhndis4VkM5blFaY3dhRXNGY3YzMEFXYnh0VG9GUkk1UVNxeXJhZ2oiLCJtYWMiOiI4NzM1NGUzYmU2MzY3Y2IzZDBmYmZlMzA0NTljM2M3ODM0ZDlhMjQyNTdhMjhmM2YwMTMzMGM4N2VjN2NlNjM4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkRHOHVyVU9PVk5QTTR4QzRaRmU3MHc9PSIsInZhbHVlIjoiRkNkUEJWbmhHT2h1MEFSaTlITmZnS1orS3FZb0c4ZGI0eGo0cC9taDhKTmNlRmllRytXTUZLVXF5THN4VWRjeFkzUWxBWEZ1K1loWjg2VGEzWXY5VWNCdFRTMFBNQW0yRjhXZlMzbHFNbWNmV3lvR2RlZGlUQWxTZGJhOTZlamxXZU1RZEwwY3ZabXMzNmIvczQ3NkxOUVlrZFN1SWRxb1JSdk1DSlRFYzVwc2YzZk01V2F6RFJtWUx5WnpUOCtMc01hSDU3ODYwUzE2RVpRQ29vZkk0QWRvY1VneUZSMDgxTXpmL25GQnFPd2w0RFl0ZDZNYWp3Q05wemc2Vm1qbk9jTmhYMzZLOUxWRGN2VzdSQ2w4UmRHU21MRzFmejdqb0VGWFBnYVRvUHVGWFpjMjNmd1gxVHBvNW5venRWOUdYR1hXZ1Bjb0NZd3RRU0l6QndQa0s4eDdnOEV1cTBhWnFETmdxNFZmQkVSeXQrQi8zWlJsMEJhdTRDQ3BCQUt1YWRWUWRxMlI5N0psNHJ4YjgwY05UOU1GejNSdVQrakNnVGdqdnIyUmV6NkxjNitMeW9HbVBxcmI2VHYrTm9DMWN2ckxHb3dzQzdqZnFEUjVUWWdXU05VZ2xsc3pmZlRRTFYyOTV2cEpRaitNd3c2a1N2QjFrNVpQZFR5MjZDZmYiLCJtYWMiOiI0NDU1NGZmYjBlZWE4OWJmMmUzZTkzZTA0OTA1ODk2YzgxMWIwMmE5ZjE1NzkwOGEyZDA2MDQxY2UyYTE1MTFjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-488595195\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-526374716 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">COI6IIrAbucReABHBN7o3ZaIzRbwBCI1gfwrcfCo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-526374716\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-235903027 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:40:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ijh3ZHhkRk56bUtPOXRIT0hPYXJ3ZFE9PSIsInZhbHVlIjoibjNjZVVjeldyQWhrSzRMUDVERC83eXF5Z2Nvc1B5cnJCVk1uTCtZb0lEbFkyY2pEVDVPZmY2VjQyMW95ak1sSEhnLzljc0M2OS9uOUt1cmdGWWVYdFcxei9vd0x2YnVmTFRob0FhRE9KSjg3Ynk1UU9WNG9oL016anFMVHZrRlZESDNsU0cyS0VvTlJNZFZOYmtoUHQ1OG9CR2t0TG9rdGhQOTBXZGJrV1VlWGdLTTYzNERHWHR4ZXdTT05PVjNYekd0Q0tFSDRON09pd0VwaVJpeXlYTkNHMGVvc01FOXB6MnN2bGwwN2dSV0p1VU5KRjJRMmRRWXVCcGt0M2xKbTUwa2FnUFBjQlZmMEpxSUdUZ3A5TTZxK2RSZzB0UEFmOWlsUUwyS0ZBWlFXSTZHcWJYUjlJSG1xMXp4SmJ0UEJXVmM5a3Frakk2VGNReFVvT2JwdmxTZ1lHdHRkNDZHb2JsY3VNT2NiTVhiR0E1Q29BbUpzVlU2eVpmTzd3TmNMRlJCbG1CemRib2trYS8yVEZGMVdnQ2NIV1Azbm9ETUxDOS9TSWJTTU5FTUlOOXdSK2VGKzZHSVF3YUVBYytBNzNDSUtXbjBnQkFWYUxnZFFwYm00Y2pZcVhYSENrSGdkbW1LQnJLeXRKTHVlTUpHS040NHNyRkx2OTUrckp6MHUiLCJtYWMiOiI3MjY2MjdmZDZiNjJmNWUyM2M3YTc3OWRiZjFlZWE1YzIxYzVkMDU2ZDUzYjNhYTcyYzQxZDE1MDU4ZmUxMjRlIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:40:02 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ijc2SlYyUnk3b2x5RlJVby9lYWw2ZlE9PSIsInZhbHVlIjoiMFArQWluUVBMcjRRUW5CUFAzQmZpZ2tONi9FRmhiZFpYL1pScVllb1NjSUtCeW9qKzl5Um4rSVlqczJadEU2VWtMMmkxRnVoVHlFTXljbXE3QmphWnhwbXVYWS9BbTc5RzlFWFpBZlU5bnRaU1ZvcnZrWVFvcDJhaktibG8weWswQU9Edlkzcm1tOTRyWFVoMDBPdnVzd0tLWktXdzE1MVpXU3dHaThVeU1sUW44WnFiYUtjaHN6Q0REVjVta3hSRGc5N3dFMkRiUytXT3QyNEw1WmZQVXM5RlpRWXF6b0tWNmc2ZCtycEJZeWdOSVE4VTkrQnJveVU3bzZLdy91R2RxNmRyb2l0MmJaQlhWWk8yT3FLc01WeTBFNWVtV29DbytvdWRQTFFvZ1dBdUl0QWIyNDFBRGFCWkVUYkc0Yk1GallXclhzc3Q3OGpDT05oTlJTTGtWMVJnbklDcjBWZjBLMDU0TU5jMFpSbXh2ZGZzTlA0REVNMnVKOU9RMjExY29URFI2UVovUW1UblBZWE92WTBka2d0YVZXQlV2THJ3eUtqNlAwb0lMSWlIQ001SEFMVlJkWnVCVHFJZkdHWHEva0ZCbk95bWtDVGl5RkhpSEZSNmw4UEIxSkFsTVNYRWg3L1hBM1dEZDk1eTY1Zzk0NEVjbE12eEcxcFVxOU0iLCJtYWMiOiIzZjg3NzA3NDhhN2M4ZWRhOWE0NGFkNDlkNWE4N2E2YjBmYmIyODIzN2Y2YjBiODViMzFhMzFhNDU1NWRiODFhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:40:02 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ijh3ZHhkRk56bUtPOXRIT0hPYXJ3ZFE9PSIsInZhbHVlIjoibjNjZVVjeldyQWhrSzRMUDVERC83eXF5Z2Nvc1B5cnJCVk1uTCtZb0lEbFkyY2pEVDVPZmY2VjQyMW95ak1sSEhnLzljc0M2OS9uOUt1cmdGWWVYdFcxei9vd0x2YnVmTFRob0FhRE9KSjg3Ynk1UU9WNG9oL016anFMVHZrRlZESDNsU0cyS0VvTlJNZFZOYmtoUHQ1OG9CR2t0TG9rdGhQOTBXZGJrV1VlWGdLTTYzNERHWHR4ZXdTT05PVjNYekd0Q0tFSDRON09pd0VwaVJpeXlYTkNHMGVvc01FOXB6MnN2bGwwN2dSV0p1VU5KRjJRMmRRWXVCcGt0M2xKbTUwa2FnUFBjQlZmMEpxSUdUZ3A5TTZxK2RSZzB0UEFmOWlsUUwyS0ZBWlFXSTZHcWJYUjlJSG1xMXp4SmJ0UEJXVmM5a3Frakk2VGNReFVvT2JwdmxTZ1lHdHRkNDZHb2JsY3VNT2NiTVhiR0E1Q29BbUpzVlU2eVpmTzd3TmNMRlJCbG1CemRib2trYS8yVEZGMVdnQ2NIV1Azbm9ETUxDOS9TSWJTTU5FTUlOOXdSK2VGKzZHSVF3YUVBYytBNzNDSUtXbjBnQkFWYUxnZFFwYm00Y2pZcVhYSENrSGdkbW1LQnJLeXRKTHVlTUpHS040NHNyRkx2OTUrckp6MHUiLCJtYWMiOiI3MjY2MjdmZDZiNjJmNWUyM2M3YTc3OWRiZjFlZWE1YzIxYzVkMDU2ZDUzYjNhYTcyYzQxZDE1MDU4ZmUxMjRlIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:40:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ijc2SlYyUnk3b2x5RlJVby9lYWw2ZlE9PSIsInZhbHVlIjoiMFArQWluUVBMcjRRUW5CUFAzQmZpZ2tONi9FRmhiZFpYL1pScVllb1NjSUtCeW9qKzl5Um4rSVlqczJadEU2VWtMMmkxRnVoVHlFTXljbXE3QmphWnhwbXVYWS9BbTc5RzlFWFpBZlU5bnRaU1ZvcnZrWVFvcDJhaktibG8weWswQU9Edlkzcm1tOTRyWFVoMDBPdnVzd0tLWktXdzE1MVpXU3dHaThVeU1sUW44WnFiYUtjaHN6Q0REVjVta3hSRGc5N3dFMkRiUytXT3QyNEw1WmZQVXM5RlpRWXF6b0tWNmc2ZCtycEJZeWdOSVE4VTkrQnJveVU3bzZLdy91R2RxNmRyb2l0MmJaQlhWWk8yT3FLc01WeTBFNWVtV29DbytvdWRQTFFvZ1dBdUl0QWIyNDFBRGFCWkVUYkc0Yk1GallXclhzc3Q3OGpDT05oTlJTTGtWMVJnbklDcjBWZjBLMDU0TU5jMFpSbXh2ZGZzTlA0REVNMnVKOU9RMjExY29URFI2UVovUW1UblBZWE92WTBka2d0YVZXQlV2THJ3eUtqNlAwb0lMSWlIQ001SEFMVlJkWnVCVHFJZkdHWHEva0ZCbk95bWtDVGl5RkhpSEZSNmw4UEIxSkFsTVNYRWg3L1hBM1dEZDk1eTY1Zzk0NEVjbE12eEcxcFVxOU0iLCJtYWMiOiIzZjg3NzA3NDhhN2M4ZWRhOWE0NGFkNDlkNWE4N2E2YjBmYmIyODIzN2Y2YjBiODViMzFhMzFhNDU1NWRiODFhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:40:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-235903027\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1782569686 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6ImNiYzkwakxlbFl5M0hmWXd6cnhmZWc9PSIsInZhbHVlIjoiaWxjU3hBK1J4cjlkSXBwVGZ2eXNWZz09IiwibWFjIjoiYmJkM2U2NzNiY2EzMmQyY2VlY2YyNjdhNmQxNjY4ZGM2NTgxOWJlMmVmMWJjZWUzNDYzYzA3MTViZjAzYzRhNCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1782569686\", {\"maxDepth\":0})</script>\n"}}