<?php

namespace WhichBrowser\Data;

DeviceModels::$BREW_INDEX = array (
  '@AX' => 
  array (
    0 => 'AX8575',
  ),
  '@CD' => 
  array (
    0 => 'CDM8992',
    1 => 'CDM8999',
  ),
  '@CO' => 
  array (
    0 => 'Coolpad D508',
    1 => 'Coolpad D510',
    2 => 'Coolpad E600',
  ),
  '@E4' => 
  array (
    0 => 'E4255',
  ),
  '@EX' => 
  array (
    0 => 'EX200!',
  ),
  '@HS' => 
  array (
    0 => 'HS-E316!',
  ),
  '@HU' => 
  array (
    0 => 'HUAWEI U526',
    1 => 'HUAWEI U528',
    2 => 'HUAWEI U2801!',
    3 => 'HUAWEI U5200!',
    4 => 'HUAWEI U5300!',
    5 => 'HUAWEI U5310!',
    6 => 'HUAWEI U6150!',
  ),
  '@LG' => 
  array (
    0 => 'LG272',
    1 => 'LG510',
    2 => 'LGE AX840',
    3 => 'LGE LG700',
    4 => 'LGE LG840',
    5 => 'LGE UX700',
    6 => 'LGE UX840',
    7 => 'LGE VX11K',
    8 => 'LGE VX8575',
    9 => 'LGE VX9700',
  ),
  '@LN' => 
  array (
    0 => 'LN240',
    1 => 'LN510',
  ),
  '@LX' => 
  array (
    0 => 'LX610',
  ),
  '@M2' => 
  array (
    0 => 'M260!',
  ),
  '@M3' => 
  array (
    0 => 'M350!',
    1 => 'M370!',
    2 => 'M380!',
    3 => 'M390!',
  ),
  '@M5' => 
  array (
    0 => 'M550!',
    1 => 'M560!',
  ),
  '@NO' => 
  array (
    0 => 'NOKIA 7705',
  ),
  '@P5' => 
  array (
    0 => 'P5000',
  ),
  '@P6' => 
  array (
    0 => 'P6020',
    1 => 'P6030',
  ),
  '@PA' => 
  array (
    0 => 'Pantech CDM8992!',
    1 => 'Pantech CDM8999!',
    2 => 'Pantech TXT8045!',
  ),
  '@PL' => 
  array (
    0 => 'PLS M330',
  ),
  '@S2' => 
  array (
    0 => 'S2151!',
  ),
  '@SA' => 
  array (
    0 => 'sam-r631',
    1 => 'sam-r640',
    2 => 'sam-r900',
  ),
  '@SC' => 
  array (
    0 => 'SCH-B309!',
    1 => 'SCH-F839',
    2 => 'SCH-M519!',
    3 => 'SCH-S559!',
    4 => 'SCH-S579!',
    5 => 'SCH-U380!',
    6 => 'SCH-U485',
    7 => 'SCH-U640!',
    8 => 'SCH-U660!',
    9 => 'SCH-U680!',
    10 => 'SCH-U750!',
    11 => 'SCH-U820!',
    12 => 'SCH-U960!',
    13 => 'SCH-W709!',
    14 => 'SCH-W799!',
    15 => 'SCP-3810',
    16 => 'SCP3810',
    17 => 'SCP-6750',
    18 => 'SCP6760',
    19 => 'SCP-6760',
    20 => 'SCP6780',
  ),
  '@SG' => 
  array (
    0 => 'SGH-A937!',
    1 => 'SGH-A947!',
  ),
  '@SM' => 
  array (
    0 => 'SM-B690V',
  ),
  '@SP' => 
  array (
    0 => 'SPH M330',
    1 => 'SPH-M570',
    2 => 'Sprint M850',
  ),
  '@TX' => 
  array (
    0 => 'TXT8045',
  ),
  '@VN' => 
  array (
    0 => 'VN170!',
    1 => 'VN250!',
    2 => 'VN271!',
    3 => 'VN280!',
    4 => 'VN360!',
    5 => 'VN370!',
    6 => 'VN530',
  ),
  '@VX' => 
  array (
    0 => 'VX5600!',
    1 => 'VX9200!',
    2 => 'VX9600!',
    3 => 'VX11000!',
  ),
  '@ZT' => 
  array (
    0 => 'ZTE F-450!',
    1 => 'ZTE R516!',
    2 => 'ZTE R518!',
  ),
);
