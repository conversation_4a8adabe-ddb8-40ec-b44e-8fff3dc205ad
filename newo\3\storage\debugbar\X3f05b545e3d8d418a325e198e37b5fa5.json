{"__meta": {"id": "X3f05b545e3d8d418a325e198e37b5fa5", "datetime": "2025-06-08 16:17:55", "utime": **********.680964, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.074825, "end": **********.680983, "duration": 0.6061580181121826, "duration_str": "606ms", "measures": [{"label": "Booting", "start": **********.074825, "relative_start": 0, "end": **********.60722, "relative_end": **********.60722, "duration": 0.5323948860168457, "duration_str": "532ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.607231, "relative_start": 0.5324058532714844, "end": **********.680986, "relative_end": 2.86102294921875e-06, "duration": 0.07375502586364746, "duration_str": "73.76ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45140048, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00766, "accumulated_duration_str": "7.66ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.64378, "duration": 0.00598, "duration_str": "5.98ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 78.068}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.661947, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 78.068, "width_percent": 10.836}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (17) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.670339, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 88.903, "width_percent": 11.097}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/eyJpdiI6IkFRYWh6YzFtM3ZKUitTR0xaMzNPL3c9PSIsInZhbHVlIjoiTTQ3YTVSVCs2WjlicXFPditOMVY3dz09IiwibWFjIjoiOWZhOWU0ZTY4ZmJmMGYwM2JhN2E5OWZiNDNlMzZjYjg2MTlkZjU4Yjk2NWEwNmE2MDVmMjkwMmY4NGY0ZmRiMSIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-445112435 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-445112435\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-535273686 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-535273686\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1165287039 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1165287039\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-789071469 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6IkFRYWh6YzFtM3ZKUitTR0xaMzNPL3c9PSIsInZhbHVlIjoiTTQ3YTVSVCs2WjlicXFPditOMVY3dz09IiwibWFjIjoiOWZhOWU0ZTY4ZmJmMGYwM2JhN2E5OWZiNDNlMzZjYjg2MTlkZjU4Yjk2NWEwNmE2MDVmMjkwMmY4NGY0ZmRiMSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwl%7C0%7C1982; _clsk=1y4k55%7C1749399469579%7C2%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjEzU0VSZVZ2Z0JTT1hjL3R3L1Y0QXc9PSIsInZhbHVlIjoiSUtMSkdSWnVURmMyejVwRlJGTTc2cENMU3N4aWhHQnk4bUN4bSsxNHhOY3BQZGluMUxnc29Ud1RXSzNNZWdvNDM5VFFqeDJ4ZmJ5RnVSTm82RzBFb1BZaitzQWJ3Uyt3cjFqNldOcWRqSTJ0UXkySzhHWnVSa3RLK25YQjZtVlhOUnBpMlhqZHpyQklKaHN1QVFHeVgrZFkwRnpuMVlPd2hTdEVSRUxYcHRBa3BsVDJIeW9xaUVsUEdmeU14NnkzYU96YnNrRkZEd2lIYndkNFFBbW9veTBFRk04aklsVTc4YVo4eHJKR25XU09hYVVXc2s5L01tbmExK0l5c3ExZFRxUVFhanBmemJldzJsY0ppMERpSThRcEFHcXlMcU5FaTg0czg0bkFzeVhSRDJtR0ZSSHVIcG11L2x3TVBvL3pEUGhDMnVCOThtSjF0VDE3cGUzSVBhTmhwaEN4ZndjdERhejFWb1JNSGlvYWpTd05wZkQwams3VTRsaEE3S3FicWVxdE02WU52QldQSEtHbFhadFo2OU9MVWhlTzBTaE42YzFYbHBhK3dqNmhrbWg3VEVQbmhJNVEreGMwV21sZEdKaDRML2VYV2VjUzZvRE5Sa1pKMFFLcm9EYmZRNm9QcENXVFN3NUlTRE5rczQ5YWRxTmM1SjNYZ1hQTTNlQjEiLCJtYWMiOiI2ZTk2YmVlMjJjNDRlZjczNjk1ZmIyYzc2YTg2OTJkYjNhNWY1MWUxMzFlOTllYTAzOGU4MjFhMTZhNzljN2NmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IndOU1VLZitLL2pqN0RKWGN4M0E3ZkE9PSIsInZhbHVlIjoiTVJWYkFQREE0MmxUUGxaS205eVdkZjlSMGo0MHJpVWV1Y0dYRTkxd1NYZGlOVXNoajBMMGJZS2FydUlJYnpwT1MrQVN0KytoVlRrN3VRSnJRY0h0MFhxYTBuMjl0dDBvcUZway9NUXVnMDZnNTVaZVBIRkpDZHBDYk9hV2tucDBNZzNTdWk5cnJPU3JVMlI5bFRZM0dESGxkVVQ1aDAvOGZjTk5CMUVNTyt6OFowRkhlNk9ocFVrd1RXcDVCVDUvUEFKclVwVExYYmVONldocGxLZ3VOUUUrNC9iOXJrMUFzZHhWUGJBM1dLdUh2NlVOV3RzQ2w2ZjltOVdiRDZRQm1qcy9zczk5RmtSWEQySWd4ellrYzczeXBpQVRlWXZRK01USmlnczdJTUgwUW9aNW9WNndpMm95UVh1KzZsRVBDOTQxUVo4bCtGazhZbVE5OXdYaXoxK2ZERFZpYklmU29NcnM1L3pIR0xnaUxkR0Y0MDN5aHVUbVBnVlVueEZSVWdOdWdlUW50L2pjd2hPbVgyblJPVzBOZy9UVjhTZW1zV1BCdmdsNUp3YVpOZUEwV3JYNVJOa0dLYlJ6VVRKQW4rMnd5endheGlhZkhzUi9pa1hwOGMxa2VSbUY2cVgzNHE1bFlmSUpvaVNYblQyM25qZGNDKytmL2hWT3pRaEEiLCJtYWMiOiI5ZWNlNzZjMmYzMzY3MDJjYWY3MTQ2ZmFkNDdkZDE0NzQ1ZWM4ZTE0ZjcyYmY5OTg5NDI0NDhmZTM4MzBlMmViIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-789071469\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-204067997 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">COI6IIrAbucReABHBN7o3ZaIzRbwBCI1gfwrcfCo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-204067997\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1774688027 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 16:17:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik9JbjdaenJtalBzNjlMRDNObGRiakE9PSIsInZhbHVlIjoicGZoakVaSk5HK2xKL1diT0VpS1krNTVXN2RlNFFId0s4NVdaZUdLcTllY0lXS240ZFo1NjFabHZZZnFFL1NSeWxvc3F1SUxXczJMNTZkd09kSW9JYkNvQWFaelNJWEN4OXJrM2F4bG02WTN4NmRCT0ovUFA5L1pKeS9ReDJ4eDVvbTVUeDNMZnB3NVc3cjcxMUFPd1c0TzI0aVhoZStKeWwrZkVEWGlUbk8xcXFRWDFSRE56QXRZeEpLdWpRU1YyUm5qMzFTcVRKZU01UStES1J1TDRWNFZWQ0RnM0hBSm5xbSswQVBzTDd6SWxMQko3TERrUHgybzBBNXFMZHZPOHFCS1FubGJTbmVQYm5LdjNkem12MXY4aCswandJWnRhd3l5Q21HVkhWWVkrTEpNT2grQ2N2S3lsUVpvRHhhQjhDTlFiS2hSOGJ0OVQ4SWhjVzNBbWlQYURlRSt4eHBSU2lJTnFodUJ4VjA5SzRVeHZ4azNmQTJFZ1lObFUrWEhDb01PTzVLM2c0dWt4bXZwNHM0VVF3MFRRT3orVnFHZlB6dDBWT0J5N2d6YjBTekVOaE51eENudHVEcnRtOFNiRHlTQ0ZnQkE4ei9KNmJRZXpGN0hld2QwNnhVa2o3eFJMbThGWFhVVy9TWXdUcVlncm5vdlRzbFJ4bWhsVzNWTTQiLCJtYWMiOiI2MTY1MmJlM2E3YjU2ZGY1NDNmMWEzYWFlMTI5OTBiZjZiMzJlNGRjMTNmMjkxYmIwMmQ2MTI2NWJmNmQ0NWM0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 18:17:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkpTRy9NOUNFL01LK0R6Wm1HWnVMNmc9PSIsInZhbHVlIjoiTno1eU5vNVhWVXY0WnQ0ZTBRS2MxZERlaGI4Z3U1cUg5bVloaUlHdXN6RmpnRDN5SUxUZWFkY2s3bFJmd3dJd2FVSGJFUURkbDJLZkQxUGg2WWJLQkhWTTFYc2xkaVZuVWZ1ZDkwMFJ4VisvU2svemZPZ09JQ2s5VGNIdkFzdk1MT3FmbnZrQWJwTDJqOExWMHlkVVhITlZwOTNQSTdkb3U0RnBlOTVtV2UrTXY0TSs2RGNEOU9uc2NYRjBlaE9icTY3SUVwV1RuRXhUb1dnNUhQbnVaRkN4Y3c1YVkrckwxbHMwcFZ3VEs1UDY0b1NNcGYzTzZEczhPcHNYdXF0aGFOZ3o1K0lNcUlmTFI4Slh0c04yUWEyVHN0NkNLT2R4Zi9sMmpLTHY4WVR6UXNIWi9kZnFRZDB1WlVzQXBNMFROT1RaZlJRUHpWdlYra0U0MnJYVnpXQ21UU2lvaWtDNUdiRm5qUXlyMzFlcFhaNTZXdVFieThWRXZraHNUUXkrU25CcHhlVGd0SFdxa2dDUVpGbVp0dEp5L2pLQzhaVWx3TWRoTENER1kwTVFPdXpZTzVqSlpVUGc5enVuSFYxR0xyWTZHb0E3cFVNZ3JWbkY4VjMzeGxISjg1WFZldTN6a0hKZ0hkRDRXMjVBdFltSDczSGxSbWpqK0pXckNhVHAiLCJtYWMiOiI3NGQ2MTEwYjllMDNjMzA5YTkyYmFmZTljNmZhNTRhMjRlNDFmZGEyMzNmODQ5MGIwNTNmMzcxODQwNWFmYTI4IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 18:17:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik9JbjdaenJtalBzNjlMRDNObGRiakE9PSIsInZhbHVlIjoicGZoakVaSk5HK2xKL1diT0VpS1krNTVXN2RlNFFId0s4NVdaZUdLcTllY0lXS240ZFo1NjFabHZZZnFFL1NSeWxvc3F1SUxXczJMNTZkd09kSW9JYkNvQWFaelNJWEN4OXJrM2F4bG02WTN4NmRCT0ovUFA5L1pKeS9ReDJ4eDVvbTVUeDNMZnB3NVc3cjcxMUFPd1c0TzI0aVhoZStKeWwrZkVEWGlUbk8xcXFRWDFSRE56QXRZeEpLdWpRU1YyUm5qMzFTcVRKZU01UStES1J1TDRWNFZWQ0RnM0hBSm5xbSswQVBzTDd6SWxMQko3TERrUHgybzBBNXFMZHZPOHFCS1FubGJTbmVQYm5LdjNkem12MXY4aCswandJWnRhd3l5Q21HVkhWWVkrTEpNT2grQ2N2S3lsUVpvRHhhQjhDTlFiS2hSOGJ0OVQ4SWhjVzNBbWlQYURlRSt4eHBSU2lJTnFodUJ4VjA5SzRVeHZ4azNmQTJFZ1lObFUrWEhDb01PTzVLM2c0dWt4bXZwNHM0VVF3MFRRT3orVnFHZlB6dDBWT0J5N2d6YjBTekVOaE51eENudHVEcnRtOFNiRHlTQ0ZnQkE4ei9KNmJRZXpGN0hld2QwNnhVa2o3eFJMbThGWFhVVy9TWXdUcVlncm5vdlRzbFJ4bWhsVzNWTTQiLCJtYWMiOiI2MTY1MmJlM2E3YjU2ZGY1NDNmMWEzYWFlMTI5OTBiZjZiMzJlNGRjMTNmMjkxYmIwMmQ2MTI2NWJmNmQ0NWM0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 18:17:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkpTRy9NOUNFL01LK0R6Wm1HWnVMNmc9PSIsInZhbHVlIjoiTno1eU5vNVhWVXY0WnQ0ZTBRS2MxZERlaGI4Z3U1cUg5bVloaUlHdXN6RmpnRDN5SUxUZWFkY2s3bFJmd3dJd2FVSGJFUURkbDJLZkQxUGg2WWJLQkhWTTFYc2xkaVZuVWZ1ZDkwMFJ4VisvU2svemZPZ09JQ2s5VGNIdkFzdk1MT3FmbnZrQWJwTDJqOExWMHlkVVhITlZwOTNQSTdkb3U0RnBlOTVtV2UrTXY0TSs2RGNEOU9uc2NYRjBlaE9icTY3SUVwV1RuRXhUb1dnNUhQbnVaRkN4Y3c1YVkrckwxbHMwcFZ3VEs1UDY0b1NNcGYzTzZEczhPcHNYdXF0aGFOZ3o1K0lNcUlmTFI4Slh0c04yUWEyVHN0NkNLT2R4Zi9sMmpLTHY4WVR6UXNIWi9kZnFRZDB1WlVzQXBNMFROT1RaZlJRUHpWdlYra0U0MnJYVnpXQ21UU2lvaWtDNUdiRm5qUXlyMzFlcFhaNTZXdVFieThWRXZraHNUUXkrU25CcHhlVGd0SFdxa2dDUVpGbVp0dEp5L2pLQzhaVWx3TWRoTENER1kwTVFPdXpZTzVqSlpVUGc5enVuSFYxR0xyWTZHb0E3cFVNZ3JWbkY4VjMzeGxISjg1WFZldTN6a0hKZ0hkRDRXMjVBdFltSDczSGxSbWpqK0pXckNhVHAiLCJtYWMiOiI3NGQ2MTEwYjllMDNjMzA5YTkyYmFmZTljNmZhNTRhMjRlNDFmZGEyMzNmODQ5MGIwNTNmMzcxODQwNWFmYTI4IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 18:17:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1774688027\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-445610866 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6IkFRYWh6YzFtM3ZKUitTR0xaMzNPL3c9PSIsInZhbHVlIjoiTTQ3YTVSVCs2WjlicXFPditOMVY3dz09IiwibWFjIjoiOWZhOWU0ZTY4ZmJmMGYwM2JhN2E5OWZiNDNlMzZjYjg2MTlkZjU4Yjk2NWEwNmE2MDVmMjkwMmY4NGY0ZmRiMSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-445610866\", {\"maxDepth\":0})</script>\n"}}