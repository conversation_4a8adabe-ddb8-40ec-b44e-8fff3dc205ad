language: php

php:
  - 8.2
  - 8.1
  - 8.0

cache:
  directories:
    - $HOME/.composer

before_install:
  - composer self-update
  - composer clear-cache

install:
  - composer update --no-interaction --no-ansi --optimize-autoloader --prefer-dist

before_script:
  - export XDEBUG_MODE=coverage

script:
  - php -derror_reporting=32759 vendor/bin/phpunit --configuration phpunit.xml.dist --no-coverage

# after_success:
#   - travis_retry php vendor/bin/php-coveralls
