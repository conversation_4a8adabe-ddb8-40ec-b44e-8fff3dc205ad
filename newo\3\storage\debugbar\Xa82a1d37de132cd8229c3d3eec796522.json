{"__meta": {"id": "Xa82a1d37de132cd8229c3d3eec796522", "datetime": "2025-06-08 15:40:12", "utime": **********.135588, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749397211.42848, "end": **********.135609, "duration": 0.7071290016174316, "duration_str": "707ms", "measures": [{"label": "Booting", "start": 1749397211.42848, "relative_start": 0, "end": **********.043126, "relative_end": **********.043126, "duration": 0.6146461963653564, "duration_str": "615ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.043137, "relative_start": 0.6146571636199951, "end": **********.135611, "relative_end": 2.1457672119140625e-06, "duration": 0.09247398376464844, "duration_str": "92.47ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45137696, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.018760000000000002, "accumulated_duration_str": "18.76ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0832891, "duration": 0.017, "duration_str": "17ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 90.618}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.113139, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 90.618, "width_percent": 3.625}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (17) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.122792, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 94.243, "width_percent": 5.757}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/eyJpdiI6ImNiYzkwakxlbFl5M0hmWXd6cnhmZWc9PSIsInZhbHVlIjoiaWxjU3hBK1J4cjlkSXBwVGZ2eXNWZz09IiwibWFjIjoiYmJkM2U2NzNiY2EzMmQyY2VlY2YyNjdhNmQxNjY4ZGM2NTgxOWJlMmVmMWJjZWUzNDYzYzA3MTViZjAzYzRhNCIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-641296296 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-641296296\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-676581065 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-676581065\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-314685547 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-314685547\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1866508273 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6ImNiYzkwakxlbFl5M0hmWXd6cnhmZWc9PSIsInZhbHVlIjoiaWxjU3hBK1J4cjlkSXBwVGZ2eXNWZz09IiwibWFjIjoiYmJkM2U2NzNiY2EzMmQyY2VlY2YyNjdhNmQxNjY4ZGM2NTgxOWJlMmVmMWJjZWUzNDYzYzA3MTViZjAzYzRhNCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwl%7C0%7C1982; _clsk=o6pnpa%7C1749397202299%7C7%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlJ3eGxodnVkS3JjOXVSeXp0ZlVSZFE9PSIsInZhbHVlIjoiRm1WWmc2cldUOWw4dW1SRDNlaHRCUVdBYW9OZ3Uya2tMOEErcThxVzlNZkdQQzRMODczK0VtdmNsWUxjNDRneUNvdzdleU5rZDRUU2dNT3RzR05PSXBwYUhNSThkM1p3V2cvYWlpMFJ4ZHpUUEUwVlExYlQ4a0gwb3lYYU5uLzRzNXZjNUY5dTBvT045UGtBZEtYUkdnWExmT0Y0SUF2VkplK3ZxTG5VTDlUWmdwTkJid3BLc3RMSnk5eml6YUJVMGFJclB4Zi9PQSsrRjExZnZmbjZPRVk4akNRMDQxMUdtc0psN3JFK3c0REFibjRndmhiUWNRSEpIbGoyUUE1Szd6MTM1OW9sdGF0d0duYlV5TktSS3dyZnh4M3hWSnRyU2kzMmlrZnpraWlxemJJTVMzYm9jN1hLV1BTMk9PR1lMbitkVjd2b0taZXh2NVJGYlZpSjlkWlYyNkJuTGkreWJLaGY5eWUrNXRPMGJNZ2NjeWlpMWJQWUdDTFhpWmlUNUJwMyswNnQ3UStEOCt5bFp5U3JLcmI2MTRCOHRybTR4cUJWeW8ycEhPMDRHS0lGa2k4NXRIRjl2MDRZRTQvclRXR0pwa3Y2RTIwVTJ6MVYyVDRYMXpKSVlpWFBiUXJEV3REMkJZOGI5TnQycFhvZkJhT3BPbUNnalJrejhsd2IiLCJtYWMiOiJhZTk0ODA4NmUzOWM3OWNlZDcxOWRkZjFhZTYzZTc4YzE0YjkzNzM4MjgzYTVlNTgyNjllN2UyMTAwOGEzODFkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkpyOGIwN3hLNkl0WkthWFArOFk4RWc9PSIsInZhbHVlIjoieitPSDhWbWVjdFNBZTNBanhHR0RXY0VJZmtoUGRReHlHd3JYWWdrTEZBNktvTmdZTUpKblYzdzJHVDZ0d2JrakZGdVNaQUQ3Q2tJUjVlMVAwUnZQOEtFYmN6VE02V2xzK05GNkwxRmxaeHBFcHY3QTN0WlQxWm1VQ0R5VFBZZFFFRzJhWmhSdzBDMklTblFtODhCd2NKaklkQzQ3OG5kWDVvQytZQ3J6eXNqOUVwSFNKcTg2TlVud0t3a01pNUI0ZExxdzhZTi9hWG1sQTB2dG1JNUJHQzJ4c21mWWpOdFpMcWk5ZHBxRzVOU0FCTld5ekhXNU1sZDV4bkhLdmcvdHU5NGt5SzAxcWliS3JYVjBMR1pTUGt6VmZsWGJWWTBDTUM4NlpjVFBMekJMS1RidzNqdTVDaS9mRTlYMTVLMW1YbERPbFRCSVMrVS9STUs4a3RROHByOVY5aE9JUWFXU2ttMWJtRlgyRHV0N3FUMDZGVEJnUVJDS2xkeXptN05taWVid2RxaTl4KzdlM1FCQTZjKytzSG9JQ1RBeXI5cnFNLzRqTlhncWZtNnFOSW5vZ3BFTTZVajNQSTcxdUNLcG1Ta2xYWHpGSnNOd0NzbnZ1NW5rb0hvejI2U0JmaVRrbVhyUFU2bWpnUVR4Nk1OVi9Dem9qR21QN1JoYXdxcWoiLCJtYWMiOiJiYjQ5ODU5YWQ5MDk1MjNmODNhMWE3ZjViYzQ4YTAxZWViYzM3ZWFkNThhOGE5NGUyM2I2YzExYmNiMDJiZjgyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1866508273\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1077735191 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">COI6IIrAbucReABHBN7o3ZaIzRbwBCI1gfwrcfCo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1077735191\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-488792956 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:40:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjB3UzJwei9tYk9ZYzBkOVFSM3R2Qnc9PSIsInZhbHVlIjoib3psR28wL2krUjQ5QmNjMEtUU3RxQ2lLVllmb2RBQ2M4aHZzYUxxeVJNTVZnTFJXSlFZTUtuUnJPVnFDbUVMRStKNi9WNmNuNll5ODUzajV2bS9uUXNqbTd1Mkt2U2QxZFg2VkpzTTI0eEhPVUNtRUR3RklMRExXTk1pT1REaXhwcFB4eXVoQThhR2JtR21jSHJkSnhvZUxiQStWK3pnZlBZNXBqUUVaOVQxc2VscG5Uc0NEWk1KZ0JNVmRxdlAvUms3WG5vcFBXN3RUYUFuZWxrZEN2MTd4YklhNGNScldjUS9EWVBBR0RpakJ4MXU2M1NFODJ2dGZRMzkrZ2xmZC9jNVdGM1NaWGR6Z2F6Vk1CeUxYL3YzWHRlUmNVUzR0RXozQ01PQVhvTDZya1g3S05DVElISzY4UDJ2TmxRN1NsMVIxUnRRQmFjSTBaMFdNRHJ5LzhlUS9qSTUrd0ZtcXJsMmlOYnJ3amdMZkVLV3FpWTdEVGloZHRKQUhWbmlDbWxvTFVOMHA5RXVpdWdDR2hWT2dvMlcybmh1a2svRnFkUHRmMUlzWjZKRjhkT3VHSGhXK3NZbzNRelJyWElmTy93VnZSK3pRdWpDRTNrbUFIM01rUEVXRVdKUnByd2tTelM2SFJXbStmaHBRMjJDTzFwSTNOdnRadzRpeFVqTHoiLCJtYWMiOiI5M2Q5MTBiMzYzMzQ1ZjJjNWViMzYzOWE5Yzk2ZGU3NjQ2ZGY0N2NiZjBmNmJiMTZkNDU2MjUwNThlMjYxZjdmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:40:12 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjNCSDNpcHcxUFNCUXRuY25OeSt5NUE9PSIsInZhbHVlIjoiYThSdGtPU0JiUndxbnNZWjFpUUYzaTJIYTVDOVI3cWlXVVYvWG1kMXo0L2NMSzFUMW9hS3ViU2ttOHN5NUNIbms4WUs0SG1Za0I4R212dHBGSllMZ1RrVXpEcnBSL0N1b2ZyL3d3TVh5cGcwZHMzUGcwRnE3SDZhdlBLQ1FyK0g2dWlLY3RRMUg5cDR3dnZ3TElidU5xN2FUSTFScDVMUklRL2g0QTQrZ1pSUVFzTGtYWTV5UERDVjJSZUNnM1M0anZlZTdBUUFQc3dNYXVRRUVlZmt4dmFPU3VhNEJrSzBEQytHM01tTndSOTZtdTJQdk5WYjF3YmtYMjg3enVPaE1obXJtaWJLcVJRZkx5UTE3NmRtQ1pxMHhFUlgxTnBUNFhObzEzYU5FVWdKTDdnQmtZbGpiOCtoV1NIZ3h4V25ZMzJmK3ZicnUveVdNR08xYm5RTWwvamVuTEFsN3FzSEsyQlEzKzZDdldFNDN2Y0FlM0VreXpuNEQ2RmtObXlzQU95ZzhwaEhNN01NV05ETTY4RHVRbFhkZFhDbmpuc0g2VFJscVpRS2ZPQmFRYk9OZzRWWE1rV2NiSXk2WENEbjczK1VKOG5mSlNPTWVFK1FOMW8zUzZDWDZPMzFjbkwyMWMveThUV05SelhCT29CTWxhVjZ5bnlEak8wdldXRHIiLCJtYWMiOiI2NDRhZTM2NGFhN2ZhNjZjNmRhZTJhNjk4NDY1YzAzNWRhNjk0NTE3NjgyM2JkOTQzOTkwMGZiZTk5M2MwOTMwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:40:12 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjB3UzJwei9tYk9ZYzBkOVFSM3R2Qnc9PSIsInZhbHVlIjoib3psR28wL2krUjQ5QmNjMEtUU3RxQ2lLVllmb2RBQ2M4aHZzYUxxeVJNTVZnTFJXSlFZTUtuUnJPVnFDbUVMRStKNi9WNmNuNll5ODUzajV2bS9uUXNqbTd1Mkt2U2QxZFg2VkpzTTI0eEhPVUNtRUR3RklMRExXTk1pT1REaXhwcFB4eXVoQThhR2JtR21jSHJkSnhvZUxiQStWK3pnZlBZNXBqUUVaOVQxc2VscG5Uc0NEWk1KZ0JNVmRxdlAvUms3WG5vcFBXN3RUYUFuZWxrZEN2MTd4YklhNGNScldjUS9EWVBBR0RpakJ4MXU2M1NFODJ2dGZRMzkrZ2xmZC9jNVdGM1NaWGR6Z2F6Vk1CeUxYL3YzWHRlUmNVUzR0RXozQ01PQVhvTDZya1g3S05DVElISzY4UDJ2TmxRN1NsMVIxUnRRQmFjSTBaMFdNRHJ5LzhlUS9qSTUrd0ZtcXJsMmlOYnJ3amdMZkVLV3FpWTdEVGloZHRKQUhWbmlDbWxvTFVOMHA5RXVpdWdDR2hWT2dvMlcybmh1a2svRnFkUHRmMUlzWjZKRjhkT3VHSGhXK3NZbzNRelJyWElmTy93VnZSK3pRdWpDRTNrbUFIM01rUEVXRVdKUnByd2tTelM2SFJXbStmaHBRMjJDTzFwSTNOdnRadzRpeFVqTHoiLCJtYWMiOiI5M2Q5MTBiMzYzMzQ1ZjJjNWViMzYzOWE5Yzk2ZGU3NjQ2ZGY0N2NiZjBmNmJiMTZkNDU2MjUwNThlMjYxZjdmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:40:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjNCSDNpcHcxUFNCUXRuY25OeSt5NUE9PSIsInZhbHVlIjoiYThSdGtPU0JiUndxbnNZWjFpUUYzaTJIYTVDOVI3cWlXVVYvWG1kMXo0L2NMSzFUMW9hS3ViU2ttOHN5NUNIbms4WUs0SG1Za0I4R212dHBGSllMZ1RrVXpEcnBSL0N1b2ZyL3d3TVh5cGcwZHMzUGcwRnE3SDZhdlBLQ1FyK0g2dWlLY3RRMUg5cDR3dnZ3TElidU5xN2FUSTFScDVMUklRL2g0QTQrZ1pSUVFzTGtYWTV5UERDVjJSZUNnM1M0anZlZTdBUUFQc3dNYXVRRUVlZmt4dmFPU3VhNEJrSzBEQytHM01tTndSOTZtdTJQdk5WYjF3YmtYMjg3enVPaE1obXJtaWJLcVJRZkx5UTE3NmRtQ1pxMHhFUlgxTnBUNFhObzEzYU5FVWdKTDdnQmtZbGpiOCtoV1NIZ3h4V25ZMzJmK3ZicnUveVdNR08xYm5RTWwvamVuTEFsN3FzSEsyQlEzKzZDdldFNDN2Y0FlM0VreXpuNEQ2RmtObXlzQU95ZzhwaEhNN01NV05ETTY4RHVRbFhkZFhDbmpuc0g2VFJscVpRS2ZPQmFRYk9OZzRWWE1rV2NiSXk2WENEbjczK1VKOG5mSlNPTWVFK1FOMW8zUzZDWDZPMzFjbkwyMWMveThUV05SelhCT29CTWxhVjZ5bnlEak8wdldXRHIiLCJtYWMiOiI2NDRhZTM2NGFhN2ZhNjZjNmRhZTJhNjk4NDY1YzAzNWRhNjk0NTE3NjgyM2JkOTQzOTkwMGZiZTk5M2MwOTMwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:40:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-488792956\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2023722062 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6ImNiYzkwakxlbFl5M0hmWXd6cnhmZWc9PSIsInZhbHVlIjoiaWxjU3hBK1J4cjlkSXBwVGZ2eXNWZz09IiwibWFjIjoiYmJkM2U2NzNiY2EzMmQyY2VlY2YyNjdhNmQxNjY4ZGM2NTgxOWJlMmVmMWJjZWUzNDYzYzA3MTViZjAzYzRhNCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2023722062\", {\"maxDepth\":0})</script>\n"}}