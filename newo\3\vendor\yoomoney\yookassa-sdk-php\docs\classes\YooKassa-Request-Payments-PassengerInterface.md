# [YooKassa API SDK](../home.md)

# Interface: PassengerInterface
### Namespace: [\YooKassa\Request\Payments](../namespaces/yookassa-request-payments.md)
---
**Summary:**

Interface PassengerInterface.

---
### Constants
* No constants found

---
### Methods
| Visibility | Name | Flag | Summary |
| ----------:| ---- | ---- | ------- |
| public | [getFirstName()](../classes/YooKassa-Request-Payments-PassengerInterface.md#method_getFirstName) |  | Возвращает имя пассажира. |
| public | [getLastName()](../classes/YooKassa-Request-Payments-PassengerInterface.md#method_getLastName) |  | Возвращает фамилию пассажира. |
| public | [setFirstName()](../classes/YooKassa-Request-Payments-PassengerInterface.md#method_setFirstName) |  | Устанавливает имя пассажира. |
| public | [setLastName()](../classes/YooKassa-Request-Payments-PassengerInterface.md#method_setLastName) |  | Устанавливает фамилию пассажира. |

---
### Details
* File: [lib/Request/Payments/PassengerInterface.php](../../lib/Request/Payments/PassengerInterface.php)
* Package: \YooKassa\Request
* See Also:
  * [](https://yookassa.ru/developers/api)

---
### Tags
| Tag | Version | Description |
| --- | ------- | ----------- |
| category |  | Class |
| author |  | <EMAIL> |
| property |  | Имя пассажира |
| property |  | Имя пассажира |
| property |  | Фамилия пассажира |
| property |  | Фамилия пассажира |

---
## Methods
<a name="method_getFirstName" class="anchor"></a>
#### public getFirstName() : ?string

```php
public getFirstName() : ?string
```

**Summary**

Возвращает имя пассажира.

**Details:**
* Inherited From: [\YooKassa\Request\Payments\PassengerInterface](../classes/YooKassa-Request-Payments-PassengerInterface.md)

**Returns:** ?string - 


<a name="method_setFirstName" class="anchor"></a>
#### public setFirstName() : self

```php
public setFirstName(string|null $value) : self
```

**Summary**

Устанавливает имя пассажира.

**Details:**
* Inherited From: [\YooKassa\Request\Payments\PassengerInterface](../classes/YooKassa-Request-Payments-PassengerInterface.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string OR null</code> | value  | Имя пассажира |

**Returns:** self - 


<a name="method_getLastName" class="anchor"></a>
#### public getLastName() : ?string

```php
public getLastName() : ?string
```

**Summary**

Возвращает фамилию пассажира.

**Details:**
* Inherited From: [\YooKassa\Request\Payments\PassengerInterface](../classes/YooKassa-Request-Payments-PassengerInterface.md)

**Returns:** ?string - 


<a name="method_setLastName" class="anchor"></a>
#### public setLastName() : self

```php
public setLastName(string|null $value) : self
```

**Summary**

Устанавливает фамилию пассажира.

**Details:**
* Inherited From: [\YooKassa\Request\Payments\PassengerInterface](../classes/YooKassa-Request-Payments-PassengerInterface.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string OR null</code> | value  | Фамилия пассажира |

**Returns:** self - 




---

### Top Namespaces

* [\YooKassa](../namespaces/yookassa.md)

---

### Reports
* [Errors - 0](../reports/errors.md)
* [Markers - 0](../reports/markers.md)
* [Deprecated - 25](../reports/deprecated.md)

---

This document was automatically generated from source code comments on 2024-07-02 using [phpDocumentor](http://www.phpdoc.org/)

&copy; 2024 YooMoney