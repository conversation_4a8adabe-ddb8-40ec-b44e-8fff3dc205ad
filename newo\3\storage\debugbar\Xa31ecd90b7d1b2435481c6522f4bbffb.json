{"__meta": {"id": "Xa31ecd90b7d1b2435481c6522f4bbffb", "datetime": "2025-06-08 15:40:15", "utime": **********.892729, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.141575, "end": **********.89275, "duration": 0.7511749267578125, "duration_str": "751ms", "measures": [{"label": "Booting", "start": **********.141575, "relative_start": 0, "end": **********.792013, "relative_end": **********.792013, "duration": 0.6504378318786621, "duration_str": "650ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.792027, "relative_start": 0.6504518985748291, "end": **********.892753, "relative_end": 2.86102294921875e-06, "duration": 0.10072588920593262, "duration_str": "101ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45499272, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.022690000000000002, "accumulated_duration_str": "22.69ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8422132, "duration": 0.02102, "duration_str": "21.02ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 92.64}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.8765252, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 92.64, "width_percent": 3.57}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.882335, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "ty", "start_percent": 96.21, "width_percent": 3.79}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1972007530 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwl%7C0%7C1982; _clsk=o6pnpa%7C1749397211842%7C8%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InI2U3VhUk5LMlF3Vmkvd3dZTVA5L0E9PSIsInZhbHVlIjoiK1pGVi9rVzlYUEsyRjMxYzQxZjFpd2tNODhBeldkUGlUREszT2NyMnozNTRFUzYzbjZMdFptT1dCMEJTV01oMlZ6TEtnZzBKRmxqNTJnY3Jud2d4aVRSU0ZUaDZWak0vbzBJcEZCMnlQbGtvS2lQeiszUDdxQ2ZHYWRBTXBpUG5HbGhVL0F0TzM5RzB4UmtrbGlUZ1lmNjkzSTF1NU56R3g2QVAxNWo2Z1NHSDM4eGVwNW9KOFFlUWpNNkFkZlRoVUZGdldRbCtLcmF3NlJOZjFoaDQwNDhlQW9USTdGSC9hMk1WY3NnVCtuMXFXclphbmRzMWVlU2Vvc3dTbFhiQ3R6bVRYZFJ2VXo2QzlNMWI0UzN0aHhOZC9nQTNXYmJ1Q2tJaW9iTVZYcHFiWk1FNzBKNEhPU3ZDM2dHclhDcG8wTVJHVWlPU2hUZTFYdFRzaWJWbFpoRWpzcWtrb2U2U0psdDRMcUU5ejZRWG9xNnpPdkkrR3dSeFZ6UC91MmV5a0V3OXpZbVdCZlNRRlpwajVZaXR0YXk0RzVhbHZUQVVxQUI1eWwxbEZNZjl4ZFJGSXl4R2t1SFVNcllEWGtMbStMTzB3ZloxL1ZXRXlDb2R6U3ZKdXdnL1VBQ2FuVU93L3BaVm11aDZISDEzajY3SjhQbGZrOVpPUStzTkFoeEQiLCJtYWMiOiJhOGE5ZWJlMzg4MDU1NjNiYjJhNTg1MDMyNjUzNmRkYmIyOTZiOWMyMDExYWIxMmMyNGU3MjNkOWUyZTkyNmNlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkNTdElIbUF2Y0pEWm5vYTRQTTBwd0E9PSIsInZhbHVlIjoiMU5Pd3doRmxQd1F5Z2QxWUNaNjE5NFNLT0JIdXovdGxTT2hMMUVkcFVMaU9VVmZxTTB1YWtzNzhHYjdqd1BrelZicTA5eTZaNGRHUm0rQmdpVXM2NXRRNEVJQkFnNW9pNUtPUlVBaFAzeUdkcnFGYlNsenh3bDh4TXVWT0htSXZWMS80WVBJb1RtUXJqa3pSSjlRc29VakZiSEw2Q3cwbzI5WkhWclNuOGNMVXlOc1FoTXJuQmhIeHNlQnYvUENMUU5CUTZmeG9CdW5TMWV1elZpaUhoQUdBb0RjQ1BDU3c5OTlFU210Ym04N0taN3ZpSEdXSlRveHFMZ3dVNnQ2a08yRnNRQThkSTc3dkZyczZsdFkrYk5ZYlZ2bHN4dVM4d2tOLzZxQ2lWMVQ3emN4a3FmU2U3SGJiQ1MvMXNlT1lETW50cExCeTBCMEJidnVvVHA4cjhrOHl4MzFhdEZNZERPVGJpL1ZCZnVDa3hOdTRUZFpPN0lLek45R2NVaWNFZlpPeVpJQitiN2ZCaGc1aHVtQ3FVOFA5aDlMS2VlNk45M3UzSlVuNVpXV3BodWhnU3h0NHZuUFBHbFhqNmdVU3paL1ZxOUlPZDlvOVNwWmV0NW1YaGUrZEhJa0xwTkxNbUcvWnp0MzFpc25pM28rZ3QvRmxyMFVzK2ZaQnNGd0ciLCJtYWMiOiJmNTFmMzc4YjAxYWFmMTQwODc5ZDg0Mjk1ZTA2ZGQ4YzVhYzVmMjRlYWY2YjkzMDJmNjJiY2I4NjU3MmQ1N2ZkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1972007530\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1599162300 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">COI6IIrAbucReABHBN7o3ZaIzRbwBCI1gfwrcfCo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1599162300\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1387666270 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:40:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InBSVlFyYy9jUkFabkE4ZSs3N21aOUE9PSIsInZhbHVlIjoiUHBOMkN5bitmbkdnMHFWV2dwTXNXS2VTRGpNWkhiZjZ4M2N1WC9RcGFyN2lTYW5xN1BVNWkyQkRBRHpBK2xOSGtqc3Zxd2E0Z2xxR015K2hRcDYwR3FHeEtHTXNGeGhyNzVUbVBnUnMrNTAwM1NNZXZ6NWNCZk9oZTJmYjJTeC9mUVdlVW1XMk5vVDFqajQ0TEQzVEwycWRHUUpJcm1TNmN3ai94MnFkUUV2cGdaWS9QWTI3M2luRm1RWDVWMXkzRVI3N0xLSFFMOHpsRmFVWHIxcDJ5YVlzSFAxWnFWSSt3UFk3RG9JUCtCK0FIQi8yaEV5MjdJR0tmbC92eUdBS0xpWlBKTTl5eW9BK2tkU2V5VlFtM0c1bXlsZGFoZlMzVFd2bUNOTjNzWnhibTEwR2t1VElsMzJseEg5ZlN6VDNiUnd6WFV6RDFCOFBDVnNGRmpJejVkcUU3V1phMFNwOTBrRko5eTRIZUNmR2VxSnpXUTQ2RWM1bWtMQ2h3aGNRNy9lV08xbDI0Smk0b2F4bVcrUEk3ajJrTmVJQlVBMzRxZEkrV203dWVzdmpxZUJtU25oUlAraGQ1Y1JCTklJbjJlU29pcnAvTjlwMVNIKysvNXpobCs0eWp4NDQ5WWNOZkZLaENkanpQQ0YzQmJZc2VIeUpaVmtYQnhOaUFBL0YiLCJtYWMiOiJiZDdlZGQzYWRhN2E1MGJhMzYwNjU1NWQzYTEyMmE1YTFkMGUwNGEyNzgyNjU1MzYyNjczN2M5ZTEyMzU1YjNhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:40:15 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlNSQmhzVm9QaGJ2SUUxWHBYK2lZbUE9PSIsInZhbHVlIjoiYnlhMjJYamsxaG1vVUNjejRtNUZ6S2lFVWdWUFVocDViMGY3VldKYjYxWFFpTXpyckplQzlMMzZFOVB3Z0YyTmhvTGIwdUVyVWVnTFgrak1LMGhnaTZOeG1QSG9mcnd3ZTFQc0FjK0liOWMyVkVjQ0JEdlAzb2FMdDV2RW02eEFFVzN6aFNzMUR6UGVPbisvaTlablZWRERZa2NYU2x3dTU4RUVEcVJ2a0NIQXJtTWpKRzhxbHV6MWJTOUJkYUl4MmlPV2JVTGhubWRLdjc0QUVIMjhZVnVWZ25MNEZyZS9sT1V1N2FwNmJQeW14QnRLZGNhcnZBWlRFSkhYUWxiWXhWck8zc0tWcGhrRHZncDh0NXkyOTYzOGVGTGJIaUZXRlhnbENZKzVEU1FBNmJEMEVMMjRjWWNKU2ljVVAvbE1tQXVQckJTUGlxMFVzOUoyRGFQUmZOd2MxTnJZUXhxa1ROS0tNTGtCOXZ2Tm9iODZUMTN1endCbnZtc0ZpVDdkTmhlQ0FhRGNwT1pPT1NoQ0t5QmhnVnEvKzN0Ykp6d2FRNFZsRGxvS05qM24zTS94YnVXRGtZc09ZalNCQ28xeHpwbXVsUWp5UGVPWTN0bGFDOWxCR3dxUUtQRlBFMk0wN3RGSURtQkNUSXkyZzhrdy9xZ3poQStUZHFvY1ljNWgiLCJtYWMiOiJjZTRmZDRiNjk4YzhmYzkwMTU1NjRlOWE2ZmUzOTlhMWZlZGM4NDRiNTIxMjNmNDEwNWU0OWQyNDdlYTBlODNlIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:40:15 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InBSVlFyYy9jUkFabkE4ZSs3N21aOUE9PSIsInZhbHVlIjoiUHBOMkN5bitmbkdnMHFWV2dwTXNXS2VTRGpNWkhiZjZ4M2N1WC9RcGFyN2lTYW5xN1BVNWkyQkRBRHpBK2xOSGtqc3Zxd2E0Z2xxR015K2hRcDYwR3FHeEtHTXNGeGhyNzVUbVBnUnMrNTAwM1NNZXZ6NWNCZk9oZTJmYjJTeC9mUVdlVW1XMk5vVDFqajQ0TEQzVEwycWRHUUpJcm1TNmN3ai94MnFkUUV2cGdaWS9QWTI3M2luRm1RWDVWMXkzRVI3N0xLSFFMOHpsRmFVWHIxcDJ5YVlzSFAxWnFWSSt3UFk3RG9JUCtCK0FIQi8yaEV5MjdJR0tmbC92eUdBS0xpWlBKTTl5eW9BK2tkU2V5VlFtM0c1bXlsZGFoZlMzVFd2bUNOTjNzWnhibTEwR2t1VElsMzJseEg5ZlN6VDNiUnd6WFV6RDFCOFBDVnNGRmpJejVkcUU3V1phMFNwOTBrRko5eTRIZUNmR2VxSnpXUTQ2RWM1bWtMQ2h3aGNRNy9lV08xbDI0Smk0b2F4bVcrUEk3ajJrTmVJQlVBMzRxZEkrV203dWVzdmpxZUJtU25oUlAraGQ1Y1JCTklJbjJlU29pcnAvTjlwMVNIKysvNXpobCs0eWp4NDQ5WWNOZkZLaENkanpQQ0YzQmJZc2VIeUpaVmtYQnhOaUFBL0YiLCJtYWMiOiJiZDdlZGQzYWRhN2E1MGJhMzYwNjU1NWQzYTEyMmE1YTFkMGUwNGEyNzgyNjU1MzYyNjczN2M5ZTEyMzU1YjNhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:40:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlNSQmhzVm9QaGJ2SUUxWHBYK2lZbUE9PSIsInZhbHVlIjoiYnlhMjJYamsxaG1vVUNjejRtNUZ6S2lFVWdWUFVocDViMGY3VldKYjYxWFFpTXpyckplQzlMMzZFOVB3Z0YyTmhvTGIwdUVyVWVnTFgrak1LMGhnaTZOeG1QSG9mcnd3ZTFQc0FjK0liOWMyVkVjQ0JEdlAzb2FMdDV2RW02eEFFVzN6aFNzMUR6UGVPbisvaTlablZWRERZa2NYU2x3dTU4RUVEcVJ2a0NIQXJtTWpKRzhxbHV6MWJTOUJkYUl4MmlPV2JVTGhubWRLdjc0QUVIMjhZVnVWZ25MNEZyZS9sT1V1N2FwNmJQeW14QnRLZGNhcnZBWlRFSkhYUWxiWXhWck8zc0tWcGhrRHZncDh0NXkyOTYzOGVGTGJIaUZXRlhnbENZKzVEU1FBNmJEMEVMMjRjWWNKU2ljVVAvbE1tQXVQckJTUGlxMFVzOUoyRGFQUmZOd2MxTnJZUXhxa1ROS0tNTGtCOXZ2Tm9iODZUMTN1endCbnZtc0ZpVDdkTmhlQ0FhRGNwT1pPT1NoQ0t5QmhnVnEvKzN0Ykp6d2FRNFZsRGxvS05qM24zTS94YnVXRGtZc09ZalNCQ28xeHpwbXVsUWp5UGVPWTN0bGFDOWxCR3dxUUtQRlBFMk0wN3RGSURtQkNUSXkyZzhrdy9xZ3poQStUZHFvY1ljNWgiLCJtYWMiOiJjZTRmZDRiNjk4YzhmYzkwMTU1NjRlOWE2ZmUzOTlhMWZlZGM4NDRiNTIxMjNmNDEwNWU0OWQyNDdlYTBlODNlIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:40:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1387666270\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-14******** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-14********\", {\"maxDepth\":0})</script>\n"}}