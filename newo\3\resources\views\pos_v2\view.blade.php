@extends('layouts.admin')

@section('page-title')
    {{ __('POS V2 Invoice Details') }} - {{ \Auth::user()->posNumberFormat($pos->pos_id) }}
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('Dashboard') }}</a></li>
    <li class="breadcrumb-item"><a href="{{ route('pos_v2.index') }}">{{ __('POS V2') }}</a></li>
    <li class="breadcrumb-item">{{ __('Invoice Details') }}</li>
@endsection

@section('action-btn')
    <div class="float-end">
        <a href="{{ route('pos_v2.thermal.print', $pos->id) }}" target="_blank" class="btn btn-sm btn-success">
            <i class="ti ti-device-mobile"></i> {{ __('Thermal Print') }}
        </a>
        <a href="{{ route('pos_v2.index') }}" class="btn btn-sm btn-primary">
            <i class="ti ti-arrow-left"></i> {{ __('Back to POS V2') }}
        </a>
    </div>
@endsection

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <h5 class="mb-0">{{ __('POS V2 Invoice Details') }}</h5>
                        </div>
                        <div class="col-6 text-end">
                            <span class="badge badge-{{ $pos->status == 1 ? 'success' : 'danger' }} p-2">
                                {{ $pos->status == 1 ? __('Completed') : __('Pending') }}
                            </span>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Invoice Header Information -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6><strong>{{ __('Invoice Information') }}</strong></h6>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>{{ __('Invoice Number') }}:</strong></td>
                                    <td>{{ \Auth::user()->posNumberFormat($pos->pos_id) }}</td>
                                </tr>
                                <tr>
                                    <td><strong>{{ __('Date') }}:</strong></td>
                                    <td>{{ \Utility::dateFormat(\Utility::settings(), $pos->pos_date) }}</td>
                                </tr>
                                <tr>
                                    <td><strong>{{ __('Time') }}:</strong></td>
                                    <td>{{ $pos->created_at->format('H:i:s') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>{{ __('Status') }}:</strong></td>
                                    <td>
                                        <span class="badge badge-{{ $pos->status_type == 'normal' ? 'success' : ($pos->status_type == 'returned' ? 'warning' : 'danger') }}">
                                            {{ \App\Models\PosV2::$statusTypes[$pos->status_type] ?? $pos->status_type }}
                                        </span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6><strong>{{ __('Customer & Warehouse Information') }}</strong></h6>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>{{ __('Customer') }}:</strong></td>
                                    <td>{{ $pos->customer->name ?? __('Walk-in Customer') }}</td>
                                </tr>
                                @if($pos->customer && $pos->customer->email)
                                <tr>
                                    <td><strong>{{ __('Email') }}:</strong></td>
                                    <td>{{ $pos->customer->email }}</td>
                                </tr>
                                @endif
                                <tr>
                                    <td><strong>{{ __('Warehouse') }}:</strong></td>
                                    <td>{{ $pos->warehouse->name ?? __('N/A') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>{{ __('Cashier') }}:</strong></td>
                                    <td>{{ $pos->createdBy->name ?? __('N/A') }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- Invoice Items -->
                    <div class="row">
                        <div class="col-12">
                            <h6><strong>{{ __('Invoice Items') }}</strong></h6>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead class="thead-dark">
                                        <tr>
                                            <th>#</th>
                                            <th>{{ __('Product') }}</th>
                                            <th>{{ __('SKU') }}</th>
                                            <th class="text-center">{{ __('Quantity') }}</th>
                                            <th class="text-right">{{ __('Unit Price') }}</th>
                                            <th class="text-right">{{ __('Discount') }}</th>
                                            <th class="text-right">{{ __('Tax') }}</th>
                                            <th class="text-right">{{ __('Total') }}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @php
                                            $subtotal = 0;
                                            $totalTax = 0;
                                            $totalDiscount = 0;
                                        @endphp
                                        @foreach($pos->items as $index => $item)
                                        @php
                                            $itemTotal = $item->price * $item->quantity;
                                            $subtotal += $itemTotal;
                                            $totalDiscount += $item->discount;
                                            
                                            // حساب الضريبة
                                            $itemTax = 0;
                                            if (!empty($item->tax)) {
                                                $taxes = explode(',', $item->tax ?? '');
                                                foreach ($taxes as $taxRate) {
                                                    $itemTax += ($itemTotal * $taxRate) / 100;
                                                }
                                            }
                                            $totalTax += $itemTax;
                                        @endphp
                                        <tr>
                                            <td>{{ $index + 1 }}</td>
                                            <td>
                                                <strong>{{ $item->product->name ?? __('Deleted Product') }}</strong>
                                                @if($item->description)
                                                    <br><small class="text-muted">{{ $item->description }}</small>
                                                @endif
                                            </td>
                                            <td>{{ $item->product->sku ?? __('N/A') }}</td>
                                            <td class="text-center">{{ $item->quantity }}</td>
                                            <td class="text-right">{{ \Auth::user()->priceFormat($item->price) }}</td>
                                            <td class="text-right">{{ \Auth::user()->priceFormat($item->discount) }}</td>
                                            <td class="text-right">{{ \Auth::user()->priceFormat($itemTax) }}</td>
                                            <td class="text-right">{{ \Auth::user()->priceFormat($itemTotal + $itemTax - $item->discount) }}</td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                    <tfoot>
                                        <tr>
                                            <td colspan="7" class="text-right"><strong>{{ __('Subtotal') }}:</strong></td>
                                            <td class="text-right"><strong>{{ \Auth::user()->priceFormat($subtotal) }}</strong></td>
                                        </tr>
                                        @if($totalDiscount > 0)
                                        <tr>
                                            <td colspan="7" class="text-right"><strong>{{ __('Total Discount') }}:</strong></td>
                                            <td class="text-right"><strong>-{{ \Auth::user()->priceFormat($totalDiscount) }}</strong></td>
                                        </tr>
                                        @endif
                                        @if($totalTax > 0)
                                        <tr>
                                            <td colspan="7" class="text-right"><strong>{{ __('Total Tax') }}:</strong></td>
                                            <td class="text-right"><strong>{{ \Auth::user()->priceFormat($totalTax) }}</strong></td>
                                        </tr>
                                        @endif
                                        <tr class="table-active">
                                            <td colspan="7" class="text-right"><strong>{{ __('Grand Total') }}:</strong></td>
                                            <td class="text-right"><strong>{{ \Auth::user()->priceFormat($subtotal + $totalTax - $totalDiscount) }}</strong></td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Information -->
                    @if(!empty($pos->customer) && $pos->customer->is_delivery)
                    <div class="row mt-4">
                        <div class="col-12">
                            <h6><strong>{{ __('Payment Information') }}</strong></h6>
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <tr>
                                        <td><strong>{{ __('Payment Status') }}:</strong></td>
                                        <td>
                                            @if($pos->is_payment_set)
                                                <span class="badge badge-success">{{ __('تم التحصيل من مندوب التوصيل') }} ✅</span>
                                            @else
                                                <span class="badge badge-warning">{{ __('جاري التحصيل') }} 🚚</span>
                                            @endif
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>{{ __('Total Amount') }}:</strong></td>
                                        <td>{{ \Auth::user()->priceFormat($pos->getTotal()) }}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                    @elseif($pos->posPayment)
                    <div class="row mt-4">
                        <div class="col-12">
                            <h6><strong>{{ __('Payment Information') }}</strong></h6>
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <tr>
                                        <td><strong>{{ __('Payment Date') }}:</strong></td>
                                        <td>{{ \Utility::dateFormat(\Utility::settings(), $pos->posPayment->date) }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>{{ __('Payment Method') }}:</strong></td>
                                        <td>
                                            @if($pos->posPayment->payment_type == 'cash')
                                                <span class="badge badge-success">{{ __('Cash') }}</span>
                                            @elseif($pos->posPayment->payment_type == 'network')
                                                <span class="badge badge-info">{{ __('Network') }}</span>
                                            @elseif($pos->posPayment->payment_type == 'split')
                                                <span class="badge badge-warning">{{ __('Split Payment') }}</span>
                                            @endif
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>{{ __('Total Amount') }}:</strong></td>
                                        <td>{{ \Auth::user()->priceFormat($pos->posPayment->amount) }}</td>
                                    </tr>
                                    
                                    @if($pos->posPayment->payment_type == 'network' && $pos->posPayment->transaction_number)
                                    <tr>
                                        <td><strong>{{ __('Transaction Number') }}:</strong></td>
                                        <td>{{ $pos->posPayment->transaction_number }}</td>
                                    </tr>
                                    @endif
                                    
                                    @if($pos->posPayment->payment_type == 'split')
                                        @if($pos->posPayment->cash_amount > 0)
                                        <tr>
                                            <td><strong>{{ __('Cash Amount') }}:</strong></td>
                                            <td>{{ \Auth::user()->priceFormat($pos->posPayment->cash_amount) }}</td>
                                        </tr>
                                        @endif
                                        @if($pos->posPayment->network_amount > 0)
                                        <tr>
                                            <td><strong>{{ __('Network Amount') }}:</strong></td>
                                            <td>{{ \Auth::user()->priceFormat($pos->posPayment->network_amount) }}</td>
                                        </tr>
                                        @endif
                                        @if($pos->posPayment->transaction_number)
                                        <tr>
                                            <td><strong>{{ __('Transaction Number') }}:</strong></td>
                                            <td>{{ $pos->posPayment->transaction_number }}</td>
                                        </tr>
                                        @endif
                                    @endif
                                </table>
                            </div>
                        </div>
                    </div>
                    @endif

                    <!-- Action Buttons -->
                    <div class="row mt-4">
                        <div class="col-12 text-center">
                            <a href="{{ route('pos_v2.thermal.print', $pos->id) }}" target="_blank" class="btn btn-success">
                                <i class="ti ti-device-mobile"></i> {{ __('Thermal Print') }}
                            </a>
                            <a href="{{ route('pos_v2.index') }}" class="btn btn-primary">
                                <i class="ti ti-arrow-left"></i> {{ __('Back to POS V2') }}
                            </a>
                            @if(Auth::user()->can('delete pos'))
                            <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
                                <i class="ti ti-trash"></i> {{ __('Delete Invoice') }}
                            </button>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    @if(Auth::user()->can('delete pos'))
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteModalLabel">{{ __('Confirm Delete') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    {{ __('Are you sure you want to delete this POS V2 invoice? This action cannot be undone.') }}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                    <form action="{{ route('pos_v2.destroy', $pos->id) }}" method="POST" style="display: inline;">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger">{{ __('Delete') }}</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    @endif
@endsection
