{"__meta": {"id": "X1df234940d25a48d752891d4af45aec3", "datetime": "2025-06-08 16:24:05", "utime": **********.590153, "method": "GET", "uri": "/add-to-cart/5/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749399844.998918, "end": **********.590177, "duration": 0.5912590026855469, "duration_str": "591ms", "measures": [{"label": "Booting", "start": 1749399844.998918, "relative_start": 0, "end": **********.455726, "relative_end": **********.455726, "duration": 0.45680785179138184, "duration_str": "457ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.455739, "relative_start": 0.4568209648132324, "end": **********.590179, "relative_end": 1.9073486328125e-06, "duration": 0.13443994522094727, "duration_str": "134ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 53749096, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1322\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1322-1579</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.020999999999999998, "accumulated_duration_str": "21ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.50114, "duration": 0.01421, "duration_str": "14.21ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 67.667}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.526788, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 67.667, "width_percent": 4.857}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.5487359, "duration": 0.00215, "duration_str": "2.15ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 72.524, "width_percent": 10.238}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.553776, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 82.762, "width_percent": 5.095}, {"sql": "select * from `product_services` where `product_services`.`id` = '5' limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1326}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.560912, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1326", "source": "app/Http/Controllers/ProductServiceController.php:1326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1326", "ajax": false, "filename": "ProductServiceController.php", "line": "1326"}, "connection": "ty", "start_percent": 87.857, "width_percent": 5.048}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 5 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["5", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1330}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.568296, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "ty", "start_percent": 92.905, "width_percent": 7.095}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 16,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1277731360 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1277731360\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.559659, "xdebug_link": null}]}, "session": {"_token": "WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:1 [\n  5 => array:9 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 1\n    \"price\" => \"12.00\"\n    \"id\" => \"5\"\n    \"tax\" => 0\n    \"subtotal\" => 12.0\n    \"originalquantity\" => 30\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/add-to-cart/5/pos", "status_code": "<pre class=sf-dump id=sf-dump-1793019247 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1793019247\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=u1db3a%7C1749399485619%7C2%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IktSSkRnZVNGbWt4UDlZNXIzUFRjWXc9PSIsInZhbHVlIjoiMjAxNVQrZ1ZYSjc0V0V1ejZrdHZtdldHUFFNOHBjU2tDM0hBZ2Fac2kwdThreWtPUDNzMDFrNU1uUGsrSlNOL2c1VVFzQ3E4TFpQclB1aWc3Yyt6L1A0T2QwYnZ0T0FZMDJUSEszNHJqVTF4cmNaZXB5UU0wK0wyYVdQcEw4VWMzZmtqeEh2Y2R2SnY2NnVuUnp1eEgyYk1qemVsZkE3QkliUCtmRjF3eHBZK2g2eUNrNlJhd2JFM3diNG9rclVjMGFTWHVXeHVya1NWYjUyUDBHSEg0V1BYdHd2RkVmQW1vbStVVzI1aEk3UTJmeU5OK0hiSFpJTm1lQmh2TXNscG9KZi8xTnY3LzZMUVZSVjFqS1pCMTd1OVFMdnJRREx5eTUwa09VVmFLQ2pSZE5EL09TSEJZUEV6c2hRd2dxR25iYWFEVGFSSG1BQnM2MExxcStYNXNlVUk2ZHhMYzQ0SGNiTlZFNlNDNThzZlc3blIvSlI0cjJTdHJSdEhHTForVUdGVjVHdjNwN0t0SHRDWkJrbXpSeFF2T0VIWUxIajIrWit3TENGbm5hMkhoczVtREpWUHJhVjVLUXR0Z3kyNE5raTg2c2RwbHB3Tzd1dTZpNEl6SC9HNVpSWlE2dW5MZVdIV29NK09NL1NLMldaSHY4N0paU1B0UmRaUkdHSHQiLCJtYWMiOiI3NzRhYzIyNzhlZWU3OGY0OGRiNzJkNzEwZGQ3YTM3ODg4Yzc1MzI5MTY2Y2M0YmRkZTNiNjNlYjQ0MDI5OTAzIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkFObU1SeXd5bUtldFBYekliZVFiOEE9PSIsInZhbHVlIjoidERCckdqZUJWSEw3Yk9ld0dkOVRPNDJFS015TS9CaU9HUGNpNkFsWlhiV3djMXQ4T3U3S1UwbktveWJxNXZlRjZwTnl4V2hUQXpVMFFDRmJ6TC93RmJYM29ydTg4aTNLckp4VDRsK0U4MXpuN2RWMDl1Z3RvcnhlTHd5eUlyWlkvakdCS3FrMHBieWt3aHo3cGlvTVZuTCtaZVJPTmpjUEdMSGF0WUFFdEF6Q1psY1lvZUJCMDlkdGFGdGtkRFZRdzd1YzlIQ1JLdFFPQzhQZVNpckZ3WnptcEl2a0VRc08yajBpNnlkTkdHWUd2NjhkQVdjU3plb293RE1GaTJWOU5EUlRHZzFmMklZSFRLcW9JdTNrSFpBKzc0MWdGTG9PL2REYldsMldZdUE3QmlGUzdNaFU2T2t3N2ZDeEVaY3R0U05QWGV1WDZyc2VNTFR5OVhKbTZnVTBvSE81N29nK0pjdlNtTyt5d09Xc1U1V3NhODV3cXBBNk9QMCtzYnBTaXFFU1k3aUE4NEY1aU9EU3lJK2djWlp5R2FadnBKaWV1Vm9INzVMcisxRStuWUhDaXVXUGQ2QjFLbVJFN0pSMFo1a1FVN2pGMklkMG5iNlFSazZxTVp5QWRvcWJtZThJbHU2RTgzMlN6T2sxcjlVNXRnaFZyT2xSV0V6VC9GbksiLCJtYWMiOiJmYWEzN2IzZGVjNzE5MzFjN2IyOGE3NWY5NDQzYTAzODNkM2NjMmM2NTFkYWQ0MGFiNWZkMDhhN2U2YjlkNTQxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1088000154 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xNV7SvMX3AgbiuDVO62eNXfryfZD4KbWr3rEIkXt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1088000154\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1281723535 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 16:24:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik52ZjF1VEFuTFF0VVVPL3VYd2NSMnc9PSIsInZhbHVlIjoiMVhvU2Q0K0RJa09JaXd5c25UVVg1QUlaM3Z3b2hrODFxSnVFYTNnL29hV3JLcE5sQXhvRG9mQm5mVm9RZ0xLNkNwbHA4NGk5dVliVXFCNWxjbGc5WENZbXh1NU8zUTNjandXaU5Zd3VyMGg1K09Ed1p5dkRFd0VHQldldlJMRjgydzJLNG1rSmRyT0gxc0xPaThudkpzTlpER0dlRzl1NW11eUZwRW5DM3lyVGJiZW1DNkkyTzNraWVXUFdFbi8rQVBReTNPMWFmY2l5R2pyNG8zYUZtMzFPSjZhR05QWVhVWnRBMjJUa1NjaHpzamdTQXFKNFIzNGs5cFlyOFEzZ1hYdEJrbU9sU2c1L21lMEhoS28zcS8ycnZFNWZxOForLzJmNUU0WXhDZ25MdEhtSTNpYy9HWlpsb2tkQVF4SGh5V0tNaHZQMGRwKzNxZ3VLMG1RNXJxNGlRcE9IWTVFSGc2QnZ3N2JJdkZsSG9zSFVNaDU5WE1zUDVKQmgvdGp3WEp4RVlqOVdPMXd2NGpuL1NUR2FxUWRuTEtRZWEwL0RhWEpEM1hyVkJvT2QrY1Z5Tm9hc3AyYSthTXo1by9uT0pCOHMyTmFmZTdnOHA3eENtK2luUmFESmtQN0RjKzBZOTFEeFV4Mk9jRFNPNEJHYUZaRndDMVhSY1BiK3dOaUQiLCJtYWMiOiIxMTQxYmIyNzM5ZDlhNWYzMmY2ODFjNDA5NDdmNDQ3NTJjNDBlYWFjMGZkMGJkMGQ2NGZjM2E4ZDEwYTAxN2QxIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 18:24:05 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjBydGZkYnJQeUkrRkg2K25uZ3BMckE9PSIsInZhbHVlIjoiUGRNRy81bGlUb0lhRTVRbUhiRjV0NllwVTVtZk0vZmI3bFlFc25WS1BOQ3NlY21td2JaVXAyM0hkQytKV1dCM2JvanNTTkZleVlla3dIbmV4M3Via1RJbDJCelBZVmJVTUlqRTJxdkhFYmhDaFpJRkxzdWRuVTVpdTZINWhuVlVsb0NHUG8zbWpINStUMzg0RkMweU84VUtEMjJkRjBhYWxzbXR3NWlCQjZybmJuTS9sdmZTc2FjZkgzZGo3U3JVZDZFbmUyc2t0N01qL0tGSkVndXpocDNxZzFIVHRsclZFTXg5WkcwVVVwZGVkaEszdDQxWVV4WmM1dTZGSHlTMjVoMGVKOFFRYXNlZXFyM1gvY3dRWkpGL3NZaUdDNGc2N256WGdUUjUrbGxSaTQybHc1b1pOSTdpamR2M2dUYitycHpNRVhZVkl2ZXpKbVN0Mm84bjlZa2dxZUQ0N1pqQklQOHM4SVFzbjlTMFVVQWFXdjROMy9VUDF5QXFOZUYrZjhWUytqM3AxRlZOR2F0dytuYmJ4bmRLT1g5V1d4WnBPcjV3TkVWUStsMmZQUG5tN2dJcU1qckN6d1J0Q1BsQ1hQZWFKVEg3TXJia1Z3eVR3VFhsay9ocTI1QzBOZWE4Wnh3N1FOZnhBSnFHMi9kVE1ZanNuWkVKelU1blNkWk4iLCJtYWMiOiI3N2Q2NDAwZDQwZTkwNDYzOWM2MWRlOGQzMDg5MTU0YWJiZTQxOTlkYzA0MGM0MWY1ODQ3ODVhYTE3NWE1ZDNmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 18:24:05 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik52ZjF1VEFuTFF0VVVPL3VYd2NSMnc9PSIsInZhbHVlIjoiMVhvU2Q0K0RJa09JaXd5c25UVVg1QUlaM3Z3b2hrODFxSnVFYTNnL29hV3JLcE5sQXhvRG9mQm5mVm9RZ0xLNkNwbHA4NGk5dVliVXFCNWxjbGc5WENZbXh1NU8zUTNjandXaU5Zd3VyMGg1K09Ed1p5dkRFd0VHQldldlJMRjgydzJLNG1rSmRyT0gxc0xPaThudkpzTlpER0dlRzl1NW11eUZwRW5DM3lyVGJiZW1DNkkyTzNraWVXUFdFbi8rQVBReTNPMWFmY2l5R2pyNG8zYUZtMzFPSjZhR05QWVhVWnRBMjJUa1NjaHpzamdTQXFKNFIzNGs5cFlyOFEzZ1hYdEJrbU9sU2c1L21lMEhoS28zcS8ycnZFNWZxOForLzJmNUU0WXhDZ25MdEhtSTNpYy9HWlpsb2tkQVF4SGh5V0tNaHZQMGRwKzNxZ3VLMG1RNXJxNGlRcE9IWTVFSGc2QnZ3N2JJdkZsSG9zSFVNaDU5WE1zUDVKQmgvdGp3WEp4RVlqOVdPMXd2NGpuL1NUR2FxUWRuTEtRZWEwL0RhWEpEM1hyVkJvT2QrY1Z5Tm9hc3AyYSthTXo1by9uT0pCOHMyTmFmZTdnOHA3eENtK2luUmFESmtQN0RjKzBZOTFEeFV4Mk9jRFNPNEJHYUZaRndDMVhSY1BiK3dOaUQiLCJtYWMiOiIxMTQxYmIyNzM5ZDlhNWYzMmY2ODFjNDA5NDdmNDQ3NTJjNDBlYWFjMGZkMGJkMGQ2NGZjM2E4ZDEwYTAxN2QxIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 18:24:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjBydGZkYnJQeUkrRkg2K25uZ3BMckE9PSIsInZhbHVlIjoiUGRNRy81bGlUb0lhRTVRbUhiRjV0NllwVTVtZk0vZmI3bFlFc25WS1BOQ3NlY21td2JaVXAyM0hkQytKV1dCM2JvanNTTkZleVlla3dIbmV4M3Via1RJbDJCelBZVmJVTUlqRTJxdkhFYmhDaFpJRkxzdWRuVTVpdTZINWhuVlVsb0NHUG8zbWpINStUMzg0RkMweU84VUtEMjJkRjBhYWxzbXR3NWlCQjZybmJuTS9sdmZTc2FjZkgzZGo3U3JVZDZFbmUyc2t0N01qL0tGSkVndXpocDNxZzFIVHRsclZFTXg5WkcwVVVwZGVkaEszdDQxWVV4WmM1dTZGSHlTMjVoMGVKOFFRYXNlZXFyM1gvY3dRWkpGL3NZaUdDNGc2N256WGdUUjUrbGxSaTQybHc1b1pOSTdpamR2M2dUYitycHpNRVhZVkl2ZXpKbVN0Mm84bjlZa2dxZUQ0N1pqQklQOHM4SVFzbjlTMFVVQWFXdjROMy9VUDF5QXFOZUYrZjhWUytqM3AxRlZOR2F0dytuYmJ4bmRLT1g5V1d4WnBPcjV3TkVWUStsMmZQUG5tN2dJcU1qckN6d1J0Q1BsQ1hQZWFKVEg3TXJia1Z3eVR3VFhsay9ocTI1QzBOZWE4Wnh3N1FOZnhBSnFHMi9kVE1ZanNuWkVKelU1blNkWk4iLCJtYWMiOiI3N2Q2NDAwZDQwZTkwNDYzOWM2MWRlOGQzMDg5MTU0YWJiZTQxOTlkYzA0MGM0MWY1ODQ3ODVhYTE3NWE1ZDNmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 18:24:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1281723535\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1024308601 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>12.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>30</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1024308601\", {\"maxDepth\":0})</script>\n"}}