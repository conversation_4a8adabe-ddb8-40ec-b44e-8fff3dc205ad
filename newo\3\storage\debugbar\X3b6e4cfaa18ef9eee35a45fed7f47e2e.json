{"__meta": {"id": "X3b6e4cfaa18ef9eee35a45fed7f47e2e", "datetime": "2025-06-08 15:43:35", "utime": **********.478391, "method": "POST", "uri": "/empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749397414.875733, "end": **********.478426, "duration": 0.6026930809020996, "duration_str": "603ms", "measures": [{"label": "Booting", "start": 1749397414.875733, "relative_start": 0, "end": **********.351489, "relative_end": **********.351489, "duration": 0.4757561683654785, "duration_str": "476ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.351501, "relative_start": 0.4757680892944336, "end": **********.478429, "relative_end": 3.0994415283203125e-06, "duration": 0.12692809104919434, "duration_str": "127ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47488728, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@emptyCart", "namespace": null, "prefix": "", "where": [], "as": "empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1655\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1655-1669</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.019960000000000002, "accumulated_duration_str": "19.96ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.396472, "duration": 0.016370000000000003, "duration_str": "16.37ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 82.014}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.427354, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 82.014, "width_percent": 5.06}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.4551091, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 87.074, "width_percent": 6.864}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.459882, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 93.938, "width_percent": 6.062}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 16,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-438447030 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-438447030\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.467658, "xdebug_link": null}]}, "session": {"_token": "WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "error": "Cart is empty!", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-1004522822 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1004522822\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-156029203 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-156029203\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-333479712 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-333479712\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-714411192 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749396510751%7C76%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Imd5UWNMazhYSTExdFJxNnJ3dkFtZXc9PSIsInZhbHVlIjoiYUJnM0FNeXYwYVNMTyttUnUycSs3VXlUOGoxaG1WMHFIM2haVldwL1lwSWMyaXpOQ3J3ek5WVm16TDdxNUowWWYxUnZ1SjJQUlRvZTZ5aDlQOFNtejdMZDFYMy9Tc0pFZVJvK29XT2tCZFBWYUNBRzhxV0tIN0lwZ2JPcXlBWk5wTklpbGNER0d6WW1WMGJLRG1TVnppRHZCV1phRXBsaDFYUUdXT1pTQ0czR1NneW5vb3VVeDhrdWFsNFNCa0xRUCtmaDZ1RGhmRTRQWWhJbmNyUHVMdWJaWnYrS2xlYTlZQkVJcEdSdFBMVndXQm5HS1A0TTQ1dWZrelQ1clpFdDBOZENML3hRbnBXb0VQZ0FEKzdFdkNrM05abE5DeUlWbThicWNWcDhlNVZZZGk3QkdkMnI4MXU5T0tTK050V1A1RFk4MjM3K0xicnVwRWp2UUYvdUR6L3lCV1BqdHVycUFiUndxWnozUElKei9iMzJqZVFGcDY1Z0lkM3lLWFJjbUtlRWpVZGg2VjhyWFlXS0RNcjBocXlIdkNEUjJTVWE3ZW1zTjQzODlwMnNoOWpIOXRNeW5NMStoMDBJSlFESGVCbklqNTROaTJzMHp4MFJUN3ptaGNQUnNvbXZDSDZpeTFsYlFneVVRNXU0RlpWM1dwNktzMDBRNGI3U3dxV1giLCJtYWMiOiJlOGQ2NWJmZDAwNTFmNTJkMGEwMzg3ZWY2MmVhMzUwZWUzYzY3M2Q2NDNkOThkNWJkYTVkOTIwMDM1Njc5NjA1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IktYK3R0UEFISGFaY1ZnbmVGeFpxV1E9PSIsInZhbHVlIjoiUk9MVXpIUGUyZGJFY2tMWXlENGIvM3o2N2lMYmd5dWlhdTl6SiswblI2aTNyS2tqVlY0ZVlHT1dONnovc3pKZUgweFVSb0RpUEdkd0hTUFNxcDVOOGc5TGFhMWEvOGRUQnhQczNETFBFaGZMcjVlZHhLNlhnWGRmNXcrbjlVYkZ1dk1JeHJoUGdGek9yUVBLUDczb0JoWFoyR3JKdEZvZElEekpPZW4wNWRWOUVLdElsbkJJdWxZV1VoNzhVdFQwcGFXM1l4WTk2alBQcW9vNmJJbnN0a3RlcUs4VWtCaWxVS2h3a1RWLzM5NkliNlFtdExTK3Q4c0FiVVhHRnJ5YkZ1WWRJWUJkSmdkSFJVVUlyQVkvdXd4QlFTRTJNV2htdTN0YXRxUXF1SDJhRjF0YnFOeG5JR3NXekdVNEdqSjY4blhxWDNuMXlnclkvVE11ZFoxK3RjR0I4Wlp1YWoreG83ZzJCclFwZGFHbHhlcGNsSFdTV3pPUldjVlJ3QlUxbVczU2dCZXE5MUpHc29UajZ4UmlQZ1hvK3A5RUdreEFYSTlGcjQwOEpjWHdEdG43dHUzbC96T2Q2MnRkZHQzeUdlTGljZVhuYWtyZlVSTVZNWHVTazNteGlxekhiYURTZkJwTEVaZGNuOEJCdlpFYUV4N2F4bEcxYktQMEcwYzQiLCJtYWMiOiJlZmYwMmZlMzBmNGMxZDFkNzdmZmM4ZWRlNjNhZjRiM2VmMTE3YWM5MjI0YTk4YTAyNTc5NjAyMDBjZWQwMTk5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-714411192\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-907321354 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xNV7SvMX3AgbiuDVO62eNXfryfZD4KbWr3rEIkXt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-907321354\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1641464555 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:43:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Iko5a0FCVU8wcHhwU3l5bjJMUitQSWc9PSIsInZhbHVlIjoiWUMrRlh0YWZ1UWN4d3lRZGpUUE1rRXBNSnV0azNYTlVHRmx1QWtJWlJSeVNpeUlla3ROaS9DeHVIcnl3Z25GSWdzN0luUXNDS3gvVHN0OTEzQzRNMlNBbDJTZlZhYTVtb014VE9RYlRIYzRVK1ppa0xIdGZOTGdLV0p6Rzd2VTAzaFcwd1orcDlkaG9EODFZamxtakpXTEV5ekx1QWVZM25FZkxPNEhvaEdjd0FXYkFwc1ZjZHZycEJWWW1TSzJhTDU3Y0R2aWdtNHN3OVZZOGVPdk9zWXR0a2NQRnNUMmxYejdwQU84VGFvRE9DcTFKd0NlT3VDYXR0NHErS3hITmo3b3JRRFA4QlBKN2NJYUJmVVV2QnI2ZHRaQW9TbHRjUkIzTTY2S1ROSHBhTGgwKzg5RE9vUnhsUVNQbmd6UENSbHFPWThHRXFHQWpyZXZsSmlZVE9YSTNSV24rUmhjS1l6UVdyZ0R4OEJ3a2tTZ2h4Rlp3QjhqUUlEZEgzTzJBcms2QmpjbCtKUkhJaThiMUs0RXg3cnNhTUFxaExOZkg0OWM1OU5IVGJSR2dFOWVwbUwrZjNaamt3MWNPR2xubzhxSXRsK25PMDhKZmJmQTYwSUQvVFNkd3pDQTR5S3dnSWFmYThDbWpES2hUYnM1YTM5TmQ3SFNLd0puZHVBZGoiLCJtYWMiOiJmNTdhNDIzZTFjMTI4MmYzYjQxZTQ2NDExODQzMmNlZjFiYjdjYTQ1MDlhOWQ0MjhkODJmN2E4MjI5NTFjZTEwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:43:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjBRa1lOVU4xakg5VDJoRStvSzRtbmc9PSIsInZhbHVlIjoibmduRkl1REhFMDVXZjBheGptVkFFaGRVVzB3cGp3MDJvNmx3TTlWRWdLb012UjJYRXJidkRQbFN6SGdsWmJRNmltQ0MxV2VmSVBWVEwzcmRoV1dIWVA0TWhMQXpkdW1RN0JiSTdTZXdvZ0dQdG03VTBxS1dlN2ZZV2ZmMWcrZFMyKzJVaDNjWjVRM1VrNXd0YjNvR21SWmcyRDJzNzR1VDB4WnZwWTVHRWU1K2ZIVWV4NzFvaUZMcTNYUDMxRStCR2kwZk5YQ2tTakdQUVVCS0pTU09JZDhwZStEYnJ3VWFOQlNHZmxiN1dOMVZITjVSYkNtbTRueC9pRmtyVVNiSXMyT0ZEb1luZEMzTEswWlRQak85UTg2aW5GcFN6UmhZeXcySm5XYWZ5WnBnNjkwRHVrMTIvQnBIUHVWODcyWnVvWStvVVIvUHRyWHpkRmFQS1ZLb3lIZGhhRytDdkpPSmhHc3lzb2FGdkhjZmk0MGpnazg5WUJsVWEvM0wybGtNQjlPeU55SHVZcDA5UWYxbjdGekZQUVV4UXdmZVFxWGRMa0t3ZUN4Y1hyUENlclNuMEdMWHd4RnhwamI1ZmJsS21wN2cvWXpxeGVkbnpxOSttekxqZ0tSeHlCbFFKZDRjSm5WTExCZlJqUDhwd2taTHJnRFBqR3B0UHJZN2hQRkwiLCJtYWMiOiJiNDg0NzUwOGJmZTEzMjA1YzhmNDY1NDY5ZjUwOWYyZTE2MjlhMDRhMjIwMDE2NzFjYTgzZDkxZTBkZjc2NThhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:43:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Iko5a0FCVU8wcHhwU3l5bjJMUitQSWc9PSIsInZhbHVlIjoiWUMrRlh0YWZ1UWN4d3lRZGpUUE1rRXBNSnV0azNYTlVHRmx1QWtJWlJSeVNpeUlla3ROaS9DeHVIcnl3Z25GSWdzN0luUXNDS3gvVHN0OTEzQzRNMlNBbDJTZlZhYTVtb014VE9RYlRIYzRVK1ppa0xIdGZOTGdLV0p6Rzd2VTAzaFcwd1orcDlkaG9EODFZamxtakpXTEV5ekx1QWVZM25FZkxPNEhvaEdjd0FXYkFwc1ZjZHZycEJWWW1TSzJhTDU3Y0R2aWdtNHN3OVZZOGVPdk9zWXR0a2NQRnNUMmxYejdwQU84VGFvRE9DcTFKd0NlT3VDYXR0NHErS3hITmo3b3JRRFA4QlBKN2NJYUJmVVV2QnI2ZHRaQW9TbHRjUkIzTTY2S1ROSHBhTGgwKzg5RE9vUnhsUVNQbmd6UENSbHFPWThHRXFHQWpyZXZsSmlZVE9YSTNSV24rUmhjS1l6UVdyZ0R4OEJ3a2tTZ2h4Rlp3QjhqUUlEZEgzTzJBcms2QmpjbCtKUkhJaThiMUs0RXg3cnNhTUFxaExOZkg0OWM1OU5IVGJSR2dFOWVwbUwrZjNaamt3MWNPR2xubzhxSXRsK25PMDhKZmJmQTYwSUQvVFNkd3pDQTR5S3dnSWFmYThDbWpES2hUYnM1YTM5TmQ3SFNLd0puZHVBZGoiLCJtYWMiOiJmNTdhNDIzZTFjMTI4MmYzYjQxZTQ2NDExODQzMmNlZjFiYjdjYTQ1MDlhOWQ0MjhkODJmN2E4MjI5NTFjZTEwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:43:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjBRa1lOVU4xakg5VDJoRStvSzRtbmc9PSIsInZhbHVlIjoibmduRkl1REhFMDVXZjBheGptVkFFaGRVVzB3cGp3MDJvNmx3TTlWRWdLb012UjJYRXJidkRQbFN6SGdsWmJRNmltQ0MxV2VmSVBWVEwzcmRoV1dIWVA0TWhMQXpkdW1RN0JiSTdTZXdvZ0dQdG03VTBxS1dlN2ZZV2ZmMWcrZFMyKzJVaDNjWjVRM1VrNXd0YjNvR21SWmcyRDJzNzR1VDB4WnZwWTVHRWU1K2ZIVWV4NzFvaUZMcTNYUDMxRStCR2kwZk5YQ2tTakdQUVVCS0pTU09JZDhwZStEYnJ3VWFOQlNHZmxiN1dOMVZITjVSYkNtbTRueC9pRmtyVVNiSXMyT0ZEb1luZEMzTEswWlRQak85UTg2aW5GcFN6UmhZeXcySm5XYWZ5WnBnNjkwRHVrMTIvQnBIUHVWODcyWnVvWStvVVIvUHRyWHpkRmFQS1ZLb3lIZGhhRytDdkpPSmhHc3lzb2FGdkhjZmk0MGpnazg5WUJsVWEvM0wybGtNQjlPeU55SHVZcDA5UWYxbjdGekZQUVV4UXdmZVFxWGRMa0t3ZUN4Y1hyUENlclNuMEdMWHd4RnhwamI1ZmJsS21wN2cvWXpxeGVkbnpxOSttekxqZ0tSeHlCbFFKZDRjSm5WTExCZlJqUDhwd2taTHJnRFBqR3B0UHJZN2hQRkwiLCJtYWMiOiJiNDg0NzUwOGJmZTEzMjA1YzhmNDY1NDY5ZjUwOWYyZTE2MjlhMDRhMjIwMDE2NzFjYTgzZDkxZTBkZjc2NThhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:43:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1641464555\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-987397276 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Cart is empty!</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-987397276\", {\"maxDepth\":0})</script>\n"}}