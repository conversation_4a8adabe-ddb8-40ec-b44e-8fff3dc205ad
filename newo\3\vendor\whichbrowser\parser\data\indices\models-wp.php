<?php

namespace WhichBrowser\Data;

DeviceModels::$WP_INDEX = array (
  '@' => 
  array (
    0 => '(HD7|.*T92(92|95|96))!',
    1 => '((7 )?Mozart|.*T86(97|98))!',
    2 => '(7 Pro|.*T75(75|76))!',
    3 => '.*T8788!',
    4 => '((7 )?Trophy|.*T8686)!',
    5 => '(Radar|.*C110e)!',
    6 => '.*X310e!',
    7 => '(Lumia ?|Nokia ?)?[0-9]{3,4}!!',
  ),
  '@- ' => 
  array (
    0 => '- BUSH Windows Phone',
    1 => '- Lazer SMART WIND',
  ),
  '@0P' => 
  array (
    0 => '0P6B140',
    1 => '0P6B180',
  ),
  '@40' => 
  array (
    0 => '40 Cesium',
  ),
  '@47' => 
  array (
    0 => 47,
  ),
  '@4A' => 
  array (
    0 => '4Afrika',
  ),
  '@69' => 
  array (
    0 => '6990LVW',
  ),
  '@7 ' => 
  array (
    0 => '((7 )?Mozart|.*T86(97|98))!',
    1 => '7 HTC MOZART',
    2 => '(7 Pro|.*T75(75|76))!',
    3 => '7 Surround',
    4 => '((7 )?Trophy|.*T8686)!',
  ),
  '@75' => 
  array (
    0 => '75eL0N',
  ),
  '@8S' => 
  array (
    0 => '8S',
    1 => '8S by HTC',
  ),
  '@8X' => 
  array (
    0 => '8X',
    1 => '8X by HTC',
  ),
  '@;H' => 
  array (
    0 => ';HD2',
  ),
  '@;P' => 
  array (
    0 => ';PD67100',
  ),
  '@<M' => 
  array (
    0 => '<model>!',
  ),
  '@A6' => 
  array (
    0 => 'A620(b|d|e|m|t)!',
  ),
  '@AC' => 
  array (
    0 => 'AC50CE',
    1 => 'ACD U',
    2 => 'Accord',
  ),
  '@AL' => 
  array (
    0 => 'Allegro',
    1 => 'Alpha Neon',
    2 => 'ALPHA PRIME 5',
  ),
  '@AN' => 
  array (
    0 => 'Andi4L',
    1 => 'Android!',
  ),
  '@AT' => 
  array (
    0 => 'Ativ S',
    1 => 'ATIV S Neo',
  ),
  '@BI' => 
  array (
    0 => 'Billy 4',
    1 => 'Billy 4.7',
  ),
  '@BP' => 
  array (
    0 => 'BP30.BM180',
  ),
  '@C6' => 
  array (
    0 => 'C620(d|e|t)!',
    1 => 'C625(a|b)!',
  ),
  '@CE' => 
  array (
    0 => 'CETUS',
  ),
  '@CH' => 
  array (
    0 => 'CHERRY-MOBILE Alpha Luxe',
  ),
  '@DO' => 
  array (
    0 => 'DolphinCT50',
  ),
  '@E2' => 
  array (
    0 => 'E260T+',
  ),
  '@E6' => 
  array (
    0 => 'E600',
  ),
  '@ET' => 
  array (
    0 => 'Eternity',
  ),
  '@FI' => 
  array (
    0 => 'FierceXL',
  ),
  '@FO' => 
  array (
    0 => 'Focus i917!',
    1 => 'FOCUS S',
  ),
  '@FT' => 
  array (
    0 => 'FTJ152E',
    1 => 'FTJ152F',
  ),
  '@FZ' => 
  array (
    0 => 'FZ-E1',
  ),
  '@GA' => 
  array (
    0 => 'Galaxy6',
  ),
  '@GO' => 
  array (
    0 => 'Gold',
  ),
  '@GT' => 
  array (
    0 => 'GT-[IS][0-9]{4,4}!!',
  ),
  '@GW' => 
  array (
    0 => 'GW910',
  ),
  '@H8' => 
  array (
    0 => 'H883G',
  ),
  '@HA' => 
  array (
    0 => 'Harley Davidson',
    1 => 'Harley-Davidson',
    2 => 'Haden',
  ),
  '@HD' => 
  array (
    0 => 'HD2',
    1 => 'HD2(Leo',
    2 => 'HD2 LEO',
    3 => '(HD7|.*T92(92|95|96))!',
  ),
  '@HE' => 
  array (
    0 => 'HELSINKI',
  ),
  '@HT' => 
  array (
    0 => 'HTC HD2',
    1 => 'HTC Mozart',
    2 => 'htc mpw6958',
    3 => 'htcmpw6958',
    4 => 'HTC 7 Trophy',
    5 => 'HTC Trophy',
    6 => 'HTC6690LVW',
    7 => 'HTC6990LVW',
    8 => 'HTC6990LVW 4G',
    9 => 'htc 8x',
    10 => 'HTCPO881',
    11 => 'HTC6995LVW',
  ),
  '@HU' => 
  array (
    0 => 'HUAWEI Ascend W1!',
    1 => 'HUAWEI ?W1-(C00|U00|U34)!',
    2 => 'HUAWEI ?W2-(T00|T01|U00|U051)!',
  ),
  '@I9' => 
  array (
    0 => 'I917',
  ),
  '@ID' => 
  array (
    0 => 'IDOL 4 PRO',
    1 => 'IDOL 4S with Windows 10',
    2 => 'id[0-9]{3,3}!!',
  ),
  '@IM' => 
  array (
    0 => 'Impera I',
    1 => 'Impera M',
    2 => 'Impera S',
  ),
  '@IN' => 
  array (
    0 => 'InfoPath!',
  ),
  '@IQ' => 
  array (
    0 => 'IQ400W',
    1 => 'IQ500W',
  ),
  '@IR' => 
  array (
    0 => 'iris Win 1',
  ),
  '@IS' => 
  array (
    0 => 'IS12T',
  ),
  '@IX' => 
  array (
    0 => 'Ixion W 5',
  ),
  '@LE' => 
  array (
    0 => 'LEO',
  ),
  '@LG' => 
  array (
    0 => 'LG-E740!',
    1 => 'LG-E-?900!',
    2 => 'LG-E906',
    3 => 'LG-C900!',
  ),
  '@LI' => 
  array (
    0 => 'Linux!',
  ),
  '@LU' => 
  array (
    0 => '(Lumia ?|Nokia ?)?[0-9]{3,4}!!',
  ),
  '@M1' => 
  array (
    0 => 'M1010',
  ),
  '@M2' => 
  array (
    0 => 'M220!',
  ),
  '@M3' => 
  array (
    0 => 'M310',
  ),
  '@M5' => 
  array (
    0 => 'M5w',
  ),
  '@MA' => 
  array (
    0 => 'Mazaa',
  ),
  '@ME' => 
  array (
    0 => 'MegaFon SP-W1',
  ),
  '@MI' => 
  array (
    0 => 'MICROMAX-W121',
    1 => 'MI4',
    2 => 'Microsoft!',
  ),
  '@MO' => 
  array (
    0 => 'MOBILE Alpha Luxe',
    1 => 'MOBILE Alpha Style',
    2 => 'MOBILE Alpha View',
    3 => 'Mondrian',
    4 => '((7 )?Mozart|.*T86(97|98))!',
    5 => 'MouseComputer MADOSMA Q501',
  ),
  '@MW' => 
  array (
    0 => 'MWP6885',
    1 => 'mwp6985',
    2 => 'MWP-47!',
  ),
  '@N8' => 
  array (
    0 => 'N880e!',
  ),
  '@NE' => 
  array (
    0 => 'NEO',
  ),
  '@NO' => 
  array (
    0 => '(Lumia ?|Nokia ?)?[0-9]{3,4}!!',
  ),
  '@OM' => 
  array (
    0 => 'Omega',
    1 => 'OM(NI|IN)A ?7!',
    2 => 'Omnia W',
  ),
  '@ON' => 
  array (
    0 => 'ONE TOUCH 5040X',
    1 => 'ONIX AWP4-215',
  ),
  '@PC' => 
  array (
    0 => 'PC93100',
    1 => 'PC40100',
    2 => 'PC40200',
  ),
  '@PD' => 
  array (
    0 => 'PD67100',
  ),
  '@PI' => 
  array (
    0 => 'PI39100',
    1 => 'PI86100',
  ),
  '@PK' => 
  array (
    0 => 'PKT-407',
  ),
  '@PM' => 
  array (
    0 => 'PM23300',
  ),
  '@PR' => 
  array (
    0 => 'PRESTIGIO-PSP8500DUO',
  ),
  '@PS' => 
  array (
    0 => 'PSP8400DUO',
    1 => 'PSP8500DUO',
  ),
  '@QS' => 
  array (
    0 => 'QSMART STORM W408',
    1 => 'QSMART DREAM W473',
    2 => 'QSMART STORM W510',
  ),
  '@QU' => 
  array (
    0 => 'Quantum',
  ),
  '@RA' => 
  array (
    0 => '(Radar|.*C110e)!',
  ),
  '@RI' => 
  array (
    0 => 'RIO U',
  ),
  '@RM' => 
  array (
    0 => 'RM-[0-9]{3,4}!!',
  ),
  '@S5' => 
  array (
    0 => 'S58',
  ),
  '@S6' => 
  array (
    0 => 'S606',
  ),
  '@SC' => 
  array (
    0 => 'Schubert!',
    1 => 'SCH-R860U',
    2 => 'SCH-I930',
  ),
  '@SE' => 
  array (
    0 => 'SeaRay',
    1 => 'Semaphore',
  ),
  '@SG' => 
  array (
    0 => 'SG 7',
    1 => 'SGH-[IT][0-9]{3,3}!!',
  ),
  '@SM' => 
  array (
    0 => 'SM-W750V',
  ),
  '@SO' => 
  array (
    0 => 'SOUL2',
  ),
  '@SP' => 
  array (
    0 => 'Spark',
    1 => 'SPH-I800',
  ),
  '@SU' => 
  array (
    0 => 'Surround',
    1 => 'Surface Phone',
  ),
  '@T6' => 
  array (
    0 => 'T698',
  ),
  '@TA' => 
  array (
    0 => 'Taylor',
    1 => 'Tania',
  ),
  '@TC' => 
  array (
    0 => 'TC70',
  ),
  '@TG' => 
  array (
    0 => 'TG01',
  ),
  '@TH' => 
  array (
    0 => 'Thunder 340W',
    1 => 'Thunder 450W',
  ),
  '@TI' => 
  array (
    0 => 'Titan',
    1 => 'Titanium Wind W4',
  ),
  '@TO' => 
  array (
    0 => 'Touch-IT HD7',
    1 => 'Touch-IT Trophy',
  ),
  '@TR' => 
  array (
    0 => '((7 )?Trophy|.*T8686)!',
  ),
  '@TS' => 
  array (
    0 => 'TSUNAGI',
  ),
  '@UL' => 
  array (
    0 => 'Ultimate',
  ),
  '@US' => 
  array (
    0 => 'USCCHTC-PC93100',
    1 => 'USCCN859',
  ),
  '@V9' => 
  array (
    0 => 'V965W',
  ),
  '@VE' => 
  array (
    0 => 'Venue Pro',
  ),
  '@VI' => 
  array (
    0 => 'Virtual!',
  ),
  '@VW' => 
  array (
    0 => 'VW820',
  ),
  '@W1' => 
  array (
    0 => 'W1i',
    1 => 'W10',
    2 => 'W1-(C00|U00|U34)!',
    3 => 'W121',
  ),
  '@W2' => 
  array (
    0 => 'W2-(T00|T01|U00|U051)!',
  ),
  '@W4' => 
  array (
    0 => 'W4',
  ),
  '@WI' => 
  array (
    0 => 'WIN HD LTE',
    1 => 'WIN HD W510l',
    2 => 'WIN HD W510u',
    3 => 'WIN JR LTE',
    4 => 'WIN JR W410a',
    5 => 'WIN JR W410i',
    6 => 'WIN JR W410l',
    7 => 'WIN JR W410u',
    8 => 'WIN 400',
    9 => 'WINJOY',
    10 => 'WinWin',
    11 => 'Windows Phone 8X by HTC!',
    12 => 'WindowsPhone8Xby',
    13 => 'Windows Phone 8S by HTC!',
    14 => 'WindowsPhone8Sby',
    15 => 'Win1',
    16 => 'Win-Q900S',
    17 => 'Win Q1000',
    18 => 'WinQ1000',
    19 => 'Windows!',
  ),
  '@WP' => 
  array (
    0 => 'WP 4.7',
  ),
  '@XX' => 
  array (
    0 => 'XXX!',
  ),
);
