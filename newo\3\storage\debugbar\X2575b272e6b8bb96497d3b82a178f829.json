{"__meta": {"id": "X2575b272e6b8bb96497d3b82a178f829", "datetime": "2025-06-08 15:29:00", "utime": **********.752416, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.845082, "end": **********.752443, "duration": 0.****************, "duration_str": "907ms", "measures": [{"label": "Booting", "start": **********.845082, "relative_start": 0, "end": **********.633511, "relative_end": **********.633511, "duration": 0.****************, "duration_str": "788ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.63353, "relative_start": 0.****************, "end": **********.752446, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "119ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01542, "accumulated_duration_str": "15.42ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.686728, "duration": 0.01269, "duration_str": "12.69ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 82.296}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.715934, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 82.296, "width_percent": 6.55}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.736315, "duration": 0.00172, "duration_str": "1.72ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 88.846, "width_percent": 11.154}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=xh9goj%7C1749396538091%7C6%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik0xZjNuM2k5NmZkd1MyMVVXSVF0WVE9PSIsInZhbHVlIjoia0p4YnJyU1A0UUg5WUp6LzRwUHRYbE12bUdKaytZeDAxQ0dhM3FyQ215VGk4dkRVUXE5L0xJcUpyOUM2SDIvZ1A3b2toTVQ1UTBIKzUvSmhJRkFlVFlsL3AwckJMTmkvYnJzSjlsVURnc1JjakdMTmw2a1VJQUVQVnFsMlFiOVBkSkpsNGNNbm5BdkpTRXpIRlpyS1pwRnRBUDgzQmR2bXA5RHF1OFAvL2xueGFpRDJhaWdmQm91NlcxRTc5UHFkbGJZdUlxbEdxYkhmYnVTU0lmeHBBN2hMOUwrZEd5d1oyaWtOeHVrRWU1a2p6SkZaQUwvU0xnSXduOUhoNHdmTXMwbkNSbGdMTnFjeWdVNVY2SlQ0ay9vRWN1NnhUZDYydFJhOStlMzhZRXQ0YXlLa1RPb2x3L0tTdFhiL2R2U2VEWVZLWHpCSk1EODU0YkhqbkRmZXk5dkNNN3hxak1mTUtsR3ZYeURqZ2o0MW1FbHlLUjA0OE10U01JbXNablZBcXlybGJ6bHlKSlEwLzFvbGJycm9sWlFJWTJKVnZ1T3FuQ3BYb244NHRoZ0FBUU5VcUlGRERmeVM3OHlOZk40bmpVUXlvVllpenRMM0xHK2JtMDhGKzg4UmZWbnFDRy9JSmhNRW4vQ1FYeTYyeVZndEQ2L3FXTThTTlF6bjJiTnAiLCJtYWMiOiJiNDQxMTU1N2JkODU3NThiZmU5YjhkY2VkYzY5ZGIwMDE2Y2Q0ZWEyZTc5YTQ3NDNhYmZkYTA3MTdhMTY1Yzk4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Iml2TkVGSUZYNG1xSE1pVGI5MnRUSUE9PSIsInZhbHVlIjoiUFljam04K0VGZEgyWWI4YnZtWkJ4azVGMWgrVzdZYnlkOGExdzNOK0k3UFpteFZ3bXo3ZlVUWlFuYjVWOXZPcFk2eUZ6WVVXeURnc1Rjb0MrbnIrQkVZbmtjaXhjaGNYcEQrVk1oa0RHVUVBQnhseSt4VmNsSkhzZDZ1RGxMLzhvWUgzdG1vVlNqVUJybytJSUJVVnhJQjZnbFhjYXBYemFQclNxTzRvS0xvK29ZMm5PbkYvMC9Oc0k5bEw1ajlSaWtaU1hLNFdHT2hQQnhtdnNhblZwVXBuSEhReVF1dlVkU0pweFhKOEhlMWNYdjRaQ3FTVVl1ckxqRUNNbzRUU3B3SFc3TSt1dzAxRm9CRnM2bFhBVHJvUUhkUjhWL2tNYmF3U0l4bStaS01mT2dBOS90V2ZLNHdtZjlyUUVpeGozT2w4UEIxZmMxdDlMNjZoeHFGS2M0Qm5IdnBTZ2x1K2dGemZFNzhNMzRLM0pXT0swS0FUd1EzMnB0dXdHU2Zrdnc5QUNJM3VYdEZMNzVaTWtKWmJjZUNmU0c3UFo4Nk82SGIvRWNuSHQwRE8zY05Rem1EdXZ0RitDT0tDQ09wNU8wM21kREkyVFJHWWFZRzhFbDFwUTdtNThvNzhOV1MvUUcxU3YxUDlSS1lNN3NuODlaMDJWOThvRUpVSjhBdDUiLCJtYWMiOiJkMTllZDc3ODkzZTE3YzZmOGY5NTViN2Q1Nzc1ZDMxODc2ODhkZGU2ZTYzMWE1YThkY2Q4OGIyYjFjMzE0MGUyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-676778913 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6s1OaNA8h4DHGuLO0qhsUHg7TnRJSWwbuakqNtUI</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-676778913\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1970455368 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:29:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InUwNkdzSHdnZ3ZwSmtibU1ZcEJaN0E9PSIsInZhbHVlIjoidFJQdUhhRXpxeTFuTEIrWnBNMG5pZzdvZzluNFBaL0Mzb0J1azFSU1NvOFhJTTdJeGVIeUhwaVhEUGtKQ2FJOXhoM2IxSGxaYmw3TmVZemdSemttVnlhUUljcEZvcVptU3ZYc1pJa3BJNlpBNnljaXhvQlJ4cVJYYlFLZ0JmaGM2dldxbzE1REhLODBpTEhFS1hSUDYwMVNGYUJiajVINExpRnpoUURKZFB4by9rcWNXV2U5U2JxMWRMQ0xrVVk2Z29Yd0VYc3ZmWjhHZUgwNDE4OUsrMDNRZjRhYjZCZmxMYWQrS3NURlRrOXlDQ2hFMEF3dWV1elk4SHhrckZsTXZwcGFRK0MxT3dVVzlRS05hZG5tOThwNS9KZVJtYzRzOTBTN2tmWmQ2MVhkMWpYYURMamN4UnVnMGdBZStUWHRrd3BqZDBDTElvMzgreG9hdjZTZXIrR2c3b09mbTdNVGthQUZOVjlvLzBJT1V4dG1abXBKbFlUWFVOL3Q5b0NvUHY3MzBjUmNIVFd0WG9OemdWaFVYZE12WlFrcVhEMVZYalZwbmtPcFhic05CWHJFSjd3OXZ6Q3BMdFNBQUsxYlFrTDZCa1BiUjQzWTNCUjFCbjFzUzVrb00wb0xqN2pFWmRTR2lENG1Uc25jZWZpSUdrT1cvMDEyQzc4T3M3WTEiLCJtYWMiOiJmNGNhYThlYmExZGRkM2ZlYzliMTJiNDEyNjE1MjczMmY1OTIwYmRiY2VmMGY0OWIzZmMzM2IxZjRkMmQ3ZDk1IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:29:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Im1SQjE5MGY2MWM3dzBhNUVsOSt3ekE9PSIsInZhbHVlIjoiY1grUTJEeFkzc0FRUXMzT2N4Z1JJVHhQTlhJbTRPYTNrVlM3dWt1Q3ZoRWxsd1VpWmxpTk8zQzhkU2RNako4V3ovOTZLbzVtTDlXekttRDZuMFJYRlgyWkpzL1ozWDU0K3lYNVpTOThTT01LbVg4emdQbFMyQTJqaHBSZzRlTURoMDNVRlU1WnJnZUVMb1Z0SlFXOWpxZmRPOVlJZ0RUb0lYUng1bnk3b2w5SWQxaU9NTEwwSjVRYmNDVEppMEQzdFptb2FrMllkNUlPSHBYOHUzQUlwKy9ib01aNWlqcDFkUzI2TjlpMjZTQ01JWnZKV0V5d2NDRXZKVnhOWEJiWFNjRnBLR2h6V3pKclZFQjlTQ3g2N2VJRGE5Q3BRMEZDVzJpZCtUakUwMytoenRPZTlUNU15NGxBZ2dtaXJ4V2NwRTcvdnZRclhkc1lEeGd4WFY1Rmlqd2dyR2pUWG5VN1FOaGRvT0RnYkZEN3dzOVZmM2xwaVZEVHlKRDBramxsMldlQ2ZoYUxmWGxyQjlQenZVb2xzR2JZK3lWb2xybFB6a1BVR21DM0RHWVFtOTArS1JnUGtxVWw5QzU4Uk9RNGZLaVJKVGdBdnEvM2F3VXdEMDJFMGI5Uy9GUUVwSGtkYVhoUFRuMHFHQTB6WFZJY2g0T0pUSUdkejVTRTR5UUkiLCJtYWMiOiIxMGE1ZjhlMTIwMzk0ZmEzOTZlMWE5ZDU5OTgxZmE1NDZjOTZkMWFiMjIyOWFiM2E0NzM3NjkwMTJiOTIxM2E1IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:29:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InUwNkdzSHdnZ3ZwSmtibU1ZcEJaN0E9PSIsInZhbHVlIjoidFJQdUhhRXpxeTFuTEIrWnBNMG5pZzdvZzluNFBaL0Mzb0J1azFSU1NvOFhJTTdJeGVIeUhwaVhEUGtKQ2FJOXhoM2IxSGxaYmw3TmVZemdSemttVnlhUUljcEZvcVptU3ZYc1pJa3BJNlpBNnljaXhvQlJ4cVJYYlFLZ0JmaGM2dldxbzE1REhLODBpTEhFS1hSUDYwMVNGYUJiajVINExpRnpoUURKZFB4by9rcWNXV2U5U2JxMWRMQ0xrVVk2Z29Yd0VYc3ZmWjhHZUgwNDE4OUsrMDNRZjRhYjZCZmxMYWQrS3NURlRrOXlDQ2hFMEF3dWV1elk4SHhrckZsTXZwcGFRK0MxT3dVVzlRS05hZG5tOThwNS9KZVJtYzRzOTBTN2tmWmQ2MVhkMWpYYURMamN4UnVnMGdBZStUWHRrd3BqZDBDTElvMzgreG9hdjZTZXIrR2c3b09mbTdNVGthQUZOVjlvLzBJT1V4dG1abXBKbFlUWFVOL3Q5b0NvUHY3MzBjUmNIVFd0WG9OemdWaFVYZE12WlFrcVhEMVZYalZwbmtPcFhic05CWHJFSjd3OXZ6Q3BMdFNBQUsxYlFrTDZCa1BiUjQzWTNCUjFCbjFzUzVrb00wb0xqN2pFWmRTR2lENG1Uc25jZWZpSUdrT1cvMDEyQzc4T3M3WTEiLCJtYWMiOiJmNGNhYThlYmExZGRkM2ZlYzliMTJiNDEyNjE1MjczMmY1OTIwYmRiY2VmMGY0OWIzZmMzM2IxZjRkMmQ3ZDk1IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:29:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Im1SQjE5MGY2MWM3dzBhNUVsOSt3ekE9PSIsInZhbHVlIjoiY1grUTJEeFkzc0FRUXMzT2N4Z1JJVHhQTlhJbTRPYTNrVlM3dWt1Q3ZoRWxsd1VpWmxpTk8zQzhkU2RNako4V3ovOTZLbzVtTDlXekttRDZuMFJYRlgyWkpzL1ozWDU0K3lYNVpTOThTT01LbVg4emdQbFMyQTJqaHBSZzRlTURoMDNVRlU1WnJnZUVMb1Z0SlFXOWpxZmRPOVlJZ0RUb0lYUng1bnk3b2w5SWQxaU9NTEwwSjVRYmNDVEppMEQzdFptb2FrMllkNUlPSHBYOHUzQUlwKy9ib01aNWlqcDFkUzI2TjlpMjZTQ01JWnZKV0V5d2NDRXZKVnhOWEJiWFNjRnBLR2h6V3pKclZFQjlTQ3g2N2VJRGE5Q3BRMEZDVzJpZCtUakUwMytoenRPZTlUNU15NGxBZ2dtaXJ4V2NwRTcvdnZRclhkc1lEeGd4WFY1Rmlqd2dyR2pUWG5VN1FOaGRvT0RnYkZEN3dzOVZmM2xwaVZEVHlKRDBramxsMldlQ2ZoYUxmWGxyQjlQenZVb2xzR2JZK3lWb2xybFB6a1BVR21DM0RHWVFtOTArS1JnUGtxVWw5QzU4Uk9RNGZLaVJKVGdBdnEvM2F3VXdEMDJFMGI5Uy9GUUVwSGtkYVhoUFRuMHFHQTB6WFZJY2g0T0pUSUdkejVTRTR5UUkiLCJtYWMiOiIxMGE1ZjhlMTIwMzk0ZmEzOTZlMWE5ZDU5OTgxZmE1NDZjOTZkMWFiMjIyOWFiM2E0NzM3NjkwMTJiOTIxM2E1IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:29:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1970455368\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1784722918 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1784722918\", {\"maxDepth\":0})</script>\n"}}