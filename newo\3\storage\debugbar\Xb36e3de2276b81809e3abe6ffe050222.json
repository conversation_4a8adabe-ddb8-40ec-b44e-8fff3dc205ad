{"__meta": {"id": "Xb36e3de2276b81809e3abe6ffe050222", "datetime": "2025-06-08 15:42:28", "utime": **********.568187, "method": "GET", "uri": "/roles/19/edit", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749397347.850572, "end": **********.568208, "duration": 0.7176358699798584, "duration_str": "718ms", "measures": [{"label": "Booting", "start": 1749397347.850572, "relative_start": 0, "end": **********.333475, "relative_end": **********.333475, "duration": 0.48290300369262695, "duration_str": "483ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.333487, "relative_start": 0.48291492462158203, "end": **********.568211, "relative_end": 3.0994415283203125e-06, "duration": 0.2347240447998047, "duration_str": "235ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 54088096, "peak_usage_str": "52MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "1x role.edit", "param_count": null, "params": [], "start": **********.475788, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/role/edit.blade.phprole.edit", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Frole%2Fedit.blade.php&line=1", "ajax": false, "filename": "edit.blade.php", "line": "?"}, "render_count": 1, "name_original": "role.edit"}, {"name": "1x components.required", "param_count": null, "params": [], "start": **********.491752, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/components/required.blade.phpcomponents.required", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Fcomponents%2Frequired.blade.php&line=1", "ajax": false, "filename": "required.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.required"}]}, "route": {"uri": "GET roles/{role}/edit", "middleware": "web, verified, auth, XSS, revalidate", "as": "roles.edit", "controller": "App\\Http\\Controllers\\RoleController@edit", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FRoleController.php&line=99\" onclick=\"\">app/Http/Controllers/RoleController.php:99-127</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.024040000000000002, "accumulated_duration_str": "24.04ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.373992, "duration": 0.01422, "duration_str": "14.22ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 59.151}, {"sql": "select * from `roles` where `id` = '19' limit 1", "type": "query", "params": [], "bindings": ["19"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 961}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 42}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 64}], "start": **********.3935418, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "ImplicitRouteBinding.php:61", "source": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FRouting%2FImplicitRouteBinding.php&line=61", "ajax": false, "filename": "ImplicitRouteBinding.php", "line": "61"}, "connection": "ty", "start_percent": 59.151, "width_percent": 3.952}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.404642, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 63.103, "width_percent": 3.453}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.426068, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 66.556, "width_percent": 4.201}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.429725, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 70.757, "width_percent": 2.87}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` = 2", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 114}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.4361541, "duration": 0.0039700000000000004, "duration_str": "3.97ms", "memory": 0, "memory_str": null, "filename": "RoleController.php:114", "source": "app/Http/Controllers/RoleController.php:114", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FRoleController.php&line=114", "ajax": false, "filename": "RoleController.php", "line": "114"}, "connection": "ty", "start_percent": 73.627, "width_percent": 16.514}, {"sql": "select * from `plans` where `plans`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Plan.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Plan.php", "line": 65}, {"index": 21, "namespace": "view", "name": "role.edit", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/role/edit.blade.php", "line": 2}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.4803798, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "Plan.php:65", "source": "app/Models/Plan.php:65", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FPlan.php&line=65", "ajax": false, "filename": "Plan.php", "line": "65"}, "connection": "ty", "start_percent": 90.141, "width_percent": 4.659}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` = 19", "type": "query", "params": [], "bindings": ["19"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/konekt/html/src/FormBuilder.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\konekt\\html\\src\\FormBuilder.php", "line": 996}, {"index": 24, "namespace": null, "name": "vendor/konekt/html/src/FormBuilder.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\konekt\\html\\src\\FormBuilder.php", "line": 938}, {"index": 25, "namespace": null, "name": "vendor/konekt/html/src/FormBuilder.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\konekt\\html\\src\\FormBuilder.php", "line": 911}, {"index": 26, "namespace": null, "name": "vendor/konekt/html/src/FormBuilder.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\konekt\\html\\src\\FormBuilder.php", "line": 888}, {"index": 27, "namespace": null, "name": "vendor/konekt/html/src/FormBuilder.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\konekt\\html\\src\\FormBuilder.php", "line": 851}], "start": **********.494137, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "FormBuilder.php:996", "source": "vendor/konekt/html/src/FormBuilder.php:996", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fkonekt%2Fhtml%2Fsrc%2FFormBuilder.php&line=996", "ajax": false, "filename": "FormBuilder.php", "line": "996"}, "connection": "ty", "start_percent": 94.8, "width_percent": 5.2}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 538, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Plan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FPlan.php&line=1", "ajax": false, "filename": "Plan.php", "line": "?"}}}, "count": 542, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => edit role, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1683464469 data-indent-pad=\"  \"><span class=sf-dump-note>edit role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">edit role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1683464469\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.435381, "xdebug_link": null}]}, "session": {"_token": "fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/roles\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/roles/19/edit", "status_code": "<pre class=sf-dump id=sf-dump-181077238 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-181077238\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-580923828 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-580923828\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-275960152 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-275960152\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1750873043 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=xh9goj%7C1749397342415%7C18%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IngvVzFDWExBa2QxQnVJeDFPNndyU3c9PSIsInZhbHVlIjoiSFhtcVhFYWhuaHFkeFNGdmRETHJzY1hlVTlidmI0Zk9jT1FKb0NoUWFzTTQ0cVV4YVFGSC82Y1FYMkJpM1pmNjRGL0lwbk4wQ294b0Y3YzhoWnhFSFRCUXNMSlZzaVpqUWZBNGJsKzdUbjNVajFwUjZmTDY4K0thOGxSSW9HVWg2UGxLSUVjYTNidXlXY1NjM1lZMFQxcFM5SGtVeWluU1VxTWM3blplWWcrTGVVR3ducDFhMGFYbXJDRjFheEh2ZGtOVjVpcDVTTzNnSVRWaDhSaWJPTnZLd1BtbG1hbit1UjlobDU4MUliV0k5Uzh4c0wwZnVFd0h5ODczY3ZTSzFzeUNuUXp3WU0zMFhjTURobzI4ZS8yeWtaS0ZwUG5BdjJ4blI5bG5ETXVERUJIWWRMVFlrWkZ6SE5maHVGQUwvRDJPalhSUnU1RkVpakZGTlBYeVBqekdaMHNEVzJqaTU1QjgzNm5oSFQ0Z0wzdkIyVkFIbkpqY1M5SysrTnBSYlZIMGl1ckFMMEJ0RHNHUVZNRVdQRW5ub0Ewc1p5bk94b3Q0SXFjRFBKZE9tZUcrT1hkU094RWtDc1RTM3F2SVJRWnhvSU1vOEVnM1g5SXZ6S3BjYUxIeE8wb1RrZVFSWmVFZUdJc0x4aU10SndhU3VBc1d4dzBPNFI4SVdPS0IiLCJtYWMiOiJiZDMxYjFiMzg4Mjc2ODJmYzIyOGVmNDVjZmVjYzE4OThjNmQwYWZjODI1MTllNjU3MDY4MjZlNGY2NjgzNTA1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Im5JUFR5T3JOc3B0cVhHYzVFU3BiR0E9PSIsInZhbHVlIjoiTndvdTRkdzM5U1EyeDE0Rnovay9QK1FLVC9UWTZPSW9SR1RDTm5CTHF2TUV0cU9acHQ1M0lVNkJBb0xqdzFhb0phWk5SRjJtVGo1bEROeEVEQWRGUU9jSUdSczlWUjhWcXc2dmdjeVBFRk04Z3RRL2pDV3h5blJKVmJxZkxhVTZKdG8wSVZxcVRuMjFncTZxMkdmQjMvNkRBdVJkeVl5MjRFcHhaRkhLTWpNaGJwOTNVRXUvL3pFRzNkOXBoSmpONlpiRjR2TUJUc1hkNjM3NzBaVG01MlU4aHJPS3d5ajBpYjFCT054aHlZa1g2eUR6U2Z1QlpRZWRlK1hDUmpoSjlrckhiNGZ4cjk5SmljSGFUREJjZnRUYWhSbUl1SUJlOFBSSlRPc21PWitqMGY2ODQrTW92RWJlUEE4T3pXeGxsY2FXa1FXYTYzZllRengrcE5DTEtqeGcrVFJ6SURDdENYc1Vld1ptTHUyU0lWU0dTcFZnTmtrVDNMOTNuSGNoditXUkx2R2V2cUZRcHpPY1UyQWdNT2hhTUkrUkQ3MEJmcHcvdEl5aVRLa3hPMXROczFnN2xia3k4MnQ5OUptVUFSQkViUzBjYnVoTGlCWCtOZXNuRlJLR2Yra1NpUythOU5xTGQ3ZGpDeU5hdFhIKzczSWRIcFI1QlcvVW9JdmoiLCJtYWMiOiI4ZTIxYjM0MGRhZDQ3NmQxZDgwZjc1MjBiYTY4ODFhYWQ0YmRlNzU3MWZlNmU1ZmEwM2M0NTNhMjIzZTQ4M2VkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1750873043\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-847208681 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">l7xz5q4Zkpyo7Zd0rhHsE8M6pi3wac4XFWhsi0lQ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-847208681\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1990711487 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:42:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ii9jWjFXeStWclZDK1pqUDVrNWUzNlE9PSIsInZhbHVlIjoiUFNYb2YwenFLejRJQW42MDJ6d29STGI0d0RSWHFzTGJzNEZhaG9yd3FvcFRkMmJnMkhXUm5FMk9kYnJZalA0SGYvRTl6M25mMTMzMVpzdEJoRmIxV2FTVUZUU3hSNTZnay9GeGYxVjBzY3lLSVE5WTlvNCsrdEhRYzZsU0U3N1IvYkJBZ0cxZ25RcmR0YlFuMWdBK0kxUTJSRmVKK0lhK1RkdklBTGYvMFlBUE1TdU1yNUR6ZDNLQkFFTThRT0dqNzJINzdMbFBwTlp6VEdvWmRhRFNENHg0UVFwSVI3YjhlQkZkeWsvK3FrTHdOVFNNV01BVVF6c3ZqNk03K2FoNjAxdTcvcy9KVU1uRDhIdXlaSWFEc09EVmtaWWJ4V1BQcXA0MXBlOGYrOFdCOWxRWmFhTnVEV2dpZ2ExWk8xeWRtUEJGcnlJT3hjUWRUQWFCNjZqN3VPcmVwL2NLWGxjazRWMG0wcnFjczJuTFczZGo5bzRNRnhZZGZLWkcyM0MyNGVrU2hBTGl0VEZKL3dWUEhRbHdpWGRlbHMxb21jK1p2a2thc3BYVkRub2w3NUxFRW5lOTcyamlqNVpWNk9zTWhwQUNkWjBONlNFQWdwaGY3MVdQK2tWNUZMbmhnUUsvc3RZQWttL1doTUNZYWx2dUdVMGd1dVpkZGRMRVEyNFgiLCJtYWMiOiJhNWRjODEwYjgwNGE3OTM3OTE0NWM0MTY5OTIwYTY4ODE5ZTAxNjhjMThhM2M0YTdmMjBkZjJiMzBhNTMyMjczIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:42:28 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ik9DSjExMTFEdW8zbEE5SGJIcG01U1E9PSIsInZhbHVlIjoiOStPTjd5N3BHWU0vdFkvM1dKWGhGUkprZ2JNSGJrbllhVUtKV0R1VkVmZWlDUTBBNWJhSlBaOU1SOXg0NHhnWVJzSXlsazh5aFlGMTFpQXU0ejJYOHdUdzRRcG1jK1JHSFFTeXFlZWVNdkhFOGlqVCtnM0kxSHlyVkpUSVVtdXpFK0N4UXlmWHJkS1FrZjlFaE50VkhJZWtxSm1uTDFrKzNTOEJseHhyWkdQeVJoQVlCR1prWXhLSXRwVjI5Vm5KUkJxVTRVZnp1SkwzVEZrM1YvdVNkc3BGaElYV1Y4RWtQeVhBN1hrMGNUVUhhaWtFbEhBb1NZUnd6WG5JZWtZdkVSSFpJZzlIdEF0SG9LeTlaNjRWdGpUcmFhdU9ySFZmVU81ZGtIR3g2TWdvMzZMZmtzZGpkeGFUYW9YQkNZcElDYW1KNzdWcG1NdEg4WjFNYS9peFU3ZDZGc0xYY3JZeXBJR0Y4WGliT3RRUS9BdWMxbXR1S3RLRncwWG1sMVUvbUpqOGEwdTMwVUg4bHQ4RXlqd080UHp3ZFJpMVowcE9UemlSckdGU0lWOHQycWxIamJEZ090UFI5dTByV2p4QkpuWFFNOG52OVZMQ2Z5YnpZNlJ5enUyNW01eG9kWmhBOS9OdWVpTHVUeVdKRUpSVGdoc3E1cFFxYmlvMXdrMjQiLCJtYWMiOiJmMzY5YjNjZGFhMDAwYjg3NWMyYzcxN2Y5MGY1OWZhNzYzNjY5OGU5YjdhMmUyM2NlZmJkMmEzZWU4MmRlYTU2IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:42:28 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ii9jWjFXeStWclZDK1pqUDVrNWUzNlE9PSIsInZhbHVlIjoiUFNYb2YwenFLejRJQW42MDJ6d29STGI0d0RSWHFzTGJzNEZhaG9yd3FvcFRkMmJnMkhXUm5FMk9kYnJZalA0SGYvRTl6M25mMTMzMVpzdEJoRmIxV2FTVUZUU3hSNTZnay9GeGYxVjBzY3lLSVE5WTlvNCsrdEhRYzZsU0U3N1IvYkJBZ0cxZ25RcmR0YlFuMWdBK0kxUTJSRmVKK0lhK1RkdklBTGYvMFlBUE1TdU1yNUR6ZDNLQkFFTThRT0dqNzJINzdMbFBwTlp6VEdvWmRhRFNENHg0UVFwSVI3YjhlQkZkeWsvK3FrTHdOVFNNV01BVVF6c3ZqNk03K2FoNjAxdTcvcy9KVU1uRDhIdXlaSWFEc09EVmtaWWJ4V1BQcXA0MXBlOGYrOFdCOWxRWmFhTnVEV2dpZ2ExWk8xeWRtUEJGcnlJT3hjUWRUQWFCNjZqN3VPcmVwL2NLWGxjazRWMG0wcnFjczJuTFczZGo5bzRNRnhZZGZLWkcyM0MyNGVrU2hBTGl0VEZKL3dWUEhRbHdpWGRlbHMxb21jK1p2a2thc3BYVkRub2w3NUxFRW5lOTcyamlqNVpWNk9zTWhwQUNkWjBONlNFQWdwaGY3MVdQK2tWNUZMbmhnUUsvc3RZQWttL1doTUNZYWx2dUdVMGd1dVpkZGRMRVEyNFgiLCJtYWMiOiJhNWRjODEwYjgwNGE3OTM3OTE0NWM0MTY5OTIwYTY4ODE5ZTAxNjhjMThhM2M0YTdmMjBkZjJiMzBhNTMyMjczIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:42:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ik9DSjExMTFEdW8zbEE5SGJIcG01U1E9PSIsInZhbHVlIjoiOStPTjd5N3BHWU0vdFkvM1dKWGhGUkprZ2JNSGJrbllhVUtKV0R1VkVmZWlDUTBBNWJhSlBaOU1SOXg0NHhnWVJzSXlsazh5aFlGMTFpQXU0ejJYOHdUdzRRcG1jK1JHSFFTeXFlZWVNdkhFOGlqVCtnM0kxSHlyVkpUSVVtdXpFK0N4UXlmWHJkS1FrZjlFaE50VkhJZWtxSm1uTDFrKzNTOEJseHhyWkdQeVJoQVlCR1prWXhLSXRwVjI5Vm5KUkJxVTRVZnp1SkwzVEZrM1YvdVNkc3BGaElYV1Y4RWtQeVhBN1hrMGNUVUhhaWtFbEhBb1NZUnd6WG5JZWtZdkVSSFpJZzlIdEF0SG9LeTlaNjRWdGpUcmFhdU9ySFZmVU81ZGtIR3g2TWdvMzZMZmtzZGpkeGFUYW9YQkNZcElDYW1KNzdWcG1NdEg4WjFNYS9peFU3ZDZGc0xYY3JZeXBJR0Y4WGliT3RRUS9BdWMxbXR1S3RLRncwWG1sMVUvbUpqOGEwdTMwVUg4bHQ4RXlqd080UHp3ZFJpMVowcE9UemlSckdGU0lWOHQycWxIamJEZ090UFI5dTByV2p4QkpuWFFNOG52OVZMQ2Z5YnpZNlJ5enUyNW01eG9kWmhBOS9OdWVpTHVUeVdKRUpSVGdoc3E1cFFxYmlvMXdrMjQiLCJtYWMiOiJmMzY5YjNjZGFhMDAwYjg3NWMyYzcxN2Y5MGY1OWZhNzYzNjY5OGU5YjdhMmUyM2NlZmJkMmEzZWU4MmRlYTU2IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:42:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1990711487\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1187216503 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1187216503\", {\"maxDepth\":0})</script>\n"}}