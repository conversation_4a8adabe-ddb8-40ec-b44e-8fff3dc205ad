# [YooKassa API SDK](../home.md)

# Class: \YooKassa\Request\Payments\ConfirmationAttributes\ConfirmationAttributesFactory
### Namespace: [\YooKassa\Request\Payments\ConfirmationAttributes](../namespaces/yookassa-request-payments-confirmationattributes.md)
---
**Summary:**

Класс, представляющий модель ConfirmationAttributesFactory.

**Description:**

Фабрика для создания подтверждения платежа.

---
### Constants
* No constants found

---
### Methods
| Visibility | Name | Flag | Summary |
| ----------:| ---- | ---- | ------- |
| public | [factory()](../classes/YooKassa-Request-Payments-ConfirmationAttributes-ConfirmationAttributesFactory.md#method_factory) |  |  |
| public | [factoryFromArray()](../classes/YooKassa-Request-Payments-ConfirmationAttributes-ConfirmationAttributesFactory.md#method_factoryFromArray) |  |  |

---
### Details
* File: [lib/Request/Payments/ConfirmationAttributes/ConfirmationAttributesFactory.php](../../lib/Request/Payments/ConfirmationAttributes/ConfirmationAttributesFactory.php)
* Package: YooKassa\Request
* Class Hierarchy:
  * \YooKassa\Request\Payments\ConfirmationAttributes\ConfirmationAttributesFactory

* See Also:
  * [](https://yookassa.ru/developers/api)

---
### Tags
| Tag | Version | Description |
| --- | ------- | ----------- |
| category |  | Class |
| author |  | <EMAIL> |

---
## Methods
<a name="method_factory" class="anchor"></a>
#### public factory() : \YooKassa\Request\Payments\ConfirmationAttributes\AbstractConfirmationAttributes

```php
public factory(string|null $type = null) : \YooKassa\Request\Payments\ConfirmationAttributes\AbstractConfirmationAttributes
```

**Details:**
* Inherited From: [\YooKassa\Request\Payments\ConfirmationAttributes\ConfirmationAttributesFactory](../classes/YooKassa-Request-Payments-ConfirmationAttributes-ConfirmationAttributesFactory.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string OR null</code> | type  |  |

**Returns:** \YooKassa\Request\Payments\ConfirmationAttributes\AbstractConfirmationAttributes - 


<a name="method_factoryFromArray" class="anchor"></a>
#### public factoryFromArray() : \YooKassa\Request\Payments\ConfirmationAttributes\AbstractConfirmationAttributes

```php
public factoryFromArray(array $data, string|null $type = null) : \YooKassa\Request\Payments\ConfirmationAttributes\AbstractConfirmationAttributes
```

**Details:**
* Inherited From: [\YooKassa\Request\Payments\ConfirmationAttributes\ConfirmationAttributesFactory](../classes/YooKassa-Request-Payments-ConfirmationAttributes-ConfirmationAttributesFactory.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">array</code> | data  |  |
| <code lang="php">string OR null</code> | type  |  |

**Returns:** \YooKassa\Request\Payments\ConfirmationAttributes\AbstractConfirmationAttributes - 



---

### Top Namespaces

* [\YooKassa](../namespaces/yookassa.md)

---

### Reports
* [Errors - 0](../reports/errors.md)
* [Markers - 0](../reports/markers.md)
* [Deprecated - 25](../reports/deprecated.md)

---

This document was automatically generated from source code comments on 2024-07-02 using [phpDocumentor](http://www.phpdoc.org/)

&copy; 2024 YooMoney