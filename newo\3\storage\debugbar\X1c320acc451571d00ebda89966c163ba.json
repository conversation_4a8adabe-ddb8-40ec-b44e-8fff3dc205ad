{"__meta": {"id": "X1c320acc451571d00ebda89966c163ba", "datetime": "2025-06-08 15:44:40", "utime": 1749397480.008576, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.288961, "end": 1749397480.008597, "duration": 0.7196359634399414, "duration_str": "720ms", "measures": [{"label": "Booting", "start": **********.288961, "relative_start": 0, "end": **********.915706, "relative_end": **********.915706, "duration": 0.6267449855804443, "duration_str": "627ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.91572, "relative_start": 0.6267590522766113, "end": 1749397480.0086, "relative_end": 3.0994415283203125e-06, "duration": 0.0928800106048584, "duration_str": "92.88ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45682536, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01402, "accumulated_duration_str": "14.02ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9589372, "duration": 0.01239, "duration_str": "12.39ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 88.374}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.9843879, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 88.374, "width_percent": 6.419}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.994902, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 94.793, "width_percent": 5.207}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-62491529 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-62491529\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-469737234 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-469737234\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1355311187 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1355311187\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1430493054 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749397477657%7C77%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkVubjBuMmVRY2JXYUdVbnExMkJPMUE9PSIsInZhbHVlIjoidnY5Zi83bm0ra0RnMnNheTVxYjNINC9uMDlQMWV2QSsybXRNV3R0elFOWkQwM1ByUzhPd0tncmVhaUkzM3NJWXJzQkpNS3FkdWhjWkFKcmRLUjZGOVRWc21pL0Ztck54TVVyRXFCbTNaQ0FQMXcxM3dNUU9JUVFLcnFWaDhOUW1KYzFnck52YUpXOWRqQVkzWWFyQ1BESWZWeFNRcjc4ZVAxS3FjMEJyYnhvUGMycjlYMmVkVVhrdkJtVmYzYnZ3UUtOeFVIdXUvVS8zbllSWTE3V3JMSVRFNzU0R01ZcTRCMDdyQ2Ryam1pK1pST0dqSE9KSEZvWUtIRFRheVFXSHR4WVFjNkRZNVUzQmQrM1dLZEMyRVBYM24zb2JJMFRuRFBqYlFDdlprRnlGUDZBQ0dpS25Rdkh5S0ZFRm9nNEVZWWhmUHFTcnpCaWtuM1UrT3pvbDdpYWE4WVd0OUkxd0xqRzkrZm1aVW5BUzFoOElvNXI4WHc5NjVzSGF0a1ZCYTVhQW9lTit6VG9CODNyOURiQW9hTjkxSXRianBwTld4ZHpwYnRBQXhYVHp4dEdzUitmTkdjYlBoajNHbWFPdXFSd3o1TmxGeTFRRVd1end6TkFzOFYwNjZkOTNLekZWTUFhM3BsQTB0NDEyMjBTOTF1RTNpVmZDd281RSs0d1kiLCJtYWMiOiI4YWNiNWYxNjJiMmQxMDJlNGM3MWRmNWFlMTYxOGY3OWRhY2ZiZWRhM2Q0YTc3ODZmNDI1N2MxMWIwMzlkNzkyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkZidFFQNVAxSUdlczZsWFBGdUdNUXc9PSIsInZhbHVlIjoiQnVVMit4QkNDaER0dmFENEN0YVFYWXorRTRKTm9CTk9OaEZJMzB1WWNoMkk3cDhITUxwSkk4aU96Zk9ZV0ExdysrVm5KVHNiQytEWmpNM2t2cUUyWlN2RlJnbGE5QmpnRC9jSk5EaDNtNWZJU0ZqUDdKYW1vemFxV01YK3J6QkRCK3RtTU12UlhWdjhtNFhmaHhMRHBmR3lrc3hqYzBpOW9hT1VoMFVPdjluTTRxU3NmN0JvWHJUWXg3SFNXK1hKSFVHNXkwU1FkRFJUeHluMitZMDBLWWRwN0JXUy83dFVPdjdoRU1WOVZFOXBPc3A3SWpqSnVnL1c4SXprWm9HcWVEVVgzT2lZaUc0VTM2MlM1dnBYTFplWXQ4ZUdVQnMyakMvLzRzNC9rQ2J4Y1RuNnJ0TXdiM2R6eUxnQVlMdVdPWUJZYkFUVWo2NXZnQllCZEFRTFRYRzRvWVJOaEZTWjgrZkc4S0JkT1V4WmFsMURYeVhnbktreG81U2V0aml0bzNOWTlGVC9FUjB3cnA5a1RVaStTSHZQZ3dLTzMwVFFoR09JQzZhMnVocUVJTHBCK243Qm15Tlp4S0gyVXRCSzExRXpuYVdDR2t2VTZEQXc5M011UExPMVI1S0ZaTnRNbjY5dXdLUFlOZDlBT0UzcXpqZFZTa2NHbE5ZZHQ1VVgiLCJtYWMiOiIxYTUxZWE0ODA1MTI3MWM1OWNhNDZmYThjMzhlYzVmMWM1N2MwYmM4NGE4YzNmMzcxOGZiMzM3ZThmMDY1MWY2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1430493054\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2032634975 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xNV7SvMX3AgbiuDVO62eNXfryfZD4KbWr3rEIkXt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2032634975\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-835661000 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:44:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ikh1YS9VQ1FmZkV4Qlk5cTNIUHJhcEE9PSIsInZhbHVlIjoiTUkyNlpZbTc4bitldko2VkVIaDBHejNnSGJNYkRaRDRNRDIrZnVMdTh2V2ZOTUcwdVUyWFFUQ2tuSUwwaVB4Zjg2Y0svbmVMUjIrcFYvM2EwQkxZTHc1eDRjYnZpZXoxSVNHWTJHQ21yNWF2Q1FmaDFramc4bzNPVS9RMWVtR3FnN1A0d2xkRG94SXJDWmhiK3JycGNRUDM2OGVwbHZ1djBTbEQ0THFEYWlVYmNxaUhwTzhkQW9UVnZjSjVNaDROc25aK0xLd3hBWlE1NnJBWXNOMFZnTEZHZ0RlcUFxWDQ4NmV1Z0ZQN0k1dnFtL2JpQ1VPYXVnWXR5NnRCWlNNRnphbDRPRDJyRVRpbWhHRnhKbFc5Vks3aEVtUVNuVlRoaDI4NC9tQ0d2NGxiVmt1enRDaFhJa0tlRG1DSFVOSU1DaCtXNThrY0EwRGJkSjFzblYzZ3AxcEU2anJhZ2hMSGdybVFORkxUNVFKR3d3akZkU2MreGNOMTVHRjJxeWJ1VG1mU2x5aCtoWTJ0Y1ZTZWNBMW5pSkxkdlFWZkN6dHNHVVVld2VDOG5maEhQNHY0L0lyR2NBWDlSbnZndG1yR3RTSlNRYUpadDVqL0p2Yi9Pall5eW9sQWFQTU9tRFVYSEViVG50STduOFhMTlMvVXlxeXZmMVRBVkFNZ3IvcEQiLCJtYWMiOiI2OGMyMDZiOWU5OWI0ODgyY2U3ODlhNmQ3Y2I0ZWQxZTE3YzFiYmU0MGY2NDgxN2Y2YmE2MGIxNGRiNjMwMDc2IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:44:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Im9zeS9GSm1lUXZRVmlsOHgwSktsWmc9PSIsInZhbHVlIjoiL1BMUlEzRWd5QkRRZnJtT3c1VldsQ2kyUGw1TkVNVnAyU3ZaaEo3N1daVnpzTHQ1YjJQNnBkOHpKOGtmSStGY2ErcUtSM0JlKzFZdDF5b1lPNGZ2UWFxOTllSEVTWm00RW5aYlozYm43RTFOMEN6RmtJN1pwSmltVHRnWW9DcFNkRzQ5U2JFQldsWEpqVm9OaU9sYWpWbkNEN1k2YTdVc2lPV254MWpVVXJEdGRHNSt3d2I5R1NVM1NLd2Rmb2MrU28xTWx0dXBCU0d6enY2UXVlTlhhYU5vVmlQSHZaRVcvWTIzNWo2T09UT1J6OFgvRWl4bEh1NlZENThiODRBbGticHNKcTd6TEhqQ2MwK3Y5YTkyMGZITDZnSURUWXFRbUZwbTNPUEs5RVlYdlMwTnNEMFR6L285M0NoOHZwai9mVUNuUXh4UzJDaTVvdGU0Z1Z2REx1amZaUVR4ODdPWDJMR3VsOXcrWnNMbnlWWjRhbjRyRjZqbFJYWnl2Rk8vc0RXdFVTdUNzaEh2dGY4eXBFYTFudUd4eHcrakgwVE5wWXE5cHB5bkFQUGppV0t0SHJ2OXhwNitka3FyTWl3QWpTVDNSQ0JmMmJTa0laZDE5b2p2REF3bHhGYmkzNUtmek8yOEFibmR4d2pQZllISzVtcHNzMGhiWVE1c2ZTNEkiLCJtYWMiOiI5YTM0MTQ1YjRhYjhjMzZkNjQwNGRjYWQ4NTQwYTcxN2JjZjA3ZDBlMGIxZjk1NTdkN2E1OTdjZDE1OTcwMDVhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:44:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ikh1YS9VQ1FmZkV4Qlk5cTNIUHJhcEE9PSIsInZhbHVlIjoiTUkyNlpZbTc4bitldko2VkVIaDBHejNnSGJNYkRaRDRNRDIrZnVMdTh2V2ZOTUcwdVUyWFFUQ2tuSUwwaVB4Zjg2Y0svbmVMUjIrcFYvM2EwQkxZTHc1eDRjYnZpZXoxSVNHWTJHQ21yNWF2Q1FmaDFramc4bzNPVS9RMWVtR3FnN1A0d2xkRG94SXJDWmhiK3JycGNRUDM2OGVwbHZ1djBTbEQ0THFEYWlVYmNxaUhwTzhkQW9UVnZjSjVNaDROc25aK0xLd3hBWlE1NnJBWXNOMFZnTEZHZ0RlcUFxWDQ4NmV1Z0ZQN0k1dnFtL2JpQ1VPYXVnWXR5NnRCWlNNRnphbDRPRDJyRVRpbWhHRnhKbFc5Vks3aEVtUVNuVlRoaDI4NC9tQ0d2NGxiVmt1enRDaFhJa0tlRG1DSFVOSU1DaCtXNThrY0EwRGJkSjFzblYzZ3AxcEU2anJhZ2hMSGdybVFORkxUNVFKR3d3akZkU2MreGNOMTVHRjJxeWJ1VG1mU2x5aCtoWTJ0Y1ZTZWNBMW5pSkxkdlFWZkN6dHNHVVVld2VDOG5maEhQNHY0L0lyR2NBWDlSbnZndG1yR3RTSlNRYUpadDVqL0p2Yi9Pall5eW9sQWFQTU9tRFVYSEViVG50STduOFhMTlMvVXlxeXZmMVRBVkFNZ3IvcEQiLCJtYWMiOiI2OGMyMDZiOWU5OWI0ODgyY2U3ODlhNmQ3Y2I0ZWQxZTE3YzFiYmU0MGY2NDgxN2Y2YmE2MGIxNGRiNjMwMDc2IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:44:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Im9zeS9GSm1lUXZRVmlsOHgwSktsWmc9PSIsInZhbHVlIjoiL1BMUlEzRWd5QkRRZnJtT3c1VldsQ2kyUGw1TkVNVnAyU3ZaaEo3N1daVnpzTHQ1YjJQNnBkOHpKOGtmSStGY2ErcUtSM0JlKzFZdDF5b1lPNGZ2UWFxOTllSEVTWm00RW5aYlozYm43RTFOMEN6RmtJN1pwSmltVHRnWW9DcFNkRzQ5U2JFQldsWEpqVm9OaU9sYWpWbkNEN1k2YTdVc2lPV254MWpVVXJEdGRHNSt3d2I5R1NVM1NLd2Rmb2MrU28xTWx0dXBCU0d6enY2UXVlTlhhYU5vVmlQSHZaRVcvWTIzNWo2T09UT1J6OFgvRWl4bEh1NlZENThiODRBbGticHNKcTd6TEhqQ2MwK3Y5YTkyMGZITDZnSURUWXFRbUZwbTNPUEs5RVlYdlMwTnNEMFR6L285M0NoOHZwai9mVUNuUXh4UzJDaTVvdGU0Z1Z2REx1amZaUVR4ODdPWDJMR3VsOXcrWnNMbnlWWjRhbjRyRjZqbFJYWnl2Rk8vc0RXdFVTdUNzaEh2dGY4eXBFYTFudUd4eHcrakgwVE5wWXE5cHB5bkFQUGppV0t0SHJ2OXhwNitka3FyTWl3QWpTVDNSQ0JmMmJTa0laZDE5b2p2REF3bHhGYmkzNUtmek8yOEFibmR4d2pQZllISzVtcHNzMGhiWVE1c2ZTNEkiLCJtYWMiOiI5YTM0MTQ1YjRhYjhjMzZkNjQwNGRjYWQ4NTQwYTcxN2JjZjA3ZDBlMGIxZjk1NTdkN2E1OTdjZDE1OTcwMDVhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:44:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-835661000\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-599449916 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-599449916\", {\"maxDepth\":0})</script>\n"}}