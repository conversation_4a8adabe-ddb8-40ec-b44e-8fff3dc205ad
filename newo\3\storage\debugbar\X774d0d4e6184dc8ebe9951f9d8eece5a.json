{"__meta": {"id": "X774d0d4e6184dc8ebe9951f9d8eece5a", "datetime": "2025-06-08 15:29:17", "utime": **********.220257, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749396556.411035, "end": **********.220278, "duration": 0.8092429637908936, "duration_str": "809ms", "measures": [{"label": "Booting", "start": 1749396556.411035, "relative_start": 0, "end": **********.119798, "relative_end": **********.119798, "duration": 0.7087628841400146, "duration_str": "709ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.119811, "relative_start": 0.7087759971618652, "end": **********.22028, "relative_end": 1.9073486328125e-06, "duration": 0.10046887397766113, "duration_str": "100ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45154336, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01591, "accumulated_duration_str": "15.91ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.166846, "duration": 0.01383, "duration_str": "13.83ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 86.926}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.1954832, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 86.926, "width_percent": 3.96}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.207394, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 90.886, "width_percent": 9.114}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-478279834 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-478279834\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1402717056 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1402717056\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1300293842 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1300293842\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1116433571 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=xh9goj%7C1749396546993%7C7%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImF5citVUnlMQno4WGw2VWZFSlM4ZFE9PSIsInZhbHVlIjoiZzZQTzZXdGdrcDFKVmhJcjNQSVFnZ1dQUFVZYmF1ZkxnV0E1RWx4VWZxWkFleDJjTy9YU2ludjhLMGRhcEQ3TDFMdlRlekJJekhDOThOVDFwMHhnRFJ4M2dsTnlkaDNQcWFmSUNqdVRqY0tFM0g5a1pNMnZ2Zld6M2l4eG04akZQSDVzNDMxWjRseDl4Z3FzMTV6bDZUam5Mak04aFpQcVpDUTZDREt5YklZSHUvbE1INjN2d1lHbHl2L2ZDdDVIbDVqMEJzVUVwUlJqVkpiV3RmMHBKcmJpNDQzeXVlN3VkMExtS2h3bVltK1lrQWZtN001KzBwbTVKa3FkVndnN2tBUFJ5L0I3bmtDRHc4MUxYK3ZkZlVpMk4yK2J3TEZnWG52R1Fqa3lyc2NXVUdMOWxkUXFYNDRlb1VpMWkraStsQndVNmF4T1BWQ3FhVnZ1N1NTYU1XKzZPdC9JcUNPNEJ3d0lBWTZaUVYxckwySVJXKy84VElKYWlpN3UyUmVLYjFLSjh3aFoyUi80cWZ5bncxclBMa3RONWpKTEpJakJpTzFDbXdzZ0ZmUWpTc0tmRmFZdHlVK0kyVzlDUGwzbjhMOUppUjROOGdLYU02ZHk2RmdKQlJianpzKzFpMTBlR09hRWxtbjhwcjltSTJNdE9GUjhteFB4VkpVakhKc2QiLCJtYWMiOiI2NzBiMTMyMjY3NTViMTVmZGYwNjU2NjZhZmY2ODEyZjY4OGY5YmFjMzhjYjJhYTg1ZDM1ZjVmNDcyODQ0MTQ1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjBvWU9pMG9iT0VwcTNIRGdYYWUwYmc9PSIsInZhbHVlIjoiTytSaG1yeVY2UTZuZWxRVmpLNkF5OFFuMm9LNXJJbXNvY1Q0a0gwaHM1NFpsU1R2aiswbTNRQXpJNXBuRjlRamVDSFU5VSs3b1daUzZNYytFNTVHaTBXUDhKcWRWejNHMXBaNFAyRGozblY5OXQ0blQyUnFtWUtGK2JZa2plVzZtajk5RkJrYno5djh3cDhreGR6SnJLaUJMUGVWSXUyazhZTVpHK29PY1hxQ2hGNW5tUlBUU1RRdFlyWmZtNjNGbGtVMVNGQ0kwd3FGM3FYUGJraWl6NEs5ZzY1aDh0bkFXSjlSUEhFTGhFc0xQVUtJZm5CbUJ2L1lnRHhXeEIxR2Z3TkhnS3Rzc3ZySExQZzJvMDF0S0NNaDZZVktPNW1jS2pmTHZRMjNOYm1HdFJNVzhtN2VNTm1QOWJNQmRNamZIV1YwVzVrUkl2OUlzbjJQQUJtWmE1dXdLOG9xajFYZC8xUmxGWWVndGtza0dKcGVLWlMxMmhvUitXUUIzVktDRkhVU1lDOFhBMGN5STFwZ3l4eHp1R2EranRPVEU1dnZIY1Q4ajN4cGNXaEpHMkdpUEJoK1dtam9NQ0d6clV1SkFTMmNxYkhPVVVXSkdWM25INmpZV0NLdmdQNVF3MHQzMC9jaU9HRDNDaFUrSzkrVHRuNnpTbGQvTXRiQzdIVGQiLCJtYWMiOiI4NTNjMjVlMzc4ODI3Y2Q5MTIzN2I2NGVkMTU3ODEwZDk1MzA2NWRiNDE2ZTc3MjYxZGI5ZjlkMDhiZjA0ZWJlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1116433571\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">l7xz5q4Zkpyo7Zd0rhHsE8M6pi3wac4XFWhsi0lQ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1789655851 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:29:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImZVT0poemhrK2hXcHVET040UnExT2c9PSIsInZhbHVlIjoiZDZKNk5qTmI1c2hqSmZqa2h6bUNMR003UlNPMTg3RHhFL0JuVUkrVHJudWRQZ3l4L3BST1pheTQ3SVd5RGl2czdacmtBZStZcUsvMXZySlBPRDI4OFZWMnJpb2h5YjNuQXBPZVBTUmpWdkduZmROZ09XNVBZeU5DQWhLZERDZ0laU0RrOWFDQzJZbWhEL1lDZzVaZlpRZHFWcGx3T3Q5Ump0TWtQcFZkMXBNdWpzTlNycTBHckxyNlQ1K1NzQ2oxWDdwSnJYN0g1UC9ramZKTldoSU5aQ3FKbzBRSHUvbXFWL2poVktKR1ZZQTNudjZzTjBpZUxGNU0vUHMrV2V6ZzlJRjZFbkh2TGI3dkxkL255ZkZNeW15QUpBQkR4R29MdXh5NmlsQnFvODk5bWVlUEpHRDZITkdxazhMWmt2WWpyb2pZakhiL09EQnc5Q2prRHE2THVtRmxFK216S3QzSC9ZcXNjeG5melVNek0vM0VYcnljbEJjWmZYVnlheEkzQXgwdFNZTUR4QnJTN0RjOUYxVHBiVkY3Y2ZwZmgvdkluRExmUE94NE90Y3I1bHlhVTI5RUpTdXl5RzVVeDNZNE1DbU55V0ZXUnkvZnFQUk9KV1pSZUNiRjVSdlBIUys3Smc4OXhHT2t5OHNoRjJtYWF2ZDVxaDVTL21pcFBsTHIiLCJtYWMiOiJkMjgyODFhY2Y0YTBjODJkN2Q1MmU3MjMyMWM3MWE3MTZjMWNiOWEyZjYzNTc3ZjllYmQ0NzliY2Q1OTQ5YmI2IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:29:17 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImNEM0NLYUQvTHpDb3JVY045Qlp5MkE9PSIsInZhbHVlIjoiK3k2cXRFRzRKNHF2eEZTZ3Qrd2Z6eEJzdlNyQnpvVE1sS1l6YVA1cGg4aUk5ZmNjWThqUjRWOCs0KzNHOEx4ajlBY2YwMWF1aVNkODM5Vjd0aFV3UnN5Z05kdjJGYS9jWTd0eXl1aS8yYVlOcjhXVWU5MFVWOEoxR3FUMy9uaDVnWUExdUdVQ2sxVUZUSmUxLzFnYlFXWEVTbTlHMVQ4cGN4ZzhIdGRzUTZyR2IzaHZZM2ROV0FBcW9CVzYrcnhlZjF1MndWaTBkVS9zUm9DblU3aSt1V0RrQlpFMGxmNVVTa2V2Q2dLY3JlYS9URDdQb2FOeWFvdEVlZmhKWmpQNE9TWklYa2FpZ0lnZXpTaldyLzdhQ21kY1Vab1EwTk1qRVQ1UU1LQzd0Q3ZMTmNKVzE5dnJlYkprK2JTM3lnSUFjYy9WS3pLSVU5dExaOW5xdnYzT0NOUVB6cG1haWM0VGlzb0wyK2RQZko4NHpUbFpmMUpPZnVOdllCSk4xNG9UQ1g1UDFuZmdkWVAzVHZFN2RwYzFQV1V2K2ZaRGM0cHFNSnZBTmtLbjQxblRUd3NYNmhwKzZQamowdjY1eHE0VnBTWkUydjJ5OVBqVGZrZC9uQlp1anJtUkdZZW5Nd2lwMjdtTCt4aGFiS2pXNEJMR3NmOTRBZUVSQ0k2S1dDa1ciLCJtYWMiOiIzNzc0ZDA4ZTI2YjdlMjdhNzY1MzA4Y2FkY2U0ZDQ3MjE0Yzk0ZTcwNDJmYTU0NDUzYTcxMjAwNDczODY4ZTAwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:29:17 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImZVT0poemhrK2hXcHVET040UnExT2c9PSIsInZhbHVlIjoiZDZKNk5qTmI1c2hqSmZqa2h6bUNMR003UlNPMTg3RHhFL0JuVUkrVHJudWRQZ3l4L3BST1pheTQ3SVd5RGl2czdacmtBZStZcUsvMXZySlBPRDI4OFZWMnJpb2h5YjNuQXBPZVBTUmpWdkduZmROZ09XNVBZeU5DQWhLZERDZ0laU0RrOWFDQzJZbWhEL1lDZzVaZlpRZHFWcGx3T3Q5Ump0TWtQcFZkMXBNdWpzTlNycTBHckxyNlQ1K1NzQ2oxWDdwSnJYN0g1UC9ramZKTldoSU5aQ3FKbzBRSHUvbXFWL2poVktKR1ZZQTNudjZzTjBpZUxGNU0vUHMrV2V6ZzlJRjZFbkh2TGI3dkxkL255ZkZNeW15QUpBQkR4R29MdXh5NmlsQnFvODk5bWVlUEpHRDZITkdxazhMWmt2WWpyb2pZakhiL09EQnc5Q2prRHE2THVtRmxFK216S3QzSC9ZcXNjeG5melVNek0vM0VYcnljbEJjWmZYVnlheEkzQXgwdFNZTUR4QnJTN0RjOUYxVHBiVkY3Y2ZwZmgvdkluRExmUE94NE90Y3I1bHlhVTI5RUpTdXl5RzVVeDNZNE1DbU55V0ZXUnkvZnFQUk9KV1pSZUNiRjVSdlBIUys3Smc4OXhHT2t5OHNoRjJtYWF2ZDVxaDVTL21pcFBsTHIiLCJtYWMiOiJkMjgyODFhY2Y0YTBjODJkN2Q1MmU3MjMyMWM3MWE3MTZjMWNiOWEyZjYzNTc3ZjllYmQ0NzliY2Q1OTQ5YmI2IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:29:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImNEM0NLYUQvTHpDb3JVY045Qlp5MkE9PSIsInZhbHVlIjoiK3k2cXRFRzRKNHF2eEZTZ3Qrd2Z6eEJzdlNyQnpvVE1sS1l6YVA1cGg4aUk5ZmNjWThqUjRWOCs0KzNHOEx4ajlBY2YwMWF1aVNkODM5Vjd0aFV3UnN5Z05kdjJGYS9jWTd0eXl1aS8yYVlOcjhXVWU5MFVWOEoxR3FUMy9uaDVnWUExdUdVQ2sxVUZUSmUxLzFnYlFXWEVTbTlHMVQ4cGN4ZzhIdGRzUTZyR2IzaHZZM2ROV0FBcW9CVzYrcnhlZjF1MndWaTBkVS9zUm9DblU3aSt1V0RrQlpFMGxmNVVTa2V2Q2dLY3JlYS9URDdQb2FOeWFvdEVlZmhKWmpQNE9TWklYa2FpZ0lnZXpTaldyLzdhQ21kY1Vab1EwTk1qRVQ1UU1LQzd0Q3ZMTmNKVzE5dnJlYkprK2JTM3lnSUFjYy9WS3pLSVU5dExaOW5xdnYzT0NOUVB6cG1haWM0VGlzb0wyK2RQZko4NHpUbFpmMUpPZnVOdllCSk4xNG9UQ1g1UDFuZmdkWVAzVHZFN2RwYzFQV1V2K2ZaRGM0cHFNSnZBTmtLbjQxblRUd3NYNmhwKzZQamowdjY1eHE0VnBTWkUydjJ5OVBqVGZrZC9uQlp1anJtUkdZZW5Nd2lwMjdtTCt4aGFiS2pXNEJMR3NmOTRBZUVSQ0k2S1dDa1ciLCJtYWMiOiIzNzc0ZDA4ZTI2YjdlMjdhNzY1MzA4Y2FkY2U0ZDQ3MjE0Yzk0ZTcwNDJmYTU0NDUzYTcxMjAwNDczODY4ZTAwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:29:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1789655851\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1201270261 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1201270261\", {\"maxDepth\":0})</script>\n"}}