{"__meta": {"id": "X733a631d89cc5474cf987e3b7cdbde48", "datetime": "2025-06-08 16:25:08", "utime": 1749399908.010105, "method": "GET", "uri": "/pos-delevery-pay?total_price=12&pos_id=44", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.440234, "end": 1749399908.010125, "duration": 0.5698909759521484, "duration_str": "570ms", "measures": [{"label": "Booting", "start": **********.440234, "relative_start": 0, "end": **********.897185, "relative_end": **********.897185, "duration": 0.4569511413574219, "duration_str": "457ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.897197, "relative_start": 0.45696306228637695, "end": 1749399908.010127, "relative_end": 2.1457672119140625e-06, "duration": 0.1129300594329834, "duration_str": "113ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52394416, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x pos.bill_type_delivery", "param_count": null, "params": [], "start": 1749399908.003585, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/pos/bill_type_delivery.blade.phppos.bill_type_delivery", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Fpos%2Fbill_type_delivery.blade.php&line=1", "ajax": false, "filename": "bill_type_delivery.blade.php", "line": "?"}, "render_count": 1, "name_original": "pos.bill_type_delivery"}]}, "route": {"uri": "GET pos-delevery-pay", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@deleveryBillType", "namespace": null, "prefix": "", "where": [], "as": "pos.delevery.billtype", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1520\" onclick=\"\">app/Http/Controllers/PosController.php:1520-1536</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00878, "accumulated_duration_str": "8.78ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.942849, "duration": 0.0050999999999999995, "duration_str": "5.1ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 58.087}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.9595559, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 58.087, "width_percent": 10.706}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 17 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["17", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.982728, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 68.793, "width_percent": 11.503}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (17) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.986421, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 80.296, "width_percent": 19.704}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => manage delevery, result => true, user => 17, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2138178418 data-indent-pad=\"  \"><span class=sf-dump-note>manage delevery</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage delevery</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2138178418\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.994244, "xdebug_link": null}]}, "session": {"_token": "SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/eyJpdiI6Ild1N2JBYjlWemdxYnI1bkswNlJQdFE9PSIsInZhbHVlIjoiazE4Z3o4RmU0TnNjTnNSbzVUcGlzQT09IiwibWFjIjoiZmU5NTlhNTJmMzU5MzI0NzQ4ZTliMzZlNzEyNTRiMzUwMDcyOGQ5NGMxNWUyOTI3OGI4NjZjNTk5Yjg5ZDg5MSIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17"}, "request": {"path_info": "/pos-delevery-pay", "status_code": "<pre class=sf-dump id=sf-dump-1104137634 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1104137634\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-819353307 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>total_price</span>\" => \"<span class=sf-dump-str title=\"2 characters\">12</span>\"\n  \"<span class=sf-dump-key>pos_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">44</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-819353307\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-267688932 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-267688932\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1519013414 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6Ild1N2JBYjlWemdxYnI1bkswNlJQdFE9PSIsInZhbHVlIjoiazE4Z3o4RmU0TnNjTnNSbzVUcGlzQT09IiwibWFjIjoiZmU5NTlhNTJmMzU5MzI0NzQ4ZTliMzZlNzEyNTRiMzUwMDcyOGQ5NGMxNWUyOTI3OGI4NjZjNTk5Yjg5ZDg5MSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwl%7C0%7C1982; _clsk=1y4k55%7C1749399896435%7C6%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlkrdjNDYmRJTGgxa1poaEtBRU5oNEE9PSIsInZhbHVlIjoiR0Vobm9qTDQ3Ulh1V3lITUlMZTBqZnhXUmNlaHNHb2JTMUtXQzcyOWkxUmdlYkZaMEM4TlJTQnhpQnMzRGhuRmpHczBBc200c0xSMU5ranUvdW85bC9TVWpPTGYwL2FiM1lMZUd6djgzRTFHZldtUm4vamp3UUd1QmRGMld3THAvUk95eXAzK2h1Y25SMld4VWQwVXRQcFpUYlg2Mk9iMGtlVU5pNHBzWXJFTE5GUXJaQzlsN01lQTJoQUtIUnpkSzU5RUU1cndBSTNISmkzNm8wWTdtdzhDSlRVQmZIME1hUVVJcjJvTDEwN3VSUkgvWnBqYXBWYUxyN3Z6VkFWV0Z3RE12YWY1SkM3b0NlTGZmekJCZjY2UVdadE5ZNHZCWDRpbG1wc0xEUzJGN0E3dVBsd2JkaDQvVGhTZWJvSlpyc0lMaDRLZGJuaE4wSDl3cVpISmVHeUhKNHZpTE14T3JuVmQ1bVB6U2lUajI1eFRwbThMSm5zK2JRUGxOTC9NbjRBQnY5V1NLb3NXbnU5YmxFYkVxeFVRMk9Lc09Ud2ozc1BGcUR5WmVGMUoydzlBcy9iMkpGZlExanlmWEl2L2djOG53ckVJSTYybHV4bkcvK2xzSHJtRUpkWlZoVHBxdGNyank2YzlUcXN2alZjVzFFTDlMR25TdmZ0M2xocnciLCJtYWMiOiJmMWRiYWM2MWIwYjAwYzc1ZDAyZjQ1ZDhmMDcxMzYxNjdhNjg3MjkyMjI0MWQwMzIwYTBjMmYyMzEwODIzYjVlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InVrMG5oSnhXY0VLY0U4OXduZzBJVnc9PSIsInZhbHVlIjoic2RqQ09FSytCaGtjSmIrZ29UUFpDWW1Hb0VaY1grbjVyMUM2dk50UEorUEwxQzlCWWVTZGxRSWlGN3FLMWR2S3Rsa3VRUWlhTEpaRGg0RG9XSWJXR2RUaGlYdDhSVGphVmlIalA1RW5yU3NwVEZmemh1R3VXSzVpZGg5SXZjeURFakdKKzczeHh2UFRsSzNndU84M3BhUnNHZ253Sm1IbEpPdXkxV3N2MUNJMEFRWUliNXJaRnh4VEYxUXJtNTIramZjZlIva0YzL1RreVQ1d1lvOWoyU1R5ZXNqMURReTNWazhNMWd5S3p2Mi9oWk04ZytoVWF5aUVKZnhOS2poL2dtRndRUnJFeWlMU0V1QWtjWUhBYis3RVk3QTJ6T1BiaGgyK3c2cVdWeExKQ2doWWZjSFhtSFFMaC9SS0xzajNSUWZlcGhZc0JkenRBaDZDM2V1elZWYStiZFVob2c2azhHaHlHUkZLSTRSUXJGUmJrSUZScVJ0YkZhVnpPKzAwOHVvamhtKy9OTHg2RDNsK0N6VmNZMWpiSXRYR0htZ09jUlVmVHlHYWtrL3JzblRSQ3VydWtrbHVNaCtzL0tRQi9XbWZ2U01NRDI1VjdRUjZLOWJjdHk4aTJqZGJBSHJSRDQyaXpOYnJsUXRLK1B5RmNGTjg2ZkEvU0p3OUFjSlQiLCJtYWMiOiJjMTlhZTRlZjI1NjRiNjNmNzcyN2ZiYzk3ZTBhZDI1YTMzODA0YzkyY2M4ZmJlNDgxMjlhZGU3YjQ1ZjUyZjk2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1519013414\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-912727439 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">COI6IIrAbucReABHBN7o3ZaIzRbwBCI1gfwrcfCo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-912727439\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1130891951 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 16:25:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlhBT3BwWVRuV3ZWMUduRW4yZzRHdGc9PSIsInZhbHVlIjoiek1SeXFRZ3BzMTNxcks2TlcxUkpueVBnd1l4TEl3VmMxQ1BUUGJXemdxbzA3VXNObWFSeWlLTTRXcytvdlJERkFic3pxeWtFQlFwZ1daWFBVMC9xeXJoZmhxRnkwV2Q1bmVMenV2dy93OU9nQndpeDU5b3lFMmU2VHhDTWZjUXdDZGc5K3AyajRYeTNnYnZST3NSUFJkVzIrVEdSbDRxcGk1bGhUcmxoaUdHRkg5eHZFSkZXWGZnRkNyMlVHM2VRUW0wVE1mWVNmS3RQMnlpWC9rMnp0a3B3Rm1wVEtURGVZNnFaaXVwSVBxZlJIRkdybDh0RWpXcVhLR2pOTlpsZXZMeS9Ld0Z0ZDBYbGxRaFUrcW1hcFErQ1Q3c1d1WUJkbldvdlMwVFZRQ09nanFFbXdEMzk2SHdSZVRxMkZFOWhzYUtqMHliRmM2NDVDM3dQQ1ZLQjZzVWRLaWhLYVA3SWd6a2Vwd2N3dnRHTVR2WW93M1k1NVBHUDhBaTV5eGs5cFA5NXRvZGZlNEpzOGhwZUtNd1N6UlVRdW9yNk9vQXM1T1h6ejg3SWZrVHRBMThwa1BxQVI2anRWcGFYaGdTY21iRkd1WThjQlVsbURjQjYyRlEyVWlMNlBpMHZnVjdwU1FhTndKOUIrUjlYcHJ0YncxNnhrUHZjbWNTWHZzUkEiLCJtYWMiOiIwNTNmZmMyYTg4OWNhYTZkZTM2MWQxMjY0MTUzNDNlNGMyNTZkMTIxYjVmMWU5ZTg4ZDViODdlNTk3ZTExMDc0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 18:25:08 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ik5uSmVFRWhqS3Nsa0tWOUt5Mm8xU3c9PSIsInZhbHVlIjoiajBUeEZvOFMwQWNMc014SURtK3YxcGMrTDJFSTFOZXFMV0oyZS9PeEdIQVZSOUtJZWMrZXJRcWRQSjBoOW9rVDM5TmhaZDd3RTJTVVFDTVVOK0I0aVVmRXhsanBXS0VySGdRd0VrVlg4SVRNTW00S3B6a0NrR3Zlc04rMmFSK2JTcGxIbkJMVHE1a2NVOVZoSGlmNk4vV210YU04MWxHMUg4djhxVnpodFE4emdKWDUyUWZIUVM3ZlJzSkdVRE5pVGt1OEZTYndkZG8rMlFndlpZOTF1ejArdHZpdDlJY09QaVNFKzdRWnJlQ29vOWJLYUtpWlBBRksxejRvOUZLWmpHaWJ2dnJzWmlSSlFyQUZaWENQTWk0b1R6ZnlubDA0RXRqczUxQ2NmbmZKZHJKbkF4bU5rbzhnd2ErdXdkT2w0aUtJc3NLOVFvTGZFelBMb2Qra1I4OHBEYnliMGRYVzlFS3ZTcWc1OGZtYm9YSFZJNUNYQ29hUm0rNm44QlkyeXFWMEdzancxOUorNm9ReFVhZTd0VFpsT1M0SU9hQWVHZkYrSXJCaEFpWS9TRHFOMEhXOHR4L2F0bFZWQWVGOGxtcGs0SU5BL3ZlZ3pXbjJjNWdIaktrN3QyY2xMZ2VyazB0dzlSL0hNajVtQzlMUkd2dmNQRjNubllLMHc4NHciLCJtYWMiOiJlOGYzODdjY2EzODIxMGNiZmFmZTRlNWE4ZmJjZGQ1ZjM5OWVjOWRmZjE1ZWQ4Mjk2MDZiMDI2YmI5ZGI4YmEwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 18:25:08 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlhBT3BwWVRuV3ZWMUduRW4yZzRHdGc9PSIsInZhbHVlIjoiek1SeXFRZ3BzMTNxcks2TlcxUkpueVBnd1l4TEl3VmMxQ1BUUGJXemdxbzA3VXNObWFSeWlLTTRXcytvdlJERkFic3pxeWtFQlFwZ1daWFBVMC9xeXJoZmhxRnkwV2Q1bmVMenV2dy93OU9nQndpeDU5b3lFMmU2VHhDTWZjUXdDZGc5K3AyajRYeTNnYnZST3NSUFJkVzIrVEdSbDRxcGk1bGhUcmxoaUdHRkg5eHZFSkZXWGZnRkNyMlVHM2VRUW0wVE1mWVNmS3RQMnlpWC9rMnp0a3B3Rm1wVEtURGVZNnFaaXVwSVBxZlJIRkdybDh0RWpXcVhLR2pOTlpsZXZMeS9Ld0Z0ZDBYbGxRaFUrcW1hcFErQ1Q3c1d1WUJkbldvdlMwVFZRQ09nanFFbXdEMzk2SHdSZVRxMkZFOWhzYUtqMHliRmM2NDVDM3dQQ1ZLQjZzVWRLaWhLYVA3SWd6a2Vwd2N3dnRHTVR2WW93M1k1NVBHUDhBaTV5eGs5cFA5NXRvZGZlNEpzOGhwZUtNd1N6UlVRdW9yNk9vQXM1T1h6ejg3SWZrVHRBMThwa1BxQVI2anRWcGFYaGdTY21iRkd1WThjQlVsbURjQjYyRlEyVWlMNlBpMHZnVjdwU1FhTndKOUIrUjlYcHJ0YncxNnhrUHZjbWNTWHZzUkEiLCJtYWMiOiIwNTNmZmMyYTg4OWNhYTZkZTM2MWQxMjY0MTUzNDNlNGMyNTZkMTIxYjVmMWU5ZTg4ZDViODdlNTk3ZTExMDc0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 18:25:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ik5uSmVFRWhqS3Nsa0tWOUt5Mm8xU3c9PSIsInZhbHVlIjoiajBUeEZvOFMwQWNMc014SURtK3YxcGMrTDJFSTFOZXFMV0oyZS9PeEdIQVZSOUtJZWMrZXJRcWRQSjBoOW9rVDM5TmhaZDd3RTJTVVFDTVVOK0I0aVVmRXhsanBXS0VySGdRd0VrVlg4SVRNTW00S3B6a0NrR3Zlc04rMmFSK2JTcGxIbkJMVHE1a2NVOVZoSGlmNk4vV210YU04MWxHMUg4djhxVnpodFE4emdKWDUyUWZIUVM3ZlJzSkdVRE5pVGt1OEZTYndkZG8rMlFndlpZOTF1ejArdHZpdDlJY09QaVNFKzdRWnJlQ29vOWJLYUtpWlBBRksxejRvOUZLWmpHaWJ2dnJzWmlSSlFyQUZaWENQTWk0b1R6ZnlubDA0RXRqczUxQ2NmbmZKZHJKbkF4bU5rbzhnd2ErdXdkT2w0aUtJc3NLOVFvTGZFelBMb2Qra1I4OHBEYnliMGRYVzlFS3ZTcWc1OGZtYm9YSFZJNUNYQ29hUm0rNm44QlkyeXFWMEdzancxOUorNm9ReFVhZTd0VFpsT1M0SU9hQWVHZkYrSXJCaEFpWS9TRHFOMEhXOHR4L2F0bFZWQWVGOGxtcGs0SU5BL3ZlZ3pXbjJjNWdIaktrN3QyY2xMZ2VyazB0dzlSL0hNajVtQzlMUkd2dmNQRjNubllLMHc4NHciLCJtYWMiOiJlOGYzODdjY2EzODIxMGNiZmFmZTRlNWE4ZmJjZGQ1ZjM5OWVjOWRmZjE1ZWQ4Mjk2MDZiMDI2YmI5ZGI4YmEwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 18:25:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1130891951\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1265418541 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6Ild1N2JBYjlWemdxYnI1bkswNlJQdFE9PSIsInZhbHVlIjoiazE4Z3o4RmU0TnNjTnNSbzVUcGlzQT09IiwibWFjIjoiZmU5NTlhNTJmMzU5MzI0NzQ4ZTliMzZlNzEyNTRiMzUwMDcyOGQ5NGMxNWUyOTI3OGI4NjZjNTk5Yjg5ZDg5MSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1265418541\", {\"maxDepth\":0})</script>\n"}}