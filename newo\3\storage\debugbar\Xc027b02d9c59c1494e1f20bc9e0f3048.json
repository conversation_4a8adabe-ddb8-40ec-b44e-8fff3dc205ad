{"__meta": {"id": "Xc027b02d9c59c1494e1f20bc9e0f3048", "datetime": "2025-06-08 15:29:35", "utime": **********.424663, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749396574.580956, "end": **********.424691, "duration": 0.8437349796295166, "duration_str": "844ms", "measures": [{"label": "Booting", "start": 1749396574.580956, "relative_start": 0, "end": **********.319479, "relative_end": **********.319479, "duration": 0.738523006439209, "duration_str": "739ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.319493, "relative_start": 0.738537073135376, "end": **********.424694, "relative_end": 3.0994415283203125e-06, "duration": 0.10520100593566895, "duration_str": "105ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45139664, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.016220000000000002, "accumulated_duration_str": "16.22ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.367707, "duration": 0.01434, "duration_str": "14.34ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 88.409}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.397521, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 88.409, "width_percent": 5.795}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.410151, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 94.205, "width_percent": 5.795}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1790236314 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1790236314\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-599106857 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-599106857\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1908291929 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1908291929\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-664631291 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=xh9goj%7C1749396565821%7C10%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkFBa2ZMTmg4cUhXUE5ncW5pZzhqT1E9PSIsInZhbHVlIjoiRWwvNWZkOGQyZ05SMjFzNlJENHczL3o0WDVMdUhwT3lsbTExK1pmUjg5UHQ4cHFlWU9LdE1kZmVMeit5eWxyUElxK002bmp0TmwrWU1oY3BvM2FHQ2IxZkZsMW9xSVAxNXFJYzIxeUo0ZWhtelNHRUdBSzgrTnlwcHUzWUFBVjZUV283UXgwVXEyTFEvVDloQS9SMkJXN1M3TzNlc0hhaU1tdWZKdXhRM1pVVmREOVBYWHFWeHAvbjZKQUQvaVBIVTNFb2RXT3NNOHIwdG5lU2orYmJuYkljSUxKWGx1cFc2Znl1cmNVSndUdmovbHdQeTNlcEJITVY5ck1iWDloUkZwSElweHpoM1I5RzQzdDhHNzNsbzdKN2pPclhDTHJTZmF3cGdpQ3pBd2J5UFFhZVBUcGQ4NTdYUGFFN2VQZE1oWDVrY1NLN2Y5Q0Z1WWRRT3dMQnJoRElhL3FwVkNmS3ZXd1MxcFpReVZpdzR2eFZNeEZuaFJKbVgrTmdHbTBQSXh2bk83TDNuSUp1Z1g5Mm5MVG0yVmFNYWlxU3Vmc2Z3dXFhYkVubXpHbjM3NjVTa0FFemxVeTVOa2ZLVXlaeW9DWFlqcHBuUzhiRjZNU3lRdDVkb2VVYUsxZzZnTGR6YWUwbDY2dWV0dE9mWVNqMlNFZ1JPOGZjSHdDektnK3giLCJtYWMiOiIyNmMyOWY5MDQyNDM4MGRhMjZiNWZkMzBlNTJjYjU0OGNhODJiNzE0MTdmNWVhODNiNDIyYjNlMWIyM2U1MTUyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Imlwa2hQYUswaWNhMlUvOS9jRjg5ckE9PSIsInZhbHVlIjoidXJiQ09lalhzOHMzN3BBVzIwdGRNZVF5aCtjTm1TdE4rYlcwdHZMU0dJYWdpUHF6YXZsZ3pRR0hSMVdJOXNLMEhuYXZHRXVmSVJCekZUS085QWR4ZEgrVDRTd0dWZkt4K0ZoQVpNd0pZV0o2RGJYTWtUaWxiOEhVd1Zzc09FT25Qcmw1azMyRlZFdTFxRU5DSzJwVm0wdVV2VlZwazhXeTVMR3d2eG1HY051TTMxWStqSERsU0RHQWtNcS9aZ3h3TDN3TFBhbVJBL3ZHZUNNM24wVFVKTHBqaTg0eDl1TXoyQXJtUXptWXkzQW5yUkJFNXJyT2pMZ1ZrTHh2ZCtXQ2JqdWE5SU12c3hXYUpwQ2pUZEJOQVlpYzJFalZ0S0NUNGpiRFVJUjZCVXZMTENTZk1GY09JNGlNQUJnTGxCLzgzRis5TWJnd3hJTUhqZGRyYkh3VEpkaXNxVnQyb1dvSkVnaGJ0S0hLNStwUFY0bkMrbm9BYUxrMDI1eE45YkZjbHkwb01ab3ZjYzVIVnc4bGgyd3NMcjVNZlJ3bkJoNWduSXl5NHFxY3Vsci8xOG9BNTkzZXJ6WENvN0tmS2ZtclhuNE53Y25BMS9DZ0xtcnpWa0xsNEJBeXNBV2xmajEyTUZadmlOSElUb1pFUmg5NytwVlhOZWw4Ym85TVJ5RWIiLCJtYWMiOiJhZDZjOTRhMzI0MDcxZGUyZDUyMmIzZTkzMDk5MTUyMmZkMmI0ZWY0ZTQzZjc2MmI0MTRmMDk2ZDMyZWRhODkwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-664631291\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1302759572 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">l7xz5q4Zkpyo7Zd0rhHsE8M6pi3wac4XFWhsi0lQ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1302759572\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1368068975 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:29:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImFOc3cwZ1FhL2tndER4L1kzTnc2Tnc9PSIsInZhbHVlIjoidlVBLzhXNFYxT1pXSjZOKy9COUE4YUJkOEZzektBZUlnSVp6dTZyTS95bU81WUNmbHBSRzRwMHhTMC8ycnVtQXd6cVhBN2R1TDBNVXlua2grMzJRTVBudjhvK213ODRmckd5cHJZWnZvaVNjOXFPQ1dEZUVLVm56L3cyb20wdnlHczI1aEFMY3hicXJVMnE3ajA3N2pSN2FxVVFLdUQ4TUlOR29VSmJqdnplMnIreVRBQjBCdEhPRUN4TWdLQVpieEQzQkR4cVM2VkFSOWt5dWpkNGR0N0xiR3Y3TUtUa2JTWDR4eTgwcFVhbUkyb3l4ekNJUkZLTjJyaG82Q2V0aHJoVVpHL0tIb0JvNGJLYjA5N3UzZ1J4ZE1Xd2NITFdnek5DbFNRdXZmMDArMHNBbHoxMERQWXlxTjhhd0dZYll2bHpmemxBanFaNytrRmlubFdwODlvcmRhTzdVa0RPdGVoSWdETjJsT3JQS0Ewd3BHQjlYTHZXdHdhdXFleVhMT0l0Z1pUdDN2Q1JueDNVanlOS0E1RkJvTUxLRUhQWlh1Y0RDZ0J6KzFCdXdwam95UWEreXRqdzdmMllrY3ovbENxZ1VGaGNNOVdrRXVzS0FUb3RwS1FIZ0d1NlVrNHZFZHIyVmliaGVZaWNaT3pjaUZUbVZTRzRSbDV3RGs4U1oiLCJtYWMiOiI3ZTRiYzIyNDNhMGVhOWVlOWI1MTk4NThlNTI0NzgxMGEyYTBlMjNlNzk1ODVhMGZlODM1NGJlZGVhMzY1Njg0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:29:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImJlOE8yeXhSYkFrTkpFVXVyc1Zod3c9PSIsInZhbHVlIjoiSk45RlRRNHZPY1NYKzEzc3Q0OE5RWTBybGRQdXVkMk93cXRiYVF0QnNNYzBjMElLNm45bkhlalFhbUphL21MYld1R1NWK2VjMVlnV3U0aE52U0lWcnpyTURjWHcwN2pCT0kxMVU0R2VXQ3F4N2lCWlBwY2g0VlVITitmQ0Y5UkQyWFpwYzlsTngyZGxuUDBmZTZ3MnRQM0s1N0hENWVYeHJxNS9BTXFjQ2MwblpXR3pQTE43KzV2cVQwSlBkQWdUTnpMNWNwRFh1VnJ6aUVWQjVTa0tCaUdCVXh3SGk0bEJtSWxiQjRCV2xBZ3JZNEJnZUMwYzZnVElUSHNqSTB0NUhCb3pGN3N4Mk9jRURuT0hUR0dMaHBQbUcvTmd2aVN3bjFiZGF4aGsrZE1mdGtBbTZGSWpSNTdpYlZrSlJDQlhWQkRGL3lUc2pjY2ppbDJPTHN0K3dEQnlzdGtpT2l6NktyVG02WDh2UzU4UTBZbEMzNzk4cUh0VzdMYjBVQ2J2aW51ZlkrZFZiYlR2bVc3RERWOWhXcjBRZUFsTGFnWnlRRytpcEszT3laQ0RWMTlRRnlkeVA3NVBHaXMrK1MyREpINXRqcXNOSVJxV3NYVVcycVlFT3NzSzlpNWpkS1hHZmxHaGNjY2JmQUNuWW94SXViRmdxV1kvTk1yb0orL0MiLCJtYWMiOiJmYTE1OThjZGI4Nzk0MzU0Y2Q4YWExM2M5M2ZkMDZjNmQ0MWVkZTc1ZDY1ZTI0MGZjY2U2NjNiZDg2ZjU1YTgxIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:29:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImFOc3cwZ1FhL2tndER4L1kzTnc2Tnc9PSIsInZhbHVlIjoidlVBLzhXNFYxT1pXSjZOKy9COUE4YUJkOEZzektBZUlnSVp6dTZyTS95bU81WUNmbHBSRzRwMHhTMC8ycnVtQXd6cVhBN2R1TDBNVXlua2grMzJRTVBudjhvK213ODRmckd5cHJZWnZvaVNjOXFPQ1dEZUVLVm56L3cyb20wdnlHczI1aEFMY3hicXJVMnE3ajA3N2pSN2FxVVFLdUQ4TUlOR29VSmJqdnplMnIreVRBQjBCdEhPRUN4TWdLQVpieEQzQkR4cVM2VkFSOWt5dWpkNGR0N0xiR3Y3TUtUa2JTWDR4eTgwcFVhbUkyb3l4ekNJUkZLTjJyaG82Q2V0aHJoVVpHL0tIb0JvNGJLYjA5N3UzZ1J4ZE1Xd2NITFdnek5DbFNRdXZmMDArMHNBbHoxMERQWXlxTjhhd0dZYll2bHpmemxBanFaNytrRmlubFdwODlvcmRhTzdVa0RPdGVoSWdETjJsT3JQS0Ewd3BHQjlYTHZXdHdhdXFleVhMT0l0Z1pUdDN2Q1JueDNVanlOS0E1RkJvTUxLRUhQWlh1Y0RDZ0J6KzFCdXdwam95UWEreXRqdzdmMllrY3ovbENxZ1VGaGNNOVdrRXVzS0FUb3RwS1FIZ0d1NlVrNHZFZHIyVmliaGVZaWNaT3pjaUZUbVZTRzRSbDV3RGs4U1oiLCJtYWMiOiI3ZTRiYzIyNDNhMGVhOWVlOWI1MTk4NThlNTI0NzgxMGEyYTBlMjNlNzk1ODVhMGZlODM1NGJlZGVhMzY1Njg0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:29:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImJlOE8yeXhSYkFrTkpFVXVyc1Zod3c9PSIsInZhbHVlIjoiSk45RlRRNHZPY1NYKzEzc3Q0OE5RWTBybGRQdXVkMk93cXRiYVF0QnNNYzBjMElLNm45bkhlalFhbUphL21MYld1R1NWK2VjMVlnV3U0aE52U0lWcnpyTURjWHcwN2pCT0kxMVU0R2VXQ3F4N2lCWlBwY2g0VlVITitmQ0Y5UkQyWFpwYzlsTngyZGxuUDBmZTZ3MnRQM0s1N0hENWVYeHJxNS9BTXFjQ2MwblpXR3pQTE43KzV2cVQwSlBkQWdUTnpMNWNwRFh1VnJ6aUVWQjVTa0tCaUdCVXh3SGk0bEJtSWxiQjRCV2xBZ3JZNEJnZUMwYzZnVElUSHNqSTB0NUhCb3pGN3N4Mk9jRURuT0hUR0dMaHBQbUcvTmd2aVN3bjFiZGF4aGsrZE1mdGtBbTZGSWpSNTdpYlZrSlJDQlhWQkRGL3lUc2pjY2ppbDJPTHN0K3dEQnlzdGtpT2l6NktyVG02WDh2UzU4UTBZbEMzNzk4cUh0VzdMYjBVQ2J2aW51ZlkrZFZiYlR2bVc3RERWOWhXcjBRZUFsTGFnWnlRRytpcEszT3laQ0RWMTlRRnlkeVA3NVBHaXMrK1MyREpINXRqcXNOSVJxV3NYVVcycVlFT3NzSzlpNWpkS1hHZmxHaGNjY2JmQUNuWW94SXViRmdxV1kvTk1yb0orL0MiLCJtYWMiOiJmYTE1OThjZGI4Nzk0MzU0Y2Q4YWExM2M5M2ZkMDZjNmQ0MWVkZTc1ZDY1ZTI0MGZjY2U2NjNiZDg2ZjU1YTgxIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:29:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1368068975\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}