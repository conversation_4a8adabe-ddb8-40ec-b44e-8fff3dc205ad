{"__meta": {"id": "X1fd9692f36db3ff3b38103baddc660ca", "datetime": "2025-06-08 15:44:34", "utime": **********.781588, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.1624, "end": **********.78161, "duration": 0.6192100048065186, "duration_str": "619ms", "measures": [{"label": "Booting", "start": **********.1624, "relative_start": 0, "end": **********.656055, "relative_end": **********.656055, "duration": 0.4936549663543701, "duration_str": "494ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.65607, "relative_start": 0.4936699867248535, "end": **********.781613, "relative_end": 3.0994415283203125e-06, "duration": 0.12554311752319336, "duration_str": "126ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48254032, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.025790000000000004, "accumulated_duration_str": "25.79ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.700562, "duration": 0.02093, "duration_str": "20.93ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 81.155}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.7325091, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 81.155, "width_percent": 3.18}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.754993, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 84.335, "width_percent": 3.335}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.758576, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 87.67, "width_percent": 4.033}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.766209, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "ty", "start_percent": 91.702, "width_percent": 5.467}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.771414, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "ty", "start_percent": 97.169, "width_percent": 2.831}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-858662324 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-858662324\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.764775, "xdebug_link": null}]}, "session": {"_token": "WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-898974470 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-898974470\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-82263419 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-82263419\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1371582268 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1371582268\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2036230143 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749396510751%7C76%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlRNWFZKVFdqcGdkMHlYYmlSK0RWR3c9PSIsInZhbHVlIjoiVXdkY2ZmN1N1NVA4Z21RSnp6akVOeEIxTUVXTVQ0Vjd4S0hTeE50dFlmRWxXYURKYmdJSFBqZm1ibFduellIV0owYTJxbFJZbUNGcjUxZkttM0lvd1RYVGJCdXBjamorbm8weHkxVnNtMGxueW1hQXBUeUphenFwaGxQUGZiOUdpZFBqaGx2UnVFNEk5d045TGwzdzU3T1JuaHR3UmpDREtkRTM1bWJlV3JNN0NlZTliZ1RNSlp0TVNOS1RnVVVGc09zdHFUc0g3UnkxWFg3M0pjVW9pR0F2NTlTMXhwTlZrQkdCb21TOVJXSnRvVFZ5dFNOVmFJZzB3MllGSGFhaldnUERmVFdoUUllN1hvRDRFajJIdkJwdTJMaXNvM3Q5a2syaWhtUytaTk1vZGhhdzkvenM2V04yaUxjQm9icXRBNVgrYnVENDFUcTUwK1hSblliTjRQNmxLdmQwQjJ4OW5UbHdxcXdaV0FKRWVpd25kdXFQZ3lhVmoxOWRJTm9jam5WQU5qUUV6MUF5ZXMwK0xaMU9icFVId2tMRTdJL0Z6UG1GU2Z3RmFiYVZYT2FGb3RaWklvVXJUMTFZbklhRXkwTy8xaytrQzdxd2dQMlhKdTdSMTdRNFAxM2MxQWEyd084ckoxamI3Y25jWlE0QTd2Sy9RbCtNQWN4MTVSVVAiLCJtYWMiOiI0ZjU3ZWNhNjJkZmYwMzI5ZmYyYzA0ZGRkMDg3MTRlMmQ4MjQ2YWNkNDFiZDE4OGNhZTM0ODBlMmQ1NjEwOTUyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkZyRWp0aGhyRHhKbUxOT0dyNDdsMnc9PSIsInZhbHVlIjoiR2w5VFgyWC9ZTEdBcVNmWWd2cUNNUTFubjBlM3ZYZWpxdG00dVBDWWNNWXAvOXQvUkdjRy9XUFpNTm9iM2VWY2w4MVFWeXgrVkhQbThwMmpOd3hJUVhQdTMvbmh3KzFDSEtEZ0l6dGtuMExiYmhkNGNlb3BSOGxNREIzUmd5cHhzdVdXaUxTQkRTOUVZc0VrZU40U1B4Zi83WlJ5cmxxMUtlZkd6SUpzbWkwYmkxTG5aL25JQmZ4QjB4bk01eVdyREJTR1BsU09FU3BDTlRDbStPS0lmaDdHbXd4bENmNEFpdjFtcU1qQ1VGczJ0U1VNZTUwYUlQR25oQURDWHkraGl3Zkt3Uk9GK2dXSy9jaitWRE1YdVViNDNNU2t0T0pSeWVMTjB4d0duemRUVUppRUlDME5xbmZPa0F0bXZrZW5Mb2hCUkFWK0UxVDJKcVZyZEFQemEzQWsyL2pCMHBoSGd6bGFhVlZmazQyeEVhVFpCbno2cjMxZURjYWMxNkJRSWZBTCt1ZEpEWGpTMW1EQlpkNHk4WnhoRXVrV1FiQ2dkNGs1cEtCYzVzanBHQnRDV25ybGRDZUR0WmJKWWl5Q3RWdVVoalN0WTIvKzlYMlhnN0ZYR2RTRnhZYitTYWNhZTNaY2RkT0R2REhOK1J5TFdUOU9BbkRESldiK081VzUiLCJtYWMiOiI0ZDk3YmZmNWExNzg4Y2Q0ZWM2NmY5ODYxMDFkYjVlYzZiZGEzODQ0MDlkZDdkYjMwMTAxOGY3MDIyMWU2ZGU5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2036230143\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1888708525 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xNV7SvMX3AgbiuDVO62eNXfryfZD4KbWr3rEIkXt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1888708525\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:44:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjFkMkxiSldVS2xDT0dqSWRSSmNtNEE9PSIsInZhbHVlIjoiS1ZnUkRXSEhQT2V2K09nb0lpTEFYbkZ6Nzk5QnZIYWxqMFRRbVZKK1ZGZEx6ZjZDbm5zbDdDYWxMeHFRK1BKZUhyckh1TjB4dktyRDNrV3FBd0hpME1FaGpOTXJ2a3FLcUhLWDVrMytSc1hIclJ0T1BmRkNqVnl5aUtsZGdoMlJFSGhoRGVpN0NuNlpjTTBiQnJRczQxNlBtQndCM2ZCWjZZcmZMa1JseXJ1c3JHRnFyYUtNYWFDYmpQanp3Y0E2MW5iMVo2bzl1WmNTRU5rREU0Q3NmbGxtVVp4TDNGL2ZDbnk3T1h6YlZKRDVmQzl4SkdHQUJ1NTduTVFFdkwzM0tuTUZsWU9hRWtSaS9QT0dqWUQzM29lSHptdVN6RTc4d0J0eXRDM3BoY1FlbG41YU1NNzlKVzAvUXRqTWlnM3lqSzhFdnFuZVc1WkhBcVJyT2hOQXhiNTAya0p6bzB1bUZtVWZiVzRYZFNTU3hEWHdzMWF6c29uTk5WZWp0L1N1d1QyY3NNRTF3bmtsZ2VWTUFjODc1Yk03VkdFaUpJbVUvWGVvekdPcHVEZm5Qb3AwbDRpd1VVWm9KUkttSDF3SEpZOFdQNlZzS1M0TEQ2a3dOeU96ZG9mTFQyZmNac21DN1BPYkI3aHBvTUsyMGk3MFdTSS9YQ2xyQjdQem8wUFkiLCJtYWMiOiI1YjY2ZjI3NTQ3OTUwNGY1YWQ2MzNiZTE3ZGFlOTkyMmY3YjRmNGE0YTk1ZThhYWU0YmQ4NGY5NzRjMjUyNTE2IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:44:34 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IndmTFNXdUdiS3V2VmxoMVM0Ri8xeFE9PSIsInZhbHVlIjoiaG4vSG5GaHdTMGttN0kxdnZRb2VldlNZZG4wOEU1MWpiV2dEeGkvbmZXdTBoVDJZVTcrNFh0NmJGVms0OElDYU9LdThDWjdXUEhKQzU0Vmh0dmdSdWpVK3oveWdtWnh0OFMyTjF6QXF3cnVBVXhVN29iQzU1V3dRYnNFNEluZm1vb0V6WUt3VkxNODZqMmxzL3l2U0FXbFU1emF1dHNCVGp0WDUxVk1GeTVWWlpHdFF0ekNVZjZqVnRWMTNKNU1idmcydncvME5waGErQkhkNmNKWGN3cFhZL3RYMDNWa0xWeFo1a1BTMktIUDAraHQ4T2V6WGNhdHN0aDNSTEYwVERTV0srRjRIQW91YjRRSHg4ZmZobWg2cTRSUVZNR0kxKzFVSTNVOXhLd05WVzN2eEFxR0o4Z0tVTzkxdWZFZTd1T05YQXRIM2F5dXF4SUQ4UEl0Y28yR1BJS1I1V1cxSUxnd2VBZUdNODhOd3VBU1JrZlZLZ01zVDA1Q1hCVm9mNnNmUnp3UVdZZHdUMHJpVHdvUHJEQU9OY1htMkhsTlZERmdlVXJCcHJkUi9VODNGZTlhcGl3WkQ0Y3MxWGxwSnprMDJ6SEEzcEhNcTBCMHV1S2hOM1d3YkE3ZUJJOE10WXYranZCZUMvZVVTMXZWR0p0NVZEOEFLZFk0bkk0Nk8iLCJtYWMiOiIyZDIxOTdjNDAwNTA3NTk0NDQ5ZGQzZGE5Y2ZiMzliMjc1YzdlMzI1N2FmNzZkYzYwYTQwNjA0MzZjNjU0NGFjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:44:34 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjFkMkxiSldVS2xDT0dqSWRSSmNtNEE9PSIsInZhbHVlIjoiS1ZnUkRXSEhQT2V2K09nb0lpTEFYbkZ6Nzk5QnZIYWxqMFRRbVZKK1ZGZEx6ZjZDbm5zbDdDYWxMeHFRK1BKZUhyckh1TjB4dktyRDNrV3FBd0hpME1FaGpOTXJ2a3FLcUhLWDVrMytSc1hIclJ0T1BmRkNqVnl5aUtsZGdoMlJFSGhoRGVpN0NuNlpjTTBiQnJRczQxNlBtQndCM2ZCWjZZcmZMa1JseXJ1c3JHRnFyYUtNYWFDYmpQanp3Y0E2MW5iMVo2bzl1WmNTRU5rREU0Q3NmbGxtVVp4TDNGL2ZDbnk3T1h6YlZKRDVmQzl4SkdHQUJ1NTduTVFFdkwzM0tuTUZsWU9hRWtSaS9QT0dqWUQzM29lSHptdVN6RTc4d0J0eXRDM3BoY1FlbG41YU1NNzlKVzAvUXRqTWlnM3lqSzhFdnFuZVc1WkhBcVJyT2hOQXhiNTAya0p6bzB1bUZtVWZiVzRYZFNTU3hEWHdzMWF6c29uTk5WZWp0L1N1d1QyY3NNRTF3bmtsZ2VWTUFjODc1Yk03VkdFaUpJbVUvWGVvekdPcHVEZm5Qb3AwbDRpd1VVWm9KUkttSDF3SEpZOFdQNlZzS1M0TEQ2a3dOeU96ZG9mTFQyZmNac21DN1BPYkI3aHBvTUsyMGk3MFdTSS9YQ2xyQjdQem8wUFkiLCJtYWMiOiI1YjY2ZjI3NTQ3OTUwNGY1YWQ2MzNiZTE3ZGFlOTkyMmY3YjRmNGE0YTk1ZThhYWU0YmQ4NGY5NzRjMjUyNTE2IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:44:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IndmTFNXdUdiS3V2VmxoMVM0Ri8xeFE9PSIsInZhbHVlIjoiaG4vSG5GaHdTMGttN0kxdnZRb2VldlNZZG4wOEU1MWpiV2dEeGkvbmZXdTBoVDJZVTcrNFh0NmJGVms0OElDYU9LdThDWjdXUEhKQzU0Vmh0dmdSdWpVK3oveWdtWnh0OFMyTjF6QXF3cnVBVXhVN29iQzU1V3dRYnNFNEluZm1vb0V6WUt3VkxNODZqMmxzL3l2U0FXbFU1emF1dHNCVGp0WDUxVk1GeTVWWlpHdFF0ekNVZjZqVnRWMTNKNU1idmcydncvME5waGErQkhkNmNKWGN3cFhZL3RYMDNWa0xWeFo1a1BTMktIUDAraHQ4T2V6WGNhdHN0aDNSTEYwVERTV0srRjRIQW91YjRRSHg4ZmZobWg2cTRSUVZNR0kxKzFVSTNVOXhLd05WVzN2eEFxR0o4Z0tVTzkxdWZFZTd1T05YQXRIM2F5dXF4SUQ4UEl0Y28yR1BJS1I1V1cxSUxnd2VBZUdNODhOd3VBU1JrZlZLZ01zVDA1Q1hCVm9mNnNmUnp3UVdZZHdUMHJpVHdvUHJEQU9OY1htMkhsTlZERmdlVXJCcHJkUi9VODNGZTlhcGl3WkQ0Y3MxWGxwSnprMDJ6SEEzcEhNcTBCMHV1S2hOM1d3YkE3ZUJJOE10WXYranZCZUMvZVVTMXZWR0p0NVZEOEFLZFk0bkk0Nk8iLCJtYWMiOiIyZDIxOTdjNDAwNTA3NTk0NDQ5ZGQzZGE5Y2ZiMzliMjc1YzdlMzI1N2FmNzZkYzYwYTQwNjA0MzZjNjU0NGFjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:44:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1491379170 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1491379170\", {\"maxDepth\":0})</script>\n"}}