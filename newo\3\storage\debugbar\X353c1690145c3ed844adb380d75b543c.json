{"__meta": {"id": "X353c1690145c3ed844adb380d75b543c", "datetime": "2025-06-08 15:44:12", "utime": **********.790485, "method": "GET", "uri": "/customer/check/delivery?customer_id=6", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.155314, "end": **********.790508, "duration": 0.6351940631866455, "duration_str": "635ms", "measures": [{"label": "Booting", "start": **********.155314, "relative_start": 0, "end": **********.71902, "relative_end": **********.71902, "duration": 0.5637059211730957, "duration_str": "564ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.719035, "relative_start": 0.5637209415435791, "end": **********.790511, "relative_end": 2.86102294921875e-06, "duration": 0.07147598266601562, "duration_str": "71.48ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44029176, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01389, "accumulated_duration_str": "13.89ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.762799, "duration": 0.01315, "duration_str": "13.15ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 94.672}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.781004, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 94.672, "width_percent": 5.328}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-1592256012 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1592256012\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1663579899 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1663579899\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2107739283 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2107739283\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1507381437 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749396510751%7C76%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImMzQmZ5YmtySENGSnhaTVlic2l5R0E9PSIsInZhbHVlIjoiYTZyVllaS1BEQzQwbmhDR3M3cmUyd3o0a1JXYTNFVGhoU2ppVWRtV01ya0VXSDNCa29BdmxJQ3V1czhBWFBXcUNwR2xNVEZGaXp4SjJyZWhWUmRsZGV1cGExb21mL21TK2FWeHIySG5BRUtJSVMwSG45TmVlMDhoVHpvLzFLRHVtMlRpa2JoWFI2K2hQUytkcjBFWnY1MDQzeDlqZUszTkJNZDdaVEhwWngxeDkwZkhObXlDU1VQZ1h5WXlTSTZpTkk4UDZ0WSt4c245TVF6R0VmaTZjdm81MFFDNEx1OW5Sb1pWVEVGaCtCWFJ2by9uQmRTdlI1L3gzVlAxb0VwSE1TZy9HeTlpNHFqRW1TNGpjWmkzVmFaQXBWbXpTSFNGL2MzNUg2ckJOV0sxOVduVnZ4VVRaTGRkZHJ0eWpsVUtGckhkM0FkY1BIVThrTXhCUmNWeVNXcU1kaUFabGE0TkRWSHNuZnlYRG1NQkJTejlWMXo2VWo3VFlOclNwS29EUlpCdW9Zc0ZvR090MWh0ZnNxQzBvQlpZWGJGV1dQM0pLQ2piWUdMcmtualI0MkcyZTF6UGs3VEJiTUloWVRlWUtwdEp3THViOHNHMWxhZmdITkFGRnVrS1pObEtKZUNQdkQ5T3VVWUZnQnFQeUppdzczMnpqZGxGdC94ZlFRRGYiLCJtYWMiOiJlYWMzMTY4YWU1ZmEzMGZjMmVjNmRjYzdkMzUxOTY1ODBmZTlkNzAyNzgxYTJhNGRjZGZiNDhlY2RkNjU3Zjk0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ik1XYXZGRkV6MURKMUZYUnFWektCK3c9PSIsInZhbHVlIjoiNFF5T2V2a2FCYmQ4a2dZY21zWEFhYzh4a0pDajZ3K2VSRk5mdTVQb3dRVXBJbXAwWVNEajc4VE1EeHBCTS9xMmYzUFVROWljbXRpbXpJcXNra1p4NitCaks5YWIvU2ZrVENBN3B1TDRHLzNYT001MGJ5ay80NlMySEY4b0VWcHcrYy9lTDFaYTFaWVpuTUYwQVVrYTh4UWZDSGg1c1lCRGFqVmowdzFVY1NhOXpVYmVvaHg2ZXd1NkpHNTNScXN6UjJIZXNSdlpXRGF1cktObG8rTXpVNU8zUC9EcGJGRVMzOFBMdlBCdG5mS1d0eU9tYVdjelFhQWRtL0ZaQWphUHBsMjYzbmpubW1HeVpybUd6ZjRzY1Iva0g0aUJFS3B1ZFpjWUdQQVlSMVljMFRhcFQ3T25aV25vMTNtUWlTeXh3WFlxemZIT3llRlFnR1ZNYmdIQWMxZFpOa0FtNEp1dk5McVVHVXV6S0VrS005a3ZOeWtRbXgyOFE4NVYvbVhNWXZlTkJxS1FxS1VZcnAxYmZoR2tTSUVyWnJmdldHUFQrMmU0emM1WjdmSUVxdXVFQjZiMHF0OWI1dmRuVlBTZ3NUSW96L0xTa0w5eHhSbVhNZWRQOVNxVm90L1MvcXRqczZPaHljN0JQSlVReGxLdFg5anRIMDNIU1p5bjdKNnciLCJtYWMiOiIzOGI2MTk2MjkyMjIwOTQ3N2JiYWRiYjRkODk0YmM0YWE3YjYwZmVhYTc3MjA1ZTI2OWU3MDBiYTFhNTA5Yjk3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1507381437\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-105242508 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xNV7SvMX3AgbiuDVO62eNXfryfZD4KbWr3rEIkXt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-105242508\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1447900181 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:44:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkZIWlQrZVpScDdyZHlkbVg5bURGWXc9PSIsInZhbHVlIjoiK1ZaZ2dqMzEvUXJLak9WOU1sS21uSHlQWTlRT2tidG9DMUZ3VkJtTkxHWXpaakNkeTdmWDNDNWo1ZzVmclVBdkNobUlJNzVMdjQxL1pQU3BoQzJEK2JhbzZpL0FCRXpyMzA0UTdTWUJ3bDdMYWpuVFczWjJqNFUxb0RVdUJxNHpRUGN1bEU1aCtsaHZIdmtrbmNVOC9WemlDTjhNVFFFS2tKZWZFdnl1aVdFYURKcGJQVWtLMWJuTmJPQXlpV1JJQit3YU1TU1JoQ1VUYnZxdTg4emFwNEhsSEFBT1FnR2Q3VmRTMytRMmUxbGU2aEtCRGpvZThaUWxMOE8wRy9PaG9FN3ZuNkd3UlFhN2VpS0hSN1ViVHF6TW9HYU56cGVUNlZsQlRyZUpMOW1BQkxiYXFadFJqZ2ZqRkM2YkJGa0FCbVVCVU5oVUgzSTIwK3g3UDlyMWEyYmJ3czlkczU5Q2lwdFBQZ1R2QXc2UExoY012SVdib3lQVnZ4WlgvQlZIdHVkL2g4NzV0N3JEYmNPZ0YrK1FBMi9vWHVZUGE1RkJhZkNQeEh5SndNS0VZUXdWWW1pZjVXT2ZaVGRLdDUxRk4yQnhabGJyTHZWNzcyMjZWZGlYM1RHeXJnT0tyVWRFNGtGeTQ2RXF4V0Jsc0YrYkQvMDNiKzhNei8yYzgraTUiLCJtYWMiOiI3NmI2OTU3ZmYyOGVkNmFlZDM4ZjQ1YzAxN2I3MWI3M2YzYjhiYzFkNDljNzM5ZTgyMDMxYTFiMWVjMmI3YzhlIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:44:12 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlFxeC9CMkZQcWtuM3hyUnNTUmdLaUE9PSIsInZhbHVlIjoiT0RCUGgwdUJPTStkOXBueVRlankyN1JiMlF0NG44ME5qSldLVEVCckJJRjhlSVRUM2xteVhJS0ozbnpTNXc0NE9sVHdYYnNYNmJSd3o3YVhtcHlCTWNXSy9wdVo0UTVmNUJnSmoxbFpLRDYxaURaSjlSeDROa3o3SUNWTlZ3QTY2OGVpRGthMXhIUnFIRzVFZVhoU1FFRTZJVWRZR0ljN04vM1RreVBCYklSZFIyOVpCbjdCY0NraTVISzZBam52WjJLSEt3Z3l6RFB0eUcrUlhrbjI2SHNBY3VBV3BQUU9QOGluMU9PSmJJNXU4QStlbUdDYkRlRFk1S3Q3Q1hMRFNESXFIT29IVGhROE1HRzNJQzhSdHlEaXdUK0ZGVHRrcjFhT3NHaUJuVm8vMU5GeTl0Qm9oV2tDaHl4NzlDYzBXVlhLQmV4V1VqT2xUQkJQM3dCNFc3QXV1V3VlMFRCbTZkOHNDR1JlWW10R0JySGhJUUNCcmt0c2lNaVNlajBvbmFCQ1NMTTlNQkphdzZ2dmFtNHNmTUJaU3V5dDdKa3l2UFFZK3NYZGNUUnQwT3dGTHZRNHNzZHdtdTNkT0xtbld1VzBheDdXb2x0MjFEa1RJN1MyTW5SSUo1NGJydU9tK0hWTy9wSlFhLytSZ1BocTd1YnVvUVlic0kzczk1UGQiLCJtYWMiOiIyNDIyOTJhZDk5M2FlNzk1YzczMjBkNDcwYjk2YjRiZjYzZTM2MTUxZDYzMWE0YTY1NzNiNzk3OWEzOTE3NjY2IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:44:12 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkZIWlQrZVpScDdyZHlkbVg5bURGWXc9PSIsInZhbHVlIjoiK1ZaZ2dqMzEvUXJLak9WOU1sS21uSHlQWTlRT2tidG9DMUZ3VkJtTkxHWXpaakNkeTdmWDNDNWo1ZzVmclVBdkNobUlJNzVMdjQxL1pQU3BoQzJEK2JhbzZpL0FCRXpyMzA0UTdTWUJ3bDdMYWpuVFczWjJqNFUxb0RVdUJxNHpRUGN1bEU1aCtsaHZIdmtrbmNVOC9WemlDTjhNVFFFS2tKZWZFdnl1aVdFYURKcGJQVWtLMWJuTmJPQXlpV1JJQit3YU1TU1JoQ1VUYnZxdTg4emFwNEhsSEFBT1FnR2Q3VmRTMytRMmUxbGU2aEtCRGpvZThaUWxMOE8wRy9PaG9FN3ZuNkd3UlFhN2VpS0hSN1ViVHF6TW9HYU56cGVUNlZsQlRyZUpMOW1BQkxiYXFadFJqZ2ZqRkM2YkJGa0FCbVVCVU5oVUgzSTIwK3g3UDlyMWEyYmJ3czlkczU5Q2lwdFBQZ1R2QXc2UExoY012SVdib3lQVnZ4WlgvQlZIdHVkL2g4NzV0N3JEYmNPZ0YrK1FBMi9vWHVZUGE1RkJhZkNQeEh5SndNS0VZUXdWWW1pZjVXT2ZaVGRLdDUxRk4yQnhabGJyTHZWNzcyMjZWZGlYM1RHeXJnT0tyVWRFNGtGeTQ2RXF4V0Jsc0YrYkQvMDNiKzhNei8yYzgraTUiLCJtYWMiOiI3NmI2OTU3ZmYyOGVkNmFlZDM4ZjQ1YzAxN2I3MWI3M2YzYjhiYzFkNDljNzM5ZTgyMDMxYTFiMWVjMmI3YzhlIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:44:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlFxeC9CMkZQcWtuM3hyUnNTUmdLaUE9PSIsInZhbHVlIjoiT0RCUGgwdUJPTStkOXBueVRlankyN1JiMlF0NG44ME5qSldLVEVCckJJRjhlSVRUM2xteVhJS0ozbnpTNXc0NE9sVHdYYnNYNmJSd3o3YVhtcHlCTWNXSy9wdVo0UTVmNUJnSmoxbFpLRDYxaURaSjlSeDROa3o3SUNWTlZ3QTY2OGVpRGthMXhIUnFIRzVFZVhoU1FFRTZJVWRZR0ljN04vM1RreVBCYklSZFIyOVpCbjdCY0NraTVISzZBam52WjJLSEt3Z3l6RFB0eUcrUlhrbjI2SHNBY3VBV3BQUU9QOGluMU9PSmJJNXU4QStlbUdDYkRlRFk1S3Q3Q1hMRFNESXFIT29IVGhROE1HRzNJQzhSdHlEaXdUK0ZGVHRrcjFhT3NHaUJuVm8vMU5GeTl0Qm9oV2tDaHl4NzlDYzBXVlhLQmV4V1VqT2xUQkJQM3dCNFc3QXV1V3VlMFRCbTZkOHNDR1JlWW10R0JySGhJUUNCcmt0c2lNaVNlajBvbmFCQ1NMTTlNQkphdzZ2dmFtNHNmTUJaU3V5dDdKa3l2UFFZK3NYZGNUUnQwT3dGTHZRNHNzZHdtdTNkT0xtbld1VzBheDdXb2x0MjFEa1RJN1MyTW5SSUo1NGJydU9tK0hWTy9wSlFhLytSZ1BocTd1YnVvUVlic0kzczk1UGQiLCJtYWMiOiIyNDIyOTJhZDk5M2FlNzk1YzczMjBkNDcwYjk2YjRiZjYzZTM2MTUxZDYzMWE0YTY1NzNiNzk3OWEzOTE3NjY2IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:44:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1447900181\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1266810495 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1266810495\", {\"maxDepth\":0})</script>\n"}}