{"__meta": {"id": "X0c9716abb715e3e27d0c6400c4fe5418", "datetime": "2025-06-08 15:30:48", "utime": **********.614516, "method": "GET", "uri": "/customer/check/delivery?customer_id=7", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749396647.974378, "end": **********.614543, "duration": 0.640164852142334, "duration_str": "640ms", "measures": [{"label": "Booting", "start": 1749396647.974378, "relative_start": 0, "end": **********.53213, "relative_end": **********.53213, "duration": 0.5577518939971924, "duration_str": "558ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.532147, "relative_start": 0.5577688217163086, "end": **********.614545, "relative_end": 2.1457672119140625e-06, "duration": 0.0823981761932373, "duration_str": "82.4ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44029176, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.023620000000000002, "accumulated_duration_str": "23.62ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.576003, "duration": 0.02296, "duration_str": "22.96ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 97.206}, {"sql": "select * from `customers` where `customers`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.603981, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 97.206, "width_percent": 2.794}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-344884226 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-344884226\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2029980497 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2029980497\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1013675613 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1013675613\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1694109238 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749396510751%7C76%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjMyKzJ6SEdkNFJaQXF1Rk9RUlQwMlE9PSIsInZhbHVlIjoiSWJXWk9mUVVsZGw0cm9xUDIrT3d3T1I4SGNzSGZxQ2hLaG1kWWRxdDNpQ1RaOUtuZ0d4UlRvd3htYUFWMXJremNyRGxVRkpCL3Yvc0lqeDFJSFRqTE5Ca0lnRm1OZktlRVkwbzVadlgzTk5wNnJ0cktzdUhwVlhNWDM3UlQvQ1N0WXZheUFHUENmMFJGSzBhMDk0NlhQUGpwL040WFIyaWZoTDl3cnFnYkh3OCtGOXVFNHhvNEdsVXRkUEh0V3NnbXFydzFxeWNDUFdrT2RBbnltNlNpTW8za0hiakRCbUFPeVJPZUZWUlFzSzZsdGpDUnhmZzg3Ynd4dHpKYmtLQVZvSVlXRkFGa3JSM3F2RkNtcGdVcXc3U3JqTkg4RVpIeEhKcXA0dTFkRHZLNEIraHMza3lSL2JCWldXZm94bm9UeHAyclNYZy84QVd6NWZmUXBtTVV3N1BJdGY2cUkrREJ4R3pYanppTEFFVnJFUU50NGlhVWlvR1ZNaGhBOFBWWGZmRk4vR3pCam40enBqejVkQlJpT0xqNm1iSk93WU1GcXNyRmx2N2NnQm50NHlRQmY0NGU4VXZJU2RQSW8xSlVHYmw5R04wdDFKTnFoRkY4QlJLc0tzd3g1dlFqQlJVM2ZFVTdCb3k0Wi9FQ015M1hIN2ZkR093bXloQ1NVaEEiLCJtYWMiOiIwZmE0MzZjNTgyNDI1M2ViNjdhMzM4NzRkM2UwZTMyZWZmN2MyNTk3YTcxYTJkZWNkY2UzYjI1NzExOWRlMDg4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImVXYzVQcGtXMzc0aEljMFQ2bUgwaXc9PSIsInZhbHVlIjoiNm9uZkxxTU8veU4yNWE0Slpqc1E4Y3BxeW4yaEduSXpYYmgzZkM4RkNhTTZINXQ2bTl4VzdPL0ZVM0JUYzBON3ZYYUhvZ3BoMlYyczc1ZExybENHUjYvMXZsdGt6b3M4eDZVQTYxRTZMMXhqaG9CaFZqR0t5V0ZMaCtJYWJKRnVpNXZML2dPd3dQSElMZWNpUmRMR1ZFc0F3ZSs5eElhcUVNSm42b3dFZWdZNzZBUmt5WkhVNDgxZ2pidVdDMk1TeG5oVHRvd05mTHpUaXBFUXBGVlUrKzNOVVFzYTlLUmhYS2QzRWpVbGxEU2ExVFF5eTFJc1Z1WTkweFlWTzhCeEczRXVXQVlZejdHODBTZ0ZSR25oQVZHNkhHUkRUcU5SVC9uZ2E3b0JncVlyTUFFOEVkUFpuNVNjR09IYWI2WktNUDRXY3VjdlNuOTNrUWx0TE01am00Y0NVb0RrRUtJU25vME45dlRtRHZOZ2k0U29FUU9mSHVPMzdnOEZtcUs2bXkwQWJYb25RN0YyZEk5eFRESHA1RXVIaWtTblhtZjcveHdjTTJkQU1CZWluakdDQS92cGxEREV0SHhYbTV2c2ZMY2RDM0YyYW9TbUZlenpzTm1uZnRtWFM1aDJ1VTZpRFIvcjRKcXk1aWpVMkhVVGpyMzNmK2lhYTh1Znh5K0wiLCJtYWMiOiJlYTc1ZjJlZmQ3ZjE3Y2EwM2U3YzMxOGE2ZmMwNGM5NWQ5Nzg2NjJmYWYxYzEyNzhkYmUwMmFiYmE0YTdjMzBhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1694109238\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2006556924 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xNV7SvMX3AgbiuDVO62eNXfryfZD4KbWr3rEIkXt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2006556924\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1474091243 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:30:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InhYU0Rlc0dsK01EQmxQTGFOWjlXd3c9PSIsInZhbHVlIjoiY1k0WVdMeGl2bFYxbmNMMEsrNTlQcU1hNjlpWENPTk1YZ3A3VmxiNmN5dDh1M00wNS9PUjB6Vm04eTUvWStLbllDS3Q0MVhSUWYvM3BOWTdPRUtsVyt5bnFnNEFrNmxoUzF4SXZjbnpCUWg5ZnNLSUZucXI4OFlpMk5jOGFIMlZtS3BmVUFhMTZac2k1NEVic0xxZ0RHZWVPTW04UzRzZE9MN0taQ01YSmtGOHFzOGtKb3VPeFVFSFM2SloyWkNzeDVlV0tNd0p0YTVWcXIwSnJ1ZWRUQ0UxOGpxMUtDUmhxK25zaVZyTCtyMU5PbnZ5OTVDTk5MSHRjYWh4d0JIcVVQZS9LbHhBaktEYWVwR2k5ODdGd0VkYUNXY2lHNTRJUkVWcFdLUGxZQjN3V3RNMEs1dnEvdHZuVVBVdmpnT1B4VVpkMXlzS1BpYVBIek9md25YNnVrOS9DV1NERHl6T1hsUWE4YjREbUFRUEw1Mi9vYllCanhFR1lqTnNHK0prTHdCUFhKVHB0amxxSHA1eTJPZ3crYWM2amY1U3pqWHNwRU9zSDluL2RST2tNRTVuRFBGT2l6dFdpU2hGeitJNGpIMzN1SVNkMnNpeU5Gc3JLRWZleXNoL25aaHdIYlA5UmV3OEVCazZoUm4wUjhmRmtxZE9udWJjbWFSM2JiSWciLCJtYWMiOiIwNWU4ODgzY2M0NTMzYTBlMTE4OWY2OTFmNmU4OGU4YTVjOTY5YmRiNzA0NmY2OGFlNmYyYjQyYWMzNjM4YTZiIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:30:48 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImIweDd3SU5uK283TVpiSmJYcSt0bUE9PSIsInZhbHVlIjoiU0VacExSUnlJQmREVzB5cUdQU2pYVVVSVG4xMlI3Y29hRkt4eGM5UWZ2NGlISkp3WEF1d2FiaCtJN0RHWlZsSU1xbVU5Z2tsT3h6TlB0YWJhdkZ5bTdpeEpQdkUvWGhQR09lbllVNzMrYWZOclJwbkdYbzdxRTBBemxHbXZ6Y1d6dzZaNU1BTHNzNU5UYzNySFNBWmpYcEFoY2hpaHE0VEZDeWVkOUJxTERWUkFRc09jMEd1RHZGaTY4Y05Vc0tWUGxFa3NuSGlnL2hzNmtDdVl3cGRpTVdFZXZHTmV6WmRTa2F3eUxDWlc0aGFrZ1lIeGpJSGtaSDQ2UUFmd3BocFNZV3JIaHVXa0tLMTRHdWhLQmdQYlQxWnlzbjQ4Q05ERXMrKzVkeTNNa091bWNJMlNEM2Q5c3JwQlhRa3ZKWmY3b0hKY0tienJhMDB6dHl4T0JEY25aaENDeHp4ckZST2ltR0VRSkdSK0gzTjNhdENZTDlqL1dLYUVnYW82VkZVUzlINjRxRXVYcm1aMk4yVklFZStoSGhTT0d4VTVnNElhSGo1T0hFMGF3YjVydnlPTHZCc0NndTYybm5hYVNpRWJvdk5id2RqQmwvMDJzeThNREJZTXAzbEpTTm5pcG1mZ0hSTkpsNmxuVjFLYzRGL2xWbU9yT01XWis3czdlTGIiLCJtYWMiOiI1ZGQ1OWU1MGYzMTk4MzEyMmM0MTZhZjBiYzY1MTc0ZWVmM2U5MDlkYmQxZDIxZmRkYjRiZTRiMTBjMDBhMjQ5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:30:48 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InhYU0Rlc0dsK01EQmxQTGFOWjlXd3c9PSIsInZhbHVlIjoiY1k0WVdMeGl2bFYxbmNMMEsrNTlQcU1hNjlpWENPTk1YZ3A3VmxiNmN5dDh1M00wNS9PUjB6Vm04eTUvWStLbllDS3Q0MVhSUWYvM3BOWTdPRUtsVyt5bnFnNEFrNmxoUzF4SXZjbnpCUWg5ZnNLSUZucXI4OFlpMk5jOGFIMlZtS3BmVUFhMTZac2k1NEVic0xxZ0RHZWVPTW04UzRzZE9MN0taQ01YSmtGOHFzOGtKb3VPeFVFSFM2SloyWkNzeDVlV0tNd0p0YTVWcXIwSnJ1ZWRUQ0UxOGpxMUtDUmhxK25zaVZyTCtyMU5PbnZ5OTVDTk5MSHRjYWh4d0JIcVVQZS9LbHhBaktEYWVwR2k5ODdGd0VkYUNXY2lHNTRJUkVWcFdLUGxZQjN3V3RNMEs1dnEvdHZuVVBVdmpnT1B4VVpkMXlzS1BpYVBIek9md25YNnVrOS9DV1NERHl6T1hsUWE4YjREbUFRUEw1Mi9vYllCanhFR1lqTnNHK0prTHdCUFhKVHB0amxxSHA1eTJPZ3crYWM2amY1U3pqWHNwRU9zSDluL2RST2tNRTVuRFBGT2l6dFdpU2hGeitJNGpIMzN1SVNkMnNpeU5Gc3JLRWZleXNoL25aaHdIYlA5UmV3OEVCazZoUm4wUjhmRmtxZE9udWJjbWFSM2JiSWciLCJtYWMiOiIwNWU4ODgzY2M0NTMzYTBlMTE4OWY2OTFmNmU4OGU4YTVjOTY5YmRiNzA0NmY2OGFlNmYyYjQyYWMzNjM4YTZiIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:30:48 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImIweDd3SU5uK283TVpiSmJYcSt0bUE9PSIsInZhbHVlIjoiU0VacExSUnlJQmREVzB5cUdQU2pYVVVSVG4xMlI3Y29hRkt4eGM5UWZ2NGlISkp3WEF1d2FiaCtJN0RHWlZsSU1xbVU5Z2tsT3h6TlB0YWJhdkZ5bTdpeEpQdkUvWGhQR09lbllVNzMrYWZOclJwbkdYbzdxRTBBemxHbXZ6Y1d6dzZaNU1BTHNzNU5UYzNySFNBWmpYcEFoY2hpaHE0VEZDeWVkOUJxTERWUkFRc09jMEd1RHZGaTY4Y05Vc0tWUGxFa3NuSGlnL2hzNmtDdVl3cGRpTVdFZXZHTmV6WmRTa2F3eUxDWlc0aGFrZ1lIeGpJSGtaSDQ2UUFmd3BocFNZV3JIaHVXa0tLMTRHdWhLQmdQYlQxWnlzbjQ4Q05ERXMrKzVkeTNNa091bWNJMlNEM2Q5c3JwQlhRa3ZKWmY3b0hKY0tienJhMDB6dHl4T0JEY25aaENDeHp4ckZST2ltR0VRSkdSK0gzTjNhdENZTDlqL1dLYUVnYW82VkZVUzlINjRxRXVYcm1aMk4yVklFZStoSGhTT0d4VTVnNElhSGo1T0hFMGF3YjVydnlPTHZCc0NndTYybm5hYVNpRWJvdk5id2RqQmwvMDJzeThNREJZTXAzbEpTTm5pcG1mZ0hSTkpsNmxuVjFLYzRGL2xWbU9yT01XWis3czdlTGIiLCJtYWMiOiI1ZGQ1OWU1MGYzMTk4MzEyMmM0MTZhZjBiYzY1MTc0ZWVmM2U5MDlkYmQxZDIxZmRkYjRiZTRiMTBjMDBhMjQ5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:30:48 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1474091243\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1245189450 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1245189450\", {\"maxDepth\":0})</script>\n"}}