{"__meta": {"id": "X2f3a7fb4a1e242a8a44e1f04f6c07f4a", "datetime": "2025-06-08 15:28:42", "utime": **********.380713, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749396521.398652, "end": **********.380754, "duration": 0.9821019172668457, "duration_str": "982ms", "measures": [{"label": "Booting", "start": 1749396521.398652, "relative_start": 0, "end": **********.174516, "relative_end": **********.174516, "duration": 0.7758638858795166, "duration_str": "776ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.174535, "relative_start": 0.7758829593658447, "end": **********.380758, "relative_end": 4.0531158447265625e-06, "duration": 0.2062230110168457, "duration_str": "206ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46078776, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.293248, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": **********.308243, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x landingpage::layouts.buttons", "param_count": null, "params": [], "start": **********.359594, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.phplandingpage::layouts.buttons", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2FModules%2FLandingPage%2FResources%2Fviews%2Flayouts%2Fbuttons.blade.php&line=1", "ajax": false, "filename": "buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "landingpage::layouts.buttons"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.367615, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.029810000000000003, "accumulated_duration_str": "29.81ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.235134, "duration": 0.01495, "duration_str": "14.95ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 50.151}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'ty' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.255808, "duration": 0.0077, "duration_str": "7.7ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "ty", "start_percent": 50.151, "width_percent": 25.83}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.2716382, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "ty", "start_percent": 75.981, "width_percent": 3.321}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.294558, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 79.302, "width_percent": 3.455}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.php", "line": 10}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.309974, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 82.757, "width_percent": 3.489}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4081}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4123}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.336005, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4081", "source": "app/Models/Utility.php:4081", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4081", "ajax": false, "filename": "Utility.php", "line": "4081"}, "connection": "ty", "start_percent": 86.246, "width_percent": 3.388}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4082}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4123}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.3443549, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4082", "source": "app/Models/Utility.php:4082", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4082", "ajax": false, "filename": "Utility.php", "line": "4082"}, "connection": "ty", "start_percent": 89.634, "width_percent": 3.053}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.350363, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 92.687, "width_percent": 3.053}, {"sql": "select * from `landing_page_settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "Modules/LandingPage/Entities/LandingPageSetting.php", "file": "C:\\laragon\\www\\to\\newo\\3\\Modules\\LandingPage\\Entities\\LandingPageSetting.php", "line": 27}, {"index": 19, "namespace": "view", "name": "landingpage::layouts.buttons", "file": "C:\\laragon\\www\\to\\newo\\3\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.3619509, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "LandingPageSetting.php:27", "source": "Modules/LandingPage/Entities/LandingPageSetting.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2FModules%2FLandingPage%2FEntities%2FLandingPageSetting.php&line=27", "ajax": false, "filename": "LandingPageSetting.php", "line": "27"}, "connection": "ty", "start_percent": 95.74, "width_percent": 4.26}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "WEgVgdfObfw6rNC2frmzIt58FwRoAm7QcDJjfmq1", "url": "array:1 [\n  \"intended\" => \"http://localhost/users\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-265325167 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-265325167\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-764868140 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-764868140\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-212959554 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-212959554\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/customer</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=xh9goj%7C1749396266838%7C4%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik1Wd2RoKzNyNFBCTDRxTk02TlFVdnc9PSIsInZhbHVlIjoiVkwwMlR3d1pqYy8xd0sxejBwcEhWVHJ0bVJwRW1PUnE4MFRBYndibk41OWVvUjdIZEp3SWhSd0RpaXo5VWUwMEp3Z1o2OFFLUU9HMXhzZXQ1dUY2b1YzMlVzZktFRzB6eXZMVHU0cGpxVXd2TDNua1g2cERoQ0ZZbXBaSzN1M3I0eFczVUg2b2ZoeVoyelRlbDBlS1FTemxmL2d6TTluYTl3UkRGME5McW4zcExjTDJvQUdKcVlvWCtIeHBzeEt5OGVlbmNlZ0diVkt2K1h3YUFFMEpyVGhMem0rNGZRbEZjbWVZRGtrQWU1cklrN2kxeC9YcUIxM3NiblloRWhCMWp6SkFibUZFaTcvNURzL2lNS2Rtc1dOaGptcDRSUGVmcmdGYkEvQ3pabDRicHRoRlZuTUk0clMrb1p5YWpQb1g4aFpBN3o5bmlzVU9rMW9nczYzak9ya3ZTTkR4bXpkNEMvZVlOOUViVit5dWpqbmhWY1ZRSlZoaitaY25uK0FsT3pDY1Q0Qi84aHFCQ0YwSXhBVUFzSGFmUVhWOXlqMzFEaEJDaFpRbmduWnNvUWt2UVdrSkp5THhjVlBqcWtINFRwUEwrYktyemVWRXRaVTFydmNHMDFFd1Z3dDFlamNEWkt2d1hiWnhxS0NIZTlnVXZrTEpsdUZKazFGTFFVR1UiLCJtYWMiOiI5YjhjMjNlMWJiNWYwOWQ4ODNkMWNjYjM5YTJjYTZhMDlkYTExYTg4OTEzNzNjNDlmNmEwNzNkY2I5YTNhYjljIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IklkTnBldUNSb0E5RjlKUlJiTDR1eHc9PSIsInZhbHVlIjoiOEY2MWNxN3FzYTFsR3p2MEkvelhGcERtM21aOUJzdk84QTl3bWNzazM0bEg2ZUJMNmtIYnFLQUgwODJjbVJsK092ZE84eTIzMHpJNmxXSVpUR2tqOThQNkpERWs4NHhPVTRRTjYvZnVJZmxDRlVkNksrbjZnbTBPR2NQalk5NGpRMFFKVE5hNE9rTUhzL1YxcjNqeFZ5R3plbkUyZkthcG5VVHFPNkNQV3NZQ1l2SjNTOFJTWE5xUGYyQmpFNmVlakROclhkTnRub0M2SVUyREpEN0kybHJ6bjBBS2NBNFBuTFpLR2k3Q1pIa1R4Z1ZWOVRiTXoxUHhCd1RONkkrYVNVVFBpTmdDeGVTdGgrTnFwNDRocTZnT2lYenRXbHNENXNVTWFvUFZXQUZ3WUl3RVl2TDNSSzhoL2NpOTlnZjdhOW54QzJwMFlXcnpldkpNbHVhRmVJZ29zU3kwVksvekdrZ2Z0Y1NPMElHVDNvMHdBS01KbFUrQm9DdzI3dmRTamxXeWRaa1NsRWZFNU9iMFBBTzI4K3RhWGx3YUxKRWoyUUNkSnNhZUlmWTRveXhwOGRBM2Rud0xlZDI3eVA0S0hWZlNrUHBac0FOZTFUTlYxQkFqdDVraXBTUXh1ek9DL0pBcCtXZXhIZlo4TmQ0UDREUjFFY0k0MFhmMjNaSEoiLCJtYWMiOiJkM2ZkNzUzMzRmNjEwMjM1ODI1NThmMWZmYjA2NTk0NGQ4ZDA5ODA1YjY4ZTA5ODhkYTFlODgyMTZiYTU2M2Y0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WEgVgdfObfw6rNC2frmzIt58FwRoAm7QcDJjfmq1</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BJuMSlnG5Xc84hQpzCBfZadVHL6kh5OEk3auNDNe</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-8928444 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:28:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im52NENVaS9CRjVQRkdpOWZ2YjB6Q3c9PSIsInZhbHVlIjoiUnBYQmpIU1pYS0IxNS80QlkzeVBnTUVCSElQTkVYVDFOS0MrUi9yVk1DM1ZudXc1alRlL2tmcjRZWXNLd3lBb0E2VVVKcGhvdUYrd0NQLzlyZFRldktjTWJ1Vm9vOXBZemo5RnFkRmpSc3dkRGhTcjAyRXl1cURpd0dYc0xoQ082QjB6WUJaMUJMbXRWangxWlpDOWJydDM5dDRTb3hJY3RpU0ZWTlZUOEJGMmZHT2Q3VnBDNFNPcDZnb0l0ZjRDeEthSDVGZjdjYjMvaW1uNW9IdTRWWmVML29ENzcybFMxWWlXSDFjU0haNmZKa3JJemN3T2hYb1RkUHduSW1iM0VmUGRQSmVyMWJvK0VXOGNTaVpoTWxWMncyTitwUUZmd3VlU0dzcWNCb3JmRStnZi9JeGxMcktuWXdxSmRmMGhXZUNoV3N5SjBhMEN0Zk93cFJtOXpIMlZURWJzcGJ0MG1rTTFwcEJRek9oVS9lK1VFUmJuZHhoVG1qSHhkOHIxbEVqSWxzV25BMVh4SEF3Rzdna3RaUmdWVjNIdy9zMEU3SlBmZ2R3RXltRWQ3d0ZpWWk2YVlTeGo4Tm5BRVM1N1JjbTZqWEFsSWMzODJjSUdlT0RyZ3J0dFdkcnpQaGdJdkJha0V4bUsyZWh0MWsvbWpsSDE3NUI0OVNHTGdacVkiLCJtYWMiOiJjYmY0ZjZjOGEyMmRjYmVlNzFhZWViYjc4MTkzMzI5NDcxMWM2MjI4MGY1OGQ5MWIwMGM0Y2RhNmFhMTM3MDkxIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:28:42 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ik5ZV1RHeEFsejMzeTN0KzRQZG5pckE9PSIsInZhbHVlIjoiaFhxQjBmTzRIQ3RQNWM4MTNTVndoZWQwRXcwVVBDNGdqcE9ROVMvblhKT3lLeDZzbFdVMFB2M3k2NitlbzBNa0hGMXJ3d1VZMUVPSVVNUmtrcXZDczRlbVltVWV2YnZOYVJMZmJmcytKdmtYWFc3TU5ROFVzOXFielZONERMeTlaNm10N1UyNnVsU3lDakFCRktqT3liTWtPb0xXWGF0Nk1Pc2lwZ0h5WnY3VmVNOEZhd0hLcldqOTZTbWRPTmQ5T2Q3M1VYYWowd0tYdU0xQ1lBdjAwTmo5NWNQWDNKRzl0ZGg2VkIvVkhlOXVWdk93MmdycTFmNkFxMmR2RFdzMGJHb1h5dEp0M00xRDBQNTZvQ2cvQW9RRFB6MEtlNFFtUmJLMWVWbnFFaXpoRmRhZ1JWTENYV1hZOEFNc0RLTktrWXVoMkZIMk5NQ2Y5SVg1OURDaXhOSG90bC94ZUJhOGpOcS90YWxuOGRFK0ErZUpVa2NVNVdrRDVNZGJYL1YxSytZK2R5SkY2UG1YYVN6dkh2dzNMRDlUckpVa05BSzRQdURqYW9tTXA1OUNzVlZ1SW1rWDFNVTlrTDBseVQ0ck1ac2duZ1BBU3loUTc2OWtIdjhDVWhUUFkrbitjOU5vaDY0S2lPTlBHNmRFOE5SMkszd2ZEZE1FbzJySGZQcU8iLCJtYWMiOiI0N2E5OTVhZDc4N2M2OTU2YzVkNzgxNmI3MDU4ZGM0YTAyNzU4ZjZhNDQ5NGJiMGUyNmM0YmYwZjNkZTRlNmIxIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:28:42 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im52NENVaS9CRjVQRkdpOWZ2YjB6Q3c9PSIsInZhbHVlIjoiUnBYQmpIU1pYS0IxNS80QlkzeVBnTUVCSElQTkVYVDFOS0MrUi9yVk1DM1ZudXc1alRlL2tmcjRZWXNLd3lBb0E2VVVKcGhvdUYrd0NQLzlyZFRldktjTWJ1Vm9vOXBZemo5RnFkRmpSc3dkRGhTcjAyRXl1cURpd0dYc0xoQ082QjB6WUJaMUJMbXRWangxWlpDOWJydDM5dDRTb3hJY3RpU0ZWTlZUOEJGMmZHT2Q3VnBDNFNPcDZnb0l0ZjRDeEthSDVGZjdjYjMvaW1uNW9IdTRWWmVML29ENzcybFMxWWlXSDFjU0haNmZKa3JJemN3T2hYb1RkUHduSW1iM0VmUGRQSmVyMWJvK0VXOGNTaVpoTWxWMncyTitwUUZmd3VlU0dzcWNCb3JmRStnZi9JeGxMcktuWXdxSmRmMGhXZUNoV3N5SjBhMEN0Zk93cFJtOXpIMlZURWJzcGJ0MG1rTTFwcEJRek9oVS9lK1VFUmJuZHhoVG1qSHhkOHIxbEVqSWxzV25BMVh4SEF3Rzdna3RaUmdWVjNIdy9zMEU3SlBmZ2R3RXltRWQ3d0ZpWWk2YVlTeGo4Tm5BRVM1N1JjbTZqWEFsSWMzODJjSUdlT0RyZ3J0dFdkcnpQaGdJdkJha0V4bUsyZWh0MWsvbWpsSDE3NUI0OVNHTGdacVkiLCJtYWMiOiJjYmY0ZjZjOGEyMmRjYmVlNzFhZWViYjc4MTkzMzI5NDcxMWM2MjI4MGY1OGQ5MWIwMGM0Y2RhNmFhMTM3MDkxIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:28:42 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ik5ZV1RHeEFsejMzeTN0KzRQZG5pckE9PSIsInZhbHVlIjoiaFhxQjBmTzRIQ3RQNWM4MTNTVndoZWQwRXcwVVBDNGdqcE9ROVMvblhKT3lLeDZzbFdVMFB2M3k2NitlbzBNa0hGMXJ3d1VZMUVPSVVNUmtrcXZDczRlbVltVWV2YnZOYVJMZmJmcytKdmtYWFc3TU5ROFVzOXFielZONERMeTlaNm10N1UyNnVsU3lDakFCRktqT3liTWtPb0xXWGF0Nk1Pc2lwZ0h5WnY3VmVNOEZhd0hLcldqOTZTbWRPTmQ5T2Q3M1VYYWowd0tYdU0xQ1lBdjAwTmo5NWNQWDNKRzl0ZGg2VkIvVkhlOXVWdk93MmdycTFmNkFxMmR2RFdzMGJHb1h5dEp0M00xRDBQNTZvQ2cvQW9RRFB6MEtlNFFtUmJLMWVWbnFFaXpoRmRhZ1JWTENYV1hZOEFNc0RLTktrWXVoMkZIMk5NQ2Y5SVg1OURDaXhOSG90bC94ZUJhOGpOcS90YWxuOGRFK0ErZUpVa2NVNVdrRDVNZGJYL1YxSytZK2R5SkY2UG1YYVN6dkh2dzNMRDlUckpVa05BSzRQdURqYW9tTXA1OUNzVlZ1SW1rWDFNVTlrTDBseVQ0ck1ac2duZ1BBU3loUTc2OWtIdjhDVWhUUFkrbitjOU5vaDY0S2lPTlBHNmRFOE5SMkszd2ZEZE1FbzJySGZQcU8iLCJtYWMiOiI0N2E5OTVhZDc4N2M2OTU2YzVkNzgxNmI3MDU4ZGM0YTAyNzU4ZjZhNDQ5NGJiMGUyNmM0YmYwZjNkZTRlNmIxIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:28:42 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-8928444\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1651794136 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WEgVgdfObfw6rNC2frmzIt58FwRoAm7QcDJjfmq1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1651794136\", {\"maxDepth\":0})</script>\n"}}