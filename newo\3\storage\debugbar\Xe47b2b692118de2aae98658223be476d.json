{"__meta": {"id": "Xe47b2b692118de2aae98658223be476d", "datetime": "2025-06-08 15:29:17", "utime": **********.235441, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749396556.406942, "end": **********.235463, "duration": 0.8285210132598877, "duration_str": "829ms", "measures": [{"label": "Booting", "start": 1749396556.406942, "relative_start": 0, "end": **********.129797, "relative_end": **********.129797, "duration": 0.7228550910949707, "duration_str": "723ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.12981, "relative_start": 0.7228682041168213, "end": **********.235466, "relative_end": 3.0994415283203125e-06, "duration": 0.10565590858459473, "duration_str": "106ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45137504, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.019530000000000002, "accumulated_duration_str": "19.53ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.178757, "duration": 0.01729, "duration_str": "17.29ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 88.53}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.212167, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 88.53, "width_percent": 4.608}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.2224822, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 93.139, "width_percent": 6.861}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1372947696 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1372947696\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2099429090 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2099429090\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1355263476 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1355263476\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=xh9goj%7C1749396546993%7C7%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImF5citVUnlMQno4WGw2VWZFSlM4ZFE9PSIsInZhbHVlIjoiZzZQTzZXdGdrcDFKVmhJcjNQSVFnZ1dQUFVZYmF1ZkxnV0E1RWx4VWZxWkFleDJjTy9YU2ludjhLMGRhcEQ3TDFMdlRlekJJekhDOThOVDFwMHhnRFJ4M2dsTnlkaDNQcWFmSUNqdVRqY0tFM0g5a1pNMnZ2Zld6M2l4eG04akZQSDVzNDMxWjRseDl4Z3FzMTV6bDZUam5Mak04aFpQcVpDUTZDREt5YklZSHUvbE1INjN2d1lHbHl2L2ZDdDVIbDVqMEJzVUVwUlJqVkpiV3RmMHBKcmJpNDQzeXVlN3VkMExtS2h3bVltK1lrQWZtN001KzBwbTVKa3FkVndnN2tBUFJ5L0I3bmtDRHc4MUxYK3ZkZlVpMk4yK2J3TEZnWG52R1Fqa3lyc2NXVUdMOWxkUXFYNDRlb1VpMWkraStsQndVNmF4T1BWQ3FhVnZ1N1NTYU1XKzZPdC9JcUNPNEJ3d0lBWTZaUVYxckwySVJXKy84VElKYWlpN3UyUmVLYjFLSjh3aFoyUi80cWZ5bncxclBMa3RONWpKTEpJakJpTzFDbXdzZ0ZmUWpTc0tmRmFZdHlVK0kyVzlDUGwzbjhMOUppUjROOGdLYU02ZHk2RmdKQlJianpzKzFpMTBlR09hRWxtbjhwcjltSTJNdE9GUjhteFB4VkpVakhKc2QiLCJtYWMiOiI2NzBiMTMyMjY3NTViMTVmZGYwNjU2NjZhZmY2ODEyZjY4OGY5YmFjMzhjYjJhYTg1ZDM1ZjVmNDcyODQ0MTQ1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjBvWU9pMG9iT0VwcTNIRGdYYWUwYmc9PSIsInZhbHVlIjoiTytSaG1yeVY2UTZuZWxRVmpLNkF5OFFuMm9LNXJJbXNvY1Q0a0gwaHM1NFpsU1R2aiswbTNRQXpJNXBuRjlRamVDSFU5VSs3b1daUzZNYytFNTVHaTBXUDhKcWRWejNHMXBaNFAyRGozblY5OXQ0blQyUnFtWUtGK2JZa2plVzZtajk5RkJrYno5djh3cDhreGR6SnJLaUJMUGVWSXUyazhZTVpHK29PY1hxQ2hGNW5tUlBUU1RRdFlyWmZtNjNGbGtVMVNGQ0kwd3FGM3FYUGJraWl6NEs5ZzY1aDh0bkFXSjlSUEhFTGhFc0xQVUtJZm5CbUJ2L1lnRHhXeEIxR2Z3TkhnS3Rzc3ZySExQZzJvMDF0S0NNaDZZVktPNW1jS2pmTHZRMjNOYm1HdFJNVzhtN2VNTm1QOWJNQmRNamZIV1YwVzVrUkl2OUlzbjJQQUJtWmE1dXdLOG9xajFYZC8xUmxGWWVndGtza0dKcGVLWlMxMmhvUitXUUIzVktDRkhVU1lDOFhBMGN5STFwZ3l4eHp1R2EranRPVEU1dnZIY1Q4ajN4cGNXaEpHMkdpUEJoK1dtam9NQ0d6clV1SkFTMmNxYkhPVVVXSkdWM25INmpZV0NLdmdQNVF3MHQzMC9jaU9HRDNDaFUrSzkrVHRuNnpTbGQvTXRiQzdIVGQiLCJtYWMiOiI4NTNjMjVlMzc4ODI3Y2Q5MTIzN2I2NGVkMTU3ODEwZDk1MzA2NWRiNDE2ZTc3MjYxZGI5ZjlkMDhiZjA0ZWJlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">l7xz5q4Zkpyo7Zd0rhHsE8M6pi3wac4XFWhsi0lQ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-300579145 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:29:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlVLK2tGVzQ2aCsyR3N0WjBiUzJhNWc9PSIsInZhbHVlIjoia3p0REROV05SMUY0ZXNXYTF5VVAvR200bkhkMVN6THk3OWxVbXNNUnZodW9hL0ZZbWhVRjJadkZHV3JMbU1WMkg2bVk2UVJyNWtIVEw3QWJPbHdqTUNmWEV6NW5wMmlwZE1yRXgyaUpDNVIwVkMzbTNOUHJKWDdCc0loWHgzMjlhMDc2dEpJWjMvSG5yRGJKMU1KVmdJVlJFS0F2bHppakNXZHJYYm93eGQra1JxcTBxM3BGN0pFT1BHcklwN1RibUZGbnNFOEdXZFBsU0pTSXJ3SWF4OEQ1U0U0MHRKNm82d3k4d0RCNHc1RFJ6SGVQOTEzUW1YNS9EeXNUR3JCc2hBLzJ1WnNxcVBWc0xaMVlxVEtTM1dlVURYeTlsOXF2RU5KMjA5YTFjTkhUQUhOZXVjRUM5MDRYemFBdjY3QkpydWpLeTJoMFhJR3RiTU1GbXM0ZForMTYzYndRNnJRT3ZDN21EZHJjNFdUbm1QQ3VvUHFyclJOSXhSakdrUGUzQWhEWVdqekJCN1FnY29FWEtRTjB1bEk1V0JQaUtZTE5neUVkNHlNL3dYSDRMTTNKV3QwTStURGhtUHQ2RUJRZksrMHVTRUJuck4zSkFCbzVLTGk3SHBuRWxmeGNwTVM5cEpwcmxPK1dvV2gyYk54QjRJNnI1aWF3ZjFmNzFLdTgiLCJtYWMiOiJlOWE2ZTE4NjU5ZGNmMmYwY2ExMDg2ZDM4ZTQwMzhlMjg1ZDQ0YjQ2Y2FkYzYxZTIyNDg2OGJkMDg1YzkxOThjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:29:17 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlNUVCtwTkdqeHROUEFFZy8rR0pvcWc9PSIsInZhbHVlIjoiT2FuNytuVHFkZEJ5Zk56dWVNRlc1ZHRrK0toM3l0SWo2SndJMWhzUzFVNW1PeHF2NGhSUWNTTy9LU0Z5NDI3K2J3ZDB5YkNnTnlrS3c2bFJQbEVsT0xEWmsxM3pqVUhtMUd6ZDYvZEU4cmpQdk4zbFM1eDJmdUtvcVhuNndMUUFNU2FYSnlnNE5Ycm5scjhMVzViaTVSbVJXVnpuTEhSNlNxVlc3ekZBVDBoRjUrdGVrcU1kL2xrTjBKQ2tZWnhxQmJGcm1yMm11ZDY1Z0svNTNkNWgvTVNrYjJQemtwdEJXQ3pmdlFwL203Y2hlQi9saHordzFZallzWkRUR3pycjVzdCtiR2kzZXVlUFJSNWZjd1pYOVpiV2VjSSszd056ejBFR2ZsTEFtVkVFZVdyN0Zqd2NyS2lvbWNKR09ROFRSUUtOZURPWkVwb1Z1UDVUNDluZmM5ekMvOGNUenk1YjNSZUhINVQ5UDd2UVNNUWJ1YTNtTlNGTXBPU3AxbmdkVk1SZS9HOFZYU2VQSkJiSkY1TEdNWmZxN214dXRoa0E1UkRjTnd1dVVpZEl4bU9kVWEwMDFKYU9HQ0hrMzNsUjBzMXFRTVlnR3doVUZRcnRnenRHU200amV4SFB1QnAxSCtONmNKNUtuYWVIa2pGVE9MRXBlTGM5NGEwQlR4aU0iLCJtYWMiOiJhNGM4YTBkN2ZlOGE2NjAxY2JmZGVlOTQ5MjAzMTY0MDQ4NTdmOTFkMDI5MWFmNDg2OWQ2OTdjMzhhZWU4YjcwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:29:17 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlVLK2tGVzQ2aCsyR3N0WjBiUzJhNWc9PSIsInZhbHVlIjoia3p0REROV05SMUY0ZXNXYTF5VVAvR200bkhkMVN6THk3OWxVbXNNUnZodW9hL0ZZbWhVRjJadkZHV3JMbU1WMkg2bVk2UVJyNWtIVEw3QWJPbHdqTUNmWEV6NW5wMmlwZE1yRXgyaUpDNVIwVkMzbTNOUHJKWDdCc0loWHgzMjlhMDc2dEpJWjMvSG5yRGJKMU1KVmdJVlJFS0F2bHppakNXZHJYYm93eGQra1JxcTBxM3BGN0pFT1BHcklwN1RibUZGbnNFOEdXZFBsU0pTSXJ3SWF4OEQ1U0U0MHRKNm82d3k4d0RCNHc1RFJ6SGVQOTEzUW1YNS9EeXNUR3JCc2hBLzJ1WnNxcVBWc0xaMVlxVEtTM1dlVURYeTlsOXF2RU5KMjA5YTFjTkhUQUhOZXVjRUM5MDRYemFBdjY3QkpydWpLeTJoMFhJR3RiTU1GbXM0ZForMTYzYndRNnJRT3ZDN21EZHJjNFdUbm1QQ3VvUHFyclJOSXhSakdrUGUzQWhEWVdqekJCN1FnY29FWEtRTjB1bEk1V0JQaUtZTE5neUVkNHlNL3dYSDRMTTNKV3QwTStURGhtUHQ2RUJRZksrMHVTRUJuck4zSkFCbzVLTGk3SHBuRWxmeGNwTVM5cEpwcmxPK1dvV2gyYk54QjRJNnI1aWF3ZjFmNzFLdTgiLCJtYWMiOiJlOWE2ZTE4NjU5ZGNmMmYwY2ExMDg2ZDM4ZTQwMzhlMjg1ZDQ0YjQ2Y2FkYzYxZTIyNDg2OGJkMDg1YzkxOThjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:29:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlNUVCtwTkdqeHROUEFFZy8rR0pvcWc9PSIsInZhbHVlIjoiT2FuNytuVHFkZEJ5Zk56dWVNRlc1ZHRrK0toM3l0SWo2SndJMWhzUzFVNW1PeHF2NGhSUWNTTy9LU0Z5NDI3K2J3ZDB5YkNnTnlrS3c2bFJQbEVsT0xEWmsxM3pqVUhtMUd6ZDYvZEU4cmpQdk4zbFM1eDJmdUtvcVhuNndMUUFNU2FYSnlnNE5Ycm5scjhMVzViaTVSbVJXVnpuTEhSNlNxVlc3ekZBVDBoRjUrdGVrcU1kL2xrTjBKQ2tZWnhxQmJGcm1yMm11ZDY1Z0svNTNkNWgvTVNrYjJQemtwdEJXQ3pmdlFwL203Y2hlQi9saHordzFZallzWkRUR3pycjVzdCtiR2kzZXVlUFJSNWZjd1pYOVpiV2VjSSszd056ejBFR2ZsTEFtVkVFZVdyN0Zqd2NyS2lvbWNKR09ROFRSUUtOZURPWkVwb1Z1UDVUNDluZmM5ekMvOGNUenk1YjNSZUhINVQ5UDd2UVNNUWJ1YTNtTlNGTXBPU3AxbmdkVk1SZS9HOFZYU2VQSkJiSkY1TEdNWmZxN214dXRoa0E1UkRjTnd1dVVpZEl4bU9kVWEwMDFKYU9HQ0hrMzNsUjBzMXFRTVlnR3doVUZRcnRnenRHU200amV4SFB1QnAxSCtONmNKNUtuYWVIa2pGVE9MRXBlTGM5NGEwQlR4aU0iLCJtYWMiOiJhNGM4YTBkN2ZlOGE2NjAxY2JmZGVlOTQ5MjAzMTY0MDQ4NTdmOTFkMDI5MWFmNDg2OWQ2OTdjMzhhZWU4YjcwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:29:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-300579145\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1307984340 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1307984340\", {\"maxDepth\":0})</script>\n"}}