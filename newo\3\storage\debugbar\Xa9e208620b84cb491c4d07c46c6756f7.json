{"__meta": {"id": "Xa9e208620b84cb491c4d07c46c6756f7", "datetime": "2025-06-08 15:30:47", "utime": **********.963926, "method": "GET", "uri": "/customer/check/warehouse?customer_id=7&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.360642, "end": **********.963955, "duration": 0.6033129692077637, "duration_str": "603ms", "measures": [{"label": "Booting", "start": **********.360642, "relative_start": 0, "end": **********.852059, "relative_end": **********.852059, "duration": 0.49141693115234375, "duration_str": "491ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.852073, "relative_start": 0.49143099784851074, "end": **********.963958, "relative_end": 3.0994415283203125e-06, "duration": 0.11188507080078125, "duration_str": "112ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45294408, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.013, "accumulated_duration_str": "13ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9190161, "duration": 0.01117, "duration_str": "11.17ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 85.923}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.945114, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 85.923, "width_percent": 7.231}, {"sql": "select * from `customers` where `customers`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.951366, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "ty", "start_percent": 93.154, "width_percent": 6.846}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-429505678 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-429505678\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1158212829 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1158212829\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-52753107 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-52753107\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-791883025 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749396510751%7C76%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImF5OFo0ZmdRMVorSlc3c0lKazNpTFE9PSIsInZhbHVlIjoiaHJLUTdFQUhkMzVUSEJGVFFDMFdSWndZTnBMc3E1UHBIZTgwS3o3T2kxQ3pvZTBZM1A4WlMvQTUzMDkwK1dFWTB2eVZnWGVKMkwraldNQi9zNkV2cm9MSzA4UDZrUXJDMG5xTmEzNWxkaEVUdi91LzR2QjIvMXE1NjNsTzZtNFB3REc0TTMrcG9NZTBPR2FRZ3Z3MDlIZ2YvMWd2anVjZnJLZDNOM3JXSDhONFQvb01OWUVLRU56Sjh2WHc4eng3cmZraEx4dG80ZnR4Slh5YTJuOXJqRTNEU2w0QVdjR0NydXlHTlNYVXVkQlkyM21vLzNZeGRna2FWSDloR2krOWZLODU4UDV5ZzlCWDMzcTI5N2s1aForV2wwbzdxRitYOG8xZ2pNRy9CN0dvZUtNM0Q4Qmo4TWtZRFpXK296cDM5dUpHc1lEdVQxcTlOZUhxTjZSaFEyYkJ5QmxSMVRsU3dMcksrdUNnN3JYMkxTbGlla2hHQUJQRXpuR3lGdkgwTWxURGhMY2NSV1VDUG9BZk1uTXd4NXBvbnc2a0FsV0tSaW1tbWdlRm8ybnNVenNiLzFQZ1B1VXpWN2o0T0FWb0dmdFpDMzJKZjVBMlZzYTlEWFlpK2dYbmFQVU9IVDFBMHJpZkNoZXFuT05DbkhuRjZjd0laTEE2WnUrSElqWW0iLCJtYWMiOiI2ZTYwNTYxNjBmMGRiMzA5ZmM2NDdmZTA3N2QzYzEyNTc4OWEyY2ZhNTRmZGFkNTcyOTFlNDRmMmQ3ODIyZmJkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IldwRTZ2TytGRlZPekU4N2N4WTZuS1E9PSIsInZhbHVlIjoiMGVhMDduTVJ4RHZYUFF5ZjNSWldmdXlVNEdhdHp3R3Z6T2hZVEJzaW0xOXQxRno0aEdxY3kwcnNPSEZ2TUFjSnZFamtCdzJiNjRjVXFvWDMycXNpaUZlYzA2clViM25RN1d0K0VicmFnQTdqdWd5TTVXZ1pEUGlMLytwY2ViTWdXQjIrZDl2TFFZQWpqZ1BIWG1ZQTFEL3pwL1hhRWZNcTVYNFZoWk00cFh2VDdoYldrbW1NcTI3SFZ0UmJBWEI1TEc1M2dRNzF0dmFwMGlvc0NYMEJ3d3NnTDZlOVU4RXAyZXRTU0NON0pjWkREYUNPVWdlblpJVktyQWU1aTIwcXdlV2tocDlRd3R5UFl4U3E1WDN0Q1B6RmVFSzhUZkNIMllvT0xpL2oyUTFOb1pEdU84WWUvb3dZSXAwbEZYdnE1cHFhdkFhZHFJa2NSU3RLY2RqZXZIVkpBblk1eU0zZTFwdmtmZ05MSnNYVkUwamFlbFZTZURkZTJMdnVJNE5jWjZ6Z1FGc0hjbnoySGRyMTZxdWxuNDdyTWRmd3JRK2FuQWk2b0hrWTdWMGovYXVEZmx2ZlBOS1RDRGc0V2FmbkRNb3V6SVNzVG5PT2V2eS9LMVppb3ptT2Urd2laanh4N0tPdWk5UXgyeGpIVGpQcnRoWU5CYTBKcHJnM3p0NHUiLCJtYWMiOiI3MGFkNWYzY2E1NDczMDVkMjg2ZDQ5MTllMmIzOTFlZmFmNTc1OTYwMDAxZGZiMzM0YjAxZWQwNDBjMTRhYzk3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-791883025\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-984218594 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xNV7SvMX3AgbiuDVO62eNXfryfZD4KbWr3rEIkXt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-984218594\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-197614309 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:30:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjMyKzJ6SEdkNFJaQXF1Rk9RUlQwMlE9PSIsInZhbHVlIjoiSWJXWk9mUVVsZGw0cm9xUDIrT3d3T1I4SGNzSGZxQ2hLaG1kWWRxdDNpQ1RaOUtuZ0d4UlRvd3htYUFWMXJremNyRGxVRkpCL3Yvc0lqeDFJSFRqTE5Ca0lnRm1OZktlRVkwbzVadlgzTk5wNnJ0cktzdUhwVlhNWDM3UlQvQ1N0WXZheUFHUENmMFJGSzBhMDk0NlhQUGpwL040WFIyaWZoTDl3cnFnYkh3OCtGOXVFNHhvNEdsVXRkUEh0V3NnbXFydzFxeWNDUFdrT2RBbnltNlNpTW8za0hiakRCbUFPeVJPZUZWUlFzSzZsdGpDUnhmZzg3Ynd4dHpKYmtLQVZvSVlXRkFGa3JSM3F2RkNtcGdVcXc3U3JqTkg4RVpIeEhKcXA0dTFkRHZLNEIraHMza3lSL2JCWldXZm94bm9UeHAyclNYZy84QVd6NWZmUXBtTVV3N1BJdGY2cUkrREJ4R3pYanppTEFFVnJFUU50NGlhVWlvR1ZNaGhBOFBWWGZmRk4vR3pCam40enBqejVkQlJpT0xqNm1iSk93WU1GcXNyRmx2N2NnQm50NHlRQmY0NGU4VXZJU2RQSW8xSlVHYmw5R04wdDFKTnFoRkY4QlJLc0tzd3g1dlFqQlJVM2ZFVTdCb3k0Wi9FQ015M1hIN2ZkR093bXloQ1NVaEEiLCJtYWMiOiIwZmE0MzZjNTgyNDI1M2ViNjdhMzM4NzRkM2UwZTMyZWZmN2MyNTk3YTcxYTJkZWNkY2UzYjI1NzExOWRlMDg4IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:30:47 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImVXYzVQcGtXMzc0aEljMFQ2bUgwaXc9PSIsInZhbHVlIjoiNm9uZkxxTU8veU4yNWE0Slpqc1E4Y3BxeW4yaEduSXpYYmgzZkM4RkNhTTZINXQ2bTl4VzdPL0ZVM0JUYzBON3ZYYUhvZ3BoMlYyczc1ZExybENHUjYvMXZsdGt6b3M4eDZVQTYxRTZMMXhqaG9CaFZqR0t5V0ZMaCtJYWJKRnVpNXZML2dPd3dQSElMZWNpUmRMR1ZFc0F3ZSs5eElhcUVNSm42b3dFZWdZNzZBUmt5WkhVNDgxZ2pidVdDMk1TeG5oVHRvd05mTHpUaXBFUXBGVlUrKzNOVVFzYTlLUmhYS2QzRWpVbGxEU2ExVFF5eTFJc1Z1WTkweFlWTzhCeEczRXVXQVlZejdHODBTZ0ZSR25oQVZHNkhHUkRUcU5SVC9uZ2E3b0JncVlyTUFFOEVkUFpuNVNjR09IYWI2WktNUDRXY3VjdlNuOTNrUWx0TE01am00Y0NVb0RrRUtJU25vME45dlRtRHZOZ2k0U29FUU9mSHVPMzdnOEZtcUs2bXkwQWJYb25RN0YyZEk5eFRESHA1RXVIaWtTblhtZjcveHdjTTJkQU1CZWluakdDQS92cGxEREV0SHhYbTV2c2ZMY2RDM0YyYW9TbUZlenpzTm1uZnRtWFM1aDJ1VTZpRFIvcjRKcXk1aWpVMkhVVGpyMzNmK2lhYTh1Znh5K0wiLCJtYWMiOiJlYTc1ZjJlZmQ3ZjE3Y2EwM2U3YzMxOGE2ZmMwNGM5NWQ5Nzg2NjJmYWYxYzEyNzhkYmUwMmFiYmE0YTdjMzBhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:30:47 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjMyKzJ6SEdkNFJaQXF1Rk9RUlQwMlE9PSIsInZhbHVlIjoiSWJXWk9mUVVsZGw0cm9xUDIrT3d3T1I4SGNzSGZxQ2hLaG1kWWRxdDNpQ1RaOUtuZ0d4UlRvd3htYUFWMXJremNyRGxVRkpCL3Yvc0lqeDFJSFRqTE5Ca0lnRm1OZktlRVkwbzVadlgzTk5wNnJ0cktzdUhwVlhNWDM3UlQvQ1N0WXZheUFHUENmMFJGSzBhMDk0NlhQUGpwL040WFIyaWZoTDl3cnFnYkh3OCtGOXVFNHhvNEdsVXRkUEh0V3NnbXFydzFxeWNDUFdrT2RBbnltNlNpTW8za0hiakRCbUFPeVJPZUZWUlFzSzZsdGpDUnhmZzg3Ynd4dHpKYmtLQVZvSVlXRkFGa3JSM3F2RkNtcGdVcXc3U3JqTkg4RVpIeEhKcXA0dTFkRHZLNEIraHMza3lSL2JCWldXZm94bm9UeHAyclNYZy84QVd6NWZmUXBtTVV3N1BJdGY2cUkrREJ4R3pYanppTEFFVnJFUU50NGlhVWlvR1ZNaGhBOFBWWGZmRk4vR3pCam40enBqejVkQlJpT0xqNm1iSk93WU1GcXNyRmx2N2NnQm50NHlRQmY0NGU4VXZJU2RQSW8xSlVHYmw5R04wdDFKTnFoRkY4QlJLc0tzd3g1dlFqQlJVM2ZFVTdCb3k0Wi9FQ015M1hIN2ZkR093bXloQ1NVaEEiLCJtYWMiOiIwZmE0MzZjNTgyNDI1M2ViNjdhMzM4NzRkM2UwZTMyZWZmN2MyNTk3YTcxYTJkZWNkY2UzYjI1NzExOWRlMDg4IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:30:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImVXYzVQcGtXMzc0aEljMFQ2bUgwaXc9PSIsInZhbHVlIjoiNm9uZkxxTU8veU4yNWE0Slpqc1E4Y3BxeW4yaEduSXpYYmgzZkM4RkNhTTZINXQ2bTl4VzdPL0ZVM0JUYzBON3ZYYUhvZ3BoMlYyczc1ZExybENHUjYvMXZsdGt6b3M4eDZVQTYxRTZMMXhqaG9CaFZqR0t5V0ZMaCtJYWJKRnVpNXZML2dPd3dQSElMZWNpUmRMR1ZFc0F3ZSs5eElhcUVNSm42b3dFZWdZNzZBUmt5WkhVNDgxZ2pidVdDMk1TeG5oVHRvd05mTHpUaXBFUXBGVlUrKzNOVVFzYTlLUmhYS2QzRWpVbGxEU2ExVFF5eTFJc1Z1WTkweFlWTzhCeEczRXVXQVlZejdHODBTZ0ZSR25oQVZHNkhHUkRUcU5SVC9uZ2E3b0JncVlyTUFFOEVkUFpuNVNjR09IYWI2WktNUDRXY3VjdlNuOTNrUWx0TE01am00Y0NVb0RrRUtJU25vME45dlRtRHZOZ2k0U29FUU9mSHVPMzdnOEZtcUs2bXkwQWJYb25RN0YyZEk5eFRESHA1RXVIaWtTblhtZjcveHdjTTJkQU1CZWluakdDQS92cGxEREV0SHhYbTV2c2ZMY2RDM0YyYW9TbUZlenpzTm1uZnRtWFM1aDJ1VTZpRFIvcjRKcXk1aWpVMkhVVGpyMzNmK2lhYTh1Znh5K0wiLCJtYWMiOiJlYTc1ZjJlZmQ3ZjE3Y2EwM2U3YzMxOGE2ZmMwNGM5NWQ5Nzg2NjJmYWYxYzEyNzhkYmUwMmFiYmE0YTdjMzBhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:30:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-197614309\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1133932638 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1133932638\", {\"maxDepth\":0})</script>\n"}}