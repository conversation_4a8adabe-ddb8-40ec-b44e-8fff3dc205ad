# [YooKassa API SDK](../home.md)

# Interface: DealsRequestInterface
### Namespace: [\YooKassa\Request\Deals](../namespaces/yookassa-request-deals.md)
---
**Summary:**

Interface DealsRequestInterface.

---
### Constants
* No constants found

---
### Methods
| Visibility | Name | Flag | Summary |
| ----------:| ---- | ---- | ------- |
| public | [getCreatedAtGt()](../classes/YooKassa-Request-Deals-DealsRequestInterface.md#method_getCreatedAtGt) |  | Возвращает дату создания от которой будут возвращены сделки или null, если дата не была установлена. |
| public | [getCreatedAtGte()](../classes/YooKassa-Request-Deals-DealsRequestInterface.md#method_getCreatedAtGte) |  | Возвращает дату создания от которой будут возвращены сделки или null, если дата не была установлена. |
| public | [getCreatedAtLt()](../classes/YooKassa-Request-Deals-DealsRequestInterface.md#method_getCreatedAtLt) |  | Возвращает дату создания до которой будут возвращены сделки или null, если дата не была установлена. |
| public | [getCreatedAtLte()](../classes/YooKassa-Request-Deals-DealsRequestInterface.md#method_getCreatedAtLte) |  | Возвращает дату создания до которой будут возвращены сделки или null, если дата не была установлена. |
| public | [getCursor()](../classes/YooKassa-Request-Deals-DealsRequestInterface.md#method_getCursor) |  | Возвращает страницу выдачи результатов или null, если она до этого не была установлена. |
| public | [getExpiresAtGt()](../classes/YooKassa-Request-Deals-DealsRequestInterface.md#method_getExpiresAtGt) |  | Возвращает дату автоматического закрытия от которой будут возвращены сделки или null, если дата не была установлена. |
| public | [getExpiresAtGte()](../classes/YooKassa-Request-Deals-DealsRequestInterface.md#method_getExpiresAtGte) |  | Возвращает дату автоматического закрытия от которой будут возвращены сделки или null, если дата не была установлена. |
| public | [getExpiresAtLt()](../classes/YooKassa-Request-Deals-DealsRequestInterface.md#method_getExpiresAtLt) |  | Возвращает дату автоматического закрытия до которой будут возвращены сделки или null, если дата не была установлена. |
| public | [getExpiresAtLte()](../classes/YooKassa-Request-Deals-DealsRequestInterface.md#method_getExpiresAtLte) |  | Возвращает дату автоматического закрытия до которой будут возвращены сделки или null, если дата не была установлена. |
| public | [getFullTextSearch()](../classes/YooKassa-Request-Deals-DealsRequestInterface.md#method_getFullTextSearch) |  | Возвращает фильтр по описанию сделки или null, если он до этого не был установлен. |
| public | [getLimit()](../classes/YooKassa-Request-Deals-DealsRequestInterface.md#method_getLimit) |  | Возвращает ограничение количества объектов сделок или null, если оно до этого не было установлено. |
| public | [getStatus()](../classes/YooKassa-Request-Deals-DealsRequestInterface.md#method_getStatus) |  | Возвращает статус выбираемых сделок или null, если он до этого не был установлен. |
| public | [hasCreatedAtGt()](../classes/YooKassa-Request-Deals-DealsRequestInterface.md#method_hasCreatedAtGt) |  | Проверяет, была ли установлена дата создания от которой выбираются сделки. |
| public | [hasCreatedAtGte()](../classes/YooKassa-Request-Deals-DealsRequestInterface.md#method_hasCreatedAtGte) |  | Проверяет, была ли установлена дата создания от которой выбираются сделки. |
| public | [hasCreatedAtLt()](../classes/YooKassa-Request-Deals-DealsRequestInterface.md#method_hasCreatedAtLt) |  | Проверяет, была ли установлена дата создания до которой выбираются сделки. |
| public | [hasCreatedAtLte()](../classes/YooKassa-Request-Deals-DealsRequestInterface.md#method_hasCreatedAtLte) |  | Проверяет, была ли установлена дата создания до которой выбираются сделки. |
| public | [hasCursor()](../classes/YooKassa-Request-Deals-DealsRequestInterface.md#method_hasCursor) |  | Проверяет, была ли установлена страница выдачи результатов. |
| public | [hasExpiresAtGt()](../classes/YooKassa-Request-Deals-DealsRequestInterface.md#method_hasExpiresAtGt) |  | Проверяет, была ли установлена дата автоматического закрытия от которой выбираются сделки. |
| public | [hasExpiresAtGte()](../classes/YooKassa-Request-Deals-DealsRequestInterface.md#method_hasExpiresAtGte) |  | Проверяет, была ли установлена дата автоматического закрытия от которой выбираются сделки. |
| public | [hasExpiresAtLt()](../classes/YooKassa-Request-Deals-DealsRequestInterface.md#method_hasExpiresAtLt) |  | Проверяет, была ли установлена автоматического закрытия до которой выбираются сделки. |
| public | [hasExpiresAtLte()](../classes/YooKassa-Request-Deals-DealsRequestInterface.md#method_hasExpiresAtLte) |  | Проверяет, была ли установлена дата автоматического закрытия до которой выбираются сделки. |
| public | [hasFullTextSearch()](../classes/YooKassa-Request-Deals-DealsRequestInterface.md#method_hasFullTextSearch) |  | Проверяет, был ли установлен фильтр по описанию сделки. |
| public | [hasLimit()](../classes/YooKassa-Request-Deals-DealsRequestInterface.md#method_hasLimit) |  | Проверяет, было ли установлено ограничение количества объектов сделок. |
| public | [hasStatus()](../classes/YooKassa-Request-Deals-DealsRequestInterface.md#method_hasStatus) |  | Проверяет, был ли установлен статус выбираемых сделок. |
| public | [setCreatedAtGt()](../classes/YooKassa-Request-Deals-DealsRequestInterface.md#method_setCreatedAtGt) |  | Устанавливает дату создания от которой выбираются сделки. |
| public | [setCreatedAtGte()](../classes/YooKassa-Request-Deals-DealsRequestInterface.md#method_setCreatedAtGte) |  | Устанавливает дату создания от которой выбираются сделки. |
| public | [setCreatedAtLt()](../classes/YooKassa-Request-Deals-DealsRequestInterface.md#method_setCreatedAtLt) |  | Устанавливает дату создания до которой выбираются сделки. |
| public | [setCreatedAtLte()](../classes/YooKassa-Request-Deals-DealsRequestInterface.md#method_setCreatedAtLte) |  | Устанавливает дату создания до которой выбираются сделки. |
| public | [setCursor()](../classes/YooKassa-Request-Deals-DealsRequestInterface.md#method_setCursor) |  | Устанавливает страницу выдачи результатов. |
| public | [setExpiresAtGt()](../classes/YooKassa-Request-Deals-DealsRequestInterface.md#method_setExpiresAtGt) |  | Устанавливает дату автоматического закрытия от которой выбираются сделки. |
| public | [setExpiresAtGte()](../classes/YooKassa-Request-Deals-DealsRequestInterface.md#method_setExpiresAtGte) |  | Устанавливает дату автоматического закрытия от которой выбираются сделки. |
| public | [setExpiresAtLt()](../classes/YooKassa-Request-Deals-DealsRequestInterface.md#method_setExpiresAtLt) |  | Устанавливает дату автоматического закрытия до которой выбираются сделки. |
| public | [setExpiresAtLte()](../classes/YooKassa-Request-Deals-DealsRequestInterface.md#method_setExpiresAtLte) |  | Устанавливает дату автоматического закрытия до которой выбираются сделки. |
| public | [setFullTextSearch()](../classes/YooKassa-Request-Deals-DealsRequestInterface.md#method_setFullTextSearch) |  | Устанавливает фильтр по описанию сделки. |
| public | [setLimit()](../classes/YooKassa-Request-Deals-DealsRequestInterface.md#method_setLimit) |  | Устанавливает ограничение количества объектов сделок. |
| public | [setStatus()](../classes/YooKassa-Request-Deals-DealsRequestInterface.md#method_setStatus) |  | Устанавливает статус выбираемых сделок. |

---
### Details
* File: [lib/Request/Deals/DealsRequestInterface.php](../../lib/Request/Deals/DealsRequestInterface.php)
* Package: \YooKassa\Request
* See Also:
  * [](https://yookassa.ru/developers/api)

---
### Tags
| Tag | Version | Description |
| --- | ------- | ----------- |
| category |  | Interface |
| author |  | <EMAIL> |
| property |  | Страница выдачи результатов, которую необходимо отобразить |
| property |  | Ограничение количества объектов платежа, отображаемых на одной странице выдачи |
| property |  | Время создания, от (включительно) |
| property |  | Время создания, от (не включая) |
| property |  | Время создания, до (включительно) |
| property |  | Время создания, до (не включая) |
| property |  | Время автоматического закрытия, от (включительно) |
| property |  | Время автоматического закрытия, от (не включая) |
| property |  | Время автоматического закрытия, до (включительно) |
| property |  | Время автоматического закрытия, до (не включая) |
| property |  | Фильтр по описанию сделки — параметру description |
| property |  | Статус платежа |

---
## Methods
<a name="method_getCursor" class="anchor"></a>
#### public getCursor() : null|string

```php
public getCursor() : null|string
```

**Summary**

Возвращает страницу выдачи результатов или null, если она до этого не была установлена.

**Details:**
* Inherited From: [\YooKassa\Request\Deals\DealsRequestInterface](../classes/YooKassa-Request-Deals-DealsRequestInterface.md)

**Returns:** null|string - Страница выдачи результатов


<a name="method_hasCursor" class="anchor"></a>
#### public hasCursor() : bool

```php
public hasCursor() : bool
```

**Summary**

Проверяет, была ли установлена страница выдачи результатов.

**Details:**
* Inherited From: [\YooKassa\Request\Deals\DealsRequestInterface](../classes/YooKassa-Request-Deals-DealsRequestInterface.md)

**Returns:** bool - True если страница выдачи результатов была установлена, false если нет


<a name="method_setCursor" class="anchor"></a>
#### public setCursor() : self

```php
public setCursor(string $cursor) : self
```

**Summary**

Устанавливает страницу выдачи результатов.

**Details:**
* Inherited From: [\YooKassa\Request\Deals\DealsRequestInterface](../classes/YooKassa-Request-Deals-DealsRequestInterface.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string</code> | cursor  | Страница |

**Returns:** self - 


<a name="method_getCreatedAtGte" class="anchor"></a>
#### public getCreatedAtGte() : null|\DateTime

```php
public getCreatedAtGte() : null|\DateTime
```

**Summary**

Возвращает дату создания от которой будут возвращены сделки или null, если дата не была установлена.

**Details:**
* Inherited From: [\YooKassa\Request\Deals\DealsRequestInterface](../classes/YooKassa-Request-Deals-DealsRequestInterface.md)

**Returns:** null|\DateTime - Время создания, от (включительно)


<a name="method_hasCreatedAtGte" class="anchor"></a>
#### public hasCreatedAtGte() : bool

```php
public hasCreatedAtGte() : bool
```

**Summary**

Проверяет, была ли установлена дата создания от которой выбираются сделки.

**Details:**
* Inherited From: [\YooKassa\Request\Deals\DealsRequestInterface](../classes/YooKassa-Request-Deals-DealsRequestInterface.md)

**Returns:** bool - True если дата была установлена, false если нет


<a name="method_setCreatedAtGte" class="anchor"></a>
#### public setCreatedAtGte() : self

```php
public setCreatedAtGte(\DateTime|string|null $created_at_gte) : self
```

**Summary**

Устанавливает дату создания от которой выбираются сделки.

**Details:**
* Inherited From: [\YooKassa\Request\Deals\DealsRequestInterface](../classes/YooKassa-Request-Deals-DealsRequestInterface.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">\DateTime OR string OR null</code> | created_at_gte  | Дата |

**Returns:** self - 


<a name="method_getCreatedAtGt" class="anchor"></a>
#### public getCreatedAtGt() : null|\DateTime

```php
public getCreatedAtGt() : null|\DateTime
```

**Summary**

Возвращает дату создания от которой будут возвращены сделки или null, если дата не была установлена.

**Details:**
* Inherited From: [\YooKassa\Request\Deals\DealsRequestInterface](../classes/YooKassa-Request-Deals-DealsRequestInterface.md)

**Returns:** null|\DateTime - Время создания, от (не включая)


<a name="method_hasCreatedAtGt" class="anchor"></a>
#### public hasCreatedAtGt() : bool

```php
public hasCreatedAtGt() : bool
```

**Summary**

Проверяет, была ли установлена дата создания от которой выбираются сделки.

**Details:**
* Inherited From: [\YooKassa\Request\Deals\DealsRequestInterface](../classes/YooKassa-Request-Deals-DealsRequestInterface.md)

**Returns:** bool - True если дата была установлена, false если нет


<a name="method_setCreatedAtGt" class="anchor"></a>
#### public setCreatedAtGt() : self

```php
public setCreatedAtGt(\DateTime|string|null $created_at_gt) : self
```

**Summary**

Устанавливает дату создания от которой выбираются сделки.

**Details:**
* Inherited From: [\YooKassa\Request\Deals\DealsRequestInterface](../classes/YooKassa-Request-Deals-DealsRequestInterface.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">\DateTime OR string OR null</code> | created_at_gt  | Дата |

**Returns:** self - 


<a name="method_getCreatedAtLte" class="anchor"></a>
#### public getCreatedAtLte() : null|\DateTime

```php
public getCreatedAtLte() : null|\DateTime
```

**Summary**

Возвращает дату создания до которой будут возвращены сделки или null, если дата не была установлена.

**Details:**
* Inherited From: [\YooKassa\Request\Deals\DealsRequestInterface](../classes/YooKassa-Request-Deals-DealsRequestInterface.md)

**Returns:** null|\DateTime - Время создания, до (включительно)


<a name="method_hasCreatedAtLte" class="anchor"></a>
#### public hasCreatedAtLte() : bool

```php
public hasCreatedAtLte() : bool
```

**Summary**

Проверяет, была ли установлена дата создания до которой выбираются сделки.

**Details:**
* Inherited From: [\YooKassa\Request\Deals\DealsRequestInterface](../classes/YooKassa-Request-Deals-DealsRequestInterface.md)

**Returns:** bool - True если дата была установлена, false если нет


<a name="method_setCreatedAtLte" class="anchor"></a>
#### public setCreatedAtLte() : self

```php
public setCreatedAtLte(\DateTime|string|null $created_at_lte) : self
```

**Summary**

Устанавливает дату создания до которой выбираются сделки.

**Details:**
* Inherited From: [\YooKassa\Request\Deals\DealsRequestInterface](../classes/YooKassa-Request-Deals-DealsRequestInterface.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">\DateTime OR string OR null</code> | created_at_lte  | Дата |

**Returns:** self - 


<a name="method_getCreatedAtLt" class="anchor"></a>
#### public getCreatedAtLt() : null|\DateTime

```php
public getCreatedAtLt() : null|\DateTime
```

**Summary**

Возвращает дату создания до которой будут возвращены сделки или null, если дата не была установлена.

**Details:**
* Inherited From: [\YooKassa\Request\Deals\DealsRequestInterface](../classes/YooKassa-Request-Deals-DealsRequestInterface.md)

**Returns:** null|\DateTime - Время создания, до (не включая)


<a name="method_hasCreatedAtLt" class="anchor"></a>
#### public hasCreatedAtLt() : bool

```php
public hasCreatedAtLt() : bool
```

**Summary**

Проверяет, была ли установлена дата создания до которой выбираются сделки.

**Details:**
* Inherited From: [\YooKassa\Request\Deals\DealsRequestInterface](../classes/YooKassa-Request-Deals-DealsRequestInterface.md)

**Returns:** bool - True если дата была установлена, false если нет


<a name="method_setCreatedAtLt" class="anchor"></a>
#### public setCreatedAtLt() : self

```php
public setCreatedAtLt(\DateTime|string|null $created_at_lt) : self
```

**Summary**

Устанавливает дату создания до которой выбираются сделки.

**Details:**
* Inherited From: [\YooKassa\Request\Deals\DealsRequestInterface](../classes/YooKassa-Request-Deals-DealsRequestInterface.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">\DateTime OR string OR null</code> | created_at_lt  | Дата |

**Returns:** self - 


<a name="method_getExpiresAtGte" class="anchor"></a>
#### public getExpiresAtGte() : null|\DateTime

```php
public getExpiresAtGte() : null|\DateTime
```

**Summary**

Возвращает дату автоматического закрытия от которой будут возвращены сделки или null, если дата не была установлена.

**Details:**
* Inherited From: [\YooKassa\Request\Deals\DealsRequestInterface](../classes/YooKassa-Request-Deals-DealsRequestInterface.md)

**Returns:** null|\DateTime - Время автоматического закрытия, от (включительно)


<a name="method_hasExpiresAtGte" class="anchor"></a>
#### public hasExpiresAtGte() : bool

```php
public hasExpiresAtGte() : bool
```

**Summary**

Проверяет, была ли установлена дата автоматического закрытия от которой выбираются сделки.

**Details:**
* Inherited From: [\YooKassa\Request\Deals\DealsRequestInterface](../classes/YooKassa-Request-Deals-DealsRequestInterface.md)

**Returns:** bool - True если дата была установлена, false если нет


<a name="method_setExpiresAtGte" class="anchor"></a>
#### public setExpiresAtGte() : self

```php
public setExpiresAtGte(\DateTime|string|null $expires_at_gte) : self
```

**Summary**

Устанавливает дату автоматического закрытия от которой выбираются сделки.

**Details:**
* Inherited From: [\YooKassa\Request\Deals\DealsRequestInterface](../classes/YooKassa-Request-Deals-DealsRequestInterface.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">\DateTime OR string OR null</code> | expires_at_gte  | Дата |

**Returns:** self - 


<a name="method_getExpiresAtGt" class="anchor"></a>
#### public getExpiresAtGt() : null|\DateTime

```php
public getExpiresAtGt() : null|\DateTime
```

**Summary**

Возвращает дату автоматического закрытия от которой будут возвращены сделки или null, если дата не была установлена.

**Details:**
* Inherited From: [\YooKassa\Request\Deals\DealsRequestInterface](../classes/YooKassa-Request-Deals-DealsRequestInterface.md)

**Returns:** null|\DateTime - Время автоматического закрытия, от (не включая)


<a name="method_hasExpiresAtGt" class="anchor"></a>
#### public hasExpiresAtGt() : bool

```php
public hasExpiresAtGt() : bool
```

**Summary**

Проверяет, была ли установлена дата автоматического закрытия от которой выбираются сделки.

**Details:**
* Inherited From: [\YooKassa\Request\Deals\DealsRequestInterface](../classes/YooKassa-Request-Deals-DealsRequestInterface.md)

**Returns:** bool - True если дата была установлена, false если нет


<a name="method_setExpiresAtGt" class="anchor"></a>
#### public setExpiresAtGt() : self

```php
public setExpiresAtGt(\DateTime|string|null $expires_at_lt) : self
```

**Summary**

Устанавливает дату автоматического закрытия от которой выбираются сделки.

**Details:**
* Inherited From: [\YooKassa\Request\Deals\DealsRequestInterface](../classes/YooKassa-Request-Deals-DealsRequestInterface.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">\DateTime OR string OR null</code> | expires_at_lt  | Дата автоматического закрытия |

**Returns:** self - 


<a name="method_getExpiresAtLte" class="anchor"></a>
#### public getExpiresAtLte() : null|\DateTime

```php
public getExpiresAtLte() : null|\DateTime
```

**Summary**

Возвращает дату автоматического закрытия до которой будут возвращены сделки или null, если дата не была установлена.

**Details:**
* Inherited From: [\YooKassa\Request\Deals\DealsRequestInterface](../classes/YooKassa-Request-Deals-DealsRequestInterface.md)

**Returns:** null|\DateTime - Время автоматического закрытия, до (включительно)


<a name="method_hasExpiresAtLte" class="anchor"></a>
#### public hasExpiresAtLte() : bool

```php
public hasExpiresAtLte() : bool
```

**Summary**

Проверяет, была ли установлена дата автоматического закрытия до которой выбираются сделки.

**Details:**
* Inherited From: [\YooKassa\Request\Deals\DealsRequestInterface](../classes/YooKassa-Request-Deals-DealsRequestInterface.md)

**Returns:** bool - True если дата была установлена, false если нет


<a name="method_setExpiresAtLte" class="anchor"></a>
#### public setExpiresAtLte() : self

```php
public setExpiresAtLte(\DateTime|string|null $expires_at_lte) : self
```

**Summary**

Устанавливает дату автоматического закрытия до которой выбираются сделки.

**Details:**
* Inherited From: [\YooKassa\Request\Deals\DealsRequestInterface](../classes/YooKassa-Request-Deals-DealsRequestInterface.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">\DateTime OR string OR null</code> | expires_at_lte  | Дата автоматического закрытия |

**Returns:** self - 


<a name="method_getExpiresAtLt" class="anchor"></a>
#### public getExpiresAtLt() : null|\DateTime

```php
public getExpiresAtLt() : null|\DateTime
```

**Summary**

Возвращает дату автоматического закрытия до которой будут возвращены сделки или null, если дата не была установлена.

**Details:**
* Inherited From: [\YooKassa\Request\Deals\DealsRequestInterface](../classes/YooKassa-Request-Deals-DealsRequestInterface.md)

**Returns:** null|\DateTime - Время автоматического закрытия, до (не включая)


<a name="method_hasExpiresAtLt" class="anchor"></a>
#### public hasExpiresAtLt() : bool

```php
public hasExpiresAtLt() : bool
```

**Summary**

Проверяет, была ли установлена автоматического закрытия до которой выбираются сделки.

**Details:**
* Inherited From: [\YooKassa\Request\Deals\DealsRequestInterface](../classes/YooKassa-Request-Deals-DealsRequestInterface.md)

**Returns:** bool - True если дата была установлена, false если нет


<a name="method_setExpiresAtLt" class="anchor"></a>
#### public setExpiresAtLt() : self

```php
public setExpiresAtLt(\DateTime|string|null $expires_at_lt) : self
```

**Summary**

Устанавливает дату автоматического закрытия до которой выбираются сделки.

**Details:**
* Inherited From: [\YooKassa\Request\Deals\DealsRequestInterface](../classes/YooKassa-Request-Deals-DealsRequestInterface.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">\DateTime OR string OR null</code> | expires_at_lt  | Дата автоматического закрытия |

**Returns:** self - 


<a name="method_getLimit" class="anchor"></a>
#### public getLimit() : null|int

```php
public getLimit() : null|int
```

**Summary**

Возвращает ограничение количества объектов сделок или null, если оно до этого не было установлено.

**Details:**
* Inherited From: [\YooKassa\Request\Deals\DealsRequestInterface](../classes/YooKassa-Request-Deals-DealsRequestInterface.md)

**Returns:** null|int - Ограничение количества объектов сделок


<a name="method_hasLimit" class="anchor"></a>
#### public hasLimit() : bool

```php
public hasLimit() : bool
```

**Summary**

Проверяет, было ли установлено ограничение количества объектов сделок.

**Details:**
* Inherited From: [\YooKassa\Request\Deals\DealsRequestInterface](../classes/YooKassa-Request-Deals-DealsRequestInterface.md)

**Returns:** bool - True если ограничение количества объектов сделок было установлено, false если нет


<a name="method_setLimit" class="anchor"></a>
#### public setLimit() : self

```php
public setLimit(int|null $limit) : self
```

**Summary**

Устанавливает ограничение количества объектов сделок.

**Details:**
* Inherited From: [\YooKassa\Request\Deals\DealsRequestInterface](../classes/YooKassa-Request-Deals-DealsRequestInterface.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">int OR null</code> | limit  | Количества объектов сделок на странице |

**Returns:** self - 


<a name="method_getStatus" class="anchor"></a>
#### public getStatus() : null|string

```php
public getStatus() : null|string
```

**Summary**

Возвращает статус выбираемых сделок или null, если он до этого не был установлен.

**Details:**
* Inherited From: [\YooKassa\Request\Deals\DealsRequestInterface](../classes/YooKassa-Request-Deals-DealsRequestInterface.md)

**Returns:** null|string - Статус выбираемых сделок


<a name="method_hasStatus" class="anchor"></a>
#### public hasStatus() : bool

```php
public hasStatus() : bool
```

**Summary**

Проверяет, был ли установлен статус выбираемых сделок.

**Details:**
* Inherited From: [\YooKassa\Request\Deals\DealsRequestInterface](../classes/YooKassa-Request-Deals-DealsRequestInterface.md)

**Returns:** bool - True если статус был установлен, false если нет


<a name="method_setStatus" class="anchor"></a>
#### public setStatus() : self

```php
public setStatus(string|null $status) : self
```

**Summary**

Устанавливает статус выбираемых сделок.

**Details:**
* Inherited From: [\YooKassa\Request\Deals\DealsRequestInterface](../classes/YooKassa-Request-Deals-DealsRequestInterface.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string OR null</code> | status  | Статус сделок |

**Returns:** self - 


<a name="method_getFullTextSearch" class="anchor"></a>
#### public getFullTextSearch() : null|string

```php
public getFullTextSearch() : null|string
```

**Summary**

Возвращает фильтр по описанию сделки или null, если он до этого не был установлен.

**Details:**
* Inherited From: [\YooKassa\Request\Deals\DealsRequestInterface](../classes/YooKassa-Request-Deals-DealsRequestInterface.md)

**Returns:** null|string - Фильтр по описанию сделки


<a name="method_hasFullTextSearch" class="anchor"></a>
#### public hasFullTextSearch() : bool

```php
public hasFullTextSearch() : bool
```

**Summary**

Проверяет, был ли установлен фильтр по описанию сделки.

**Details:**
* Inherited From: [\YooKassa\Request\Deals\DealsRequestInterface](../classes/YooKassa-Request-Deals-DealsRequestInterface.md)

**Returns:** bool - True если фильтр по описанию сделки был установлен, false если нет


<a name="method_setFullTextSearch" class="anchor"></a>
#### public setFullTextSearch() : self

```php
public setFullTextSearch(string|null $full_text_search) : self
```

**Summary**

Устанавливает фильтр по описанию сделки.

**Details:**
* Inherited From: [\YooKassa\Request\Deals\DealsRequestInterface](../classes/YooKassa-Request-Deals-DealsRequestInterface.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string OR null</code> | full_text_search  | Фильтр по описанию сделки |

**Returns:** self - 




---

### Top Namespaces

* [\YooKassa](../namespaces/yookassa.md)

---

### Reports
* [Errors - 0](../reports/errors.md)
* [Markers - 0](../reports/markers.md)
* [Deprecated - 25](../reports/deprecated.md)

---

This document was automatically generated from source code comments on 2024-07-02 using [phpDocumentor](http://www.phpdoc.org/)

&copy; 2024 YooMoney