{"__meta": {"id": "X637bbaca0b6c247c05a5a370cd7052f1", "datetime": "2025-06-08 15:28:59", "utime": **********.846803, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.999134, "end": **********.846834, "duration": 0.****************, "duration_str": "848ms", "measures": [{"label": "Booting", "start": **********.999134, "relative_start": 0, "end": **********.733505, "relative_end": **********.733505, "duration": 0.****************, "duration_str": "734ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.733523, "relative_start": 0.****************, "end": **********.846837, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "113ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00593, "accumulated_duration_str": "5.93ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.791704, "duration": 0.0037099999999999998, "duration_str": "3.71ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 62.563}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.812937, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 62.563, "width_percent": 15.683}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.831807, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 78.246, "width_percent": 21.754}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=xh9goj%7C**********091%7C6%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InFyQUFQMnIvYUNMR0dNTlRCUFdiZ1E9PSIsInZhbHVlIjoiZC9TdTh1dlNFQXJEcFVMdnlTN01meFVlRWhIYjRYc21lWUhmUE4wREd3U0JHN2ZzY3FvUFh6YVkydC9iVXFyZEZ3QWZPNW1OVHZVYUo5TWtZTFZYeTNLM3hSMmhoWVdRWkRGOFRiaUxMRXdXNWhySC9vNHUrOVZxd2lSb2dhRHlLNnBGS09IaGJIckhpSWRPclNBNjJ0YmVucWEvOG9OMUpScVZ2RW1qNjVNOUFueng1NEhBdjRmSGN2Qjg4cVg3eUNuRU9ITnpiZXQ3MEI3Z05neElCTGpxZW1ZTHkzODNMbkN3WTQ2TDI5NlRhS1R5TVBsU0UySmFoVDBCRWNndkJtQzhXZWROeDFFRlkxQ3doankwZmhKaDJVTmkwTkk1NWFMdjNDWk80amsyZWlLQzBiaDdxWjltLzJ3VGltWWVialdsZ2RQcVRlNFFZYVh3WVZIQktHc284RmR0N1Z1RWx1cVNsZmtDbDl1RXdIMDlZeXFUWXdlMXBOZWw5WEh6bnBtWXRBbmpVQzZTR0lab3pXM3FVdS9qYzdGUXlaNGwvbTZvQ1JlaTNiMWY5MEJBQi96RE9CMUc4bzliSUpQOGZockdZNld4TmFjeHVzcVpsN1VhRXJzNUhJSWpkdHg3eC8wRUh0bTdWaDM0R2R2Smd5YjBxRXpBUk9UbG45d0wiLCJtYWMiOiJiMTA0M2UzYmI5ODc1MzY0NWIxM2E3ZTc3MjBjZmUwYzY0MGVmYTdkYzVlMzRhMmU2NTU0NTQzMGRlMzFlZGNiIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkpxYVdoY3NRN1ZWSWRzL0tsUEd6d0E9PSIsInZhbHVlIjoiWGpJV3BGRjVwWll2YWxuQjFJUForb25rUnNsMUtJbVU4ZXhZeWt3c2hhZFBFbkZJRVBhWFlPcWNURlE2NnJ4OS8wUHZRUHhqU21NZU1VU2lXdzJlUVFuZmliQm9ZMitIcHUxR001WG5Oa1h4bXlJbFlwNnlub3VYNUdSUHVnOWRoSE5KUWlIeUxZVTR1c3oxd0dwenZiU1VsOXg1OVZPN0JXbk02dFl4VVVjamFkVG5EcG5WKzFhNlZEMHA5Z0ZtSU5Cb2dBU1kzMHFOUFluK3dMei9QYmUwQ01Yb1dncVcwUjhlTm14dXVnU3RBNmdoU0lDUnVnU0pDMGRDNksyQUU0ZzlXU0tiMzJBUWZBVUFwTjBsSDFFcGhvNEFqMUFXbGMrWXRZQXZUbEVBSmpOYkxGQi96cmkrYTNjWE81REVBemVaeHBoMFAxbFJpeVJTSjBqK3B3Uy9MU0xNWGJmU2daa01yTTU2YlVzQllMWUN3N0ZjWHdsaEJYenBkVXJvQTZlNlEycUlqK2hQUzNxK29SZzBOLyszeUNTb1ZOV2crVGNNZmp3MTVwVWxXL0dBZUptdnloZHEwWCsrbVVndU8yQ3ZGWWdIdnNmUDdxOGMxa2tzYWU4UkM4SG1vVEViamxqaHJXWDFSclF0enN4TFNwSFFONk5XWm03ck5TMG4iLCJtYWMiOiJmMGJjODVkYjA5ZjQ5OGYyN2MxMjcyOTkwMzE1MDcxNTI2MTc1OTE1ZThlYTQzODk2NTlhMjc4NzFlYTc5N2I5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1543637414 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6s1OaNA8h4DHGuLO0qhsUHg7TnRJSWwbuakqNtUI</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1543637414\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-405534336 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:28:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjhuRG1wQVAxUjRNcUJkNTc0SkpFVHc9PSIsInZhbHVlIjoiS1BacFRUV21xRVY4YUlUUkRTck9ZWDlZZTB3OGtSbjgrNG9DOWV4REJqU1RqNTdmZnhlZ0oyYld6MmNQWExLblZkT0pjYTN3UmRHZ1JMeGFqYmp3RzVDbmFXTm9OQmdMT2pOcmdjL2Z2SW43RGFyUitmdHg1cnRIUlljTWZ6eUVLcWV4QnlaRFBRelJFWllnQjNSb0NPbWJnSElSanZZdDI0UlNjb1FqdnRUZDZEVnFNb01TQjVodUlHT3ZjQS9QNHRTeWtlK0pkcHFVWDR2akZ5QXBZVkhsZnJOUjVmREZlOUJOSUpFRzdId0tEaDA1REQwbXBvb3JneThQTDFPdEFZS2poNGFTTkFpWmNpM2pqelBGa1dqeXhyMkdGazNyaFdhdk82UG5pWU85dzNWa0hjNS9zYnVHMmFvQlppVGhLcDRTc0N0NVpyamphdEduczlrU2E3ZlkzbHRSSzJhNHM0Vm4yazZBcDRxU3poQ3BuQ1BpcVNEZjBNZW9YY1czMy9zRE9LNzZLZG0rdGZldkpXUG1FZmR2V2VxOSt2elgzbFRRaUtjTWtrcmtoYlhwWGs2dGk3ay9xdjVNanFEYTYxRUIvOUhRRGlBMjcrUkF4MFQ0eStQNENKUk9oRkkzUTZlSnlCK2Z2MnFDVVorR2U1VHM5d3lxeWZGZVc2MXQiLCJtYWMiOiI2ZDIxNTMxMjJlNjk3ZmJkYjc1ZjJmNWY0MmM2YzExMmJiYTM0MTViNmJmYTcxM2M1MTQyZWRlMWNmMWYxNDY0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:28:59 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IklmMlY0dWJhaVlpZE1oTXVJNEVWaFE9PSIsInZhbHVlIjoiWGJxNHQzVTdQbHJHWjlwZlBMWE9vaElyYUFSdjNHUkloWW5yZGxxT1RjZnhIaGc0SDF5TDlKbXFXN1Q3Q2RIRkQ1WlA2SXg5NkcvbDhQK2l3amF0ek9rWFJLa3Q5QmZraUM0OGVTb2pJNTZ6dTNLVTJ3dHAyQytacVBVWmsybW1HbmNtcG8rWCtjeXZHR0xWY0EybGpBTHYwQUpCN00weGdDaDdOUlVSYjZuWHFZZGFyTVpFQXY3WEZ4Q3dBOVVzNjVHOUFoSm56elN1QlZLY2l0UjlWaEZtVVE2RWZkSnJSVDFRcFJBL21zSnBFVXdHTjVxWkwwUDVrMzc3NVdvcFQ3ckFYNmo0UXhZR09XUGRTbG4yRU5qNnM4SmVmK1E5c2d3cllrakFiZlpvY3N0NFdwM2tVQWJESGdoNnhHa1dlYXNwUGRuMWVySFFKUjlkZ0tkK2Vrd0ZpTW15ZU1RbjNaREk4NzN5YzRwR1h4cEdJTDMrbmo1b1VZSXRqMVM5UEVJU241dDc2d0pZeXMycENCTVkzdjkzd1N3ZGc5eE9URnZyVGFtWnFVWnBiVjd6ZFZhdXV2WjV0bTViUHVnbmdzMnRCcnp3MWFNYUtmdkpzVjBhUllNWTYzUHZtWUJudGhnSzdsZDM2RXNSVm4ybHUwdzJIMy9Vc0ZxWjFKTFIiLCJtYWMiOiJkYTA2MWQ0MjQxMGJhM2NkM2FjOWMxOTQyNzU1M2NiOTBiNDZlNTEwMDYzMzcxMDQyZWFmNDU5YWI4Y2Y2NzU5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:28:59 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjhuRG1wQVAxUjRNcUJkNTc0SkpFVHc9PSIsInZhbHVlIjoiS1BacFRUV21xRVY4YUlUUkRTck9ZWDlZZTB3OGtSbjgrNG9DOWV4REJqU1RqNTdmZnhlZ0oyYld6MmNQWExLblZkT0pjYTN3UmRHZ1JMeGFqYmp3RzVDbmFXTm9OQmdMT2pOcmdjL2Z2SW43RGFyUitmdHg1cnRIUlljTWZ6eUVLcWV4QnlaRFBRelJFWllnQjNSb0NPbWJnSElSanZZdDI0UlNjb1FqdnRUZDZEVnFNb01TQjVodUlHT3ZjQS9QNHRTeWtlK0pkcHFVWDR2akZ5QXBZVkhsZnJOUjVmREZlOUJOSUpFRzdId0tEaDA1REQwbXBvb3JneThQTDFPdEFZS2poNGFTTkFpWmNpM2pqelBGa1dqeXhyMkdGazNyaFdhdk82UG5pWU85dzNWa0hjNS9zYnVHMmFvQlppVGhLcDRTc0N0NVpyamphdEduczlrU2E3ZlkzbHRSSzJhNHM0Vm4yazZBcDRxU3poQ3BuQ1BpcVNEZjBNZW9YY1czMy9zRE9LNzZLZG0rdGZldkpXUG1FZmR2V2VxOSt2elgzbFRRaUtjTWtrcmtoYlhwWGs2dGk3ay9xdjVNanFEYTYxRUIvOUhRRGlBMjcrUkF4MFQ0eStQNENKUk9oRkkzUTZlSnlCK2Z2MnFDVVorR2U1VHM5d3lxeWZGZVc2MXQiLCJtYWMiOiI2ZDIxNTMxMjJlNjk3ZmJkYjc1ZjJmNWY0MmM2YzExMmJiYTM0MTViNmJmYTcxM2M1MTQyZWRlMWNmMWYxNDY0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:28:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IklmMlY0dWJhaVlpZE1oTXVJNEVWaFE9PSIsInZhbHVlIjoiWGJxNHQzVTdQbHJHWjlwZlBMWE9vaElyYUFSdjNHUkloWW5yZGxxT1RjZnhIaGc0SDF5TDlKbXFXN1Q3Q2RIRkQ1WlA2SXg5NkcvbDhQK2l3amF0ek9rWFJLa3Q5QmZraUM0OGVTb2pJNTZ6dTNLVTJ3dHAyQytacVBVWmsybW1HbmNtcG8rWCtjeXZHR0xWY0EybGpBTHYwQUpCN00weGdDaDdOUlVSYjZuWHFZZGFyTVpFQXY3WEZ4Q3dBOVVzNjVHOUFoSm56elN1QlZLY2l0UjlWaEZtVVE2RWZkSnJSVDFRcFJBL21zSnBFVXdHTjVxWkwwUDVrMzc3NVdvcFQ3ckFYNmo0UXhZR09XUGRTbG4yRU5qNnM4SmVmK1E5c2d3cllrakFiZlpvY3N0NFdwM2tVQWJESGdoNnhHa1dlYXNwUGRuMWVySFFKUjlkZ0tkK2Vrd0ZpTW15ZU1RbjNaREk4NzN5YzRwR1h4cEdJTDMrbmo1b1VZSXRqMVM5UEVJU241dDc2d0pZeXMycENCTVkzdjkzd1N3ZGc5eE9URnZyVGFtWnFVWnBiVjd6ZFZhdXV2WjV0bTViUHVnbmdzMnRCcnp3MWFNYUtmdkpzVjBhUllNWTYzUHZtWUJudGhnSzdsZDM2RXNSVm4ybHUwdzJIMy9Vc0ZxWjFKTFIiLCJtYWMiOiJkYTA2MWQ0MjQxMGJhM2NkM2FjOWMxOTQyNzU1M2NiOTBiNDZlNTEwMDYzMzcxMDQyZWFmNDU5YWI4Y2Y2NzU5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:28:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-405534336\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-51468168 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-51468168\", {\"maxDepth\":0})</script>\n"}}