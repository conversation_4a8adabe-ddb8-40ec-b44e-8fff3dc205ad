{"__meta": {"id": "X574eacf4930b7eeec12a4a8610b4d117", "datetime": "2025-06-08 15:42:44", "utime": **********.185634, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749397363.542407, "end": **********.185657, "duration": 0.6432499885559082, "duration_str": "643ms", "measures": [{"label": "Booting", "start": 1749397363.542407, "relative_start": 0, "end": **********.103868, "relative_end": **********.103868, "duration": 0.5614609718322754, "duration_str": "561ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.103881, "relative_start": 0.5614738464355469, "end": **********.18566, "relative_end": 2.86102294921875e-06, "duration": 0.08177900314331055, "duration_str": "81.78ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45168616, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0065699999999999995, "accumulated_duration_str": "6.57ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.143285, "duration": 0.00458, "duration_str": "4.58ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 69.711}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.161454, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 69.711, "width_percent": 13.699}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (17) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.172489, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 83.409, "width_percent": 16.591}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/eyJpdiI6InRWOGJ2eHRKM1Fpc2NXaGVRbGREekE9PSIsInZhbHVlIjoiZjN3YzRMbnc0K3FRanU5bW1yTnVhZz09IiwibWFjIjoiZjE4NTMxYjk4YWZhMzQxNWZlNDMwYjNhZmVlZDNjY2EwODMzNzQzMDk3MTU0NWQ5NTM3MTFmNmY3MjVkMDlmOCIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-300119031 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-300119031\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-720195135 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-720195135\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-449846593 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-449846593\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1818151602 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6InRWOGJ2eHRKM1Fpc2NXaGVRbGREekE9PSIsInZhbHVlIjoiZjN3YzRMbnc0K3FRanU5bW1yTnVhZz09IiwibWFjIjoiZjE4NTMxYjk4YWZhMzQxNWZlNDMwYjNhZmVlZDNjY2EwODMzNzQzMDk3MTU0NWQ5NTM3MTFmNmY3MjVkMDlmOCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwl%7C0%7C1982; _clsk=o6pnpa%7C1749397360704%7C12%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlBxdkhRUVhXRW5oZlJvbkkxYkRVcnc9PSIsInZhbHVlIjoiNFF4cm43SkkzakU3T3k1ODlOelduTjI2VHVHT3NNVlh2eG9iWE1qek8vN0ZxemZjWk15NXNtdkxZNzluZFBzK1VqM1hBbkZLcEJnUXZWUkRQS1VZWkxERDZPVUtobXl6OTEyT0Vqc0RPRVZ4WjdYK1NiWG1SL1Rka1JBZ1MxbzVmOVJ0SkVQb1JYamtsYzFSUmFEYVdTKzhLUEczVnRtUURVQ3hJVVB1L2NmRHhqNWI2MkpwUW5yNlVDL3Iyd3BPUzJWUVBibVI2Z3hTeWdrN082SmpVN0N5bXNEUVdYd0dyTVRINFRHWGU3WW5TcHJoY3NUUVBzaHIyeXFSLzA0Nkw4ajlrNjNUZ2JaQ0tLc0hNRG82WXBWZitubGI1ekNtOFRWdG5LTURMY25pN3ExQnk1MTRud1Zsc1VuTHNFdmtXbUZ2VlhiYkdBRThJeXFlenVoREhHQkFrOVJVakJFaXZwckgzM1hRSnFaUjFFY2NWcHdKWG92U0VWQ0hrejBlUHFibEgxdWl6eHpGSTNmMnFSVFRJUk14Q1BLaFZxdWZqQnU1d1BLM2dIUEgyOXJDUk9QTWp0SHZTL2xiNnd1RXdYVWJNV09XMCt5c2paWFhMSWZSdzJ0dHdmaUdtVGxrb3ZtQ1NWM0ZhTGFWQUhuZFc0RFlKQzlXbzJUUTJ6R2QiLCJtYWMiOiI0ZGJhNmY4ZDIyZDBjY2I3ODI0ZTQwODQxMWVkNTRhNDRjNmU0ZTYxOWNmNDk5OWQwZmM0Y2FjNGRkMDk3NjdmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImZNNUx2bURIM1l3OFc4dTFSYlJ5clE9PSIsInZhbHVlIjoiaHhnbVEwc0w0RnhPSlNmRU5zcEVDK1d0REpobkFQcGxxYjFMRG1OOEc2bDdaTzFXU2ZSZkhrU095Q25qWm5qU1RxMDBrSFFtRkx6UDF0M3V4Qm1WV0REb1RHS3RRb0F1WWhBSjM0K0ZlL0d4ejdUSGZza1Z1bzAxQk9ENkgxKzBXaUMzanpyN3dTcC9FY1N0UU8xNU5YbHhva0lNWTFwUG0vTmdhSTBSbmphMHBabG5NZFZBTWVKaFhGSTBTMkZaNUhvNHJJVWphQzk3Tzdna0JtcVlza3JJdGM2SWpTMUNTaEdGRGNLNFJUSnRsMHdublFVT3Fxb213aGVjbDM4NXN6bE1JWHliRlVlK1l2WGlnclpyQlpmdTJ1TWtaaWlIYzg5R05wTjdMSk1IU2xCNjZTWlk2WWRpS3JEVlVTZktpOEhESGFUMEpGR05ZeGtYa1NtTlptRFFiamxXN3ZScHdwOVp4RzJiWU5XTS9WMEx0eEJrVlhiN0Z1VUNZVWkyazQwK0x2QUw3R284SnZrTkJMNEU5Tzc1aVdiUGRqWEEwaDVtWmd0QUVwSVJ6enFTZ0htbzlnM1RpM0FKVlBwa2lzNXh2VS83b3AvZ255cW4xdU5TbWVyMVJyZytuYzVTb0tZOFZnaU54ckl6eW9BYUEwdmFINE5ETFNSTkxvQS8iLCJtYWMiOiJmNGY3YzUzMmM4MTIzYTM2MDZmZTdhZmNjNTI1MmJkY2IyNTE0NTdjZmNkNGJjODNlY2I0YThkNmMyNGIyN2Q4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1818151602\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">COI6IIrAbucReABHBN7o3ZaIzRbwBCI1gfwrcfCo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-726152904 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:42:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlRJbUVwMTBjVnJlZGNjUjJ6K1dCOGc9PSIsInZhbHVlIjoieUJqMmF4djhjR2VDT2dPQlVnSEdTcTd5S1VTdjc5eG9LQm9UNzNTeTdUaVVreTN2aWVSSDZxVVd4dlZ5YjNYUVRmZVBWZU9IZVhkSUQzaDVRL1YyMXQ2YWU2MUxtTmRVUHoxMzVTZnpqSmZ6ckZSbU81WmZvbmdBZnlzc0lMUFJqZk9VaDBDM2NMZzJVbU1NV3phS1d6Q3draFdMSlNxV2pTNUcvU2ZHZXpPLzB4RjFsd2ZFN20rZ29zY0FiYjVuTTZ3dnYrckdwVmxBV1F1dWh4cmhuWStOaG00WUZlRUxia25SYnRjV0MxUUQ1ZzdhSUh2d1NoZzQrMkJKZWNDWWVETkNFeGhFUkdyR2c5dUtSZG1vUHFrRTc3NGtiZVFGQTMyOHhNK0w5bXZkdnRZaGl5RzZGcWorR29WODBIMW5aSkdkM1pvNHpDZk1mSzAwVVBCRnB2Y3Y1ZkRUQmdYR04wV1JLT1lzQXNpbk9wWUVLc2FTRnh1STZCL042VU9zSWl3SUJJZnlGVW1QUGx3WkRBaDN6VVQ1b0Q3YnJRSEJWSmltSWlabk1DQmxreUx3T3ZHSnd4ejFPcTcwOENNZ2llRnUrQmZHNU5vZ21QZ1JwU00rblF5Z2tIMGpSQ1dHaC9SQ1FDaG15U3VJMzNRbW9OTmRUQmZ5MFZkTG0xWVIiLCJtYWMiOiJlYzQxNGVlZDkzMzZjMzNhMWZiNjVlNTRjN2FkNGVjYzRhOTk1ZTVlZmZhODZjMGY3YzVkY2VlNGY1NzZmYmJjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:42:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Im9LYWVNcnR2S3R6NnBaTjh1aXgweGc9PSIsInZhbHVlIjoiUFpQaEduTFVzNFFiSTcyMkI4SVhoRURNK0tKamxrQzBqbVAxYlJnclZOQ3VqVER5QVYvbnhtVGYwTTEyS1FZWVk2eFB6dmFmRlBJSlBtU0l3RkxPdzlxczJLVW10Y1VBeEpQZ3V2ektVL2VHdlA3Z2ZoeHI3aThPaFhXMFIveTdOamFaaVpSNlg2V1Ayblc2cFFRdGFHRDJqMzQvVHdLN0hkeXBHLzF2YzRYVlpXWnpXZkhnem03bFVzdytJWXlXZHhjMXNhZ1BLNHQyb0RSRVlHcHdmYXlwU2NoOFB3c2JyNjR5M3VBT2IxMkwvYUJzdTU3YVNXUVd2VkN3R05vTzg4bVltcW5qdlZPY05GbDFqRWhrdHFRTitTakFGeTBsT25TK0s1cmw4MWpRd1M1dXo3K01uVml1T0FWR012VkRKTGRMY3lpeGppbk8rYkEvaEFRdjhDdW10NEM0djdvV0R2a2VPWWxYWk55ejFaajc5T2Z0czV4WnYweSsrMExIVlNjUnR0clFWVUI4Nm14eGxtMkdPblgrTGdIb1RMc0dBSldkKytwc0g5MjJmeVRrYkMxNlVJTEN3MWxXRkpoREZhQ1JBem5ZZmV5RkxCWVpzeW5JVzI1RTNkTUFVOGx0cDdKcmZuenpvNEJnVVlPaXI5UkUyak5aY0VaY3plNW0iLCJtYWMiOiIzMGJjMGY1NWVlNjA2ODIzYmRlYzQzZDZiYTUzOTdlODY2MjMzNGNjNjIxYjY5NGYwZTA4OTUwM2M2ZTEyMDY3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:42:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlRJbUVwMTBjVnJlZGNjUjJ6K1dCOGc9PSIsInZhbHVlIjoieUJqMmF4djhjR2VDT2dPQlVnSEdTcTd5S1VTdjc5eG9LQm9UNzNTeTdUaVVreTN2aWVSSDZxVVd4dlZ5YjNYUVRmZVBWZU9IZVhkSUQzaDVRL1YyMXQ2YWU2MUxtTmRVUHoxMzVTZnpqSmZ6ckZSbU81WmZvbmdBZnlzc0lMUFJqZk9VaDBDM2NMZzJVbU1NV3phS1d6Q3draFdMSlNxV2pTNUcvU2ZHZXpPLzB4RjFsd2ZFN20rZ29zY0FiYjVuTTZ3dnYrckdwVmxBV1F1dWh4cmhuWStOaG00WUZlRUxia25SYnRjV0MxUUQ1ZzdhSUh2d1NoZzQrMkJKZWNDWWVETkNFeGhFUkdyR2c5dUtSZG1vUHFrRTc3NGtiZVFGQTMyOHhNK0w5bXZkdnRZaGl5RzZGcWorR29WODBIMW5aSkdkM1pvNHpDZk1mSzAwVVBCRnB2Y3Y1ZkRUQmdYR04wV1JLT1lzQXNpbk9wWUVLc2FTRnh1STZCL042VU9zSWl3SUJJZnlGVW1QUGx3WkRBaDN6VVQ1b0Q3YnJRSEJWSmltSWlabk1DQmxreUx3T3ZHSnd4ejFPcTcwOENNZ2llRnUrQmZHNU5vZ21QZ1JwU00rblF5Z2tIMGpSQ1dHaC9SQ1FDaG15U3VJMzNRbW9OTmRUQmZ5MFZkTG0xWVIiLCJtYWMiOiJlYzQxNGVlZDkzMzZjMzNhMWZiNjVlNTRjN2FkNGVjYzRhOTk1ZTVlZmZhODZjMGY3YzVkY2VlNGY1NzZmYmJjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:42:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Im9LYWVNcnR2S3R6NnBaTjh1aXgweGc9PSIsInZhbHVlIjoiUFpQaEduTFVzNFFiSTcyMkI4SVhoRURNK0tKamxrQzBqbVAxYlJnclZOQ3VqVER5QVYvbnhtVGYwTTEyS1FZWVk2eFB6dmFmRlBJSlBtU0l3RkxPdzlxczJLVW10Y1VBeEpQZ3V2ektVL2VHdlA3Z2ZoeHI3aThPaFhXMFIveTdOamFaaVpSNlg2V1Ayblc2cFFRdGFHRDJqMzQvVHdLN0hkeXBHLzF2YzRYVlpXWnpXZkhnem03bFVzdytJWXlXZHhjMXNhZ1BLNHQyb0RSRVlHcHdmYXlwU2NoOFB3c2JyNjR5M3VBT2IxMkwvYUJzdTU3YVNXUVd2VkN3R05vTzg4bVltcW5qdlZPY05GbDFqRWhrdHFRTitTakFGeTBsT25TK0s1cmw4MWpRd1M1dXo3K01uVml1T0FWR012VkRKTGRMY3lpeGppbk8rYkEvaEFRdjhDdW10NEM0djdvV0R2a2VPWWxYWk55ejFaajc5T2Z0czV4WnYweSsrMExIVlNjUnR0clFWVUI4Nm14eGxtMkdPblgrTGdIb1RMc0dBSldkKytwc0g5MjJmeVRrYkMxNlVJTEN3MWxXRkpoREZhQ1JBem5ZZmV5RkxCWVpzeW5JVzI1RTNkTUFVOGx0cDdKcmZuenpvNEJnVVlPaXI5UkUyak5aY0VaY3plNW0iLCJtYWMiOiIzMGJjMGY1NWVlNjA2ODIzYmRlYzQzZDZiYTUzOTdlODY2MjMzNGNjNjIxYjY5NGYwZTA4OTUwM2M2ZTEyMDY3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:42:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-726152904\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-589429888 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6InRWOGJ2eHRKM1Fpc2NXaGVRbGREekE9PSIsInZhbHVlIjoiZjN3YzRMbnc0K3FRanU5bW1yTnVhZz09IiwibWFjIjoiZjE4NTMxYjk4YWZhMzQxNWZlNDMwYjNhZmVlZDNjY2EwODMzNzQzMDk3MTU0NWQ5NTM3MTFmNmY3MjVkMDlmOCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-589429888\", {\"maxDepth\":0})</script>\n"}}