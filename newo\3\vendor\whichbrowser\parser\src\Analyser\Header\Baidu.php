<?php

namespace Which<PERSON><PERSON><PERSON>\Analyser\Header;

class Baidu
{
    private $data;

    public function __construct($header, &$data)
    {
        $this->data =& $data;

        if (!isset($this->data->browser->name) || $this->data->browser->name != 'Bai<PERSON> Browser') {
            $this->data->browser->name = '<PERSON><PERSON> Browser';
            $this->data->browser->version = null;
            $this->data->browser->stock = false;
        }
    }
}
