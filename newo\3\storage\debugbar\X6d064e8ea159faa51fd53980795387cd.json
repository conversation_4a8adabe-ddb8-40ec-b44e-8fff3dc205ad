{"__meta": {"id": "X6d064e8ea159faa51fd53980795387cd", "datetime": "2025-06-08 15:43:44", "utime": **********.170551, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749397423.457812, "end": **********.170575, "duration": 0.7127628326416016, "duration_str": "713ms", "measures": [{"label": "Booting", "start": 1749397423.457812, "relative_start": 0, "end": **********.086583, "relative_end": **********.086583, "duration": 0.6287708282470703, "duration_str": "629ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.086599, "relative_start": 0.6287870407104492, "end": **********.170577, "relative_end": 2.1457672119140625e-06, "duration": 0.08397793769836426, "duration_str": "83.98ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45152152, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0057799999999999995, "accumulated_duration_str": "5.78ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.129445, "duration": 0.0033599999999999997, "duration_str": "3.36ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 58.131}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.146739, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 58.131, "width_percent": 17.474}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (17) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.158192, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 75.606, "width_percent": 24.394}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1839114590 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1839114590\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-879048193 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-879048193\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1155280316 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1155280316\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1978449634 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwl%7C0%7C1982; _clsk=o6pnpa%7C1749397377026%7C15%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjV5RVVYeUFXL0NBbEJ5Y0dKVW1JSGc9PSIsInZhbHVlIjoicXNOeENCem5URWNzc3I4cXlncWxYK0prRC9WZUpXaG5sSlVlMVROK0NUNWJjcUdpZjdjV1daZWRNOGt0VXVmckJ5SlAvWFZsTzllSUxObitSc3craTlSWkRZdkJudW5qU1c3T2lSekxQcE5KVWc5QWFwZGRnbkh5TzEvZERzT3VzWk9GNXI0WE44MUVvVFgxOFF6R3dsMVoyMWpEVkNSbCs3Ymg3dFFGeHBLSEd2Q1dmTTJuYW1Rd3JsYTBadnlQMFprTFByWklJLzFCVVBqMmJGbUs5bEtjU0dwa2VRV0NuVTdlUGY5cTFjWVEyZ1pkSWVpL1VxaHZBZzdHUC9vMCtxallMQ0orb2htQVA3dTdDVFBtK01GRWREQisrOFpZc3FwZnJ3ZnBiMjJFRkozMkxia29QNjJWOWJvMnlxV041UFpOc0JkYlM0TWtuZ3hmZU03Rkgxb0Z1V0VKQ3NBYysyNmtQK0RESEVQZ3ZUZkxweE5GSFZ4OWloUVpNcVN1ajZicmdvQ0JmU1E0dFZ2TnI4WHJrb2VzOXYySWhxZDg0djdRR0RmQ2RSY2lxRlU0YzlYMUd2M1ZtSmYwaUFmQ2dreWdPWVFQMis5eHdUNkdNNlY4bWpiS1FDdFZITnRLQW42MGVJUy9lWHU1TEJuTnJyT3lPWFBXSjlNNjcwRGIiLCJtYWMiOiJjMjljZDczYzE1YTk1MDYxOTdkZjlkMWE3ODdjZDQ2YWI0MzhlNDJjYmYxZmY2MzE1NDVlOGVlMWFhZTg1ZmY1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkJFWi8rR25naXVqaklIRDVVekxoalE9PSIsInZhbHVlIjoiaGNCVnNyMy9NZTVHL1AxQmR3Mk5zZzVuTnRxTS96UzlkbHVWbmhZSVRJV0QwR2dZbUdGR0VmT1Vad2l2UlpGUHpCd3RpSVQrSit2bWtYdi9mT3lUeWRmalNuRlVBcy9JM2ZSemU4S3doSFhQOXlWQjFWM2gzOFpWVjROak1ZSmxKY05BRGdiZU45YzJ3alN1Vk91K0hmcWNtQ0FGSnc3Y3ZMVlBWcERITTFsU2FabHdyUGlieWNIckE5dUFrbSsxdEpwK0lidlU1Ym1abFFPSzhLWjZ2WjhYRVFERWpBYzBCMXZMQXFDdVY2RWJoRkdLWWVBODJ0YjEyU1UzZ0ZUc2dhUjdJaUlydGpjK29Uek9XRWZJMkp6cXhXeGtmTGxtbE8zcjA4TnphQmxlcWVwQkVFR2dPU1JITlNaZUg2WmVrZzlxMTluWkp5Zk51dVNPeDhCbjN3Y1VPZVljcEZmanVrZFZCUmRveVdxcThsT05IeUxuM2R4TG41ZCs3ZnVtNkJ3U1BzQ2ovVHl0TEtFYnVsUGJSSVcyUnhQZE9MNmo4UkVtTWpKT1B4YStNUFcvd2FqbCtUbXJHc2k5a2VDOGFkaFFlQW5vclFsVDl3Z1F6aytqZmUyUmJ1MTNQemdqMEExWEVpbFNPSXJnYkJ5TkIrRmx5TDcwU3BCd2NXSTciLCJtYWMiOiIwOWU5OGQzOWYxNGY0NGNiZjhkOWVkNTRiMWFhNWY2MTkzNzkxMjM2NTRiYzQzNWNjNzA1YjM1ZDNkOGM0NTA3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1978449634\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-455826032 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">COI6IIrAbucReABHBN7o3ZaIzRbwBCI1gfwrcfCo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-455826032\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-945790772 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:43:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InZTbzRZeWtUS2o4eExIVG84VzNUZkE9PSIsInZhbHVlIjoiM0NON3J6M29hU2x1T3ZpZVYrSjZnM0Y0SGllMTMxVG9nc1FhVXkzbFBrVS8valIvVWtMNEpiRGlVNTNNMzJ1L05Fa3ovaHlBclhHeTVTd0VPSHFGMkY4SjRycDFldThsV2ZadHhQOWQ0dFpmb1FydWRCMldicU9RZHBMb2k0U29nUGpuKytaUTlOeEsyWUNQUmZseUZUY0ZlODVqN2lib3hyQk1ZYitYSGNTdDF0MkIvN2pVSEw3UFBtcWdYL09Cc2tGbk43ODdDYTRKMkFXVmFDOCtsVDlwaTN2TWlmeDhLaUNhMmx1c1Zod1pvTHpuSUtKRDkyR25vamJpdUtxSm9BSDN3TnlFc0dETGE1cEY4STVQV1l1T2xab0tWOUdyd1hLUUk3OUZoYjRWRW9IV2dlc1JsS3BOU1h5RVY1Mmwvd2Uyck5OU1BQdUpiZ3N3YzFYNHNsRFgwN2xqdWFSNjZwVTRZQkNMbjQxVGkwNlVVVEI2KzdYaFNPbWcwK0pTb2Z1a3UwK1VhVG9lbmNhZzJUc1JQQWRheU1QTHVaRFY0aWNrQS96OW9sUlUwVUVKSERmdmFEMzVreFFBc0FoWC9WU24wTVdWZGx5c1dQWjlzbUtkd1FsV1R6UHFJV1NwZzNKM2xuU2ZmR1N5em53ZGwxUmdqSWw4aG4ySUhXaWwiLCJtYWMiOiJmYjQwYzM5MjExYzM3ZjBkNTY2ZTU5NWJmZTFjM2NlNzEwYzVkZDA4ZDA1NmIxMmIxNDRiYzJjZmQwMTlhZWI3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:43:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlJ2Q0FHQndKTDdhdHIxTUhKS1pJN1E9PSIsInZhbHVlIjoiQ0hyS2Y2b0p3M0dYNjdJN2FaMXJXem4xN2o1V3Q2QmF6MlhRTUptaksvQXlxbk1sNmtCM3hCakptdEN3VU55QWlIME1HR1hUaDZJK1I2eGIzeU9FdWwxcWtYQVFrMkx6VVQxL0tETzlFeHhKcHgwVDUzUHF0SVcwSklqQTE3elZDUHAzbmJ5WGc4YnJDMUhTWjdyMkFZSEoxRDIrMkN1Tmd2MS9BTjJNRm9lb0ZsRkNMM0swak1nN1JPZFZlbHJuMUtWN21MWnBuN2hsUnZUd29IZTVYSTJmK1F6NUQ5TnRZSnBUbEpKdW9maEZsLzlBQWFIN3B3SUorYUVDRjkwbXluYmRTcm1nY2ZrcWlkVis0dUV5NzVSSURVcHhyM2E0TTd2eitQTFBBblloZjBsYjRWZmZIQ3hOcGIxcyszLzhlS3lxTW9KVkc5TlpDU3BldC9ranJLb0RVc1ZaTHN1QnBlT1d5U2pwY1dGb2tjRjBxNFlWN1JSKzJ2My9QaU5lZlE5b1Zsd09vdXpXcUNKa1RISnIyR3RmWitzRUxvMHpPblkzREtNVWpxQWU0ZTB0WkJnd1VETHJlWCtPRDdGb1ZwQVordWIxUjNYdFNKaU1FZXdvaC9tcXhWaEVXNVMrektKbDQwaTE4d2daVDRPbk9vQmNNZ1VIV3c4MVRmT2siLCJtYWMiOiJiZjUxYTU1OTMyZGYxYmIwZmRkOTI3NTUzOTcyOWM0NGI5MjdjOGJjZGMzZjYyMDdkNDk1ZDdlMjU3NzAwMjg3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:43:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InZTbzRZeWtUS2o4eExIVG84VzNUZkE9PSIsInZhbHVlIjoiM0NON3J6M29hU2x1T3ZpZVYrSjZnM0Y0SGllMTMxVG9nc1FhVXkzbFBrVS8valIvVWtMNEpiRGlVNTNNMzJ1L05Fa3ovaHlBclhHeTVTd0VPSHFGMkY4SjRycDFldThsV2ZadHhQOWQ0dFpmb1FydWRCMldicU9RZHBMb2k0U29nUGpuKytaUTlOeEsyWUNQUmZseUZUY0ZlODVqN2lib3hyQk1ZYitYSGNTdDF0MkIvN2pVSEw3UFBtcWdYL09Cc2tGbk43ODdDYTRKMkFXVmFDOCtsVDlwaTN2TWlmeDhLaUNhMmx1c1Zod1pvTHpuSUtKRDkyR25vamJpdUtxSm9BSDN3TnlFc0dETGE1cEY4STVQV1l1T2xab0tWOUdyd1hLUUk3OUZoYjRWRW9IV2dlc1JsS3BOU1h5RVY1Mmwvd2Uyck5OU1BQdUpiZ3N3YzFYNHNsRFgwN2xqdWFSNjZwVTRZQkNMbjQxVGkwNlVVVEI2KzdYaFNPbWcwK0pTb2Z1a3UwK1VhVG9lbmNhZzJUc1JQQWRheU1QTHVaRFY0aWNrQS96OW9sUlUwVUVKSERmdmFEMzVreFFBc0FoWC9WU24wTVdWZGx5c1dQWjlzbUtkd1FsV1R6UHFJV1NwZzNKM2xuU2ZmR1N5em53ZGwxUmdqSWw4aG4ySUhXaWwiLCJtYWMiOiJmYjQwYzM5MjExYzM3ZjBkNTY2ZTU5NWJmZTFjM2NlNzEwYzVkZDA4ZDA1NmIxMmIxNDRiYzJjZmQwMTlhZWI3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:43:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlJ2Q0FHQndKTDdhdHIxTUhKS1pJN1E9PSIsInZhbHVlIjoiQ0hyS2Y2b0p3M0dYNjdJN2FaMXJXem4xN2o1V3Q2QmF6MlhRTUptaksvQXlxbk1sNmtCM3hCakptdEN3VU55QWlIME1HR1hUaDZJK1I2eGIzeU9FdWwxcWtYQVFrMkx6VVQxL0tETzlFeHhKcHgwVDUzUHF0SVcwSklqQTE3elZDUHAzbmJ5WGc4YnJDMUhTWjdyMkFZSEoxRDIrMkN1Tmd2MS9BTjJNRm9lb0ZsRkNMM0swak1nN1JPZFZlbHJuMUtWN21MWnBuN2hsUnZUd29IZTVYSTJmK1F6NUQ5TnRZSnBUbEpKdW9maEZsLzlBQWFIN3B3SUorYUVDRjkwbXluYmRTcm1nY2ZrcWlkVis0dUV5NzVSSURVcHhyM2E0TTd2eitQTFBBblloZjBsYjRWZmZIQ3hOcGIxcyszLzhlS3lxTW9KVkc5TlpDU3BldC9ranJLb0RVc1ZaTHN1QnBlT1d5U2pwY1dGb2tjRjBxNFlWN1JSKzJ2My9QaU5lZlE5b1Zsd09vdXpXcUNKa1RISnIyR3RmWitzRUxvMHpPblkzREtNVWpxQWU0ZTB0WkJnd1VETHJlWCtPRDdGb1ZwQVordWIxUjNYdFNKaU1FZXdvaC9tcXhWaEVXNVMrektKbDQwaTE4d2daVDRPbk9vQmNNZ1VIV3c4MVRmT2siLCJtYWMiOiJiZjUxYTU1OTMyZGYxYmIwZmRkOTI3NTUzOTcyOWM0NGI5MjdjOGJjZGMzZjYyMDdkNDk1ZDdlMjU3NzAwMjg3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:43:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-945790772\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-710196158 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-710196158\", {\"maxDepth\":0})</script>\n"}}