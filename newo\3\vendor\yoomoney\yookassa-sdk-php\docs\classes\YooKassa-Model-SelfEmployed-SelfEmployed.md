# [YooKassa API SDK](../home.md)

# Class: \YooKassa\Model\SelfEmployed\SelfEmployed
### Namespace: [\YooKassa\Model\SelfEmployed](../namespaces/yookassa-model-selfemployed.md)
---
**Summary:**

Класс, представляющий модель SelfEmployed.

**Description:**

Объект самозанятого.

---
### Constants
* No constants found

---
### Properties
| Visibility | Name | Flag | Summary |
| ----------:| ---- | ---- | ------- |
| public | [$confirmation](../classes/YooKassa-Model-SelfEmployed-SelfEmployed.md#property_confirmation) |  | Сценарий подтверждения пользователем заявки ЮMoney на получение прав для регистрации чеков в сервисе Мой налог. |
| public | [$created_at](../classes/YooKassa-Model-SelfEmployed-SelfEmployed.md#property_created_at) |  | Время создания объекта самозанятого. |
| public | [$createdAt](../classes/YooKassa-Model-SelfEmployed-SelfEmployed.md#property_createdAt) |  | Время создания объекта самозанятого. |
| public | [$id](../classes/YooKassa-Model-SelfEmployed-SelfEmployed.md#property_id) |  | Идентификатор самозанятого в ЮKassa. |
| public | [$itn](../classes/YooKassa-Model-SelfEmployed-SelfEmployed.md#property_itn) |  | ИНН самозанятого. |
| public | [$phone](../classes/YooKassa-Model-SelfEmployed-SelfEmployed.md#property_phone) |  | Телефон самозанятого, который привязан к личному кабинету в сервисе Мой налог. |
| public | [$status](../classes/YooKassa-Model-SelfEmployed-SelfEmployed.md#property_status) |  | Статус подключения самозанятого и выдачи ЮMoney прав на регистрацию чеков. |
| public | [$test](../classes/YooKassa-Model-SelfEmployed-SelfEmployed.md#property_test) |  | Признак тестовой операции. |
| protected | [$_confirmation](../classes/YooKassa-Model-SelfEmployed-SelfEmployed.md#property__confirmation) |  | Сценарий подтверждения пользователем заявки ЮMoney на получение прав для регистрации чеков в сервисе Мой налог. |
| protected | [$_created_at](../classes/YooKassa-Model-SelfEmployed-SelfEmployed.md#property__created_at) |  | Время создания объекта самозанятого. |
| protected | [$_id](../classes/YooKassa-Model-SelfEmployed-SelfEmployed.md#property__id) |  | Идентификатор самозанятого в ЮKassa. |
| protected | [$_itn](../classes/YooKassa-Model-SelfEmployed-SelfEmployed.md#property__itn) |  | ИНН самозанятого. Формат: 12 цифр без пробелов. |
| protected | [$_phone](../classes/YooKassa-Model-SelfEmployed-SelfEmployed.md#property__phone) |  | Телефон самозанятого, который привязан к личному кабинету в сервисе Мой налог. |
| protected | [$_status](../classes/YooKassa-Model-SelfEmployed-SelfEmployed.md#property__status) |  | Статус подключения самозанятого и выдачи ЮMoney прав на регистрацию чеков |
| protected | [$_test](../classes/YooKassa-Model-SelfEmployed-SelfEmployed.md#property__test) |  | Признак тестовой операции |

---
### Methods
| Visibility | Name | Flag | Summary |
| ----------:| ---- | ---- | ------- |
| public | [__construct()](../classes/YooKassa-Common-AbstractObject.md#method___construct) |  | AbstractObject constructor. |
| public | [__get()](../classes/YooKassa-Common-AbstractObject.md#method___get) |  | Возвращает значение свойства. |
| public | [__isset()](../classes/YooKassa-Common-AbstractObject.md#method___isset) |  | Проверяет наличие свойства. |
| public | [__set()](../classes/YooKassa-Common-AbstractObject.md#method___set) |  | Устанавливает значение свойства. |
| public | [__unset()](../classes/YooKassa-Common-AbstractObject.md#method___unset) |  | Удаляет свойство. |
| public | [fromArray()](../classes/YooKassa-Common-AbstractObject.md#method_fromArray) |  | Устанавливает значения свойств текущего объекта из массива. |
| public | [getConfirmation()](../classes/YooKassa-Model-SelfEmployed-SelfEmployed.md#method_getConfirmation) |  | Возвращает сценарий подтверждения. |
| public | [getCreatedAt()](../classes/YooKassa-Model-SelfEmployed-SelfEmployed.md#method_getCreatedAt) |  | Возвращает время создания объекта самозанятого. |
| public | [getId()](../classes/YooKassa-Model-SelfEmployed-SelfEmployed.md#method_getId) |  | Возвращает идентификатор самозанятого в ЮKassa. |
| public | [getItn()](../classes/YooKassa-Model-SelfEmployed-SelfEmployed.md#method_getItn) |  | Возвращает ИНН самозанятого. |
| public | [getPhone()](../classes/YooKassa-Model-SelfEmployed-SelfEmployed.md#method_getPhone) |  | Возвращает телефон самозанятого. |
| public | [getStatus()](../classes/YooKassa-Model-SelfEmployed-SelfEmployed.md#method_getStatus) |  | Возвращает статус подключения самозанятого и выдачи ЮMoney прав на регистрацию чеков. |
| public | [getTest()](../classes/YooKassa-Model-SelfEmployed-SelfEmployed.md#method_getTest) |  | Возвращает признак тестовой операции. |
| public | [getValidator()](../classes/YooKassa-Common-AbstractObject.md#method_getValidator) |  |  |
| public | [jsonSerialize()](../classes/YooKassa-Common-AbstractObject.md#method_jsonSerialize) |  | Возвращает ассоциативный массив со свойствами текущего объекта для его дальнейшей JSON сериализации. |
| public | [offsetExists()](../classes/YooKassa-Common-AbstractObject.md#method_offsetExists) |  | Проверяет наличие свойства. |
| public | [offsetGet()](../classes/YooKassa-Common-AbstractObject.md#method_offsetGet) |  | Возвращает значение свойства. |
| public | [offsetSet()](../classes/YooKassa-Common-AbstractObject.md#method_offsetSet) |  | Устанавливает значение свойства. |
| public | [offsetUnset()](../classes/YooKassa-Common-AbstractObject.md#method_offsetUnset) |  | Удаляет свойство. |
| public | [setConfirmation()](../classes/YooKassa-Model-SelfEmployed-SelfEmployed.md#method_setConfirmation) |  | Устанавливает сценарий подтверждения. |
| public | [setCreatedAt()](../classes/YooKassa-Model-SelfEmployed-SelfEmployed.md#method_setCreatedAt) |  | Устанавливает время создания объекта самозанятого. |
| public | [setId()](../classes/YooKassa-Model-SelfEmployed-SelfEmployed.md#method_setId) |  | Устанавливает идентификатор самозанятого в ЮKassa. |
| public | [setItn()](../classes/YooKassa-Model-SelfEmployed-SelfEmployed.md#method_setItn) |  | Устанавливает ИНН самозанятого. |
| public | [setPhone()](../classes/YooKassa-Model-SelfEmployed-SelfEmployed.md#method_setPhone) |  | Устанавливает телефон самозанятого. |
| public | [setStatus()](../classes/YooKassa-Model-SelfEmployed-SelfEmployed.md#method_setStatus) |  | Устанавливает статус подключения самозанятого и выдачи ЮMoney прав на регистрацию чеков. |
| public | [setTest()](../classes/YooKassa-Model-SelfEmployed-SelfEmployed.md#method_setTest) |  | Устанавливает признак тестовой операции. |
| public | [toArray()](../classes/YooKassa-Common-AbstractObject.md#method_toArray) |  | Возвращает ассоциативный массив со свойствами текущего объекта для его дальнейшей JSON сериализации Является алиасом метода AbstractObject::jsonSerialize(). |
| protected | [getUnknownProperties()](../classes/YooKassa-Common-AbstractObject.md#method_getUnknownProperties) |  | Возвращает массив свойств которые не существуют, но были заданы у объекта. |
| protected | [validatePropertyValue()](../classes/YooKassa-Common-AbstractObject.md#method_validatePropertyValue) |  |  |

---
### Details
* File: [lib/Model/SelfEmployed/SelfEmployed.php](../../lib/Model/SelfEmployed/SelfEmployed.php)
* Package: YooKassa\Model
* Class Hierarchy: 
  * [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)
  * \YooKassa\Model\SelfEmployed\SelfEmployed
* Implements:
  * [\YooKassa\Model\SelfEmployed\SelfEmployedInterface](../classes/YooKassa-Model-SelfEmployed-SelfEmployedInterface.md)

* See Also:
  * [](https://yookassa.ru/developers/api)

---
### Tags
| Tag | Version | Description |
| --- | ------- | ----------- |
| category |  | Class |
| author |  | <EMAIL> |

---
## Properties
<a name="property_confirmation"></a>
#### public $confirmation : null|\YooKassa\Model\SelfEmployed\SelfEmployedConfirmation
---
***Description***

Сценарий подтверждения пользователем заявки ЮMoney на получение прав для регистрации чеков в сервисе Мой налог.

**Type:** <a href="../null|\YooKassa\Model\SelfEmployed\SelfEmployedConfirmation"><abbr title="null|\YooKassa\Model\SelfEmployed\SelfEmployedConfirmation">SelfEmployedConfirmation</abbr></a>

**Details:**


<a name="property_created_at"></a>
#### public $created_at : \DateTime
---
***Description***

Время создания объекта самозанятого.

**Type:** \DateTime

**Details:**


<a name="property_createdAt"></a>
#### public $createdAt : \DateTime
---
***Description***

Время создания объекта самозанятого.

**Type:** \DateTime

**Details:**


<a name="property_id"></a>
#### public $id : string
---
***Description***

Идентификатор самозанятого в ЮKassa.

**Type:** <a href="../string"><abbr title="string">string</abbr></a>

**Details:**


<a name="property_itn"></a>
#### public $itn : null|string
---
***Description***

ИНН самозанятого.

**Type:** <a href="../null|string"><abbr title="null|string">null|string</abbr></a>

**Details:**


<a name="property_phone"></a>
#### public $phone : null|string
---
***Description***

Телефон самозанятого, который привязан к личному кабинету в сервисе Мой налог.

**Type:** <a href="../null|string"><abbr title="null|string">null|string</abbr></a>

**Details:**


<a name="property_status"></a>
#### public $status : string
---
***Description***

Статус подключения самозанятого и выдачи ЮMoney прав на регистрацию чеков.

**Type:** <a href="../string"><abbr title="string">string</abbr></a>

**Details:**


<a name="property_test"></a>
#### public $test : bool
---
***Description***

Признак тестовой операции.

**Type:** <a href="../bool"><abbr title="bool">bool</abbr></a>

**Details:**


<a name="property__confirmation"></a>
#### protected $_confirmation : ?\YooKassa\Model\SelfEmployed\SelfEmployedConfirmation
---
**Summary**

Сценарий подтверждения пользователем заявки ЮMoney на получение прав для регистрации чеков в сервисе Мой налог.

**Type:** <a href="../?\YooKassa\Model\SelfEmployed\SelfEmployedConfirmation"><abbr title="?\YooKassa\Model\SelfEmployed\SelfEmployedConfirmation">SelfEmployedConfirmation</abbr></a>

**Details:**


<a name="property__created_at"></a>
#### protected $_created_at : ?\DateTime
---
**Summary**

Время создания объекта самозанятого.

***Description***

Указывается по [UTC](https://ru.wikipedia.org/wiki/Всемирное_координированное_время) и передается в формате [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601).
Пример: ~`2017-11-03T11:52:31.827Z

**Type:** <a href="../?\DateTime"><abbr title="?\DateTime">DateTime</abbr></a>

**Details:**


<a name="property__id"></a>
#### protected $_id : ?string
---
**Summary**

Идентификатор самозанятого в ЮKassa.

**Type:** <a href="../?string"><abbr title="?string">?string</abbr></a>

**Details:**


<a name="property__itn"></a>
#### protected $_itn : ?string
---
**Summary**

ИНН самозанятого. Формат: 12 цифр без пробелов.

**Type:** <a href="../?string"><abbr title="?string">?string</abbr></a>

**Details:**


<a name="property__phone"></a>
#### protected $_phone : ?string
---
**Summary**

Телефон самозанятого, который привязан к личному кабинету в сервисе Мой налог.

**Type:** <a href="../?string"><abbr title="?string">?string</abbr></a>

**Details:**


<a name="property__status"></a>
#### protected $_status : ?string
---
**Summary**

Статус подключения самозанятого и выдачи ЮMoney прав на регистрацию чеков

**Type:** <a href="../?string"><abbr title="?string">?string</abbr></a>

**Details:**


<a name="property__test"></a>
#### protected $_test : ?bool
---
**Summary**

Признак тестовой операции

**Type:** <a href="../?bool"><abbr title="?bool">?bool</abbr></a>

**Details:**



---
## Methods
<a name="method___construct" class="anchor"></a>
#### public __construct() : mixed

```php
public __construct(array|null $data = []) : mixed
```

**Summary**

AbstractObject constructor.

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">array OR null</code> | data  |  |

**Returns:** mixed - 


<a name="method___get" class="anchor"></a>
#### public __get() : mixed

```php
public __get(string $propertyName) : mixed
```

**Summary**

Возвращает значение свойства.

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string</code> | propertyName  | Имя свойства |

**Returns:** mixed - Значение свойства


<a name="method___isset" class="anchor"></a>
#### public __isset() : bool

```php
public __isset(string $propertyName) : bool
```

**Summary**

Проверяет наличие свойства.

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string</code> | propertyName  | Имя проверяемого свойства |

**Returns:** bool - True если свойство имеется, false если нет


<a name="method___set" class="anchor"></a>
#### public __set() : void

```php
public __set(string $propertyName, mixed $value) : void
```

**Summary**

Устанавливает значение свойства.

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string</code> | propertyName  | Имя свойства |
| <code lang="php">mixed</code> | value  | Значение свойства |

**Returns:** void - 


<a name="method___unset" class="anchor"></a>
#### public __unset() : void

```php
public __unset(string $propertyName) : void
```

**Summary**

Удаляет свойство.

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string</code> | propertyName  | Имя удаляемого свойства |

**Returns:** void - 


<a name="method_fromArray" class="anchor"></a>
#### public fromArray() : void

```php
public fromArray(array|\Traversable $sourceArray) : void
```

**Summary**

Устанавливает значения свойств текущего объекта из массива.

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">array OR \Traversable</code> | sourceArray  | Ассоциативный массив с настройками |

**Returns:** void - 


<a name="method_getConfirmation" class="anchor"></a>
#### public getConfirmation() : \YooKassa\Model\SelfEmployed\SelfEmployedConfirmation|null

```php
public getConfirmation() : \YooKassa\Model\SelfEmployed\SelfEmployedConfirmation|null
```

**Summary**

Возвращает сценарий подтверждения.

**Details:**
* Inherited From: [\YooKassa\Model\SelfEmployed\SelfEmployed](../classes/YooKassa-Model-SelfEmployed-SelfEmployed.md)

**Returns:** \YooKassa\Model\SelfEmployed\SelfEmployedConfirmation|null - Сценарий подтверждения


<a name="method_getCreatedAt" class="anchor"></a>
#### public getCreatedAt() : \DateTime|null

```php
public getCreatedAt() : \DateTime|null
```

**Summary**

Возвращает время создания объекта самозанятого.

**Details:**
* Inherited From: [\YooKassa\Model\SelfEmployed\SelfEmployed](../classes/YooKassa-Model-SelfEmployed-SelfEmployed.md)

**Returns:** \DateTime|null - Время создания объекта самозанятого


<a name="method_getId" class="anchor"></a>
#### public getId() : string|null

```php
public getId() : string|null
```

**Summary**

Возвращает идентификатор самозанятого в ЮKassa.

**Details:**
* Inherited From: [\YooKassa\Model\SelfEmployed\SelfEmployed](../classes/YooKassa-Model-SelfEmployed-SelfEmployed.md)

**Returns:** string|null - 


<a name="method_getItn" class="anchor"></a>
#### public getItn() : string|null

```php
public getItn() : string|null
```

**Summary**

Возвращает ИНН самозанятого.

**Details:**
* Inherited From: [\YooKassa\Model\SelfEmployed\SelfEmployed](../classes/YooKassa-Model-SelfEmployed-SelfEmployed.md)

**Returns:** string|null - 


<a name="method_getPhone" class="anchor"></a>
#### public getPhone() : string|null

```php
public getPhone() : string|null
```

**Summary**

Возвращает телефон самозанятого.

**Details:**
* Inherited From: [\YooKassa\Model\SelfEmployed\SelfEmployed](../classes/YooKassa-Model-SelfEmployed-SelfEmployed.md)

**Returns:** string|null - Телефон самозанятого


<a name="method_getStatus" class="anchor"></a>
#### public getStatus() : string|null

```php
public getStatus() : string|null
```

**Summary**

Возвращает статус подключения самозанятого и выдачи ЮMoney прав на регистрацию чеков.

**Details:**
* Inherited From: [\YooKassa\Model\SelfEmployed\SelfEmployed](../classes/YooKassa-Model-SelfEmployed-SelfEmployed.md)

**Returns:** string|null - 


<a name="method_getTest" class="anchor"></a>
#### public getTest() : bool

```php
public getTest() : bool
```

**Summary**

Возвращает признак тестовой операции.

**Details:**
* Inherited From: [\YooKassa\Model\SelfEmployed\SelfEmployed](../classes/YooKassa-Model-SelfEmployed-SelfEmployed.md)

**Returns:** bool - Признак тестовой операции


<a name="method_getValidator" class="anchor"></a>
#### public getValidator() : \YooKassa\Validator\Validator

```php
public getValidator() : \YooKassa\Validator\Validator
```

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

**Returns:** \YooKassa\Validator\Validator - 


<a name="method_jsonSerialize" class="anchor"></a>
#### public jsonSerialize() : array

```php
public jsonSerialize() : array
```

**Summary**

Возвращает ассоциативный массив со свойствами текущего объекта для его дальнейшей JSON сериализации.

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

**Returns:** array - Ассоциативный массив со свойствами текущего объекта


<a name="method_offsetExists" class="anchor"></a>
#### public offsetExists() : bool

```php
public offsetExists(string $offset) : bool
```

**Summary**

Проверяет наличие свойства.

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string</code> | offset  | Имя проверяемого свойства |

**Returns:** bool - True если свойство имеется, false если нет


<a name="method_offsetGet" class="anchor"></a>
#### public offsetGet() : mixed

```php
public offsetGet(string $offset) : mixed
```

**Summary**

Возвращает значение свойства.

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string</code> | offset  | Имя свойства |

**Returns:** mixed - Значение свойства


<a name="method_offsetSet" class="anchor"></a>
#### public offsetSet() : void

```php
public offsetSet(string $offset, mixed $value) : void
```

**Summary**

Устанавливает значение свойства.

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string</code> | offset  | Имя свойства |
| <code lang="php">mixed</code> | value  | Значение свойства |

**Returns:** void - 


<a name="method_offsetUnset" class="anchor"></a>
#### public offsetUnset() : void

```php
public offsetUnset(string $offset) : void
```

**Summary**

Удаляет свойство.

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string</code> | offset  | Имя удаляемого свойства |

**Returns:** void - 


<a name="method_setConfirmation" class="anchor"></a>
#### public setConfirmation() : $this

```php
public setConfirmation(\YooKassa\Model\SelfEmployed\SelfEmployedConfirmation|array|null $confirmation = null) : $this
```

**Summary**

Устанавливает сценарий подтверждения.

**Details:**
* Inherited From: [\YooKassa\Model\SelfEmployed\SelfEmployed](../classes/YooKassa-Model-SelfEmployed-SelfEmployed.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">\YooKassa\Model\SelfEmployed\SelfEmployedConfirmation OR array OR null</code> | confirmation  | Сценарий подтверждения |

**Returns:** $this - 


<a name="method_setCreatedAt" class="anchor"></a>
#### public setCreatedAt() : self

```php
public setCreatedAt(\DateTime|string|null $created_at = null) : self
```

**Summary**

Устанавливает время создания объекта самозанятого.

**Details:**
* Inherited From: [\YooKassa\Model\SelfEmployed\SelfEmployed](../classes/YooKassa-Model-SelfEmployed-SelfEmployed.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">\DateTime OR string OR null</code> | created_at  | Время создания объекта самозанятого. |

**Returns:** self - 


<a name="method_setId" class="anchor"></a>
#### public setId() : self

```php
public setId(string|null $id = null) : self
```

**Summary**

Устанавливает идентификатор самозанятого в ЮKassa.

**Details:**
* Inherited From: [\YooKassa\Model\SelfEmployed\SelfEmployed](../classes/YooKassa-Model-SelfEmployed-SelfEmployed.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string OR null</code> | id  | Идентификатор самозанятого в ЮKassa. |

**Returns:** self - 


<a name="method_setItn" class="anchor"></a>
#### public setItn() : self

```php
public setItn(string|null $itn = null) : self
```

**Summary**

Устанавливает ИНН самозанятого.

**Details:**
* Inherited From: [\YooKassa\Model\SelfEmployed\SelfEmployed](../classes/YooKassa-Model-SelfEmployed-SelfEmployed.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string OR null</code> | itn  | ИНН самозанятого |

**Returns:** self - 


<a name="method_setPhone" class="anchor"></a>
#### public setPhone() : self

```php
public setPhone(string|null $phone = null) : self
```

**Summary**

Устанавливает телефон самозанятого.

**Details:**
* Inherited From: [\YooKassa\Model\SelfEmployed\SelfEmployed](../classes/YooKassa-Model-SelfEmployed-SelfEmployed.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string OR null</code> | phone  | Телефон самозанятого |

**Returns:** self - 


<a name="method_setStatus" class="anchor"></a>
#### public setStatus() : self

```php
public setStatus(string|null $status = null) : self
```

**Summary**

Устанавливает статус подключения самозанятого и выдачи ЮMoney прав на регистрацию чеков.

**Details:**
* Inherited From: [\YooKassa\Model\SelfEmployed\SelfEmployed](../classes/YooKassa-Model-SelfEmployed-SelfEmployed.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string OR null</code> | status  | Статус подключения самозанятого |

**Returns:** self - 


<a name="method_setTest" class="anchor"></a>
#### public setTest() : self

```php
public setTest(bool|null $test = null) : self
```

**Summary**

Устанавливает признак тестовой операции.

**Details:**
* Inherited From: [\YooKassa\Model\SelfEmployed\SelfEmployed](../classes/YooKassa-Model-SelfEmployed-SelfEmployed.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">bool OR null</code> | test  | Признак тестовой операции |

**Returns:** self - 


<a name="method_toArray" class="anchor"></a>
#### public toArray() : array

```php
public toArray() : array
```

**Summary**

Возвращает ассоциативный массив со свойствами текущего объекта для его дальнейшей JSON сериализации
Является алиасом метода AbstractObject::jsonSerialize().

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

**Returns:** array - Ассоциативный массив со свойствами текущего объекта


<a name="method_getUnknownProperties" class="anchor"></a>
#### protected getUnknownProperties() : array

```php
protected getUnknownProperties() : array
```

**Summary**

Возвращает массив свойств которые не существуют, но были заданы у объекта.

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

**Returns:** array - Ассоциативный массив с не существующими у текущего объекта свойствами


<a name="method_validatePropertyValue" class="anchor"></a>
#### protected validatePropertyValue() : mixed

```php
protected validatePropertyValue(string $propertyName, mixed $propertyValue) : mixed
```

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string</code> | propertyName  |  |
| <code lang="php">mixed</code> | propertyValue  |  |

**Returns:** mixed - 



---

### Top Namespaces

* [\YooKassa](../namespaces/yookassa.md)

---

### Reports
* [Errors - 0](../reports/errors.md)
* [Markers - 0](../reports/markers.md)
* [Deprecated - 25](../reports/deprecated.md)

---

This document was automatically generated from source code comments on 2024-07-02 using [phpDocumentor](http://www.phpdoc.org/)

&copy; 2024 YooMoney