{"__meta": {"id": "X74337f51ce9fbdc044f95d4fe31eb57d", "datetime": "2025-06-08 15:42:22", "utime": **********.590532, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749397341.887611, "end": **********.590557, "duration": 0.7029461860656738, "duration_str": "703ms", "measures": [{"label": "Booting", "start": 1749397341.887611, "relative_start": 0, "end": **********.487597, "relative_end": **********.487597, "duration": 0.5999860763549805, "duration_str": "600ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.487614, "relative_start": 0.6000030040740967, "end": **********.59056, "relative_end": 2.86102294921875e-06, "duration": 0.10294604301452637, "duration_str": "103ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45139664, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02195, "accumulated_duration_str": "21.95ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.534543, "duration": 0.01984, "duration_str": "19.84ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 90.387}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.5683198, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 90.387, "width_percent": 4.282}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.5782192, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 94.67, "width_percent": 5.33}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/roles\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1296282767 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1296282767\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-949755019 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-949755019\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-910567474 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-910567474\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=xh9goj%7C1749397319718%7C17%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InFrVjNDSjhhZ2JOTUJKeHdWYzlwVmc9PSIsInZhbHVlIjoiQXNVUFUwNVBoZVUrWmlEcjFxWXlGWmViZU1KMm9lUVdwRjI1RWtlYWwyeGFSUDRPdmxGVFhlMnYxdDdXWk8xcmVBcU1CUEs1c2ZRK2I2U2h6ZTZ1SlJsUWpRcGZzYXdSZmlCVGtQOGJPS1dad1NNMkFQSU9IeHJjL0NmV09JeEtueTc0Y3hzblRkeUtHT1lnUGpOcVQ5eFB4Tkd2VjJaemNDL1lDcTdqajFFbGlEb2FZOTFVZUtpaFBTUnc4L3lITlFqUHFFQ0NmTEswYW82QzFnQWdsb0dJeFVDTkNYU3JoWDRlVWRjdUxNNVVwV3NUdkptWU5LaWZiVVpnOHRHdTFvR1BsdTg5TVhicXcrUkhmamFmWDMrVkpQb0Jxelk0QVpNZmtrN0RFQXFKZnozNkJ2ODdLSGx5ZmtKS0sraU5nZGg0ckdpUmNweUQ4MVFrYUZiQzJYcHUxOTkxQkNKQUZjL1dDUUU0T1pIRVBGVjlSeXpQRTRSTEhFMUx5NERNakd0amlQaHB2WGMzZFBvVDFGdVVhMnkyVkVJNkduL0RrV2RURFFCUzMwalpuanFYQVJ6eks0a3FEZURwT1BuVnR4SUFidERSaTI5NGltTForK1VBSk51NEJqU0JBL2x3dm55YVZZNWNqZFIzdExUQkFXc0VsWUN1aHBuYkljbVAiLCJtYWMiOiJkZTU4YzFjMjFkODUwODQzNGZhMzMyZTQyYTQxZmIyZTU3ZjA2ODg0YWNiNzgyYjVhNWViNTA0Zjk3MTJlYzA3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlltMmdrOUZ1cHJHdktVcFlicmg3bEE9PSIsInZhbHVlIjoiaUdLVDFoWFRxc1RheDJ3bVdDdThjZWE3akRmeENzL3ByeWJaTFkyd0YxOHVHRmJEU2hFNDVtdmtmVm9Lc2ZPbXF6a3A4c0tvU1JORU95aEJhL1JsQ05Kckg1MjdhbzI2aWw2SU1LL0h4Z2VDTnB1RjBMenJKYkV1R1VFU0ZWb0ZKVloxT0xVMDRUVlU1SFVmMHhOaXVZejhja3RGcHVJZE5JMjE5eCtKa05DRnEzZWFEcHc2RmVlRkllRG1aUURDS0JnQnB2cGRrdis5NjVUcllRc2VORFBSTGdtVHozT2tmamYvU1RnL05OdUhzd2FoU2ZFME9SWlVIWlExZFpCamlEeGVaeEVDbExqamdpM2xLU1QzL0gyaGJsNVFxWk04ZURncjhCSjUxRExTZ1pPL0JuWFFKUHJLNk1yeEdSL3NEMml3OVUrMXUwWE9ud3pOQ0M5ZEdQUEtKbEUvd1NnTEUxVWttL3JRWUtpY3lqcFNRSXhJUmFMYktvb1pPbkk4NGNlRWRmOUNKTGNyYzVnaWUzOUo4QTZKZVpHejJSOVBSTVcvTjR6SWZ6QXYzdVVjRDhOM0d0blQ0dnpJWG9aUmVuLytMVDYzTWlKbFRZemVMelRpUzlsbHN3MER5QVFJN2p0b242ZTJ1NkhnNnZ3TXhoeXZCK0pDVEtyYjVKUGMiLCJtYWMiOiJjOGJiODIxOTQwYzljNzFjNzZiMzQ4ZGRmNWMwOTZkYTc5NTZkMmYyNDNiNWZkYTE3MjM4MmFmYWQwYzZjYzA4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1746088635 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">l7xz5q4Zkpyo7Zd0rhHsE8M6pi3wac4XFWhsi0lQ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1746088635\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2132001753 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:42:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjlRM3lsakdVZDFCc1VIZlI5Q1d4VkE9PSIsInZhbHVlIjoiajFIQWF5Tm5SUWZwaVdRNURQc3lHS2xac2ZvM3E5UTZObzBmYXlPNFdjMDFTTWJBcUxQOWg2TzBXWUcvMDBKemtBMlY0UGI4ejZhNzZjNHVYdGZtVHpwWEhpV3psYWVVY09wSUpuc082cXFYV1FDTFp6R0o2RUMwdkN5TzdOZTRDV3FWc1ltNzZmZVk5a3ZZS1R1MFBoVUhFZWZENUdrQ1RNbUhnaElSN1JieFViSVdXU2ZmbkhzWGRsSmw5dkZaQW95WXBxZzNRVEt4UnRzSC84a3AzOGowNkk1TDhUQjkxMmdSWTM5a050L01HSkNNdWVtSDRFQ2hwOXNYMmxQWDBBNElyRWJDdnZYN293WlNvcXNiM3MvdUlwalpWMFZlR202OFVvSWhMc3VZbUZVQ0U5NXE4ME0yRkZWZ2h6WlhIMWUvUU5NNlJtV0sxdzlOUUR0QjZnWi9acEFJNzY3UEtmWCtWTkhoVXFmUWRDSXdLaHZEQkYwVTFoa2g4cXp0OSsvWEc3dHZLTWQxZlpraXJUUm41Z0loVGFaZzFIU2NnL1NGcGNhc1NzYkd0RURVM2NaOC8wTU9idmtoaGcyK1V4dGZYTVYxWDNhUTdISFVaWEcvaEk4RHNXbk9TYmhZK1l5TGlZeHFwcVZNYUN4S1kxWDNkaExuTUVINW5Ja3YiLCJtYWMiOiJlMGUwZDdlNjJhOTcyZmY0NDE2MjNlMTFjNDM0MTA1N2RkY2FiYmM4MTVjMWIxZDY2ZDAzNzk1Y2RjZWU4NTVjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:42:22 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlNzZlZmTTExZ0RyUXdUSk42WlQwRFE9PSIsInZhbHVlIjoid0w1RllIZS9JQ3BWbUc0UUlEZ3dJaEdnb1NnSnRIaDVJOU5oV0lUcHQzei9FT214Zyt3TzRKMUtoRm93VENFcnFYQ3U1WGZ0TjU1TDZsWEI3VVVYY0UyS3RDL0NUN2hzUjhHMFZ4OFZwOEZWd2FYYjg3bEpKZjBiM01WMjB1NDNMQ09TR044bndSTWttbjBYTks4bHVzOWIvTTd0TFkvSTNtQ0xPYWdvY2tkNHJjbk9ZNFNnbmZWVFVxaVpSYnNrT3cwRy9STG84YzVZTDdIdC9yQUJRdG41S2k4UU1aZGFRb2pjN2JzWUFta0N2M0d1a3B1WmYyMzVkUWZ0QVdVUHF1V2EvZzRRcWtDOGFBa2UxWktTMTJuMUVPTzlib1BMaTV1ekJIVGkyTDR0K2xLQzNWTjJaOXFURDMwaTF2QVdRL1Z2cDcvazI2bjVFZEdzejJhNmdnNjZrQU0wZXJtUmVlRFhjMGJjY1VQbnh2RUFVbCtWcGk3OEtxN25qQXJ3YUpPQjE1ZTJoMVN5dHNVMkJJOEZmZ1llUHVCb0p6bDFHYS9VZGR1bWgvTUZ4eVFjRHpEUTJZMnN3OTJVWnBXQTBmWnVlYzEwaHpGTFVIL3lwOVFoMVpmYWZFSFlxREpNSjZIMCtITEVNemdpWU1yWnE0K1lHaWhnb1dCQXcxbWYiLCJtYWMiOiIzYWJkMTUyZDc1ZTRkMGRiN2VjNjVjMTU0M2RkMzI5OGJhMWFkNmUxYjUwYjBiZWU1NTA4ZDJhNWZhZGUzMjU5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:42:22 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjlRM3lsakdVZDFCc1VIZlI5Q1d4VkE9PSIsInZhbHVlIjoiajFIQWF5Tm5SUWZwaVdRNURQc3lHS2xac2ZvM3E5UTZObzBmYXlPNFdjMDFTTWJBcUxQOWg2TzBXWUcvMDBKemtBMlY0UGI4ejZhNzZjNHVYdGZtVHpwWEhpV3psYWVVY09wSUpuc082cXFYV1FDTFp6R0o2RUMwdkN5TzdOZTRDV3FWc1ltNzZmZVk5a3ZZS1R1MFBoVUhFZWZENUdrQ1RNbUhnaElSN1JieFViSVdXU2ZmbkhzWGRsSmw5dkZaQW95WXBxZzNRVEt4UnRzSC84a3AzOGowNkk1TDhUQjkxMmdSWTM5a050L01HSkNNdWVtSDRFQ2hwOXNYMmxQWDBBNElyRWJDdnZYN293WlNvcXNiM3MvdUlwalpWMFZlR202OFVvSWhMc3VZbUZVQ0U5NXE4ME0yRkZWZ2h6WlhIMWUvUU5NNlJtV0sxdzlOUUR0QjZnWi9acEFJNzY3UEtmWCtWTkhoVXFmUWRDSXdLaHZEQkYwVTFoa2g4cXp0OSsvWEc3dHZLTWQxZlpraXJUUm41Z0loVGFaZzFIU2NnL1NGcGNhc1NzYkd0RURVM2NaOC8wTU9idmtoaGcyK1V4dGZYTVYxWDNhUTdISFVaWEcvaEk4RHNXbk9TYmhZK1l5TGlZeHFwcVZNYUN4S1kxWDNkaExuTUVINW5Ja3YiLCJtYWMiOiJlMGUwZDdlNjJhOTcyZmY0NDE2MjNlMTFjNDM0MTA1N2RkY2FiYmM4MTVjMWIxZDY2ZDAzNzk1Y2RjZWU4NTVjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:42:22 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlNzZlZmTTExZ0RyUXdUSk42WlQwRFE9PSIsInZhbHVlIjoid0w1RllIZS9JQ3BWbUc0UUlEZ3dJaEdnb1NnSnRIaDVJOU5oV0lUcHQzei9FT214Zyt3TzRKMUtoRm93VENFcnFYQ3U1WGZ0TjU1TDZsWEI3VVVYY0UyS3RDL0NUN2hzUjhHMFZ4OFZwOEZWd2FYYjg3bEpKZjBiM01WMjB1NDNMQ09TR044bndSTWttbjBYTks4bHVzOWIvTTd0TFkvSTNtQ0xPYWdvY2tkNHJjbk9ZNFNnbmZWVFVxaVpSYnNrT3cwRy9STG84YzVZTDdIdC9yQUJRdG41S2k4UU1aZGFRb2pjN2JzWUFta0N2M0d1a3B1WmYyMzVkUWZ0QVdVUHF1V2EvZzRRcWtDOGFBa2UxWktTMTJuMUVPTzlib1BMaTV1ekJIVGkyTDR0K2xLQzNWTjJaOXFURDMwaTF2QVdRL1Z2cDcvazI2bjVFZEdzejJhNmdnNjZrQU0wZXJtUmVlRFhjMGJjY1VQbnh2RUFVbCtWcGk3OEtxN25qQXJ3YUpPQjE1ZTJoMVN5dHNVMkJJOEZmZ1llUHVCb0p6bDFHYS9VZGR1bWgvTUZ4eVFjRHpEUTJZMnN3OTJVWnBXQTBmWnVlYzEwaHpGTFVIL3lwOVFoMVpmYWZFSFlxREpNSjZIMCtITEVNemdpWU1yWnE0K1lHaWhnb1dCQXcxbWYiLCJtYWMiOiIzYWJkMTUyZDc1ZTRkMGRiN2VjNjVjMTU0M2RkMzI5OGJhMWFkNmUxYjUwYjBiZWU1NTA4ZDJhNWZhZGUzMjU5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:42:22 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2132001753\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-537218214 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-537218214\", {\"maxDepth\":0})</script>\n"}}