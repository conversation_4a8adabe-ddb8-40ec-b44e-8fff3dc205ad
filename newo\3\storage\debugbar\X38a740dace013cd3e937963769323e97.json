{"__meta": {"id": "X38a740dace013cd3e937963769323e97", "datetime": "2025-06-08 16:25:15", "utime": **********.897086, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.294592, "end": **********.897109, "duration": 0.6025171279907227, "duration_str": "603ms", "measures": [{"label": "Booting", "start": **********.294592, "relative_start": 0, "end": **********.813863, "relative_end": **********.813863, "duration": 0.5192711353302002, "duration_str": "519ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.813876, "relative_start": 0.5192840099334717, "end": **********.897111, "relative_end": 1.9073486328125e-06, "duration": 0.08323502540588379, "duration_str": "83.24ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45154912, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.013330000000000002, "accumulated_duration_str": "13.33ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8508399, "duration": 0.01148, "duration_str": "11.48ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 86.122}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.874373, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 86.122, "width_percent": 6.227}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (17) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.884953, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 92.348, "width_percent": 7.652}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/eyJpdiI6Ild1N2JBYjlWemdxYnI1bkswNlJQdFE9PSIsInZhbHVlIjoiazE4Z3o4RmU0TnNjTnNSbzVUcGlzQT09IiwibWFjIjoiZmU5NTlhNTJmMzU5MzI0NzQ4ZTliMzZlNzEyNTRiMzUwMDcyOGQ5NGMxNWUyOTI3OGI4NjZjNTk5Yjg5ZDg5MSIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1948936748 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1948936748\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1321956269 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1321956269\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-298512824 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-298512824\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-888600918 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6Ild1N2JBYjlWemdxYnI1bkswNlJQdFE9PSIsInZhbHVlIjoiazE4Z3o4RmU0TnNjTnNSbzVUcGlzQT09IiwibWFjIjoiZmU5NTlhNTJmMzU5MzI0NzQ4ZTliMzZlNzEyNTRiMzUwMDcyOGQ5NGMxNWUyOTI3OGI4NjZjNTk5Yjg5ZDg5MSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwl%7C0%7C1982; _clsk=1y4k55%7C1749399896435%7C6%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkVtc1BqOVQwSVlzUmRIdTB3QWZUMEE9PSIsInZhbHVlIjoiSlB3YkxuUU5zU2NFODZUVW1MaElvNTNQb0c4ZzBXazY2VVcydjltQTFZdDZwV2ZjRHFYdnpDSUNLOGVmN1QzcDBkTkw4dUZlRjVGRjBVWEZRU1ZtTkp6ZnlTS20rTDJTOTNjMk1Dc0tzQUNPTEtTODVEbk9LejJDUHVxM0dkZHNkKzBieC91eVFxZ2tRWHVYSm14QnNhYWcybG9EeWwxa2h0a1pCNmpiM0cvOEZERThhQytUc3l6MmJTaDArV25GU1BMSHNKemYyS3hqdEtpVXU1SEF3YzBmRDNxT0NiMUdyZWdYVDJ4aTRFOXp5Nmh6a2xwZG1od2RBWndVT1pnSUw2MnlVWXVCT053RHRDaGVoa3BOU1RPQUhSMEc3cnNKeVpiWHlvcXV6Z2lNc3FrY0tvTzhJY0tzN1VzQWk3b1hxNm1lSUhPZDhqZXU4dGQ1TEZyZ1dPRXdmMjEyZUpxOFVlNEsrRTVWdmlBRktvT0xrQ2dNL1BOZVpIVHU0TnpTbHJlNVpuWjJHM0hvNjh0NXpERGV1Y0hiUDUyNHdTN1ovbzVBSzlaNjMzYWphcHluNjBWekMyR3k5RnI0WEhMdExSRGszRmU3OVd5T3o5WFdOS2VXWjd0cjJOM2dHd1diNmt4cXliNlhwcWxzVWtFVEFVeWlrRkVCcHhMY3FOdWwiLCJtYWMiOiI0Zjk0YjRmZDhhY2ZmZWQ1NWEwNzg3NGQzYThiNjI0ZmUxYzdjOTlmMzA2N2IwNzk5OGMyNzg2ZTNmODk5ZTZhIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImhJdjlia0YzekVVTytzNnN5eFpDa0E9PSIsInZhbHVlIjoibEtRbUN5MGZWRjA5dDdVdDVsK2FMQytQM3dlWkZyd1d0cGxYVUM5TGdkUTlzOExGN0tLZTBQS3N1TGNPTk5CSTh3TWlIZXh6aUw1TE5aSnNBeCtiSlVGSFF1VEd3dGpEdXJzclBxTVNqcm5CRjRUOVdQd00xeUxWZVcrcUlnWUFqZ3RZOTZGaXZLaWV2YjFaVTR1c2ZBeTc3WDBFNmkzaE5lN0Ewbmp6citsSzZEU1d5SjJrNXBLNzcrczVOZG5WLytxbThZeFZ6NmtuTHNTNHNHWlVrbExVYkV0ZS8zS3Y5SzM5QlIyOVNkbW9CWHp4WEtuSEgzUkxvUCs5TkpDUXVrcVh0Z3BmaEYzQUNMYklxd0d1RWp3ZVRpY0I5R01kWlIrTllIY2hqY0d4VjQvR2c4SXVpRTVpYVBpRzJmajRaOGQ3UFRIUjhwSmVnQ3BXbStTVTE1SnZvdzZHcFFPbTBkczk3SEtaYVA4N0R6eTBxUWxFRDVPNDY3cDh6b1hEQ2VKK1dXZ01qVVRzZXhuR0xmMGhQQ0I3MC9EbjI2VlpCTkpXSFdSaWN4RTZLbXhId2JqTm9lZ292c3Z6LzZyZDdyeW9KT1NGeEF5UlJrZHg4ZGIwS096dlVuMFAzYnp6SmhSdnlRN0Nvb2VWZmxaSHNNQ2dpaVBUS0lON0tuLzUiLCJtYWMiOiI3ZGYzZDZjZTllNGZiMTM5NDYzNGFkNjY2YWE3MTM4OGMyZjY3NDViNjFkNTQxOTNkNDg5OTI5OGYwNThkNzA0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-888600918\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1472628620 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">COI6IIrAbucReABHBN7o3ZaIzRbwBCI1gfwrcfCo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1472628620\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1030732695 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 16:25:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkROcjZYRjY5d25PcHVsa055ZHEzRkE9PSIsInZhbHVlIjoiWDZXdHd2aU0vbFVDV0hUNTFDSHBWWEw4RmpEYTlzV1JrZS9uY0JQcEs1RldYZEhsdUZXSE9CT2VIYTFOM1V1T0lXY1phRkFZQm5QVmtHZ0N0YmloNC95L2pKbjRibHVMNmp4alNPZTUxZFl6MlVlVUdlcXJhZ3lzb1ZMWEwrcUlrVXFmZGJTbGQxcE5PNUpTMmU0OXVGSXJyNXJESFBhTitDemRYRU9xQit0ekFwOG41b1hFNmtUYmpJV21iTTMwU2prSkN0YS82STFiK3JtSnNDTUpVcjVJYnc2cEx1OTdBdStDUE9DNkczWnl5ZXlvdXcxNmtROHk4cERpUG1RNTNIS1dxMDdKelBQZVRiNGdlTkRueG1tZnczTC9mNkc2U3ZKQlByR2t1YU8xVnR2T01ZZG5JUmx2V01PZndxL0taVEZwYlNEZW9WaGdkUVNZOFVKMkcvUlUwTGlmRzNCRU5SYS9xNHh5QTRXTk85b29tODNGRENSMmpac0JsaDZLM1puMk05Tk50WlUvcldTZ2JIVWpibTV6UjQvTHlvMThHdk01dUh3MTRsM2g5UWNqUzNqMGVoUVVob3hTb3hFK3U2ZjU3OW4wajB5Z2ZlSmNVZHdtc0lFdURqOFl2T1RFd0U5cGZDQitVMm5zQktYWkIyYzRLZWFLUEd0ZFdhcjgiLCJtYWMiOiIwYWIwYjNiYjhkZGQ3ZDMwMWRiMWJmZjY3MmIxOTA0NDkzMmVkMWYzYWRkMmQ2ZTJiNDc4NDY5M2U5ODA4Y2M0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 18:25:15 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlpnUi92VXpnUEtHMjhVOE5DTTgzOWc9PSIsInZhbHVlIjoiS0I5NWpraGhXSWdTMVI3KzEyQjVkZXBjS1pGVTR3SUIyZy9mV0h0Rmo3TW5sREdhTWJzdXcvQjB2ZWg1YW01blNsa2dPaVpPaXJsVjJsVGN4b0QwL1lJMExBUDVoRUJ4dkhHVlhRNWNjU0VtblRXNUU5VGtQNmtyZnRRemRCVFRvMXpramNiWmsvNGV2NU5TMkovYVBqb0hsSXJCVlFBdUdIeGRwb1IzQmZOSjlZemtNYWo2L1E4ajRCTXBoMGlLZG91OU5FNUJpenFISE1IWm5TK1ZmSzVQeEl3b2J6ODJvYkZtckVPclhkZmlmN1ZYeW5RY2lVUlRlYm1sK3VRYWs1QnQ2aHBPUVNJOVRmRDd1TFNEUkJMSlc3a3BGdWtudFFJc2FNUGxHbDQrZS9RWXkvK1VzVUJ0ZVorVHpTTW90WXNoMnc0Y3RDZDlrNmRpT2pGVm50Vk03QXVuejdNaTRpS1QzUGpFaVQ3eWh6V1hpbnhabmI1Y1hqVjI4VEk2ZUpFOGtLUHZEWGo3N0daR3g5T3lLais4bUl6N3NmK0VkejI1TEhmNUdtbnZWNFNoS3dQbXIvWncyRDZhdGhFMlJaRWc1dGNWM2pXUVZvQm8wM1hJYkxyY2JDcG10UGlEd0FEYTQ4THJNeHM0UlBxM1dVT1NQOHV5REZqRDNockciLCJtYWMiOiIzMWU1YTdlMzgzZTgwNzY0NDQ2OTVkNDIyNmM1YjNmZDIwOTgxYjA5YWMyNTk4Mzc5MTQ0NWM3ODdkMDkyNzZkIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 18:25:15 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkROcjZYRjY5d25PcHVsa055ZHEzRkE9PSIsInZhbHVlIjoiWDZXdHd2aU0vbFVDV0hUNTFDSHBWWEw4RmpEYTlzV1JrZS9uY0JQcEs1RldYZEhsdUZXSE9CT2VIYTFOM1V1T0lXY1phRkFZQm5QVmtHZ0N0YmloNC95L2pKbjRibHVMNmp4alNPZTUxZFl6MlVlVUdlcXJhZ3lzb1ZMWEwrcUlrVXFmZGJTbGQxcE5PNUpTMmU0OXVGSXJyNXJESFBhTitDemRYRU9xQit0ekFwOG41b1hFNmtUYmpJV21iTTMwU2prSkN0YS82STFiK3JtSnNDTUpVcjVJYnc2cEx1OTdBdStDUE9DNkczWnl5ZXlvdXcxNmtROHk4cERpUG1RNTNIS1dxMDdKelBQZVRiNGdlTkRueG1tZnczTC9mNkc2U3ZKQlByR2t1YU8xVnR2T01ZZG5JUmx2V01PZndxL0taVEZwYlNEZW9WaGdkUVNZOFVKMkcvUlUwTGlmRzNCRU5SYS9xNHh5QTRXTk85b29tODNGRENSMmpac0JsaDZLM1puMk05Tk50WlUvcldTZ2JIVWpibTV6UjQvTHlvMThHdk01dUh3MTRsM2g5UWNqUzNqMGVoUVVob3hTb3hFK3U2ZjU3OW4wajB5Z2ZlSmNVZHdtc0lFdURqOFl2T1RFd0U5cGZDQitVMm5zQktYWkIyYzRLZWFLUEd0ZFdhcjgiLCJtYWMiOiIwYWIwYjNiYjhkZGQ3ZDMwMWRiMWJmZjY3MmIxOTA0NDkzMmVkMWYzYWRkMmQ2ZTJiNDc4NDY5M2U5ODA4Y2M0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 18:25:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlpnUi92VXpnUEtHMjhVOE5DTTgzOWc9PSIsInZhbHVlIjoiS0I5NWpraGhXSWdTMVI3KzEyQjVkZXBjS1pGVTR3SUIyZy9mV0h0Rmo3TW5sREdhTWJzdXcvQjB2ZWg1YW01blNsa2dPaVpPaXJsVjJsVGN4b0QwL1lJMExBUDVoRUJ4dkhHVlhRNWNjU0VtblRXNUU5VGtQNmtyZnRRemRCVFRvMXpramNiWmsvNGV2NU5TMkovYVBqb0hsSXJCVlFBdUdIeGRwb1IzQmZOSjlZemtNYWo2L1E4ajRCTXBoMGlLZG91OU5FNUJpenFISE1IWm5TK1ZmSzVQeEl3b2J6ODJvYkZtckVPclhkZmlmN1ZYeW5RY2lVUlRlYm1sK3VRYWs1QnQ2aHBPUVNJOVRmRDd1TFNEUkJMSlc3a3BGdWtudFFJc2FNUGxHbDQrZS9RWXkvK1VzVUJ0ZVorVHpTTW90WXNoMnc0Y3RDZDlrNmRpT2pGVm50Vk03QXVuejdNaTRpS1QzUGpFaVQ3eWh6V1hpbnhabmI1Y1hqVjI4VEk2ZUpFOGtLUHZEWGo3N0daR3g5T3lLais4bUl6N3NmK0VkejI1TEhmNUdtbnZWNFNoS3dQbXIvWncyRDZhdGhFMlJaRWc1dGNWM2pXUVZvQm8wM1hJYkxyY2JDcG10UGlEd0FEYTQ4THJNeHM0UlBxM1dVT1NQOHV5REZqRDNockciLCJtYWMiOiIzMWU1YTdlMzgzZTgwNzY0NDQ2OTVkNDIyNmM1YjNmZDIwOTgxYjA5YWMyNTk4Mzc5MTQ0NWM3ODdkMDkyNzZkIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 18:25:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1030732695\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6Ild1N2JBYjlWemdxYnI1bkswNlJQdFE9PSIsInZhbHVlIjoiazE4Z3o4RmU0TnNjTnNSbzVUcGlzQT09IiwibWFjIjoiZmU5NTlhNTJmMzU5MzI0NzQ4ZTliMzZlNzEyNTRiMzUwMDcyOGQ5NGMxNWUyOTI3OGI4NjZjNTk5Yjg5ZDg5MSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}