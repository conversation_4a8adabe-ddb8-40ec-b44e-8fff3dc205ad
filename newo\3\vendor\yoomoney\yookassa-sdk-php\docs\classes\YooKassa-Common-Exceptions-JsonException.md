# [YooKassa API SDK](../home.md)

# Class: \YooKassa\Common\Exceptions\JsonException
### Namespace: [\YooKassa\Common\Exceptions](../namespaces/yookassa-common-exceptions.md)
---

---
### Constants
* No constants found

---
### Properties
| Visibility | Name | Flag | Summary |
| ----------:| ---- | ---- | ------- |
| public | [$errorLabels](../classes/YooKassa-Common-Exceptions-JsonException.md#property_errorLabels) |  |  |

---
### Methods
| Visibility | Name | Flag | Summary |
| ----------:| ---- | ---- | ------- |
| public | [__construct()](../classes/YooKassa-Common-Exceptions-JsonException.md#method___construct) |  |  |

---
### Details
* File: [lib/Common/Exceptions/JsonException.php](../../lib/Common/Exceptions/JsonException.php)
* Package: Default
* Class Hierarchy: 
  * [\UnexpectedValueException](\UnexpectedValueException)
  * \YooKassa\Common\Exceptions\JsonException

---
## Properties
<a name="property_errorLabels"></a>
#### public $errorLabels : array
---
**Type:** <a href="../array"><abbr title="array">array</abbr></a>

**Details:**



---
## Methods
<a name="method___construct" class="anchor"></a>
#### public __construct() : mixed

```php
public __construct(mixed $message = &#039;&#039;, mixed $code, mixed $previous = null) : mixed
```

**Details:**
* Inherited From: [\YooKassa\Common\Exceptions\JsonException](../classes/YooKassa-Common-Exceptions-JsonException.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">mixed</code> | message  |  |
| <code lang="php">mixed</code> | code  |  |
| <code lang="php">mixed</code> | previous  |  |

**Returns:** mixed - 



---

### Top Namespaces

* [\YooKassa](../namespaces/yookassa.md)

---

### Reports
* [Errors - 0](../reports/errors.md)
* [Markers - 0](../reports/markers.md)
* [Deprecated - 25](../reports/deprecated.md)

---

This document was automatically generated from source code comments on 2024-07-02 using [phpDocumentor](http://www.phpdoc.org/)

&copy; 2024 YooMoney