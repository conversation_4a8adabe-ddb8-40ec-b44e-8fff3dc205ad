<?php

namespace WhichBrowser\Data;

use WhichBrowser\Constants\DeviceType;

DeviceModels::$WP_MODELS = [
    'Allegro'                                   => [ 'Acer', 'Allegro' ],
    'M310'                                      => [ 'Acer', 'Allegro' ],
    'S58'                                       => [ 'Acer', 'Jade Primo' ],
    'M220!'                                     => [ 'Acer', 'Liquid' ],
    'FierceXL'                                  => [ 'Alcatel', 'One Touch Fierce XL' ],
    'ONE TOUCH 5040X'                           => [ 'Alcatel', 'One Touch View' ],
    'IDOL 4 PRO'                                => [ 'Alcatel', 'Idol 4 Pro' ],
    'IDOL 4S with Windows 10'                   => [ 'Alcatel', 'Idol 4S' ],
    'Impera I'                                  => [ 'Allview', 'Impera i' ],
    'Impera M'                                  => [ 'Allview', 'Impera M' ],
    'Impera S'                                  => [ 'Allview', 'Impera S' ],
    'W1i'                                       => [ 'Allview', 'W1i' ],
    '40 Cesium'                                 => [ 'Archos', '40 Cesium' ],
    'AC50CE'                                    => [ 'Archos', '50 Cesium' ],
    '- BUSH Windows Phone'                      => [ 'Argos', 'BUSH Windows Phone' ],
    'Galaxy6'                                   => [ 'Asus', 'Galaxy 6' ],
    'E600'                                      => [ 'Asus', 'E600' ],
    'WIN HD LTE'                                => [ 'BLU', 'Win HD' ],
    'WIN HD W510l'                              => [ 'BLU', 'Win HD' ],
    'WIN HD W510u'                              => [ 'BLU', 'Win HD' ],
    'WIN JR LTE'                                => [ 'BLU', 'Win JR' ],
    'WIN JR W410a'                              => [ 'BLU', 'Win JR' ],
    'WIN JR W410i'                              => [ 'BLU', 'Win JR' ],
    'WIN JR W410l'                              => [ 'BLU', 'Win JR' ],
    'WIN JR W410u'                              => [ 'BLU', 'Win JR' ],
    'BP30.BM180'                                => [ 'Bluebird', 'Pidion' ],
    'WIN 400'                                   => [ 'Celkon', 'Win 400' ],
    'Alpha Neon'                                => [ 'Cherry Mobile', 'Alpha Neon' ],
    'ALPHA PRIME 5'                             => [ 'Cherry Mobile', 'Alpha Prime 5' ],
    'MOBILE Alpha Luxe'                         => [ 'Cherry Mobile', 'Alpha Luxe' ],
    'CHERRY-MOBILE Alpha Luxe'                  => [ 'Cherry Mobile', 'Alpha Luxe' ],
    'MOBILE Alpha Style'                        => [ 'Cherry Mobile', 'Alpha Style' ],
    'MOBILE Alpha View'                         => [ 'Cherry Mobile', 'Alpha View' ],
    'PKT-407'                                   => [ 'Condor', 'Griffe W1' ],
    'T698'                                      => [ 'Cube', 'WP10', DeviceType::TABLET ],
    'Venue Pro'                                 => [ 'Dell', 'Venue Pro' ],
    'Ixion W 5'                                 => [ 'DEXP', 'Ixion W 5' ],
    'IQ400W'                                    => [ 'Fly', 'IQ400W Era Windows' ],
    'IQ500W'                                    => [ 'Fly', 'IQ500W Evo Windows' ],
    'FTJ152E'                                   => [ 'Freetel', 'KATANA 01' ],
    'FTJ152F'                                   => [ 'Freetel', 'KATANA 02' ],
    'IS12T'                                     => [ 'Fujitsu Toshiba', 'IS12T' ],
    'W10'                                       => [ 'GoMobile', 'W10' ],
    'WINJOY'                                    => [ 'Highscreen', 'WinJoy' ],
    'WinWin'                                    => [ 'Highscreen', 'WinWin' ],
    'E260T+'                                    => [ 'Hisense', 'NANA' ],
    'SG 7'                                      => [ 'HIWIKI', 'SG 7' ],
    'DolphinCT50'                               => [ 'Honeywell', 'Dolphin CT50' ],
    '75eL0N'                                    => [ 'Honeywell', 'Dolphin 75e' ],
    'HELSINKI'                                  => [ 'Honeywell', '"Helsinki"' ],
    'HD2'                                       => [ 'HTC', 'HD2' ],
    ';HD2'                                      => [ 'HTC', 'HD2' ],
    'LEO'                                       => [ 'HTC', 'HD2' ],
    'HD2(Leo'                                   => [ 'HTC', 'HD2' ],
    'HD2 LEO'                                   => [ 'HTC', 'HD2' ],
    'HTC HD2'                                   => [ 'HTC', 'HD2' ],
    '(HD7|.*T92(92|95|96))!'                    => [ 'HTC', 'HD7' ],
    'Schubert!'                                 => [ 'HTC', 'HD7' ],
    'Mondrian'                                  => [ 'HTC', 'HD7' ],
    'Gold'                                      => [ 'HTC', 'HD7' ],
    'Touch-IT HD7'                              => [ 'HTC', 'HD7' ],
    '((7 )?Mozart|.*T86(97|98))!'               => [ 'HTC', '7 Mozart' ],
    'PD67100'                                   => [ 'HTC', '7 Mozart' ],
    ';PD67100'                                  => [ 'HTC', '7 Mozart' ],
    'HTC Mozart'                                => [ 'HTC', '7 Mozart' ],
    '7 HTC MOZART'                              => [ 'HTC', '7 Mozart' ],
    'PC93100'                                   => [ 'HTC', '7 Pro' ],
    'MWP6885'                                   => [ 'HTC', '7 Pro' ],
    '(7 Pro|.*T75(75|76))!'                     => [ 'HTC', '7 Pro' ],
    'USCCHTC-PC93100'                           => [ 'HTC', '7 Pro' ],
    'Surround'                                  => [ 'HTC', '7 Surround' ],
    '7 Surround'                                => [ 'HTC', '7 Surround' ],
    '.*T8788!'                                  => [ 'HTC', '7 Surround' ],
    '((7 )?Trophy|.*T8686)!'                    => [ 'HTC', '7 Trophy' ],
    'mwp6985'                                   => [ 'HTC', '7 Trophy' ],
    'htc mpw6958'                               => [ 'HTC', '7 Trophy' ],
    'htcmpw6958'                                => [ 'HTC', '7 Trophy' ],
    'PC40100'                                   => [ 'HTC', '7 Trophy' ],
    'PC40200'                                   => [ 'HTC', '7 Trophy' ],
    'Touch-IT Trophy'                           => [ 'HTC', '7 Trophy' ],
    'Spark'                                     => [ 'HTC', '7 Trophy' ],
    'HTC 7 Trophy'                              => [ 'HTC', '7 Trophy' ],
    'HTC Trophy'                                => [ 'HTC', '7 Trophy' ],
    '(Radar|.*C110e)!'                          => [ 'HTC', 'Radar' ],
    'Omega'                                     => [ 'HTC', 'Radar' ],
    'Mazaa'                                     => [ 'HTC', 'Mazaa' ],              // Never released
    'Titan'                                     => [ 'HTC', 'Titan' ],
    '.*X310e!'                                  => [ 'HTC', 'Titan' ],
    'PI39100'                                   => [ 'HTC', 'Titan' ],
    'Eternity'                                  => [ 'HTC', 'Titan' ],
    'Ultimate'                                  => [ 'HTC', 'Titan' ],
    'PI86100'                                   => [ 'HTC', 'Titan II' ],
    '8X'                                        => [ 'HTC', '8X' ],
    'HTC6690LVW'                                => [ 'HTC', '8X' ],
    'HTC6990LVW'                                => [ 'HTC', '8X' ],
    'HTC6990LVW 4G'                             => [ 'HTC', '8X' ],
    '6990LVW'                                   => [ 'HTC', '8X' ],
    'RIO U'                                     => [ 'HTC', '8X' ],
    'ACD U'                                     => [ 'HTC', '8X' ],
    'Accord'                                    => [ 'HTC', '8X' ],
    'C620(d|e|t)!'                              => [ 'HTC', '8X' ],
    'C625(a|b)!'                                => [ 'HTC', '8X' ],
    '8X by HTC'                                 => [ 'HTC', '8X' ],
    'Windows Phone 8X by HTC!'                  => [ 'HTC', '8X' ],
    'WindowsPhone8Xby'                          => [ 'HTC', '8X' ],
    'PM23300'                                   => [ 'HTC', '8X' ],
    'htc 8x'                                    => [ 'HTC', '8X' ],
    'A620(b|d|e|m|t)!'                          => [ 'HTC', '8S' ],
    '8S'                                        => [ 'HTC', '8S' ],
    '8S by HTC'                                 => [ 'HTC', '8S' ],
    'Windows Phone 8S by HTC!'                  => [ 'HTC', '8S' ],
    'WindowsPhone8Sby'                          => [ 'HTC', '8S' ],
    'HTCPO881'                                  => [ 'HTC', '8XT' ],
    'HTC6995LVW'                                => [ 'HTC', 'One (M8) for Windows' ],
    '0P6B140'                                   => [ 'HTC', 'One (M8) for Windows' ],
    '0P6B180'                                   => [ 'HTC', 'One (M8) for Windows' ],
    'HUAWEI Ascend W1!'                         => [ 'Huawei', 'Ascend W1' ],
    'HUAWEI ?W1-(C00|U00|U34)!'                 => [ 'Huawei', 'Ascend W1' ],
    'W1-(C00|U00|U34)!'                         => [ 'Huawei', 'Ascend W1' ],
    'H883G'                                     => [ 'Huawei', 'Ascend W1' ],
    'HUAWEI ?W2-(T00|T01|U00|U051)!'            => [ 'Huawei', 'Ascend W2' ],
    'W2-(T00|T01|U00|U051)!'                    => [ 'Huawei', 'Ascend W2' ],
    '4Afrika'                                   => [ 'Huawei', '4Afrika' ],
    'Andi4L'                                    => [ 'iBall', 'Andi 4L Pulse' ],
    'M1010'                                     => [ 'Ingram Micro', 'M1010' ],
    'Titanium Wind W4'                          => [ 'Karbonn', 'Titanium Wind W4' ],
    'W4'                                        => [ 'Karbonn', 'Titanium Wind W4' ],
    'Thunder 340W'                              => [ 'KAZAM', 'Thunder 340W' ],
    'Thunder 450W'                              => [ 'KAZAM', 'Thunder 450W' ],
    'SOUL2'                                     => [ 'Kruger&Matz', 'Soul 2' ],
    'iris Win 1'                                => [ 'Lava', 'Iris Win 1' ],
    'Win1'                                      => [ 'Lava', 'Iris Win 1' ],
    '- Lazer SMART WIND'                        => [ 'Lazer', 'Smart Wind' ],           // By Archos
    'GW910'                                     => [ 'LG', 'Optimus 7' ],
    'LG-E740!'                                  => [ 'LG', 'Miracle' ],
    'LG-E-?900!'                                => [ 'LG', 'Optimus 7' ],
    'LG-E906'                                   => [ 'LG', 'Jil Sander' ],
    'LG-C900!'                                  => [ 'LG', 'Optimus 7Q' ],
    'VW820'                                     => [ 'LG', 'Lancet' ],
    'Quantum'                                   => [ 'LG', 'Quantum' ],
    'Harley Davidson'                           => [ 'NGM', 'Harley Davidson' ],
    'Harley-Davidson'                           => [ 'NGM', 'Harley Davidson' ],
    'NEO'                                       => [ 'NuAns', 'Neo' ],
    'MegaFon SP-W1'                             => [ 'Мегафон', 'SP-W1' ],
    'MWP-47!'                                   => [ 'Miia', 'Windows Phone 4.7" iimotion' ],
    'W121'                                      => [ 'Micromax', 'Canvas Win W121' ],
    'MICROMAX-W121'                             => [ 'Micromax', 'Canvas Win W121' ],
    'M5w'                                       => [ 'Mint', 'M5 Windows' ],
    'TC70'                                      => [ 'Motorola', 'TC70' ],              // Motorola Solutions
    'MouseComputer MADOSMA Q501'                => [ 'MouseComputer', 'MADOSMA Q501' ],

    '(Lumia ?|Nokia ?)?[0-9]{3,4}!!' => [
        'Lumia 430!'                                => [ 'Microsoft', 'Lumia 430' ],
        'Lumia 435!'                                => [ 'Microsoft', 'Lumia 435' ],
        '(Lumia ?|Nokia ?)?505!'                    => [ 'Nokia', 'Lumia 505' ],
        '(Lumia ?|Nokia ?)?510!'                    => [ 'Nokia', 'Lumia 510' ],
        '(Lumia ?|Nokia ?)?520!'                    => [ 'Nokia', 'Lumia 520' ],
        '(Lumia ?|Nokia ?)?521!'                    => [ 'Nokia', 'Lumia 521' ],
        '(Lumia ?|Nokia ?)?525!'                    => [ 'Nokia', 'Lumia 525' ],
        '(Lumia ?|Nokia ?)?526!'                    => [ 'Nokia', 'Lumia 526' ],
        '(Lumia ?|Nokia ?)?530!'                    => [ 'Nokia', 'Lumia 530' ],
        'Lumia 532!'                                => [ 'Microsoft', 'Lumia 532' ],
        'Lumia 535!'                                => [ 'Microsoft', 'Lumia 535' ],
        'Lumia 540!'                                => [ 'Microsoft', 'Lumia 540' ],
        'Lumia 550!'                                => [ 'Microsoft', 'Lumia 550' ],
        '(Lumia ?|Nokia ?)?610!'                    => [ 'Nokia', 'Lumia 610' ],
        '(Lumia ?|Nokia ?)?620!'                    => [ 'Nokia', 'Lumia 620' ],
        '(Lumia ?|Nokia ?)?625!'                    => [ 'Nokia', 'Lumia 625' ],
        '(Lumia ?|Nokia ?)?630!'                    => [ 'Nokia', 'Lumia 630' ],
        '(Lumia ?|Nokia ?)?635!'                    => [ 'Nokia', 'Lumia 635' ],
        '(Lumia ?|Nokia ?)?636!'                    => [ 'Nokia', 'Lumia 636' ],
        'Lumia 638'                                 => [ 'Nokia', 'Lumia 638' ],
        '(Lumia ?|Nokia ?)?640 XL!'                 => [ 'Microsoft', 'Lumia 640 XL' ],
        '(Lumia ?|Nokia ?)?640!'                    => [ 'Microsoft', 'Lumia 640' ],
        'Lumia 650!'                                => [ 'Microsoft', 'Lumia 650' ],
        '(Lumia ?|Nokia ?)?710!'                    => [ 'Nokia', 'Lumia 710' ],
        '(Lumia ?|Nokia ?)?719!'                    => [ 'Nokia', 'Lumia 719' ],
        '(Lumia ?|Nokia ?)?720!'                    => [ 'Nokia', 'Lumia 720' ],
        'Lumia 730!'                                => [ 'Nokia', 'Lumia 730' ],
        'Lumia 735!'                                => [ 'Nokia', 'Lumia 735' ],
        '(Lumia ?|Nokia ?)?800!'                    => [ 'Nokia', 'Lumia 800' ],
        '(Lumia ?|Nokia ?)?810!'                    => [ 'Nokia', 'Lumia 810' ],
        '(Lumia ?|Nokia ?)?820!'                    => [ 'Nokia', 'Lumia 820' ],
        '(Lumia ?|Nokia ?)?822!'                    => [ 'Nokia', 'Lumia 822' ],
        '(Lumia ?|Nokia ?)?830!'                    => [ 'Nokia', 'Lumia 830' ],
        'Lumia 850!'                                => [ 'Microsoft', 'Lumia 850' ],
        '(Lumia ?|Nokia ?)?900!'                    => [ 'Nokia', 'Lumia 900' ],
        '(Lumia ?|Nokia ?)?920!'                    => [ 'Nokia', 'Lumia 920' ],
        '(Lumia ?|Nokia ?)?925!'                    => [ 'Nokia', 'Lumia 925' ],
        '(Lumia ?|Nokia ?)?928!'                    => [ 'Nokia', 'Lumia 928' ],
        '(Lumia ?|Nokia ?)?929!'                    => [ 'Nokia', 'Lumia 929 Icon' ],
        '(Lumia ?|Nokia ?)?930!'                    => [ 'Nokia', 'Lumia 930' ],
        'Lumia 950 XL!'                             => [ 'Microsoft', 'Lumia 950 XL' ],
        'Lumia 950!'                                => [ 'Microsoft', 'Lumia 950' ],
        '(Lumia ?|Nokia ?)?909!'                    => [ 'Nokia', 'Lumia 1020' ],           // 909 was the original name of the 1020
        '(Lumia ?|Nokia ?)?1020!'                   => [ 'Nokia', 'Lumia 1020' ],
        '(Lumia ?|Nokia ?)?1320!'                   => [ 'Nokia', 'Lumia 1320' ],
        '(Lumia ?|Nokia ?)?1520!'                   => [ 'Nokia', 'Lumia 1520' ],
        'Lumia 1530'                                => [ 'Microsoft', 'Lumia 1530' ],
    ],

    'RM-[0-9]{3,4}!!' => [
        'RM-1099!'                                  => [ 'Microsoft', 'Lumia 430' ],
        'RM-10(68|69|70|71)!'                       => [ 'Microsoft', 'Lumia 435' ],
        'RM-1114!'                                  => [ 'Microsoft', 'Lumia 435' ],
        'RM-9(13|14|15)!'                           => [ 'Nokia', 'Lumia 520' ],
        'RM-917!'                                   => [ 'Nokia', 'Lumia 521' ],
        'RM-998!'                                   => [ 'Nokia', 'Lumia 525' ],
        'RM-997!'                                   => [ 'Nokia', 'Lumia 526' ],
        'RM-10(17|18|19|20)!'                       => [ 'Nokia', 'Lumia 530' ],
        'RM-10(31|32|34)!'                          => [ 'Microsoft', 'Lumia 532' ],
        'RM-1115!'                                  => [ 'Microsoft', 'Lumia 532' ],
        'RM-10(89|90|91|92)!'                       => [ 'Microsoft', 'Lumia 535' ],
        'RM-1141!'                                  => [ 'Microsoft', 'Lumia 540' ],
        'RM-1127!'                                  => [ 'Microsoft', 'Lumia 550' ],
        'RM-846!'                                   => [ 'Nokia', 'Lumia 620' ],
        'RM-9(41|42|43)!'                           => [ 'Nokia', 'Lumia 625' ],
        'RM-9(76|77|78|79)!'                        => [ 'Nokia', 'Lumia 630' ],
        'RM-9(74|75)!'                              => [ 'Nokia', 'Lumia 635' ],
        'RM-1078!'                                  => [ 'Nokia', 'Lumia 635' ],
        'RM-1027!'                                  => [ 'Nokia', 'Lumia 636' ],
        'RM-1010!'                                  => [ 'Nokia', 'Lumia 638' ],
        'RM-10(72|74|75|77)!'                       => [ 'Microsoft', 'Lumia 640' ],
        'RM-11(09|13)!'                             => [ 'Microsoft', 'Lumia 640' ],
        'RM-10(63|64|65|66|67|73|96)!'              => [ 'Microsoft', 'Lumia 640 XL' ],
        'RM-11(52|54)!'                             => [ 'Microsoft', 'Lumia 650' ],
        'RM-8(85|87)!'                              => [ 'Nokia', 'Lumia 720' ],
        'RM-1040!'                                  => [ 'Nokia', 'Lumia 730' ],
        'RM-1038!'                                  => [ 'Nokia', 'Lumia 735' ],
        'RM-878!'                                   => [ 'Nokia', 'Lumia 810' ],
        'RM-8(24|25|26)!'                           => [ 'Nokia', 'Lumia 820' ],
        'RM-845!'                                   => [ 'Nokia', 'Lumia 822' ],
        'RM-9(83|84|85)!'                           => [ 'Nokia', 'Lumia 830' ],
        'RM-8(20|21|22)!'                           => [ 'Nokia', 'Lumia 920' ],
        'RM-867!'                                   => [ 'Nokia', 'Lumia 920' ],
        'RM-8(92|93)!'                              => [ 'Nokia', 'Lumia 925' ],
        'RM-910!'                                   => [ 'Nokia', 'Lumia 925' ],
        'RM-955!'                                   => [ 'Nokia', 'Lumia 925T' ],
        'RM-860!'                                   => [ 'Nokia', 'Lumia 928' ],
        'RM-927!'                                   => [ 'Nokia', 'Lumia 929 Icon' ],
        'RM-10(45|87)!'                             => [ 'Nokia', 'Lumia 930' ],
        'RM-11(04|18)!'                             => [ 'Microsoft', 'Lumia 950' ],
        'RM-1085!'                                  => [ 'Microsoft', 'Lumia 950 XL' ],
        'RM-1116!'                                  => [ 'Microsoft', 'Lumia 950 XL' ],
        'RM-8(75|76|77)!'                           => [ 'Nokia', 'Lumia 1020' ],
        'RM-9(94|95|96)!'                           => [ 'Nokia', 'Lumia 1320' ],
        'RM-9(37|38|39|40)!'                        => [ 'Nokia', 'Lumia 1520' ],
    ],

    'id[0-9]{3,3}!!' => [
        'id102!'                                    => [ 'Nokia', 'Lumia 822' ],
        'id308!'                                    => [ 'Nokia', 'Lumia 1520' ],
        'id313!'                                    => [ 'Nokia', 'Lumia 635' ],
        'id316!'                                    => [ 'Nokia', 'Lumia 530' ],
    ],

    'SeaRay'                                    => [ 'Nokia', 'Lumia 800' ],
    'Semaphore'                                 => [ 'Nokia', '"Juggernaut Semaphore"' ],
    'Surface Phone'                             => [ 'Microsoft', 'Surface Phone' ],
    'ONIX AWP4-215'                             => [ 'ONYX', 'AWP4-215' ],
    'QSMART STORM W408'                         => [ 'Q-Mobile', 'Q-Smart Storm W408' ],
    'QSMART DREAM W473'                         => [ 'Q-Mobile', 'Q-Smart Dream W473' ],
    'QSMART STORM W510'                         => [ 'Q-Mobile', 'Q-Smart Storm W510' ],
    'FZ-E1'                                     => [ 'Panasonic', 'Toughpad FZ-E1' ],
    'PSP8400DUO'                                => [ 'Prestigio', 'MultiPhone 8400 Duo' ],
    'PSP8500DUO'                                => [ 'Prestigio', 'MultiPhone 8500 Duo' ],
    'PRESTIGIO-PSP8500DUO'                      => [ 'Prestigio', 'MultiPhone 8500 Duo' ],

    'GT-[IS][0-9]{4,4}!!' => [
        'GT-I8350!'                                 => [ 'Samsung', 'Omnia W' ],
        'GT-i8700'                                  => [ 'Samsung', 'Omnia 7' ],
        'GT-I8710'                                  => [ 'Samsung', 'Ativ S' ],
        'GT-I8750'                                  => [ 'Samsung', 'Ativ S' ],
        'GT-S7530!'                                 => [ 'Samsung', 'Omnia M' ],
    ],

    'SGH-[IT][0-9]{3,3}!!' => [
        'SGH-I187'                                  => [ 'Samsung', 'Ativ S Neo' ],
        'SGH-i667'                                  => [ 'Samsung', 'Focus 2' ],
        'SGH-i677'                                  => [ 'Samsung', 'Focus Flash' ],
        'SGH-i707'                                  => [ 'Samsung', 'Taylor' ],         // Developer phone - never released
        'SGH-i917!'                                 => [ 'Samsung', 'Focus' ],
        'SGH-i937!'                                 => [ 'Samsung', 'Focus S' ],
        'SGH-T899M'                                 => [ 'Samsung', 'Ativ S' ],
    ],

    'SM-W750V'                                  => [ 'Samsung', 'Ativ SE' ],
    'SPH-I800'                                  => [ 'Samsung', 'Ativ S Neo' ],
    'SCH-R860U'                                 => [ 'Samsung', 'Ativ Odyssey' ],
    'SCH-I930'                                  => [ 'Samsung', 'Ativ Odyssey' ],
    'Ativ S'                                    => [ 'Samsung', 'Ativ S' ],
    'ATIV S Neo'                                => [ 'Samsung', 'Ativ S Neo' ],
    'CETUS'                                     => [ 'Samsung', 'Focus' ],
    'I917'                                      => [ 'Samsung', 'Focus' ],
    'Focus i917!'                               => [ 'Samsung', 'Focus' ],
    'FOCUS S'                                   => [ 'Samsung', 'Focus S' ],
    'OM(NI|IN)A ?7!'                            => [ 'Samsung', 'Omnia 7' ],
    'Omnia W'                                   => [ 'Samsung', 'Omnia W' ],
    'Haden'                                     => [ 'Samsung', 'Omnia W' ],
    'Taylor'                                    => [ 'Samsung', 'Taylor' ],
    'S606'                                      => [ 'TCL', 'Horizon S606' ],
    'TSUNAGI'                                   => [ 'Toshiba', 'Tsunagi' ],
    'TG01'                                      => [ 'Toshiba', 'TG01' ],
    'WP 4.7'                                    => [ 'Trekstor', 'WP 4.7' ],
    'MI4'                                       => [ 'Xiaomi', 'Mi 4' ],
    'Win-Q900S'                                 => [ 'XOLO', 'Win Q900s' ],
    'Win Q1000'                                 => [ 'XOLO', 'Win Q1000' ],
    'WinQ1000'                                  => [ 'XOLO', 'Win Q1000' ],
    'Billy 4'                                   => [ 'Yezz', 'Billy 4' ],
    'Billy 4.7'                                 => [ 'Yezz', 'Billy 4.7' ],
    '47'                                        => [ 'Yezz', 'Monaco 47' ],
    'USCCN859'                                  => [ 'ZTE', 'N859 Render' ],
    'N880e!'                                    => [ 'ZTE', 'N880e' ],
    'V965W'                                     => [ 'ZTE', 'V965W Tania' ],
    'Tania'                                     => [ 'ZTE', 'V965W Tania' ],


    'Microsoft!'                                => [ null, null ],
    'Windows!'                                  => [ null, null ],
    'Virtual!'                                  => [ null, null ],
    'Android!'                                  => [ null, null ],
    'Linux!'                                    => [ null, null ],
    '<model>!'                                  => [ null, null ],
    'XXX!'                                      => [ null, null ],
    'InfoPath!'                                 => [ null, null ],
];
