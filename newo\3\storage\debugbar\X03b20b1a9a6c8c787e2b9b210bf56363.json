{"__meta": {"id": "X03b20b1a9a6c8c787e2b9b210bf56363", "datetime": "2025-06-08 16:24:01", "utime": **********.127175, "method": "GET", "uri": "/customer/check/warehouse?customer_id=7&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749399840.568788, "end": **********.127197, "duration": 0.5584089756011963, "duration_str": "558ms", "measures": [{"label": "Booting", "start": 1749399840.568788, "relative_start": 0, "end": **********.044362, "relative_end": **********.044362, "duration": 0.4755740165710449, "duration_str": "476ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.044381, "relative_start": 0.47559285163879395, "end": **********.127199, "relative_end": 1.9073486328125e-06, "duration": 0.08281803131103516, "duration_str": "82.82ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45276744, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01905, "accumulated_duration_str": "19.05ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.084564, "duration": 0.0176, "duration_str": "17.6ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 92.388}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.113371, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 92.388, "width_percent": 4.094}, {"sql": "select * from `customers` where `customers`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.118077, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "ty", "start_percent": 96.483, "width_percent": 3.517}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-836453719 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-836453719\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1248049182 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1248049182\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-593373073 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-593373073\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-554934244 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=u1db3a%7C1749399485619%7C2%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IldLVnNVc1VabU9KcW4zNnl5TDluM0E9PSIsInZhbHVlIjoiVXZwMmdiWEllejhCUFZJZHNmam1EVzdNQ213bzRqNTBnUks0WTBUZ0FDRlhnYzRZYXlQVVFuMmNZZk9kVlZkT2ZiOUJGMlNFeTFRNG5iSHFvcUExZjNGUWNvNW9QRTFibk5pQUVCOXF1L0VUVWNpSGRPdnIrUnZYUENsYzNTSmEvdFcvNWowc3pxcWc0NEJYMXBzaW13dWg1dktKWXpMRE5nODNKZWZpRzEva1kyOVBvSjdtN1U1akdhZGd6MnNDbDR3cHZCMzY1alI3cGFIZXNYR1VDendYamJtTENrNEE1RnpTUU9MY05nSXJScHRHVGRBZWVLWGF5RXRMV0c2bVZ6UFBlbXliMll1cXBMdU1YNVlVR0dhSXdIZmNBTzNoUVlKeEd3akkvbTNaTzRmOXBsbVFXZnFTVGsrOS9FZndBczZyc0VyelFyYSs1N1hleGdldG9RUFRpZ29BQUZBRU44ZnNDSVlkaTNFSGQzR2UvWlBUNW5TK3NJUXdRcy9yaFRIQW5qMjlOelAvOTQ1NnAxZWFaREh0aERnanBUOUFlSmpGYm1NUWRESFdQQ1FVcUZ4RmRsZVlBcFp2eHRwdFRFV25mcFFZWjZveHBrby8rUHNReVFrTVE1cVFhN2lvQzBscU5UeFFsTmlUeUl4Y0k2RlpXOE5QVWJ4VTZqUzciLCJtYWMiOiI1NTg3ZTY1M2FjNmU3MmUyMzkwZjcyOTI3MzI2M2E1MzNiZjQ2YzY1MmJlNzJkYjg4ZWMzMzNkNzI3MjdjMmY5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InFWbkZ0bzJoSVZiNThBL05rakczMnc9PSIsInZhbHVlIjoiZ053VFlsd0hJeDl5RngrU1l4aTAzaUZpMTUzVVM0WWQxVXNJRjZDcWNhSmIvSjFHelBXMS9lOXVmVm9UMThLYm83UEozYWl3dlpqQ01XUWM1UzZ2RVVXQWUzMFp1azZpN0g3MEVHUXdDek5KOWZDS09iaFpncFZlYVZxeElHc3NjWFI0S1BYbnRHbjJzckcyN09vZnVOMjNSWDl5TEtOQkNnRi9acXg0NFhUK1I1dWhKbm4vVmJCc1VYTEcrWm1ONWJYL0RTaUp4YVNCbXNtM3lvNzdIaG9sWURPaEZBbytZclQ5eXZVMnV5MnZEdkZZVGRIcys4RkhEWHdheGp6OU1aVmQxTGdXdFVBYTMycTNNSFNicktzd2VkTDRlYVcwUzdQaUpoMHdzZEl1KzExRmRVMHR4bC92QmRrZEI0ZW1ST3pPN1c3aFVVbTJLZVZqZFhGRGk5NE9sRVRsUzRaVmFDV2lneVg1djB4UzdvbG44RVova1BVRks2cGtwSVVxQXRCUmsyT0JsZDVBT3M0cXdTUmVQU3MwVGp6bGc0c0VsT3E2Q2hwU0FIb0ZhQnpRNWJEdmZheU5RRVBwVDA4YzdmZWQwcURCK2F0QWY3THhHaDRPbHFFbHlBNXIzN3dnWTNoVGR6U1JOaUtqU2FwTlhMRU92d3RuMzg1ZHBWQTUiLCJtYWMiOiJkMmQwYjAxZDBmZGY4ZGIxYjc5NDdmYTIzMmRhM2EzNTNlMjkyZGQ5ZDcyZTgxYmFkMzU3YjBmYTRkZDg1YzZkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-554934244\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xNV7SvMX3AgbiuDVO62eNXfryfZD4KbWr3rEIkXt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1315782806 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 16:24:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImxwcEZncjlNRWl2aUZoaTlXbGpWNHc9PSIsInZhbHVlIjoiNmdORGJQVFE4aEJxTnhWNlpGa2d3OUFWT0lLdkltYlR6OTBXeTlab0NzNWJkaUg5akRGeHNwaXBISU1sTTRVRm9CTG8zQmlyZmgyYkdyMkxCNGdIaUNaRURIUjNXaDZYU09zem5pTSs3YmY5ZVBrcVpHS3pqOEdWKzVObFR0M09QSjhZN1dVUzBxckZyc0pETW5hVVMzZVN3K0oyeFJiS2ZhdVZoTyt0Mm5SVmpNSmJPeWtPNjVYUkRsL2tEYVRrMFk0TnpDaEVFSmlmRjN1bCs2aUI5bWIzWWtXMU1FTmxhQUZMelMxdDBJWHdiV2poVnhCekRtelNoUzIyT2dXUWpGZVRUdzFkL1BSVUQwbU90TjJZMGFXTHVMaTZQT1UwMjJMYXJnaU9Gb3UzeUsweGtCWGxLbXlHUmNOTmd5bEZ1UFRxVnRoN1lndzR0UWhmMlkvSFBnUkVSdm1wSnU2TStoMmsvL28wcWhtMW1wQUJSYXhHZkhNZ0RQdFJzMGFMeXZlL0hFeE9pZHdGTTFDdkFxNnFmMmJkZWZ6UFhvcHl4Z08wRDlJU3ZNT0ZveTJPc3NhN0ZBckdSVk4zQkdydlhXNDhNTjlCT0Uzd3VRbEVZVzlpMUhWaldEdktLcHZiM0Z1UFQ5VVFqT0NTbStNcXBJTUFwRGxwS0hjYWJwSnIiLCJtYWMiOiJjZTY4NTdjNDJkNzgxMGNlYzFiMDdhNjAyMmRlZmU5ZmJjMzExNTgzMjUyYzY3YzQ2ZmU5YWYzOGEwMzUwYjAyIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 18:24:01 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Inozamp3OXh1aUZzbDJuTVZ1U2pRTHc9PSIsInZhbHVlIjoiRjV2MkZFTDZ2aFM1b2ZQWUtEQmNtUWY5Y0xTV0xBV2hFWHBpNVlTalZQTWozdjg4WmM3NVBuMk5kcmZWQWRYR3ZhR0RIWjBEK3NFblBlSUpHUXQ3V3BYMkdLZ0VNZ2gvdUVnVmZwdHZOOEVjUno4OEU2azNGK2tLYmRnTENTMm9SQUlmNEhjOXdjYThieEl1Q3Y4RDJraXdlK3p2K1FIOU9IdE5SazRndkIzR00zN2xBMFpBZnNMMWg2bk1ZWVB0OVlQOXNvTktsb0VQU3NxWitDVlgrQlVud1YzcEZtbVRVUHZTekxsQTZERWNoTndUMURFcjFKRS93YlQrRmpuWjlHbW1odEZxZjJ3dHd3cEduNkhvbU9LcHdUMTVYWXp2R2dHclNVa05idXd1aFFpR1ZKOTE1SE52NzFVMlhFZHZWTzg0ZUc4TzRkbXU4UWl6VzM0RlFuZFJuNHlrVS9hZFZIRG1rYWlsYnB6WGRtcTNDUkoxcUNGelNWVUt4WHI5ZCt6Mk05b1N2SldaeHV4cTlIMERySlBDTllTak5FRzNCN1hsQ0RYbmZwMHlaRjZiT2o2bE1wNXJURFRXNTJaRlFxaUI2NlAwcEErRFBLSitkZUxlekZvVFRNeWhCS21PMVVzdy82TUp3K3BUNWlNbzZKZWhRMitPcjJwNm5xMnYiLCJtYWMiOiI1OTU5NTgyNjNkNTYxNGU1YzlmMGZhZjIwZGE2YTM1ZDNlNGM5OTk0YTUwZTgwYmI5Mzk1ZTNhMDc4OGUwZWZlIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 18:24:01 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImxwcEZncjlNRWl2aUZoaTlXbGpWNHc9PSIsInZhbHVlIjoiNmdORGJQVFE4aEJxTnhWNlpGa2d3OUFWT0lLdkltYlR6OTBXeTlab0NzNWJkaUg5akRGeHNwaXBISU1sTTRVRm9CTG8zQmlyZmgyYkdyMkxCNGdIaUNaRURIUjNXaDZYU09zem5pTSs3YmY5ZVBrcVpHS3pqOEdWKzVObFR0M09QSjhZN1dVUzBxckZyc0pETW5hVVMzZVN3K0oyeFJiS2ZhdVZoTyt0Mm5SVmpNSmJPeWtPNjVYUkRsL2tEYVRrMFk0TnpDaEVFSmlmRjN1bCs2aUI5bWIzWWtXMU1FTmxhQUZMelMxdDBJWHdiV2poVnhCekRtelNoUzIyT2dXUWpGZVRUdzFkL1BSVUQwbU90TjJZMGFXTHVMaTZQT1UwMjJMYXJnaU9Gb3UzeUsweGtCWGxLbXlHUmNOTmd5bEZ1UFRxVnRoN1lndzR0UWhmMlkvSFBnUkVSdm1wSnU2TStoMmsvL28wcWhtMW1wQUJSYXhHZkhNZ0RQdFJzMGFMeXZlL0hFeE9pZHdGTTFDdkFxNnFmMmJkZWZ6UFhvcHl4Z08wRDlJU3ZNT0ZveTJPc3NhN0ZBckdSVk4zQkdydlhXNDhNTjlCT0Uzd3VRbEVZVzlpMUhWaldEdktLcHZiM0Z1UFQ5VVFqT0NTbStNcXBJTUFwRGxwS0hjYWJwSnIiLCJtYWMiOiJjZTY4NTdjNDJkNzgxMGNlYzFiMDdhNjAyMmRlZmU5ZmJjMzExNTgzMjUyYzY3YzQ2ZmU5YWYzOGEwMzUwYjAyIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 18:24:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Inozamp3OXh1aUZzbDJuTVZ1U2pRTHc9PSIsInZhbHVlIjoiRjV2MkZFTDZ2aFM1b2ZQWUtEQmNtUWY5Y0xTV0xBV2hFWHBpNVlTalZQTWozdjg4WmM3NVBuMk5kcmZWQWRYR3ZhR0RIWjBEK3NFblBlSUpHUXQ3V3BYMkdLZ0VNZ2gvdUVnVmZwdHZOOEVjUno4OEU2azNGK2tLYmRnTENTMm9SQUlmNEhjOXdjYThieEl1Q3Y4RDJraXdlK3p2K1FIOU9IdE5SazRndkIzR00zN2xBMFpBZnNMMWg2bk1ZWVB0OVlQOXNvTktsb0VQU3NxWitDVlgrQlVud1YzcEZtbVRVUHZTekxsQTZERWNoTndUMURFcjFKRS93YlQrRmpuWjlHbW1odEZxZjJ3dHd3cEduNkhvbU9LcHdUMTVYWXp2R2dHclNVa05idXd1aFFpR1ZKOTE1SE52NzFVMlhFZHZWTzg0ZUc4TzRkbXU4UWl6VzM0RlFuZFJuNHlrVS9hZFZIRG1rYWlsYnB6WGRtcTNDUkoxcUNGelNWVUt4WHI5ZCt6Mk05b1N2SldaeHV4cTlIMERySlBDTllTak5FRzNCN1hsQ0RYbmZwMHlaRjZiT2o2bE1wNXJURFRXNTJaRlFxaUI2NlAwcEErRFBLSitkZUxlekZvVFRNeWhCS21PMVVzdy82TUp3K3BUNWlNbzZKZWhRMitPcjJwNm5xMnYiLCJtYWMiOiI1OTU5NTgyNjNkNTYxNGU1YzlmMGZhZjIwZGE2YTM1ZDNlNGM5OTk0YTUwZTgwYmI5Mzk1ZTNhMDc4OGUwZWZlIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 18:24:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1315782806\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-755614978 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-755614978\", {\"maxDepth\":0})</script>\n"}}