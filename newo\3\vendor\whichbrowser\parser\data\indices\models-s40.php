<?php

namespace WhichBrowser\Data;

DeviceModels::$S40_INDEX = array (
  '@10' => 
  array (
    0 => 109,
  ),
  '@11' => 
  array (
    0 => 110,
    1 => 111,
    2 => '111.1',
    3 => 112,
    4 => 114,
  ),
  '@16' => 
  array (
    0 => '1682c',
  ),
  '@20' => 
  array (
    0 => 200,
    1 => 201,
    2 => 202,
    3 => 203,
    4 => 205,
    5 => '205.1',
    6 => '205.3',
    7 => 206,
    8 => '206.1',
    9 => 208,
    10 => '208.1',
    11 => '208.2',
    12 => '208.3',
    13 => '208.4',
    14 => 2055,
    15 => 2060,
  ),
  '@21' => 
  array (
    0 => 210,
    1 => '210.2',
    2 => '210.3',
    3 => '210.4',
    4 => '210.5',
  ),
  '@22' => 
  array (
    0 => '2220s!',
  ),
  '@23' => 
  array (
    0 => '2320c!',
    1 => '2322c!',
    2 => '2323c!',
    3 => '2330c!',
  ),
  '@26' => 
  array (
    0 => '2600c!',
    1 => '2680s!',
    2 => 2690,
    3 => 2692,
  ),
  '@27' => 
  array (
    0 => '2700c!',
    1 => '2710c',
    2 => '2710c-2',
    3 => '2720a!',
    4 => '2730c!',
  ),
  '@30' => 
  array (
    0 => 300,
    1 => 301,
    2 => '301.1',
    3 => 302,
    4 => 303,
    5 => 305,
    6 => 306,
    7 => 308,
    8 => 309,
    9 => 3020,
  ),
  '@31' => 
  array (
    0 => 310,
    1 => 311,
    2 => '3109c',
    3 => '3110c',
    4 => '3120c',
    5 => '3120classic',
  ),
  '@32' => 
  array (
    0 => '3208c',
  ),
  '@35' => 
  array (
    0 => '3500c',
    1 => '3555b',
  ),
  '@36' => 
  array (
    0 => '3600s',
    1 => '3610a',
  ),
  '@37' => 
  array (
    0 => '3710fold',
    1 => '3720c',
  ),
  '@50' => 
  array (
    0 => '5000!',
  ),
  '@51' => 
  array (
    0 => 515,
    1 => '515.2',
    2 => '5130!',
    3 => '5130c!',
    4 => 5132,
  ),
  '@52' => 
  array (
    0 => '5200!',
    1 => '5220!',
  ),
  '@53' => 
  array (
    0 => '5300!',
    1 => '5310!',
    2 => 5330,
    3 => '5330-1d',
  ),
  '@56' => 
  array (
    0 => '5610d!',
  ),
  '@60' => 
  array (
    0 => '6070!',
  ),
  '@62' => 
  array (
    0 => '6208c',
    1 => '6212c',
    2 => '6260s!',
    3 => '6230i!',
    4 => 6233,
    5 => 6234,
    6 => '6263!',
    7 => 6280,
  ),
  '@63' => 
  array (
    0 => 6300,
    1 => '6300i',
    2 => 6301,
    3 => '6303c',
    4 => '6303classic',
    5 => '6303iclassic',
    6 => '6303ci',
    7 => 6350,
  ),
  '@65' => 
  array (
    0 => '6500s!',
  ),
  '@66' => 
  array (
    0 => '6600f!',
    1 => '6600s!',
    2 => '6600i!',
  ),
  '@67' => 
  array (
    0 => '6700c!',
    1 => '6750c',
  ),
  '@70' => 
  array (
    0 => '7070!',
  ),
  '@71' => 
  array (
    0 => '7100s!',
  ),
  '@72' => 
  array (
    0 => '7210s!',
    1 => '7210Supernova!',
    2 => '7230!',
  ),
  '@73' => 
  array (
    0 => '7310c!',
  ),
  '@75' => 
  array (
    0 => 7500,
    1 => '7510Supernova!',
  ),
  '@76' => 
  array (
    0 => '7610Supernova!',
  ),
  '@88' => 
  array (
    0 => '8800!',
  ),
  '@C1' => 
  array (
    0 => 'C1-01!',
    1 => 'C1-02!',
    2 => 'C1-03!',
  ),
  '@C2' => 
  array (
    0 => 'C2-00!',
    1 => 'C2-01!',
    2 => 'C2-02!',
    3 => 'C2-03!',
    4 => 'C2-05!',
    5 => 'C2-06!',
  ),
  '@C3' => 
  array (
    0 => 'C3-00!',
    1 => 'C3-01!',
  ),
  '@X2' => 
  array (
    0 => 'X2-00!',
    1 => 'X2-01!',
    2 => 'X2-02!',
    3 => 'X2-03!',
    4 => 'X2-05!',
  ),
  '@X3' => 
  array (
    0 => 'X3-00!',
    1 => 'X3-02!',
  ),
);
