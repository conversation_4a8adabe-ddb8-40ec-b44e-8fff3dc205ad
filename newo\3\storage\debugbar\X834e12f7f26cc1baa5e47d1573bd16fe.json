{"__meta": {"id": "X834e12f7f26cc1baa5e47d1573bd16fe", "datetime": "2025-06-08 16:23:54", "utime": **********.220939, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749399833.583073, "end": **********.220958, "duration": 0.6378850936889648, "duration_str": "638ms", "measures": [{"label": "Booting", "start": 1749399833.583073, "relative_start": 0, "end": **********.084152, "relative_end": **********.084152, "duration": 0.5010790824890137, "duration_str": "501ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.084163, "relative_start": 0.5010900497436523, "end": **********.22096, "relative_end": 1.9073486328125e-06, "duration": 0.1367969512939453, "duration_str": "137ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48249944, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.024720000000000006, "accumulated_duration_str": "24.72ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.136926, "duration": 0.018760000000000002, "duration_str": "18.76ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 75.89}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.1685379, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 75.89, "width_percent": 2.994}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.190991, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 78.883, "width_percent": 5.906}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.195009, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 84.79, "width_percent": 5.057}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.202943, "duration": 0.00177, "duration_str": "1.77ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "ty", "start_percent": 89.846, "width_percent": 7.16}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.209873, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "ty", "start_percent": 97.006, "width_percent": 2.994}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-45032435 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-45032435\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.201534, "xdebug_link": null}]}, "session": {"_token": "WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1309666723 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1309666723\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1710864350 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1710864350\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-854007478 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-854007478\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-277560405 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=u1db3a%7C1749399485619%7C2%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InRJUFpHUkZpMHI0K2J4L2VRQTBPVmc9PSIsInZhbHVlIjoiMWdVNjVNdGJjUlUvVm5qWFNCUHB1c0pmVzZEOC8rcU1qSUtDdTFRS3crd05mVzVxdnRGUVY2YjZlMzBQRjlFUCs4NlA4TEdSVWpmTEVIM2NZZVZXTlpML2ZqVlpGVlY4aEhsem02T0JPRU1scnJua2xtSC9Zakpra0ZCTlNpVjU5ZTVWbjJnV21jUXNTWjkvQTEzMjhkTmtYZXBjdWRoTVhsbG9BaVRvbUNuRkZuY1FIL2YxNjAxc0JBbUNVY2NhQStaVDA3NUxOZHVjb3laQzh5TVNzc2ZjL3VLY2pUVzh3VEtYMGNqY0dnQS9yYmYwRVgzbVdDNzQ1WC91QjM3eTFtWElZMyt4MFRQdVcvbTR2Slk0QXhIZWs1aXgwZGFzYU1wdGtadDViM0VrS2hMTGVPWDVGbE85VG5TTWliTllHT2graDhlYXFXaERxVWovMUo2KzQ5YUxlWTRxMWlLdGJpZllJbkVDRHZLTkFJc0xmRmRUdXFJTFdJNmR1WjQ4ZXdvL3NYaGQzdVVlakg1T0dRT0NmUzBzR0lZU0FMT3JQeFhuTXBKYm83VmljWE9iUXAyM1BYaUErRTd5N1NYakN5eWVkL3ZJK2s1RUhBZjRMYno2dzVXN2M3VjArZVZuS01hU0IwbjVaa3ZtcTVqMzdHd0hZbm9ZQ0ZoWkdPWkQiLCJtYWMiOiIyM2Y5ZGQ5NzE3NzlhYmNkNmRiZTc4MDQ5MjllMDVhOTliOGE3YjMyZmNmZDYwYWMwNTY5NDM3MDQ1NDM4YTRmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkMwdUE3Um82N0djOWNKNjFmOSt2MlE9PSIsInZhbHVlIjoiQXBoM2dXSVNUSkJpVFdDUWZSQlFDMlhmVmlZMzZTVUU0QU1DODArVEdsdEV4QktEZXYxQ0pLMEF3VzBGbk80dm0wZ2ZMd3BTUEJzQnV4UVdyUGtuS1NQcDFNK1VhL3BMVlh4SDQvNG12VlZkRXNYYUNWcm5DKzBZR2g3SFpraXNiYm8wcXc2a0doSk5kZXphcFp2MFVHcDRBQzBkbkhneTNXNW9xOHpWcVZ4cmloM2NzRURvYU1tdW45ekJxL1FXQXFEVzIza242bll5VHBIcGtQeHpjWUFuZUhXcm9rRnBMMWEzendyZ1ZoeUpmRXF6bmNlN3B0R1NObVJ0M1N3OU5nb3VLd05uNlV3a1NPSHZROXNHbEVveDd6ZW9pRlpzNnhpS1hOcGRQNmhBK3JLaVpoTWVkVTh5c2dXb2tyYXpyV2l6NkhPTFhrOXM1V29VMjdJZEFMNzV4blQ3LzRLR2dzcTlBSFBDd3BMMWQzZUZ3c3RrMW5ybzJubmFlc2UrNldxMDRmdk1sMm96MFI3eVRFSmpRNFdvYjFwUXpOT2FUa0xjNXROY1pETjRnMDNDQlp1WXZhOFQvY3hjQWZ5ZjlMNWtXOHJ3NUdIeHVGT25BNU4vY3dkM0VTZjFaOTNsQytoQ3hRY0pvN0VYOXNiNzVaTGJWVmhrU3lQYkdZaksiLCJtYWMiOiI2Y2NmMTY1YjRjM2M1NGJjMDk1YjQ3MGFkYTEwZDc2M2U4MDk4ZTkxMDNlNzcxYzM5ZGI3ZWE4YjJlYmU4YjMwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-277560405\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-277297204 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xNV7SvMX3AgbiuDVO62eNXfryfZD4KbWr3rEIkXt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-277297204\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 16:23:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjhSbWsxMm1uZWhJVE1rWEc4UURYZEE9PSIsInZhbHVlIjoiRWprWEdiYVA0akZCR1plWTZxVkY5MnpvSUFCTWJwVS8rY3ZzYU1Fd05kUUUyTnUvVERLUExraW1ZUFVma1N1Y3hyYVdtbFFpbytEc3NyS1E3YUdCellJbjk3d1lncUlJdytTR1ZVSEJmU0VOSEZtRVdXdHQxSnZXK1I4cVNkVE1jRGJQbVQyZGJKcjd0YXdnSDhwK2ZqUTdwNGdPbUVrelEvdlFtcmZRWStyZmtYZE05K0FPTWZUMnFEUjhuYk1BZXhPU29QOHpNRWRGWGJkM1hrdFgvR0pnNHZleHlUMk9SMGFMeUdaQVF6OWhaV0FwaEQ5T1h1aUszV0NpN2tMZnRLMnV4UDdONDcwR24xNnQ4UmlaT1NOd2NvNVRGTi9iRWFrVUtvQzQ1MG53K2duSmJkQkx1bFRTbUpIRCtoS2V2NFlDeXIzQjh4ZE9BS3loK2c3ZzRoWnNsTDBLM3JxYVNlMXIwVU9XSWZ3ZDFYSFlkUkw3TEdqREZPL2dxT3lSY0tJSC8vNWJFK2lkY1lqWWxZT0pIVk5sMUxueHB6a2tNak12NFJ6YVVBNmdpZ09IRHVLNXNXcWdZb3JFdVdQVStVNGdyM3VzbzV5MS9DWnJ0KzNONm1mSGx5T2dMTFVMSjJqdHFzdTFXUHo4d1ViaWptS3NzYzQvNk5xOU5qQTEiLCJtYWMiOiI0MzA5NzhhMTNmMWRkNGIyMWZlMmY4OGI3NjFkYjA0NzAxMDBhOWRhNzEyZTgwMzJlY2Y2MzY4MDlmOTc2M2Y3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 18:23:54 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ik5MZElUNlIwN2FJMUxIR3Qva0J5bGc9PSIsInZhbHVlIjoiNysxWTFHVWFqMjZuci9KWS9lM0xrT1N0a1o5c3RLdHR1R3hMMEwwZjBTTGJpVWxNZnhVNE9lVE5UOWJ0aFlWdXJ1eWU5YzRXR2V5b1pCNFZ5Z2VpYXJPd1BUVVBBZThkcWdyblFlR3RtSThpSjJUaUJMdFI0YnVGSE9SYWY5RFVHVTVRYVFSOUswdlJ6L3hrUHJ4VUlMWWRRVE9tZjZHVi9BTkVwM3NZVjVpUGJMTHRlYmJCRlI2WElYRXBzenUzN0t2eWV2NWlWd0U0VFVTVlBraTdWMDR6L25tZXVzSDRnRHhROFNoVFErS1pwYmNHdmE5MHBpOHhRQlNtaEhFUldEQ25DNmtjbC9SeEU0eDJIWHZuQU5JQkt3N1JvTkdQS005eEc3Y0Q2bnlmNzRLMDNBRWo2dkhDMndOMmViRDlrSjRwUmJBWmtZb2FCemRreVMxeFJtYWhMVlVNNlhMdmdKRlRuWjdJU09SMnhDaGRlREQ5enhSZk1ReUJvcFVSZUFBZUdUYVhXeUpIY3lQbW1jNWYrZHNkUG1ROTZ3RDE4U1o2M3hCOEhUbThQQW9OcTZPeklpZ1FNK1hxNUp0bFVCSzVYSVA2Z0pxVkR1WVh5b0cwWWxnUDhMWUpSM1dNbWN3VUtFdUVrYnAwVjkwb2h5dkdlL1c1WVFKY2FJd3QiLCJtYWMiOiI2MjVjNTgyNTUwYTA5OWIzZjNjZDcyNzZiODg1MzljMjhmZDllNDNhMTBhNGQyN2NlMDhmZDZhNDg2NjFlYjZkIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 18:23:54 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjhSbWsxMm1uZWhJVE1rWEc4UURYZEE9PSIsInZhbHVlIjoiRWprWEdiYVA0akZCR1plWTZxVkY5MnpvSUFCTWJwVS8rY3ZzYU1Fd05kUUUyTnUvVERLUExraW1ZUFVma1N1Y3hyYVdtbFFpbytEc3NyS1E3YUdCellJbjk3d1lncUlJdytTR1ZVSEJmU0VOSEZtRVdXdHQxSnZXK1I4cVNkVE1jRGJQbVQyZGJKcjd0YXdnSDhwK2ZqUTdwNGdPbUVrelEvdlFtcmZRWStyZmtYZE05K0FPTWZUMnFEUjhuYk1BZXhPU29QOHpNRWRGWGJkM1hrdFgvR0pnNHZleHlUMk9SMGFMeUdaQVF6OWhaV0FwaEQ5T1h1aUszV0NpN2tMZnRLMnV4UDdONDcwR24xNnQ4UmlaT1NOd2NvNVRGTi9iRWFrVUtvQzQ1MG53K2duSmJkQkx1bFRTbUpIRCtoS2V2NFlDeXIzQjh4ZE9BS3loK2c3ZzRoWnNsTDBLM3JxYVNlMXIwVU9XSWZ3ZDFYSFlkUkw3TEdqREZPL2dxT3lSY0tJSC8vNWJFK2lkY1lqWWxZT0pIVk5sMUxueHB6a2tNak12NFJ6YVVBNmdpZ09IRHVLNXNXcWdZb3JFdVdQVStVNGdyM3VzbzV5MS9DWnJ0KzNONm1mSGx5T2dMTFVMSjJqdHFzdTFXUHo4d1ViaWptS3NzYzQvNk5xOU5qQTEiLCJtYWMiOiI0MzA5NzhhMTNmMWRkNGIyMWZlMmY4OGI3NjFkYjA0NzAxMDBhOWRhNzEyZTgwMzJlY2Y2MzY4MDlmOTc2M2Y3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 18:23:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ik5MZElUNlIwN2FJMUxIR3Qva0J5bGc9PSIsInZhbHVlIjoiNysxWTFHVWFqMjZuci9KWS9lM0xrT1N0a1o5c3RLdHR1R3hMMEwwZjBTTGJpVWxNZnhVNE9lVE5UOWJ0aFlWdXJ1eWU5YzRXR2V5b1pCNFZ5Z2VpYXJPd1BUVVBBZThkcWdyblFlR3RtSThpSjJUaUJMdFI0YnVGSE9SYWY5RFVHVTVRYVFSOUswdlJ6L3hrUHJ4VUlMWWRRVE9tZjZHVi9BTkVwM3NZVjVpUGJMTHRlYmJCRlI2WElYRXBzenUzN0t2eWV2NWlWd0U0VFVTVlBraTdWMDR6L25tZXVzSDRnRHhROFNoVFErS1pwYmNHdmE5MHBpOHhRQlNtaEhFUldEQ25DNmtjbC9SeEU0eDJIWHZuQU5JQkt3N1JvTkdQS005eEc3Y0Q2bnlmNzRLMDNBRWo2dkhDMndOMmViRDlrSjRwUmJBWmtZb2FCemRreVMxeFJtYWhMVlVNNlhMdmdKRlRuWjdJU09SMnhDaGRlREQ5enhSZk1ReUJvcFVSZUFBZUdUYVhXeUpIY3lQbW1jNWYrZHNkUG1ROTZ3RDE4U1o2M3hCOEhUbThQQW9OcTZPeklpZ1FNK1hxNUp0bFVCSzVYSVA2Z0pxVkR1WVh5b0cwWWxnUDhMWUpSM1dNbWN3VUtFdUVrYnAwVjkwb2h5dkdlL1c1WVFKY2FJd3QiLCJtYWMiOiI2MjVjNTgyNTUwYTA5OWIzZjNjZDcyNzZiODg1MzljMjhmZDllNDNhMTBhNGQyN2NlMDhmZDZhNDg2NjFlYjZkIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 18:23:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}