{"__meta": {"id": "Xe70be9202f37bdb4ca3cf632dc837eee", "datetime": "2025-06-08 15:31:09", "utime": **********.262585, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749396668.471574, "end": **********.262618, "duration": 0.7910439968109131, "duration_str": "791ms", "measures": [{"label": "Booting", "start": 1749396668.471574, "relative_start": 0, "end": **********.165748, "relative_end": **********.165748, "duration": 0.6941738128662109, "duration_str": "694ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.165765, "relative_start": 0.6941909790039062, "end": **********.262621, "relative_end": 2.86102294921875e-06, "duration": 0.09685587882995605, "duration_str": "96.86ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45137288, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00528, "accumulated_duration_str": "5.28ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.216103, "duration": 0.0037099999999999998, "duration_str": "3.71ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 70.265}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.2368999, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 70.265, "width_percent": 14.773}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (17) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.2488768, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 85.038, "width_percent": 14.962}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1930559554 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1930559554\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1353152115 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1353152115\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1440703262 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1440703262\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1813864489 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwl%7C0%7C1982; _clsk=o6pnpa%7C1749396598191%7C3%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IksyZVgvcU56aTZueHVuS2E5YTYwV3c9PSIsInZhbHVlIjoidnZwTFVpaTlqM3NTZFJsR2JvRTlidWhRejFuajlQbXI2c1dUenVvUjdvNHdCZVRaNGZydFpGRzdrK0x2YUF5ZDgwVXdtWUkzL2cvR25FRGxSS3p2UUhKTzdVUVNtVXc0LzUxdzVrcnRmZ0R4QnhJWHhQeGVCWFJwajdVNkk5bDJlSTBmV210dnplczNUc3dXNVlEYUVaMTdLWEFwMDZLRm9wUDg0dkRJU2gzcjNlZ1lMSmJ4Nzlqdm1JSEpVSDlXbUNIODlScjVHWEw5SlBJSzI2OWtUT2lsVVdHWkJrQWorYWswNDR2ZVlOVEpaUkVuc0RYTFErVTQ1TkluNTQzejNTNU9EeVlhNE56TFZ2NzA4WUkvVG1YYi9JZTlidFBaS01sN1FYcUduMytjOHNmcURLUHErNjhYZEdnejFUOExoTkFxWTRmRHdIQW9FQ1VEMzRDSWdUWURnVHNpcE5tQU00QS9Pei9NZ3pXWlcybTdob3JmMXlqcDRiUTN0U05DUjU3UzFuMWk4VzV3Rk83RXZMWkFtdjVURXBCRUQ2ZkZzbmVsd3BPMjI3YitkOFBJOVhIMkJseTExZjNvWGxlUEljVjdBTEFEM3A0VnAvaHNzVnpUeFNqYTBTSlZYUnh6c2pIeGdCeGRpbHpVYytiYVdNMnIrcnYyUXJqQzg4WFYiLCJtYWMiOiJlYjE5ZTQ4NzBlNTIwODI2Y2JkMjc1MDNhYmIxMGNhNjM4MDJhMjQ3ZDAyNTgxNjhmNDIxZjc4YTZkNTJmMmJmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImxQUkN4SWdVazcyS0JwR1hwZ0JRMlE9PSIsInZhbHVlIjoiVkxFMUtMalR1dDMrdUEyVW0yOUVQZnRqeUlpalVMcW9yR3g3aGN6SkJnd3JiNWdJS2svOTRnSHlaTStwRTV6RThjVGdBYXB5ZDhoKzdaM1lDdUw5WVBJTTVRYlpLdVVlWDlVODBCY2ZndmNma3BZenkzMlp6UVZWZWFpaU9sTEQ4c0dQWjQrS2ZLM2UyOGtJUnFCcDlWMU1IYmdmbmJrT2hMb2E5ZXVRTXdvZ2ZRTU1kVFoyVXRNUlRSTWI5cnd6VlIzWFpNNFlxc0ppdjZqd3NOL00wNm9jbnZOcWNjK3g5eXZMZ0htWjVGOU0vMDFXM1pnOGRlRWNzOUFHL2FwdVFZbWJZUEZwZkRKVWVkZ1RzZTlsN2lMalNXckFYVHhWNXUzVWVQYmdWSldHWXJ0eUxyS3F2SUtzUlRuYU91VVQrRWdHWlR6V2RWOVlvS2M3bVpjcHJlNGhCOGpoNEJ6NDZUMDIycVJJU0xsRTJ4M2I4UzEzL2dDV2hXR3NGSk9Bb3R3RDh0NU1kYzJkZ2Y0YmlDRE9Jb1pmS1NuRURqUlNoNW5SUFZJRnhpYUNBQTE3WnlvMCtrbWhJbHhEeW0xOGpxbnBnKy9mNm0vZENFTDJIWVRPZjRzWGJHeHBKV1ZUQitTOEN5cmVsclkzemt1V2V6aWlOYndwR09yY3NmV2UiLCJtYWMiOiIyOTk3N2ZiNTAzODMxZmRjOGE4NzE3NzZmNzY2OWMyY2EyNWVhZGNmNjQ3YWE3ZDA1MmQwZDYxZTBlYzY3MmE0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1813864489\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1929166445 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">COI6IIrAbucReABHBN7o3ZaIzRbwBCI1gfwrcfCo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1929166445\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1511437253 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:31:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImF3ekNVMHl4amF1ZGxtcHNXOFFHYVE9PSIsInZhbHVlIjoiNDFWWUpQLzFrSGJlZG9wK0NoSFZxUXE0Uyt1eXBVRWJXMTlMWGFsaUhlR09RbHFWS0F1MXRuSW9qOGs3WTRhT2pEb2lMU1hVc3lWNVVUL0FFL3E0U2UxNngybmlCYkZjVjkxTEQ3UUI1RjJJNE45UjA2V1JDbVdrYllKVkM1S0NrcmlNQkNHdkFjcFVLRFQwYXhwREFodjRJbFc0V1RPWVlZS2hSTWRSbE0rcC9DWHVTYjBoanVsVkdScWpyZDJtUHJzc1FsdEIxK1R1UlBIRkt3QVpPd2RZZjQ2Mm9ZS3REOWE3L0x1QWJDejU0NnB0NFZOUHdDbDdscis2aGxTT2pNVGk5bTd5MGsxNzE0RDQxSXVlWnZmeDRRM29qbzY5RmpSVjgzMVFMVTBQdWxWelVSNXZDSTc2WlJON2V6M3cvMWFZQ0lCMkpIQlp2S3hrN0x0ZVk2eXl0K3BMQnpxNzJOa0pYdmRZVlNobDBpb1Uwb0FNbnovZ1hHK1BibzlPWUVjbHViT3ZFbnNXay9qcXE1R1lMVTNmSm56YkhZU250QWVpTk41MTFrTVJDT0NqWHlFeEFDem1pY0J1R3Q1blRkaEdCVDRPUFE3SFVVMkVQQkVOYTc3Q0FZTEJ1UFVhQUI3SnB1NFltRStLalZ6bzBQKzNZUjdBL2QvaWdLN3QiLCJtYWMiOiIwODFiZDUzODNmNjFkOWYyODBlYmU5NmNmMTg3NDY5ZWZhMjFiYjUwNjQ4NjhhMGViZTdiMDcyYWI2OTU5NWQyIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:31:09 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkdLTVk1MHZNRDRZeGRsRkpPdysrSHc9PSIsInZhbHVlIjoiUUtINS9xNkFyb3JmQUdiQUF5RGd2UG1yVTJLUTQ5UU03dXBRdEh0RUR3dnRmNXZzK0hSdDgxSEJkSjgwS2lxQ0hVK2Z4YUJoMnM3V1FRSmM5SDA4OVprNkg3NXVyYXFpbjVHZW5jQjhNUlQ2OXA1TWpWWHZLWk9yL2U4VDJkc2FLK3lRMlphUkpGUnkyclFCeTh6QjdYWTJPbEEyaCtSNW0wM3dLcHRRUGw2K250dHlYSWFHZC9SU1R1TXBONisyWDFMK2l1VVRTcnprK2hHZGtYM2ZoR3RLaXJYYmhLdkpsaGdEdUNTRTQ5d3l2Vk5NdkQ4Z1BHSXNDbGdNRmhpQmNVMVNSMm5PTGUwcEVib3NVSWNtcmtBbk5xQjBGU1V6VTVlTk1ISGlGZlFrMzBPcW9UOG1xR1pYWlcxRnVrczhiczNOZURXRlBNeFlSRm9PdWVtM0pzZE81d2FYenFrNkoybDZkRjF6ZW4wSnl5bzA0U2E0UnBnL2NVcmFoSDg2dkp6QWxDQnl1NGVaUHR0YUJsS0pYYldxaTNWaWh2Rkh0emZUSC9CSGVTaUY5RkZvSzZOaTI5WTFtdTFSa09VTEFVclpTcDgwU3ZmenA5MVFkS3VPOUl3MDhLem5pb2ZyMDg1OGFVd2NISHc3KzdMNVhpa3QvRXBndUF1NFhqOGUiLCJtYWMiOiI2YTU3NDgwOGQ3ZWYwNDk3MzQ4ZWUxOTgwMjY4OTViN2ExYmMyYzA1ZjVhMTg1MWQyMGQxZWEzMjJjOWE1N2JjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:31:09 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImF3ekNVMHl4amF1ZGxtcHNXOFFHYVE9PSIsInZhbHVlIjoiNDFWWUpQLzFrSGJlZG9wK0NoSFZxUXE0Uyt1eXBVRWJXMTlMWGFsaUhlR09RbHFWS0F1MXRuSW9qOGs3WTRhT2pEb2lMU1hVc3lWNVVUL0FFL3E0U2UxNngybmlCYkZjVjkxTEQ3UUI1RjJJNE45UjA2V1JDbVdrYllKVkM1S0NrcmlNQkNHdkFjcFVLRFQwYXhwREFodjRJbFc0V1RPWVlZS2hSTWRSbE0rcC9DWHVTYjBoanVsVkdScWpyZDJtUHJzc1FsdEIxK1R1UlBIRkt3QVpPd2RZZjQ2Mm9ZS3REOWE3L0x1QWJDejU0NnB0NFZOUHdDbDdscis2aGxTT2pNVGk5bTd5MGsxNzE0RDQxSXVlWnZmeDRRM29qbzY5RmpSVjgzMVFMVTBQdWxWelVSNXZDSTc2WlJON2V6M3cvMWFZQ0lCMkpIQlp2S3hrN0x0ZVk2eXl0K3BMQnpxNzJOa0pYdmRZVlNobDBpb1Uwb0FNbnovZ1hHK1BibzlPWUVjbHViT3ZFbnNXay9qcXE1R1lMVTNmSm56YkhZU250QWVpTk41MTFrTVJDT0NqWHlFeEFDem1pY0J1R3Q1blRkaEdCVDRPUFE3SFVVMkVQQkVOYTc3Q0FZTEJ1UFVhQUI3SnB1NFltRStLalZ6bzBQKzNZUjdBL2QvaWdLN3QiLCJtYWMiOiIwODFiZDUzODNmNjFkOWYyODBlYmU5NmNmMTg3NDY5ZWZhMjFiYjUwNjQ4NjhhMGViZTdiMDcyYWI2OTU5NWQyIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:31:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkdLTVk1MHZNRDRZeGRsRkpPdysrSHc9PSIsInZhbHVlIjoiUUtINS9xNkFyb3JmQUdiQUF5RGd2UG1yVTJLUTQ5UU03dXBRdEh0RUR3dnRmNXZzK0hSdDgxSEJkSjgwS2lxQ0hVK2Z4YUJoMnM3V1FRSmM5SDA4OVprNkg3NXVyYXFpbjVHZW5jQjhNUlQ2OXA1TWpWWHZLWk9yL2U4VDJkc2FLK3lRMlphUkpGUnkyclFCeTh6QjdYWTJPbEEyaCtSNW0wM3dLcHRRUGw2K250dHlYSWFHZC9SU1R1TXBONisyWDFMK2l1VVRTcnprK2hHZGtYM2ZoR3RLaXJYYmhLdkpsaGdEdUNTRTQ5d3l2Vk5NdkQ4Z1BHSXNDbGdNRmhpQmNVMVNSMm5PTGUwcEVib3NVSWNtcmtBbk5xQjBGU1V6VTVlTk1ISGlGZlFrMzBPcW9UOG1xR1pYWlcxRnVrczhiczNOZURXRlBNeFlSRm9PdWVtM0pzZE81d2FYenFrNkoybDZkRjF6ZW4wSnl5bzA0U2E0UnBnL2NVcmFoSDg2dkp6QWxDQnl1NGVaUHR0YUJsS0pYYldxaTNWaWh2Rkh0emZUSC9CSGVTaUY5RkZvSzZOaTI5WTFtdTFSa09VTEFVclpTcDgwU3ZmenA5MVFkS3VPOUl3MDhLem5pb2ZyMDg1OGFVd2NISHc3KzdMNVhpa3QvRXBndUF1NFhqOGUiLCJtYWMiOiI2YTU3NDgwOGQ3ZWYwNDk3MzQ4ZWUxOTgwMjY4OTViN2ExYmMyYzA1ZjVhMTg1MWQyMGQxZWEzMjJjOWE1N2JjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:31:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1511437253\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-41946781 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-41946781\", {\"maxDepth\":0})</script>\n"}}