{"__meta": {"id": "X00d04341dc252e0a5cbccaed5bec4509", "datetime": "2025-06-08 15:39:52", "utime": **********.685429, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.047058, "end": **********.68545, "duration": 0.6383919715881348, "duration_str": "638ms", "measures": [{"label": "Booting", "start": **********.047058, "relative_start": 0, "end": **********.600524, "relative_end": **********.600524, "duration": 0.5534658432006836, "duration_str": "553ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.600535, "relative_start": 0.5534768104553223, "end": **********.685452, "relative_end": 1.9073486328125e-06, "duration": 0.08491706848144531, "duration_str": "84.92ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45152368, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.017349999999999997, "accumulated_duration_str": "17.35ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.635763, "duration": 0.015269999999999999, "duration_str": "15.27ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 88.012}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.663675, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 88.012, "width_percent": 4.899}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.673848, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 92.911, "width_percent": 7.089}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/roles\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-42001780 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-42001780\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1094682119 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1094682119\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2045272931 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2045272931\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=xh9goj%7C1749396885198%7C15%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImVxZzExRzYvUHBUbkRRaWJNa1VVZXc9PSIsInZhbHVlIjoiL1dBbXFLTTdCUXY4czhSN1pmYVpUMHNNQjZNenZmR29pUkZncjd0MzF3TXY1UERNMjk1MmE1eEI0TUs5cjFISkc3TjNGWEFKNHMxMGp3bzlkRW5tQVFEdm8wUzdyWmlZbGtDVW5GY3YydVlpNlpoTm9HRVFvMUozc0dpcjN6T2lMaGdROHdtL3lYZk5PaVU1dmI1L2EwaWNsR0F4eGVjbFprRlp4MjMzNG9tajd5RUhpaENSTkkwOUxacmxLd0FWV0xUNzdaMnNQanRNaWhRckNRRzNoL1JSdU40dW1IZ010SjE1c0czejRmRjVyQk52TnZpaEdOclJPZjZ5Ky9SQ25jZ0ZDblhrc1JlVVdGcElqSjk4ZUJ3ZGpSN1g5WXRPd0M0MGxHY29GKzltZVM4NW5DOEZyeEF1SkExVTJWUFAwWktHL1N6YU5IbFhqOSt5bVVOam53eFZkTmhTTitoV21sc20ycjRWL2VxR1Z2TnNZbm4rYkNxdjNLQ0NqZEV0NWprM0M1Tkwyb3FSb2xOYzZOdGRqcm9WYnpubUpaSWdqN2RVNUhyM1dvbTlOeG4ySGUrYm5BSDA2ZkQ4ZXozWDZ1SlRyM1ZhN0p1RkI4SVV1ZHQ5alNFb2RjUUJHSzVobi8yblk4eXhzYVVqejNIUUZKSXh4TmFCdUFkRkJxSG4iLCJtYWMiOiI4NTAyMzBkNmU1YjhmNDUyNDkyYzRhMDIyMzRlNmM3NGUwZDk5YzlhMjQyY2ZlYjZhMzMzYjA2NTg5YjU0NTcxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImdSbW9uVGV3QkZ5anI4aGN3azVqMGc9PSIsInZhbHVlIjoidUJXNml1WnRHcW1kdWJDZG8wUGJST3E5YWd2ZldxTnhvZzVUdWRqREZSRVI2U0VkWjY3cmJqc0craVpzbnBDbjA0QkJpVmIwbFd2SmNFWFJzc1AvMk5lYkUwYUlZMUlmaVdFbFFkalkwK3FGNGVMczdXcjRveGZROEFxZXg4Q2Q0VTFZOTRpODNRc09EWVFNRzhLUTgzSnZ2dkVqNVlBemhKM3p6WGhZcFUxaXpoY2ZyTHFJSGJyUkpQeWFQRG5XaXp3QlJnRlkxV3ZuRHJjZjU2WGZWdDY5SEJOeDdOS3k0NUJrMXk0VitaNGZReFVEa1dKU1dTZGlkZnZncm5UMERCc2FPcy9RMW5aS3NBN1ZuZkZvR1FTay9kbGYvNnR5eEtSWXBWc3dxek9qNFhYWWxDdEpxUC9rZENmcGxPUmZSVWNGcXpRbDY4VlFqRzlUUS83UWZaandkamVGckp4T25hSkIwRDM4OGxiVDNyNG10cEdDN010d2svc3BmM2h2NmtEa3VlU3Vsbktab0ZxOHlyUXUzeG1OWVAvd0FVcVgxSldFWTFsMnBRVGE3RVpISW1acG9DaVNMaXFLa3NMcFpmQlpLQVZPWldKd3ZSTDlJYXBpdGR1ZmFnRGpqay9pU3BjdnNheURJNEY4YkVMQTlEMDZGdU5HMVVxQnVzQXoiLCJtYWMiOiI1YTQzMTA0NTZkZmFkZTZmMWNiODY2YThlZDcxMjdiYjk1ZTZkMTczMDM4MjIwZGU1MWRlYjY4NjkwOTg1Mzg4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-264507986 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">l7xz5q4Zkpyo7Zd0rhHsE8M6pi3wac4XFWhsi0lQ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-264507986\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1925802866 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:39:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im1KYXhwS0dBMmRjZndlZkd0N0gzcXc9PSIsInZhbHVlIjoiUGg2WlJDdEpmY2lnR08xcXRhQ3Z3Q3lGWjhFU0wyWUFzNzBiVUkwM0Q5OXAzVmhBbXg4eng4UHdiVnRiaUtGVmJhMXZPZDNkRERValR6YVBFbzVPcU9aeXZlbVQ2bFZaaUFxZ1hlbE1iZ0V5VStFN0owdWJSSmFuZzJSSmgwelRIeEFHN1Z1QWNmRyt4WURrMmUwazlaUFlYUzFxZTdlVXZOQTVLUnk5QWwyLzNPeVFUaGZQZ3k5RXZCSVBHUy9BTTFJNTVMY3JaTGpYVEtDZEl6eEZQczczZUd3dnpvUGFuMzhOV01XRDlMVGtzZnVmcUNGdnd3bFk0WUtqemtKOEh4N2xUYit5MmpxT1hWTE45Q3VpT2NHMnF4dEkvMjN3QWRlUnF2MTErd01uYm81cTJvTFNXQ0ZWcThDOG8wNEJPRFdPSjJLK1JqUVRJOGlnQ2s1U0U5MTgrT3daTFVQdFNhM1Y0cHRUUzB4WGswc3Vtc2ZJQWhpb1ZpOVhWQUtrOCs0ZTFET3k3UWV6K1N3bjN0QjVEckpEZEQrZmhyNnIyUE8zNzN5QmYrMEF6ZVZHNmFFb3pjQSsySHVpelYxRzlNVWVsM1FGRndnWG15NkQwVlBJNDRDbDBhZUtMdGJZRi9DaW9iaGR1KzlsNVpYdCtsUGdURkdJYUxlN1VQSXUiLCJtYWMiOiJlODdhZTM1MWU5NDEyMmYzMGVlMzAxMzZjYjViYWE2ZDE0NjMyOGFmNTk4NGI4MWY4MmU0YTcxMjUwMjMyMTcxIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:39:52 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlNGeTVPTjMweWtpWFBSblN1N0pvcXc9PSIsInZhbHVlIjoicUVadWI0amlUN1NTelBETWkxODNyRlNNdStOLzNXb0pERjZZemJGeW03MU1SUjQ2Z2crTStEaVVGNk43Vlg5NGVBeGNwb1d1TGRLeVVJVEFQbFBJbXo0RU95V2ZqQmZXa1Q3NFFEemwvYy93c0tGTWh4aktQQTE5ZFQxcmloVldVUVMvNmlVdkdSQWlHaHJqdXhBODh5Y2NWdk1mMmhvRTVTT0xXQU9HNXRDejl4M2pxOTh0VFkxcVI5SjltamxPQ1dibDUvWUxZM1Y1RSsweCs2VVJQSkhhYkd3MzE5bUZWNGIxT1RQdGlNbVVQOGpmWkZNZU8zdWlnN2QrektaNjRQZXJORFRQVlo5RjZTWGVncVNhUmtUeHJQY09OVFpqNGhCM2RXYmRuWjFxdFF3TlRSTzJneXI0Y0xmdXhDQmZ6OCt0dUxOejZWTDNMUXN0RzlrSEc5c2YxbHAyVkNQd1Vsem1LektNZ3lKdjZjTVArZzE2YkROVU9tMnZ2Y3k3QmdPamYzY3dnOFlUTG82Yi92U1J0UmpvUVNla2hINUUrZTlGR0lHTkZ2TFFvY3NBNzdWTFBLMlFpanlpUXBUZE96MktHQUZEc2VRd0diaHBVWGRDZnkyeEcyZ2c3WE1XK0hveDBHRnFaUWJ4MnZueTdRd2VRa2czUEp0eERxTUgiLCJtYWMiOiJmNzk2OWVmZTEwZDhlMjRjNDdiY2MwYmNkOGYxZmZmMWMyNzY3YjMzMTQ2ODBkYjliNDI5MDM1OTQxYjZjMmFhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:39:52 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im1KYXhwS0dBMmRjZndlZkd0N0gzcXc9PSIsInZhbHVlIjoiUGg2WlJDdEpmY2lnR08xcXRhQ3Z3Q3lGWjhFU0wyWUFzNzBiVUkwM0Q5OXAzVmhBbXg4eng4UHdiVnRiaUtGVmJhMXZPZDNkRERValR6YVBFbzVPcU9aeXZlbVQ2bFZaaUFxZ1hlbE1iZ0V5VStFN0owdWJSSmFuZzJSSmgwelRIeEFHN1Z1QWNmRyt4WURrMmUwazlaUFlYUzFxZTdlVXZOQTVLUnk5QWwyLzNPeVFUaGZQZ3k5RXZCSVBHUy9BTTFJNTVMY3JaTGpYVEtDZEl6eEZQczczZUd3dnpvUGFuMzhOV01XRDlMVGtzZnVmcUNGdnd3bFk0WUtqemtKOEh4N2xUYit5MmpxT1hWTE45Q3VpT2NHMnF4dEkvMjN3QWRlUnF2MTErd01uYm81cTJvTFNXQ0ZWcThDOG8wNEJPRFdPSjJLK1JqUVRJOGlnQ2s1U0U5MTgrT3daTFVQdFNhM1Y0cHRUUzB4WGswc3Vtc2ZJQWhpb1ZpOVhWQUtrOCs0ZTFET3k3UWV6K1N3bjN0QjVEckpEZEQrZmhyNnIyUE8zNzN5QmYrMEF6ZVZHNmFFb3pjQSsySHVpelYxRzlNVWVsM1FGRndnWG15NkQwVlBJNDRDbDBhZUtMdGJZRi9DaW9iaGR1KzlsNVpYdCtsUGdURkdJYUxlN1VQSXUiLCJtYWMiOiJlODdhZTM1MWU5NDEyMmYzMGVlMzAxMzZjYjViYWE2ZDE0NjMyOGFmNTk4NGI4MWY4MmU0YTcxMjUwMjMyMTcxIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:39:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlNGeTVPTjMweWtpWFBSblN1N0pvcXc9PSIsInZhbHVlIjoicUVadWI0amlUN1NTelBETWkxODNyRlNNdStOLzNXb0pERjZZemJGeW03MU1SUjQ2Z2crTStEaVVGNk43Vlg5NGVBeGNwb1d1TGRLeVVJVEFQbFBJbXo0RU95V2ZqQmZXa1Q3NFFEemwvYy93c0tGTWh4aktQQTE5ZFQxcmloVldVUVMvNmlVdkdSQWlHaHJqdXhBODh5Y2NWdk1mMmhvRTVTT0xXQU9HNXRDejl4M2pxOTh0VFkxcVI5SjltamxPQ1dibDUvWUxZM1Y1RSsweCs2VVJQSkhhYkd3MzE5bUZWNGIxT1RQdGlNbVVQOGpmWkZNZU8zdWlnN2QrektaNjRQZXJORFRQVlo5RjZTWGVncVNhUmtUeHJQY09OVFpqNGhCM2RXYmRuWjFxdFF3TlRSTzJneXI0Y0xmdXhDQmZ6OCt0dUxOejZWTDNMUXN0RzlrSEc5c2YxbHAyVkNQd1Vsem1LektNZ3lKdjZjTVArZzE2YkROVU9tMnZ2Y3k3QmdPamYzY3dnOFlUTG82Yi92U1J0UmpvUVNla2hINUUrZTlGR0lHTkZ2TFFvY3NBNzdWTFBLMlFpanlpUXBUZE96MktHQUZEc2VRd0diaHBVWGRDZnkyeEcyZ2c3WE1XK0hveDBHRnFaUWJ4MnZueTdRd2VRa2czUEp0eERxTUgiLCJtYWMiOiJmNzk2OWVmZTEwZDhlMjRjNDdiY2MwYmNkOGYxZmZmMWMyNzY3YjMzMTQ2ODBkYjliNDI5MDM1OTQxYjZjMmFhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:39:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1925802866\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1580241163 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1580241163\", {\"maxDepth\":0})</script>\n"}}