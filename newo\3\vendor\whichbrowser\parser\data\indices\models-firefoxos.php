<?php

namespace WhichBrowser\Data;

DeviceModels::$FIREFOXOS_INDEX = array (
  '@' => 
  array (
    0 => '.LYF\\/.F30C!',
    1 => '.LYF\\/.F101K!',
  ),
  '@AL' => 
  array (
    0 => 'ALCATEL ONE TOUCH FIRE',
    1 => 'ALCATEL ONE TOUCH 4012A',
    2 => 'ALCATEL ONE TOUCH 4012X',
    3 => 'ALCATELOneTouch4012A!',
    4 => 'ALCATELOneTouch4012X!',
    5 => 'ALCATELOneTouch4019A!',
    6 => 'ALCATELOneTouch4019X!',
    7 => 'ALCATELOneTouch4020D!',
    8 => 'ALCATELOneTouch4022!',
    9 => 'ALCATELOneTouch4023!',
    10 => 'ALCATELOneTouch6015X!',
    11 => 'ALCATEL4044[MNOTVW]!',
    12 => 'ALCATEL4044C!',
  ),
  '@HU' => 
  array (
    0 => 'HUAWEI Ascend Y300-F1',
    1 => 'HUAWEIY300-F1',
  ),
  '@LG' => 
  array (
    0 => 'LG-D3(00|01)!',
    1 => 'LGL25',
  ),
  '@LY' => 
  array (
    0 => 'LYF\\/F30C!',
    1 => 'LYF\\/F41T!',
    2 => 'LYF\\/F50Y!',
    3 => 'LYF\\/F61F!',
    4 => 'LYF\\/F81E!',
    5 => 'LYF\\/F90M!',
    6 => 'LYF\\/F101K!',
  ),
  '@MA' => 
  array (
    0 => 'madai',
  ),
  '@ON' => 
  array (
    0 => 'OneTouch4019A',
  ),
  '@OP' => 
  array (
    0 => 'OPEN',
    1 => 'OpenC',
    2 => 'Open C',
    3 => 'OPENC2',
    4 => 'OPEN2',
  ),
  '@OR' => 
  array (
    0 => 'Orange KLIF',
    1 => 'Orange KLIFD',
  ),
  '@ZT' => 
  array (
    0 => 'ZTEOPEN',
  ),
);
