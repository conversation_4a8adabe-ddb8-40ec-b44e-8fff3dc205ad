{"__meta": {"id": "X45e2efc33cb6d47c2d10ce2d1a313b96", "datetime": "2025-06-08 16:24:35", "utime": **********.697315, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.078764, "end": **********.697335, "duration": 0.6185710430145264, "duration_str": "619ms", "measures": [{"label": "Booting", "start": **********.078764, "relative_start": 0, "end": **********.617807, "relative_end": **********.617807, "duration": 0.5390429496765137, "duration_str": "539ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.61782, "relative_start": 0.5390560626983643, "end": **********.697338, "relative_end": 3.0994415283203125e-06, "duration": 0.07951807975769043, "duration_str": "79.52ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45676416, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.012889999999999999, "accumulated_duration_str": "12.89ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.653737, "duration": 0.01057, "duration_str": "10.57ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 82.002}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.675186, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 82.002, "width_percent": 5.663}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.683224, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 87.665, "width_percent": 12.335}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1142214801 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1142214801\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-327475176 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-327475176\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1704464241 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1704464241\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1137102440 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=u1db3a%7C1749399860582%7C3%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InNGOVJGT3IxZ0h2VndHSWZHQThZZ0E9PSIsInZhbHVlIjoiREh2MVo4Qk42aWZObjBlZnp1TnpWK3M5YzhUNFdSMVhwSXBsaW5FK3VyNHlETjA0UmVMc3EvdHJibVhDR3REci9DUG4xdG1GSnJ5bFp2ejVBS2xCbFdmM1pwZHZselBVb0pLcUVQNEZId1F6cFBuYkx0ZzM2WkptdGNQNGw5K2ZsYS93TWlRbkpXVStYamtiSitJU0tpMUl2dnBXSUxreTRESHRIbzdCL01FQ24xTW5IN2hLcnlKMm4rZU56M1RGbjA2K3lJQ296MVVwM3R1djUvN1NWUVh3b1pwREdVTDV1bWk3NnA3bVRkOERzQnJET2JpZThVSTlUeVBTNlRGa0ZYWjBTYnRtdXIvZmpmaXFQaVJacitkR0pPWjVZV2NtVGgxbGNvWHJoc2dVQm5sN0VOS1pyeVEzM05mTTltSUxkWlpIUzlXRjNpTzBxRVJxcDd2SDEwOVMxbWhXYjYrc2VlTjBCa2pvTGNlWXhOV3hkd3crVDFIWmxkZHgxMzNWdnJJc0pydUFxUHlKU0ljUnJxOE1xNlFycEIrWGU2T0lKRnhwa3FFanNEenlnUm1LNmROZ1hwaDNtQmowb0pHS1doNG1VKzFFblYyeHVyYTVUUk5CN01LNEgxWGtIM1l6UkQ0QVIydGtNMDErdkEvTW5YVGRZOUltME1oaGkwMW0iLCJtYWMiOiJhOTViZDRmMDEyM2MxNDgyN2Y0NGQwNWZiMDA0MTUyODU2MWFlNGE0MWNiNTE2MjJlODU3NzE2N2E5MDk0ZjI1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkhhMFJoaDQvTGg3WHRiSmlPU2xBUHc9PSIsInZhbHVlIjoidm5XZEFjeHJBMHRoSXErVFpDcW9mV1l1eGMxMEp5cWtQdUlrM0pOKzhsSStDWnVJWnEwcWFxSzZOTytmSmYrSXRGT00zVDgyMkZuRHpTKzJ4K2dTZGJTYVc2dXRrQTZQL0YvdG5QTGJobTVYa0ZrZHlUSVl0WFByUGZsYmVPVlRsME5iU1VndzlvTkNVcnBRb08zNk1MQTROMWJWcGtMMFlDK2p5NFAzdENJUHI5UDgzeURqSjg4OHk5b21DSlBpcC9TTWhZZXZaakM0ZUhzNTBCN2xDamx1bDdqdjU2YW5aa2IvNnd0TW1JN0Ura2ZrZ2JsWkJ5N2U4QklUYUxnS2dhOFBWRTRSSWFXQmxLN282WlVjWWcxNnR5WXRpWWE2dDRQWVZJTWZmb1hnRkdnWlJjN0VqeW53UG81THZxYmVESzNGMVFwOFcvUmJQNlJBdUU4WDFCQUJweXhYWHFUc1pUeWdkY05kelRRVnpxNVRPemt1Z1laMFRwWnlPRUZQZGRrcGJmdUJ1ckEzMVpPNFdqRXhOaXNITkwydmwxd2JHQU9ZVzhtVEd4S0VoclZUWHlaTW54azdmaXRBNkpWUERqRlA1eFZvcDJKZjZJcGtVblhlcnV3WmRCT3NGWENPcUFETkJKUjRNams3MERMMHNaQWkyOWxRRC8rZFZ3OUIiLCJtYWMiOiI1ODg2NjhhZjJjOTYzMDg0NDg2MDQzNmVmODk5Njk0MTM0NmM1YzlkMWFjMjMyMDFlYzU2ZWM1NTQ4YjZjNzE4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1137102440\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-78463409 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xNV7SvMX3AgbiuDVO62eNXfryfZD4KbWr3rEIkXt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-78463409\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1215689342 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 16:24:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IktzZHN4SHo5U2F5Qm1DbFk5ZzhUbVE9PSIsInZhbHVlIjoiV2FhSGVYSDNwY2ZQRGJPZWc3MzJBWm81bS9TdzI1M2E4Um5wMzhFUncwQTFGMFBTMysreURMZGc2S1pkZGhHa24xTytRRHFxNDFJZ0VqeDVkWWdnVThteXRqSlk3a0MvSjJuS1FyQmJKS3ZTMnZYV3pYOXg2eEd1aU1jR2JpWUQzQVN3R1IwY1ZBZW5UMlVic3c5Y29jVG1nS0JoQ3U3aHAvRURFeThCSnV5U2dmcGZ2ZkRrZ1JuaHNXOFk1dDBib2VMaWFQcWhqWHBrTEtuYWNTRzFMcXI3MjhvTWxHZzc1U3labE1tVm1HREYxam9DLzVFWFJmdW1qNi94RmFXSW8wQVNGdkxEamxEWDhjMllxdDUxTm9TMTVES2pobE12YUlIU1BjUXR5M1VHZ29Fcjc4T2FZWVNVaVZZeERHOWs2dklhVzI3dUh3eTNBVUw5RUJtZVh4OTc0Z0tXMnBoNjRwcTEzQ1lBT1FNZTVjb0x5TlFyWXEraG1JMmtJWTJTTlhHcFE1dE9FQTNEQ242b1ZYbGNoWFljYVJaYU1RV3hsRG01K2wvQ0lCemxjZ0J2b3pING13cUg1Z3RyU1VXamt2Q0R6MCsyMFZ5YkpXaTMzRHV6ek1QQXFjV1R3b3JZSzhvbDUxWTRoY2FrMFVRYVBLb1RPSm5CTkhFbkZKSEoiLCJtYWMiOiI3ZWI3YWE0Y2NhNTA5OWE3MmI0ZGJjZDI4MjMxNDY0YTY4OGJlNzA4NDc3OGQzN2RiZjUyMzg5MGFiZjQ4M2U5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 18:24:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjRjcndhbFlDcm0yeWRtSWlGZ3ArNVE9PSIsInZhbHVlIjoiUmxoVkZRUEtweC9CdEQ2d2VraTMwazQrUnBCMzNQMzluMjhYSWF0NFFoZjU3eFkwVUVnQkNOWDlHdG9NZjZtSGpIZjh3ZGNmVS9KQUl2MStPYkV1UnozYzBLZ2VWaWJCMklQOXY4blBKc0JhMDZKbzlRWnplVktodDl0RWxUNFV1U1ZOenpXWURhTEJNV2Jlb2JzMHBOeHVjb0lxR0hEelMwOXdhSDhlS2xvUGRSQjVSYlNhcXo1USswWlhLRGlaMG4yTHZwVFBwdTNQRzlMekZYUDVpNEdOT3ptaHZ3QklpbWorazRtNFhXb3BIOFByQmVKQks4b0dFYnR2MlVuY3dSQ2lYQXJma3hmN2ZNNThpSG1vVkl0NVUvcWtoOHUxcFZSNkNpWW80ZFNOVEhFWGVWYlNDdm1BUjVxVUthekxlRlBBSFBSRm1oRjhTZkw3SmI4VW9HaVVZc0xxaHVKdDBRWGcwM0lCaHIycXZOSGRFRXowYXBEM3Y1M0QzVHF5cWFrM0w0OXIxWVpZc2dVQ2JHcTlJbjRpemxmRktOOFRDY2JYR2pjcTVnM2Z5NGFQd05rbGthMVZNNXFRcDRCdk9NVWhqUUI3ak85UTlzSzZIdXpidXZZZVRtVnQ2ZzVHSnJiS0FqbU1SUHVMeFBvM2U2b1ZoNFdzWHYvbmpnV00iLCJtYWMiOiI0NmZjMDczYTRkMjIwMzdlMDM4OGEwYWQ1YjdjYjBjM2RiZGQwYTQwOGQ5NDU3NzIxNWQ1N2QzNGRlOWM4OWJmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 18:24:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IktzZHN4SHo5U2F5Qm1DbFk5ZzhUbVE9PSIsInZhbHVlIjoiV2FhSGVYSDNwY2ZQRGJPZWc3MzJBWm81bS9TdzI1M2E4Um5wMzhFUncwQTFGMFBTMysreURMZGc2S1pkZGhHa24xTytRRHFxNDFJZ0VqeDVkWWdnVThteXRqSlk3a0MvSjJuS1FyQmJKS3ZTMnZYV3pYOXg2eEd1aU1jR2JpWUQzQVN3R1IwY1ZBZW5UMlVic3c5Y29jVG1nS0JoQ3U3aHAvRURFeThCSnV5U2dmcGZ2ZkRrZ1JuaHNXOFk1dDBib2VMaWFQcWhqWHBrTEtuYWNTRzFMcXI3MjhvTWxHZzc1U3labE1tVm1HREYxam9DLzVFWFJmdW1qNi94RmFXSW8wQVNGdkxEamxEWDhjMllxdDUxTm9TMTVES2pobE12YUlIU1BjUXR5M1VHZ29Fcjc4T2FZWVNVaVZZeERHOWs2dklhVzI3dUh3eTNBVUw5RUJtZVh4OTc0Z0tXMnBoNjRwcTEzQ1lBT1FNZTVjb0x5TlFyWXEraG1JMmtJWTJTTlhHcFE1dE9FQTNEQ242b1ZYbGNoWFljYVJaYU1RV3hsRG01K2wvQ0lCemxjZ0J2b3pING13cUg1Z3RyU1VXamt2Q0R6MCsyMFZ5YkpXaTMzRHV6ek1QQXFjV1R3b3JZSzhvbDUxWTRoY2FrMFVRYVBLb1RPSm5CTkhFbkZKSEoiLCJtYWMiOiI3ZWI3YWE0Y2NhNTA5OWE3MmI0ZGJjZDI4MjMxNDY0YTY4OGJlNzA4NDc3OGQzN2RiZjUyMzg5MGFiZjQ4M2U5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 18:24:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjRjcndhbFlDcm0yeWRtSWlGZ3ArNVE9PSIsInZhbHVlIjoiUmxoVkZRUEtweC9CdEQ2d2VraTMwazQrUnBCMzNQMzluMjhYSWF0NFFoZjU3eFkwVUVnQkNOWDlHdG9NZjZtSGpIZjh3ZGNmVS9KQUl2MStPYkV1UnozYzBLZ2VWaWJCMklQOXY4blBKc0JhMDZKbzlRWnplVktodDl0RWxUNFV1U1ZOenpXWURhTEJNV2Jlb2JzMHBOeHVjb0lxR0hEelMwOXdhSDhlS2xvUGRSQjVSYlNhcXo1USswWlhLRGlaMG4yTHZwVFBwdTNQRzlMekZYUDVpNEdOT3ptaHZ3QklpbWorazRtNFhXb3BIOFByQmVKQks4b0dFYnR2MlVuY3dSQ2lYQXJma3hmN2ZNNThpSG1vVkl0NVUvcWtoOHUxcFZSNkNpWW80ZFNOVEhFWGVWYlNDdm1BUjVxVUthekxlRlBBSFBSRm1oRjhTZkw3SmI4VW9HaVVZc0xxaHVKdDBRWGcwM0lCaHIycXZOSGRFRXowYXBEM3Y1M0QzVHF5cWFrM0w0OXIxWVpZc2dVQ2JHcTlJbjRpemxmRktOOFRDY2JYR2pjcTVnM2Z5NGFQd05rbGthMVZNNXFRcDRCdk9NVWhqUUI3ak85UTlzSzZIdXpidXZZZVRtVnQ2ZzVHSnJiS0FqbU1SUHVMeFBvM2U2b1ZoNFdzWHYvbmpnV00iLCJtYWMiOiI0NmZjMDczYTRkMjIwMzdlMDM4OGEwYWQ1YjdjYjBjM2RiZGQwYTQwOGQ5NDU3NzIxNWQ1N2QzNGRlOWM4OWJmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 18:24:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1215689342\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-528399507 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-528399507\", {\"maxDepth\":0})</script>\n"}}