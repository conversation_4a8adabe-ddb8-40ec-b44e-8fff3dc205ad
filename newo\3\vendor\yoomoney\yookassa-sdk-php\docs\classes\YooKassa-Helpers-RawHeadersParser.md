# [YooKassa API SDK](../home.md)

# Class: \YooKassa\Helpers\RawHeadersParser
### Namespace: [\YooKassa\Helpers](../namespaces/yookassa-helpers.md)
---
**Summary:**

Класс, представляющий модель Random.

**Description:**

Класс хэлпера для генерации случайных значений, используется в тестах.

---
### Constants
* No constants found

---
### Methods
| Visibility | Name | Flag | Summary |
| ----------:| ---- | ---- | ------- |
| public | [parse()](../classes/YooKassa-Helpers-RawHeadersParser.md#method_parse) |  |  |

---
### Details
* File: [lib/Helpers/RawHeadersParser.php](../../lib/Helpers/RawHeadersParser.php)
* Package: YooKassa\Helpers
* Class Hierarchy:
  * \YooKassa\Helpers\RawHeadersParser

* See Also:
  * [](https://yookassa.ru/developers/api)

---
### Tags
| Tag | Version | Description |
| --- | ------- | ----------- |
| category |  | Class |
| author |  | <EMAIL> |

---
## Methods
<a name="method_parse" class="anchor"></a>
#### public parse() : mixed

```php
Static public parse(mixed $rawHeaders) : mixed
```

**Details:**
* Inherited From: [\YooKassa\Helpers\RawHeadersParser](../classes/YooKassa-Helpers-RawHeadersParser.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">mixed</code> | rawHeaders  |  |

**Returns:** mixed - 



---

### Top Namespaces

* [\YooKassa](../namespaces/yookassa.md)

---

### Reports
* [Errors - 0](../reports/errors.md)
* [Markers - 0](../reports/markers.md)
* [Deprecated - 25](../reports/deprecated.md)

---

This document was automatically generated from source code comments on 2024-07-02 using [phpDocumentor](http://www.phpdoc.org/)

&copy; 2024 YooMoney