# [YooKassa API SDK](../home.md)

# Class: \YooKassa\Helpers\Config\ConfigurationLoader
### Namespace: [\YooKassa\Helpers\Config](../namespaces/yookassa-helpers-config.md)
---
**Summary:**

Класс, представляющий модель ConfigurationLoader.

**Description:**

Класс для загрузки конфига Curl клиента.

---
### Constants
* No constants found

---
### Methods
| Visibility | Name | Flag | Summary |
| ----------:| ---- | ---- | ------- |
| public | [getConfig()](../classes/YooKassa-Helpers-Config-ConfigurationLoader.md#method_getConfig) |  |  |
| public | [load()](../classes/YooKassa-Helpers-Config-ConfigurationLoader.md#method_load) |  |  |

---
### Details
* File: [lib/Helpers/Config/ConfigurationLoader.php](../../lib/Helpers/Config/ConfigurationLoader.php)
* Package: YooKassa\Helpers
* Class Hierarchy:
  * \YooKassa\Helpers\Config\ConfigurationLoader
* Implements:
  * [\YooKassa\Helpers\Config\ConfigurationLoaderInterface](../classes/YooKassa-Helpers-Config-ConfigurationLoaderInterface.md)

* See Also:
  * [](https://yookassa.ru/developers/api)

---
### Tags
| Tag | Version | Description |
| --- | ------- | ----------- |
| category |  | Class |
| author |  | <EMAIL> |

---
## Methods
<a name="method_getConfig" class="anchor"></a>
#### public getConfig() : array

```php
public getConfig() : array
```

**Details:**
* Inherited From: [\YooKassa\Helpers\Config\ConfigurationLoader](../classes/YooKassa-Helpers-Config-ConfigurationLoader.md)

**Returns:** array - 


<a name="method_load" class="anchor"></a>
#### public load() : self

```php
public load(mixed $filePath = null) : self
```

**Details:**
* Inherited From: [\YooKassa\Helpers\Config\ConfigurationLoader](../classes/YooKassa-Helpers-Config-ConfigurationLoader.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">mixed</code> | filePath  |  |

##### Throws:
| Type | Description |
| ---- | ----------- |
| \JsonException |  |

**Returns:** self - 



---

### Top Namespaces

* [\YooKassa](../namespaces/yookassa.md)

---

### Reports
* [Errors - 0](../reports/errors.md)
* [Markers - 0](../reports/markers.md)
* [Deprecated - 25](../reports/deprecated.md)

---

This document was automatically generated from source code comments on 2024-07-02 using [phpDocumentor](http://www.phpdoc.org/)

&copy; 2024 YooMoney