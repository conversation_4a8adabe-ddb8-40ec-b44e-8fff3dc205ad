{"__meta": {"id": "X631cf3ecd08d0bcb6d1fe67a4556f27a", "datetime": "2025-06-08 15:43:12", "utime": **********.830002, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.086934, "end": **********.830024, "duration": 0.7430899143218994, "duration_str": "743ms", "measures": [{"label": "Booting", "start": **********.086934, "relative_start": 0, "end": **********.738001, "relative_end": **********.738001, "duration": 0.6510670185089111, "duration_str": "651ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.738016, "relative_start": 0.6510818004608154, "end": **********.830027, "relative_end": 3.0994415283203125e-06, "duration": 0.0920112133026123, "duration_str": "92.01ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45388800, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1671\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1671-1681</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01481, "accumulated_duration_str": "14.81ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.787292, "duration": 0.01422, "duration_str": "14.22ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 96.016}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.816736, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 96.016, "width_percent": 3.984}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-386131084 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-386131084\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-225280822 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-225280822\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-575798850 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-575798850\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1196411221 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749396510751%7C76%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ikd5bzBObEx2YTBoVzNzTHBsODBNNmc9PSIsInZhbHVlIjoiSUV5ZTVUS0FUa3phNW9xVkw2L2kzOEh2TldiRnFlZlZ0eHNYRW4yK0c5UkdBTks0cCtMbWdwUkxoQzlFejJGS1Y4MTc2b1NyWlIrWHhYQ214dVZ2bG5VVUtxOWgxWllQTkVYZVQzN3FlQnlwcFFaamUxOTFFUlJCakwzYkVobEo2UXd0L3U2N1FmK0FkNTlxQnRUTERqMys2SWgxZ0lZTy9uTEo1TFRMak9uaDJCRFZJc25FaXdCLzUzNHlGblNCaVVpUkkyVDVta2VuM1BldzZlc3NDK3JWcGdhMHo1YWNrb0lKMm94L3p2Qng5SFdMekNEMW95NTVDU0RQdmhTYkV0QmJRL3JXRkh1U3I2c0Irby9GUE9OUUhweWMxcEg3VCtYcmxacXZmMkhvb2FBRFltM3h6TEJabzRFbmFxS1VIbitmRmRMZjFpQllzY1BrS2pmUTY0cFJ1RnNpUlBEWVdhekxFcFdpc054TXc4U09MTGxJQ2wvZW5yNmVvRnp6bG1YVVJ2bmRKSnJxT3BCS2pVVHJXM1pvcHFUQWR1OXBGTnlXcFBvWksvSVBwRXdYalZBZEZlU1crYmN0QTFiejdXZmVjT1ExT3lzc0FHb3EzKy9vbVI2Ylo2NFBwcGtVamR3ZDI0Q1pTVGFNbldCTDhDU2hBdjJaSlRlWTNxTkMiLCJtYWMiOiIwZjkzMGRiNGM2NmZlMzA2NDFjYWYzN2M5NWJjOGI4NTIzMTc2ZmE5ZjVkMmRkNjI3Yjk0ZjE4M2JkNTUzZjYyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ild2ZTU4TGZwcDdmYU1DeEZWNWRtdmc9PSIsInZhbHVlIjoiWlpIT1J1V0crWGFUUWFKOXRBZGVlbUJGVmI2SEMwdFRCMnNYYXAxTWFHTmZjUEdtc1F1dzMyMEI0bUN0NVFXenNQaU1FWnE3TXRjcnpjanEwSGE3UFppVmhWNGl0S1JFZkw3UWVCVnlLZGRUREFoN0UvWndicHhrMkV1WFBMd2c1TEhLRGRJRU0vcEdFVmp2UllDTEtPZ1dobGlKZWNIVDVyLzh1cW0rSmtqVnZHd1V6ZDJHeFE2L2FwRlhWMEVOVjJBZ0RLc1U2aHBUMi9iTDFWMWN0dUdsVVJLUVZ3VmpWOHdQOVlpUklZVTdzQ2hPY3pnYlY1am5EZTgrZHFFY1p0M3RrdW10UDJ1dFBOTUJib0E1MEF3aHFIN0JJcHdEMGY3d1VualhuTFNZcXhtaG9zbzJXNVNUSmcvSEZQUFZsc1RLaVVJWGRaWnVCNDhVTTdQOU12K0dCWllRSW5MbmZWRkVFL2JIZGl0N1RhdHcrdmdocDMwRFVoUU1nNmIrVWNpUHJld0ZPV2pvRmV6elBKU2pSMWhOcDZheHR2b0dOS3AyU00vT0JPQ2Npa2JXY3p4VjlmWll3UU9NQkpWT0lGQ0x0YjdDQlNkSlNOSWQrRVBWUk5ucmh2U1BVMG9PK2FxQTZPVW1lbDcwdmg4SnZGcXlNYWd1d3lMb09NK0wiLCJtYWMiOiI0N2VlOTM0NmNmYTIwMDlhYjEyMmExMjkyNDQ2NDRjNzQzMmIyZTY0N2Y2ZTVjNjk1NzM1ZTk0ZjU1NTU0ZWZlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1196411221\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-9047645 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xNV7SvMX3AgbiuDVO62eNXfryfZD4KbWr3rEIkXt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-9047645\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1941538334 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:43:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjNxMDFJZ3dFVHZWQy81UmhveDM1WlE9PSIsInZhbHVlIjoiZlFtZW13YTFwSVFUYjlMeDhheDFCUXp0N3ZvcFFuSjg2UzRvNXV3aDBWNFZEcHVMTDQ3YUVQci91SmZBcFp4UkNsaHVHallmN0NxRzE1L1Y4eGNRdDczaGZEcXM2MEN4cU1iVW95am9STTg5N2RKQVo5dXVvSllnQW1VQWFYeHJYSWxJc0wxK0FLZmNFcXE0QTFySnozak1tQVFWZlZNMm1MczJLdnQyQUMybTJpRTBDeG15dWZkKysyLzYzYkxWVS9YQXoyRVpwdXQ4VTNzaEpoVkhQcVhvdWc3UnVDOUJoczAwN0xEampZdkM5MkVCN3hZZ0FhZXl3TFF3KzdvSEtQUWlob2NpeFJ6RUpwYlNBay9oUHpBSC9ZMXhKRVZ2dnBIcmM4RkU1b2ZCcGpkWTl0TVJRWWdpSjR6UHhKb1RvL01GRmhFR2QrUWJyVnhLRnBzQTQ2TlovM0QwdHJaTU5aOGxyUWc4eUN5TU9QeXlNV3VoZkJhdHR3YjlHdHhqUkE2ZVpuNmtEQ0NPSUVBYTVyTW9TdU9BV2NiRHN1QzBmSnRPOCt5ZExrNG9kRVlYQzdVcmR5cWdxaTVBd2dqbFlqN0R1N1Z5bXFqSFN3SWh3VUtoVFU3cGtYNDFYS1dvOVYvZEVxMHR6NTNmc1Z3Sk9KcWR0aHFhUUZmaVVLZ2wiLCJtYWMiOiJhYzYwMzcyNjQ4ZjRhZDY3MTRmMDlkMmEyOWVkOTY3M2MxNTlkNDYwYzllNDAzNzRmNmY4MjcwY2I4OWQzODg2IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:43:12 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImZNWktsMEdhOVEzdXdrc1NoMmpCRlE9PSIsInZhbHVlIjoiUFZMYlVHbzU5WXNzNFFhdnBDWi9ndzUwTy9UZDdoSUtYT3BqQWRURWhoMERvRHIremE3ODF3SFhKaTByWFp2MmUrQ25CN1FvTFBianRBamtGYjRlM2FwNmdINlk4Nm1JN21GVm1tdDMzc1djR2h5bkxDbzZ1S0hKK1pxckNmQlJBUXI5cjVyS3BJaHJCQlEwbjl1NjdpZWFGTVRRaXpYUW9HSnFmcnNnSk9DZGw3cnFrWUFnZXVQZTZlZi9xL0dUb0dYdmlQT3dJa1RWUndyL2poa2EwS3ZORVZvRjgydnNPY1lydHg0SHFtL2xnazVYZnZTZUxiaUdEeVplRC8zbGRhT3Ewa3JOb1dWVVpsTFRReEFHbVpPMnllZURwS3Jsc2RVUUJqSjNHRDAvNkt6ZHFxTnk1S0xiSUFkNVRVNlFORjdNSWFlVGxnWFk5QkowR2dkRG5oQzU4VjhObk9hWno3TUx3RGhRSm45dTV1UzJLQ21CQnF1dWVpNjlPNThSYmdxMnFZNTN1c0RmRnRjQjZScDg1bkZ1YVNyV2ZtdTc0cWdkVWg2THBVbENVNVl4UW1aeG9xbjJiSFltSERBVWFrdlZ2dE5DMUYvNkJiL1MyWVNhOFlUN2pyZVBsNC9vN1M3QVZncTJwbHp3ZnZTRENiY1EreCtWUWxZWktSZ0UiLCJtYWMiOiJmOGU5ZGNjOGU0MDIzYTAyNjgzZDU5ZjU3MDNlZjBlYmZiZGY2ZTgxZjI4YjYyMDMwMDg0MTkwNTQxMGY3ZDFlIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:43:12 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjNxMDFJZ3dFVHZWQy81UmhveDM1WlE9PSIsInZhbHVlIjoiZlFtZW13YTFwSVFUYjlMeDhheDFCUXp0N3ZvcFFuSjg2UzRvNXV3aDBWNFZEcHVMTDQ3YUVQci91SmZBcFp4UkNsaHVHallmN0NxRzE1L1Y4eGNRdDczaGZEcXM2MEN4cU1iVW95am9STTg5N2RKQVo5dXVvSllnQW1VQWFYeHJYSWxJc0wxK0FLZmNFcXE0QTFySnozak1tQVFWZlZNMm1MczJLdnQyQUMybTJpRTBDeG15dWZkKysyLzYzYkxWVS9YQXoyRVpwdXQ4VTNzaEpoVkhQcVhvdWc3UnVDOUJoczAwN0xEampZdkM5MkVCN3hZZ0FhZXl3TFF3KzdvSEtQUWlob2NpeFJ6RUpwYlNBay9oUHpBSC9ZMXhKRVZ2dnBIcmM4RkU1b2ZCcGpkWTl0TVJRWWdpSjR6UHhKb1RvL01GRmhFR2QrUWJyVnhLRnBzQTQ2TlovM0QwdHJaTU5aOGxyUWc4eUN5TU9QeXlNV3VoZkJhdHR3YjlHdHhqUkE2ZVpuNmtEQ0NPSUVBYTVyTW9TdU9BV2NiRHN1QzBmSnRPOCt5ZExrNG9kRVlYQzdVcmR5cWdxaTVBd2dqbFlqN0R1N1Z5bXFqSFN3SWh3VUtoVFU3cGtYNDFYS1dvOVYvZEVxMHR6NTNmc1Z3Sk9KcWR0aHFhUUZmaVVLZ2wiLCJtYWMiOiJhYzYwMzcyNjQ4ZjRhZDY3MTRmMDlkMmEyOWVkOTY3M2MxNTlkNDYwYzllNDAzNzRmNmY4MjcwY2I4OWQzODg2IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:43:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImZNWktsMEdhOVEzdXdrc1NoMmpCRlE9PSIsInZhbHVlIjoiUFZMYlVHbzU5WXNzNFFhdnBDWi9ndzUwTy9UZDdoSUtYT3BqQWRURWhoMERvRHIremE3ODF3SFhKaTByWFp2MmUrQ25CN1FvTFBianRBamtGYjRlM2FwNmdINlk4Nm1JN21GVm1tdDMzc1djR2h5bkxDbzZ1S0hKK1pxckNmQlJBUXI5cjVyS3BJaHJCQlEwbjl1NjdpZWFGTVRRaXpYUW9HSnFmcnNnSk9DZGw3cnFrWUFnZXVQZTZlZi9xL0dUb0dYdmlQT3dJa1RWUndyL2poa2EwS3ZORVZvRjgydnNPY1lydHg0SHFtL2xnazVYZnZTZUxiaUdEeVplRC8zbGRhT3Ewa3JOb1dWVVpsTFRReEFHbVpPMnllZURwS3Jsc2RVUUJqSjNHRDAvNkt6ZHFxTnk1S0xiSUFkNVRVNlFORjdNSWFlVGxnWFk5QkowR2dkRG5oQzU4VjhObk9hWno3TUx3RGhRSm45dTV1UzJLQ21CQnF1dWVpNjlPNThSYmdxMnFZNTN1c0RmRnRjQjZScDg1bkZ1YVNyV2ZtdTc0cWdkVWg2THBVbENVNVl4UW1aeG9xbjJiSFltSERBVWFrdlZ2dE5DMUYvNkJiL1MyWVNhOFlUN2pyZVBsNC9vN1M3QVZncTJwbHp3ZnZTRENiY1EreCtWUWxZWktSZ0UiLCJtYWMiOiJmOGU5ZGNjOGU0MDIzYTAyNjgzZDU5ZjU3MDNlZjBlYmZiZGY2ZTgxZjI4YjYyMDMwMDg0MTkwNTQxMGY3ZDFlIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:43:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1941538334\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1027454980 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1027454980\", {\"maxDepth\":0})</script>\n"}}