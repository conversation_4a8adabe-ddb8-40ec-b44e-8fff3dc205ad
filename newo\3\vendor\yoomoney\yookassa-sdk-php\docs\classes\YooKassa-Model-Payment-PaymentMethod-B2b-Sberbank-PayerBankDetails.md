# [YooKassa API SDK](../home.md)

# Class: \YooKassa\Model\Payment\PaymentMethod\B2b\Sberbank\PayerBankDetails
### Namespace: [\YooKassa\Model\Payment\PaymentMethod\B2b\Sberbank](../namespaces/yookassa-model-payment-paymentmethod-b2b-sberbank.md)
---
**Summary:**

Класс, представляющий модель B2bSberbankPayerBankDetails.

**Description:**

Банковские реквизиты плательщика (юридического лица или ИП).

---
### Constants
* No constants found

---
### Properties
| Visibility | Name | Flag | Summary |
| ----------:| ---- | ---- | ------- |
| public | [$account](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetails.md#property_account) |  | Номер счета организации |
| public | [$address](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetails.md#property_address) |  | Адрес организации |
| public | [$bank_bik](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetails.md#property_bank_bik) |  | БИК банка организации |
| public | [$bank_branch](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetails.md#property_bank_branch) |  | Отделение банка организации |
| public | [$bank_name](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetails.md#property_bank_name) |  | Наименование банка организации |
| public | [$bankBik](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetails.md#property_bankBik) |  | БИК банка организации |
| public | [$bankBranch](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetails.md#property_bankBranch) |  | Отделение банка организации |
| public | [$bankName](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetails.md#property_bankName) |  | Наименование банка организации |
| public | [$full_name](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetails.md#property_full_name) |  | Полное наименование организации |
| public | [$fullName](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetails.md#property_fullName) |  | Полное наименование организации |
| public | [$inn](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetails.md#property_inn) |  | ИНН организации |
| public | [$kpp](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetails.md#property_kpp) |  | КПП организации |
| public | [$short_name](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetails.md#property_short_name) |  | Сокращенное наименование организации |
| public | [$shortName](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetails.md#property_shortName) |  | Сокращенное наименование организации |

---
### Methods
| Visibility | Name | Flag | Summary |
| ----------:| ---- | ---- | ------- |
| public | [__construct()](../classes/YooKassa-Common-AbstractObject.md#method___construct) |  | AbstractObject constructor. |
| public | [__get()](../classes/YooKassa-Common-AbstractObject.md#method___get) |  | Возвращает значение свойства. |
| public | [__isset()](../classes/YooKassa-Common-AbstractObject.md#method___isset) |  | Проверяет наличие свойства. |
| public | [__set()](../classes/YooKassa-Common-AbstractObject.md#method___set) |  | Устанавливает значение свойства. |
| public | [__unset()](../classes/YooKassa-Common-AbstractObject.md#method___unset) |  | Удаляет свойство. |
| public | [fromArray()](../classes/YooKassa-Common-AbstractObject.md#method_fromArray) |  | Устанавливает значения свойств текущего объекта из массива. |
| public | [getAccount()](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetails.md#method_getAccount) |  | Возвращает номер счета организации. |
| public | [getAddress()](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetails.md#method_getAddress) |  | Возвращает адрес организации. |
| public | [getBankBik()](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetails.md#method_getBankBik) |  | Возвращает БИК банка организации. |
| public | [getBankBranch()](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetails.md#method_getBankBranch) |  | Возвращает отделение банка организации. |
| public | [getBankName()](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetails.md#method_getBankName) |  | Возвращает наименование банка организации. |
| public | [getFullName()](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetails.md#method_getFullName) |  | Возвращает полное наименование организации. |
| public | [getInn()](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetails.md#method_getInn) |  | Возвращает ИНН организации. |
| public | [getKpp()](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetails.md#method_getKpp) |  | Возвращает КПП организации. |
| public | [getShortName()](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetails.md#method_getShortName) |  | Возвращает сокращенное наименование организации. |
| public | [getValidator()](../classes/YooKassa-Common-AbstractObject.md#method_getValidator) |  |  |
| public | [jsonSerialize()](../classes/YooKassa-Common-AbstractObject.md#method_jsonSerialize) |  | Возвращает ассоциативный массив со свойствами текущего объекта для его дальнейшей JSON сериализации. |
| public | [offsetExists()](../classes/YooKassa-Common-AbstractObject.md#method_offsetExists) |  | Проверяет наличие свойства. |
| public | [offsetGet()](../classes/YooKassa-Common-AbstractObject.md#method_offsetGet) |  | Возвращает значение свойства. |
| public | [offsetSet()](../classes/YooKassa-Common-AbstractObject.md#method_offsetSet) |  | Устанавливает значение свойства. |
| public | [offsetUnset()](../classes/YooKassa-Common-AbstractObject.md#method_offsetUnset) |  | Удаляет свойство. |
| public | [setAccount()](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetails.md#method_setAccount) |  | Устанавливает номер счета организации. |
| public | [setAddress()](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetails.md#method_setAddress) |  | Устанавливает адрес организации. |
| public | [setBankBik()](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetails.md#method_setBankBik) |  | Устанавливает БИК банка организации. |
| public | [setBankBranch()](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetails.md#method_setBankBranch) |  | Устанавливает отделение банка организации. |
| public | [setBankName()](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetails.md#method_setBankName) |  | Устанавливает наименование банка организации. |
| public | [setFullName()](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetails.md#method_setFullName) |  | Устанавливает полное наименование организации. |
| public | [setInn()](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetails.md#method_setInn) |  | Устанавливает ИНН организации. |
| public | [setKpp()](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetails.md#method_setKpp) |  | Устанавливает КПП организации. |
| public | [setShortName()](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetails.md#method_setShortName) |  | Устанавливает сокращенное наименование организации. |
| public | [toArray()](../classes/YooKassa-Common-AbstractObject.md#method_toArray) |  | Возвращает ассоциативный массив со свойствами текущего объекта для его дальнейшей JSON сериализации Является алиасом метода AbstractObject::jsonSerialize(). |
| protected | [getUnknownProperties()](../classes/YooKassa-Common-AbstractObject.md#method_getUnknownProperties) |  | Возвращает массив свойств которые не существуют, но были заданы у объекта. |
| protected | [validatePropertyValue()](../classes/YooKassa-Common-AbstractObject.md#method_validatePropertyValue) |  |  |

---
### Details
* File: [lib/Model/Payment/PaymentMethod/B2b/Sberbank/PayerBankDetails.php](../../lib/Model/Payment/PaymentMethod/B2b/Sberbank/PayerBankDetails.php)
* Package: YooKassa\Model
* Class Hierarchy: 
  * [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)
  * \YooKassa\Model\Payment\PaymentMethod\B2b\Sberbank\PayerBankDetails
* Implements:
  * [\YooKassa\Model\Payment\PaymentMethod\B2b\Sberbank\PayerBankDetailsInterface](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetailsInterface.md)

* See Also:
  * [](https://yookassa.ru/developers/api)

---
### Tags
| Tag | Version | Description |
| --- | ------- | ----------- |
| category |  | Class |
| author |  | <EMAIL> |

---
## Properties
<a name="property_account"></a>
#### public $account : string
---
***Description***

Номер счета организации

**Type:** <a href="../string"><abbr title="string">string</abbr></a>

**Details:**


<a name="property_address"></a>
#### public $address : string
---
***Description***

Адрес организации

**Type:** <a href="../string"><abbr title="string">string</abbr></a>

**Details:**


<a name="property_bank_bik"></a>
#### public $bank_bik : string
---
***Description***

БИК банка организации

**Type:** <a href="../string"><abbr title="string">string</abbr></a>

**Details:**


<a name="property_bank_branch"></a>
#### public $bank_branch : string
---
***Description***

Отделение банка организации

**Type:** <a href="../string"><abbr title="string">string</abbr></a>

**Details:**


<a name="property_bank_name"></a>
#### public $bank_name : string
---
***Description***

Наименование банка организации

**Type:** <a href="../string"><abbr title="string">string</abbr></a>

**Details:**


<a name="property_bankBik"></a>
#### public $bankBik : string
---
***Description***

БИК банка организации

**Type:** <a href="../string"><abbr title="string">string</abbr></a>

**Details:**


<a name="property_bankBranch"></a>
#### public $bankBranch : string
---
***Description***

Отделение банка организации

**Type:** <a href="../string"><abbr title="string">string</abbr></a>

**Details:**


<a name="property_bankName"></a>
#### public $bankName : string
---
***Description***

Наименование банка организации

**Type:** <a href="../string"><abbr title="string">string</abbr></a>

**Details:**


<a name="property_full_name"></a>
#### public $full_name : string
---
***Description***

Полное наименование организации

**Type:** <a href="../string"><abbr title="string">string</abbr></a>

**Details:**


<a name="property_fullName"></a>
#### public $fullName : string
---
***Description***

Полное наименование организации

**Type:** <a href="../string"><abbr title="string">string</abbr></a>

**Details:**


<a name="property_inn"></a>
#### public $inn : string
---
***Description***

ИНН организации

**Type:** <a href="../string"><abbr title="string">string</abbr></a>

**Details:**


<a name="property_kpp"></a>
#### public $kpp : string
---
***Description***

КПП организации

**Type:** <a href="../string"><abbr title="string">string</abbr></a>

**Details:**


<a name="property_short_name"></a>
#### public $short_name : string
---
***Description***

Сокращенное наименование организации

**Type:** <a href="../string"><abbr title="string">string</abbr></a>

**Details:**


<a name="property_shortName"></a>
#### public $shortName : string
---
***Description***

Сокращенное наименование организации

**Type:** <a href="../string"><abbr title="string">string</abbr></a>

**Details:**



---
## Methods
<a name="method___construct" class="anchor"></a>
#### public __construct() : mixed

```php
public __construct(array|null $data = []) : mixed
```

**Summary**

AbstractObject constructor.

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">array OR null</code> | data  |  |

**Returns:** mixed - 


<a name="method___get" class="anchor"></a>
#### public __get() : mixed

```php
public __get(string $propertyName) : mixed
```

**Summary**

Возвращает значение свойства.

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string</code> | propertyName  | Имя свойства |

**Returns:** mixed - Значение свойства


<a name="method___isset" class="anchor"></a>
#### public __isset() : bool

```php
public __isset(string $propertyName) : bool
```

**Summary**

Проверяет наличие свойства.

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string</code> | propertyName  | Имя проверяемого свойства |

**Returns:** bool - True если свойство имеется, false если нет


<a name="method___set" class="anchor"></a>
#### public __set() : void

```php
public __set(string $propertyName, mixed $value) : void
```

**Summary**

Устанавливает значение свойства.

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string</code> | propertyName  | Имя свойства |
| <code lang="php">mixed</code> | value  | Значение свойства |

**Returns:** void - 


<a name="method___unset" class="anchor"></a>
#### public __unset() : void

```php
public __unset(string $propertyName) : void
```

**Summary**

Удаляет свойство.

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string</code> | propertyName  | Имя удаляемого свойства |

**Returns:** void - 


<a name="method_fromArray" class="anchor"></a>
#### public fromArray() : void

```php
public fromArray(array|\Traversable $sourceArray) : void
```

**Summary**

Устанавливает значения свойств текущего объекта из массива.

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">array OR \Traversable</code> | sourceArray  | Ассоциативный массив с настройками |

**Returns:** void - 


<a name="method_getAccount" class="anchor"></a>
#### public getAccount() : string|null

```php
public getAccount() : string|null
```

**Summary**

Возвращает номер счета организации.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\PaymentMethod\B2b\Sberbank\PayerBankDetails](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetails.md)

**Returns:** string|null - Номер счета организации


<a name="method_getAddress" class="anchor"></a>
#### public getAddress() : string|null

```php
public getAddress() : string|null
```

**Summary**

Возвращает адрес организации.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\PaymentMethod\B2b\Sberbank\PayerBankDetails](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetails.md)

**Returns:** string|null - Адрес организации


<a name="method_getBankBik" class="anchor"></a>
#### public getBankBik() : string|null

```php
public getBankBik() : string|null
```

**Summary**

Возвращает БИК банка организации.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\PaymentMethod\B2b\Sberbank\PayerBankDetails](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetails.md)

**Returns:** string|null - БИК банка организации


<a name="method_getBankBranch" class="anchor"></a>
#### public getBankBranch() : string|null

```php
public getBankBranch() : string|null
```

**Summary**

Возвращает отделение банка организации.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\PaymentMethod\B2b\Sberbank\PayerBankDetails](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetails.md)

**Returns:** string|null - Отделение банка организации


<a name="method_getBankName" class="anchor"></a>
#### public getBankName() : string|null

```php
public getBankName() : string|null
```

**Summary**

Возвращает наименование банка организации.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\PaymentMethod\B2b\Sberbank\PayerBankDetails](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetails.md)

**Returns:** string|null - Наименование банка организации


<a name="method_getFullName" class="anchor"></a>
#### public getFullName() : string|null

```php
public getFullName() : string|null
```

**Summary**

Возвращает полное наименование организации.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\PaymentMethod\B2b\Sberbank\PayerBankDetails](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetails.md)

**Returns:** string|null - Полное наименование организации


<a name="method_getInn" class="anchor"></a>
#### public getInn() : string|null

```php
public getInn() : string|null
```

**Summary**

Возвращает ИНН организации.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\PaymentMethod\B2b\Sberbank\PayerBankDetails](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetails.md)

**Returns:** string|null - ИНН организации


<a name="method_getKpp" class="anchor"></a>
#### public getKpp() : string|null

```php
public getKpp() : string|null
```

**Summary**

Возвращает КПП организации.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\PaymentMethod\B2b\Sberbank\PayerBankDetails](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetails.md)

**Returns:** string|null - КПП организации


<a name="method_getShortName" class="anchor"></a>
#### public getShortName() : string|null

```php
public getShortName() : string|null
```

**Summary**

Возвращает сокращенное наименование организации.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\PaymentMethod\B2b\Sberbank\PayerBankDetails](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetails.md)

**Returns:** string|null - Сокращенное наименование организации


<a name="method_getValidator" class="anchor"></a>
#### public getValidator() : \YooKassa\Validator\Validator

```php
public getValidator() : \YooKassa\Validator\Validator
```

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

**Returns:** \YooKassa\Validator\Validator - 


<a name="method_jsonSerialize" class="anchor"></a>
#### public jsonSerialize() : array

```php
public jsonSerialize() : array
```

**Summary**

Возвращает ассоциативный массив со свойствами текущего объекта для его дальнейшей JSON сериализации.

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

**Returns:** array - Ассоциативный массив со свойствами текущего объекта


<a name="method_offsetExists" class="anchor"></a>
#### public offsetExists() : bool

```php
public offsetExists(string $offset) : bool
```

**Summary**

Проверяет наличие свойства.

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string</code> | offset  | Имя проверяемого свойства |

**Returns:** bool - True если свойство имеется, false если нет


<a name="method_offsetGet" class="anchor"></a>
#### public offsetGet() : mixed

```php
public offsetGet(string $offset) : mixed
```

**Summary**

Возвращает значение свойства.

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string</code> | offset  | Имя свойства |

**Returns:** mixed - Значение свойства


<a name="method_offsetSet" class="anchor"></a>
#### public offsetSet() : void

```php
public offsetSet(string $offset, mixed $value) : void
```

**Summary**

Устанавливает значение свойства.

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string</code> | offset  | Имя свойства |
| <code lang="php">mixed</code> | value  | Значение свойства |

**Returns:** void - 


<a name="method_offsetUnset" class="anchor"></a>
#### public offsetUnset() : void

```php
public offsetUnset(string $offset) : void
```

**Summary**

Удаляет свойство.

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string</code> | offset  | Имя удаляемого свойства |

**Returns:** void - 


<a name="method_setAccount" class="anchor"></a>
#### public setAccount() : self

```php
public setAccount(string|null $account) : self
```

**Summary**

Устанавливает номер счета организации.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\PaymentMethod\B2b\Sberbank\PayerBankDetails](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetails.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string OR null</code> | account  | Номер счета организации |

**Returns:** self - 


<a name="method_setAddress" class="anchor"></a>
#### public setAddress() : self

```php
public setAddress(string|null $address) : self
```

**Summary**

Устанавливает адрес организации.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\PaymentMethod\B2b\Sberbank\PayerBankDetails](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetails.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string OR null</code> | address  | Адрес организации |

**Returns:** self - 


<a name="method_setBankBik" class="anchor"></a>
#### public setBankBik() : self

```php
public setBankBik(string|null $bank_bik) : self
```

**Summary**

Устанавливает БИК банка организации.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\PaymentMethod\B2b\Sberbank\PayerBankDetails](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetails.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string OR null</code> | bank_bik  | БИК банка организации |

**Returns:** self - 


<a name="method_setBankBranch" class="anchor"></a>
#### public setBankBranch() : self

```php
public setBankBranch(string|null $bank_branch) : self
```

**Summary**

Устанавливает отделение банка организации.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\PaymentMethod\B2b\Sberbank\PayerBankDetails](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetails.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string OR null</code> | bank_branch  | Отделение банка организации |

**Returns:** self - 


<a name="method_setBankName" class="anchor"></a>
#### public setBankName() : self

```php
public setBankName(string|null $bank_name) : self
```

**Summary**

Устанавливает наименование банка организации.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\PaymentMethod\B2b\Sberbank\PayerBankDetails](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetails.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string OR null</code> | bank_name  | Наименование банка организации |

**Returns:** self - 


<a name="method_setFullName" class="anchor"></a>
#### public setFullName() : self

```php
public setFullName(string|null $full_name) : self
```

**Summary**

Устанавливает полное наименование организации.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\PaymentMethod\B2b\Sberbank\PayerBankDetails](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetails.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string OR null</code> | full_name  | Полное наименование организации |

**Returns:** self - 


<a name="method_setInn" class="anchor"></a>
#### public setInn() : self

```php
public setInn(string|null $inn) : self
```

**Summary**

Устанавливает ИНН организации.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\PaymentMethod\B2b\Sberbank\PayerBankDetails](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetails.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string OR null</code> | inn  | ИНН организации |

**Returns:** self - 


<a name="method_setKpp" class="anchor"></a>
#### public setKpp() : self

```php
public setKpp(string|null $kpp) : self
```

**Summary**

Устанавливает КПП организации.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\PaymentMethod\B2b\Sberbank\PayerBankDetails](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetails.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string OR null</code> | kpp  | КПП организации |

**Returns:** self - 


<a name="method_setShortName" class="anchor"></a>
#### public setShortName() : self

```php
public setShortName(string|null $short_name) : self
```

**Summary**

Устанавливает сокращенное наименование организации.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\PaymentMethod\B2b\Sberbank\PayerBankDetails](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetails.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string OR null</code> | short_name  | Сокращенное наименование организации |

**Returns:** self - 


<a name="method_toArray" class="anchor"></a>
#### public toArray() : array

```php
public toArray() : array
```

**Summary**

Возвращает ассоциативный массив со свойствами текущего объекта для его дальнейшей JSON сериализации
Является алиасом метода AbstractObject::jsonSerialize().

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

**Returns:** array - Ассоциативный массив со свойствами текущего объекта


<a name="method_getUnknownProperties" class="anchor"></a>
#### protected getUnknownProperties() : array

```php
protected getUnknownProperties() : array
```

**Summary**

Возвращает массив свойств которые не существуют, но были заданы у объекта.

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

**Returns:** array - Ассоциативный массив с не существующими у текущего объекта свойствами


<a name="method_validatePropertyValue" class="anchor"></a>
#### protected validatePropertyValue() : mixed

```php
protected validatePropertyValue(string $propertyName, mixed $propertyValue) : mixed
```

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string</code> | propertyName  |  |
| <code lang="php">mixed</code> | propertyValue  |  |

**Returns:** mixed - 



---

### Top Namespaces

* [\YooKassa](../namespaces/yookassa.md)

---

### Reports
* [Errors - 0](../reports/errors.md)
* [Markers - 0](../reports/markers.md)
* [Deprecated - 25](../reports/deprecated.md)

---

This document was automatically generated from source code comments on 2024-07-02 using [phpDocumentor](http://www.phpdoc.org/)

&copy; 2024 YooMoney