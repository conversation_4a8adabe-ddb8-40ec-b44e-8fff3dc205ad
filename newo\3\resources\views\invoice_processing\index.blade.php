@extends('layouts.admin')
@section('page-title')
    {{__('معالجة فواتير المبيعات')}}
@endsection
@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{route('dashboard')}}">{{__('Dashboard')}}</a></li>
    <li class="breadcrumb-item">{{__('معالجة فواتير المبيعات')}}</li>
@endsection

@push('css-page')
    <link rel="stylesheet" href="{{ asset('css/datatable/buttons.dataTables.min.css') }}">
@endpush

@section('action-btn')
    <div class="float-end">
        <a href="#" data-size="lg" data-url="{{ route('pos.create') }}" data-ajax-popup="true" data-bs-toggle="tooltip" title="{{__('Create New POS')}}" class="btn btn-sm btn-primary">
            <i class="ti ti-plus"></i>
        </a>
    </div>
@endsection

@section('content')
    <div id="printableArea">
        <div class="row mt-3">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5>{{ __('ملخص فواتير نقاط البيع') }}</h5>
                    </div>
                    <div class="card-body table-border-style">
                        <div class="table-responsive">
                            <table class="table datatable">
                                <thead>
                                <tr>
                                    <th>{{__('معرف نقاط البيع')}}</th>
                                    <th>{{ __('تاريخ') }}</th>
                                    <th>{{ __('عميل') }}</th>
                                    <th>{{ __('مستودع') }}</th>
                                    <th>{{ __('مجموع') }}</th>
                                    <th>{{ __('تخفيض') }}</th>
                                    <th>{{ __('المجموع') }}</th>
                                    <th>{{ __('طريقة الدفع') }}</th>
                                </tr>
                                </thead>

                                <tbody>
                                @forelse ($posPayments as $pos)
                                    <tr>
                                        <td class="Id">
                                            <a href="{{ route('invoice.processing.show', $pos->id) }}" class="btn btn-outline-primary">
                                                {{ AUth::user()->posNumberFormat($pos->id) }}
                                            </a>
                                        </td>
                                        <td>{{ Auth::user()->dateFormat($pos->pos_date) }}</td>
                                        <td>{{ !empty($pos->customer) ? $pos->customer->name : __('Walk-in Customer') }}</td>
                                        <td>{{ !empty($pos->warehouse) ? $pos->warehouse->name : '' }}</td>
                                        <td>{{ Auth::user()->priceFormat($pos->getSubTotal()) }}</td>
                                        <td>{{ Auth::user()->priceFormat($pos->getTotalDiscount()) }}</td>
                                        <td>{{ Auth::user()->priceFormat($pos->getTotal()) }}</td>
                                        <td>
                                            @if(!empty($pos->customer) && $pos->customer->is_delivery)
                                                @if($pos->is_payment_set)
                                                    <span class="badge bg-success text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('تم التحصيل من مندوب التوصيل') }} ✅</span>
                                                @else
                                                    <span class="badge bg-warning text-dark" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('جاري التحصيل') }} 🚚</span>
                                                @endif
                                            @elseif(isset($pos->posPayment) && $pos->posPayment->payment_type == 'cash')
                                                <span class="badge bg-success text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('نقد') }} 💵</span>
                                            @elseif(isset($pos->posPayment) && $pos->posPayment->payment_type == 'network')
                                                <span class="badge bg-info text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('شبكة') }} 💳</span>
                                            @elseif(isset($pos->posPayment) && $pos->posPayment->payment_type == 'split')
                                                <span class="badge bg-warning text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('مقسم') }} 💰</span>
                                            @elseif(isset($pos->payment_method))
                                                <span class="badge bg-primary text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ $pos->payment_method }}</span>
                                            @else
                                                <span class="badge bg-success text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('نقد') }} 💵</span>
                                            @endif
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="8" class="text-center">{{ __('لا توجد فواتير متاحة') }}</td>
                                    </tr>
                                @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
