<?php

namespace WhichBrowser\Data;

DeviceModels::$TIZEN_INDEX = array (
  '@AR' => 
  array (
    0 => '(ARMV7 )?SM-Z9005!',
  ),
  '@BA' => 
  array (
    0 => 'Baltic',
  ),
  '@EM' => 
  array (
    0 => 'Emulator',
  ),
  '@FA' => 
  array (
    0 => 'FamilyHub',
  ),
  '@GT' => 
  array (
    0 => 'GT-I8800!',
    1 => 'GT-I8805!',
    2 => 'GT-I9500!',
  ),
  '@HA' => 
  array (
    0 => 'hawkp',
  ),
  '@KI' => 
  array (
    0 => 'KIRAN',
  ),
  '@MO' => 
  array (
    0 => 'Mobile-RD-PQ',
    1 => 'Mobile-Emulator',
  ),
  '@NX' => 
  array (
    0 => 'NX300',
  ),
  '@RF' => 
  array (
    0 => 'RF10M9995!',
    1 => 'RF23M8590!',
    2 => 'RF265BEAE!',
    3 => 'RF28M9580!',
    4 => 'RF56M9540!',
    5 => 'RF85K9993!',
    6 => 'RF85M95A2!',
  ),
  '@RH' => 
  array (
    0 => 'RH81M8090!',
  ),
  '@SC' => 
  array (
    0 => 'SC-03F',
  ),
  '@SD' => 
  array (
    0 => 'sdk',
  ),
  '@SE' => 
  array (
    0 => 'SEC SC-001',
    1 => 'SEC SC-03F',
  ),
  '@SG' => 
  array (
    0 => 'SGH-N099',
  ),
  '@SM' => 
  array (
    0 => 'SM-HIGGS',
    1 => '(ARMV7 )?SM-Z9005!',
    2 => 'SM-Z130!',
    3 => 'SM-Z200!',
    4 => 'SM-Z250!',
    5 => 'SM-Z300!',
    6 => 'SM-Z400!',
    7 => 'SM-Z500!',
    8 => 'SM-Z700!',
    9 => 'SM-Z900!',
    10 => 'SM-Z910!',
    11 => 'SM-G870F0',
    12 => 'SM-R360!',
    13 => 'SM-R600!',
    14 => 'SM-R720!',
    15 => 'SM-R730!',
    16 => 'SM-R732!',
    17 => 'SM-R735!',
    18 => 'SM-R750!',
    19 => 'SM-R760!',
    20 => 'SM-R765!',
    21 => 'SM-R770!',
    22 => 'SM-R805!',
  ),
  '@TI' => 
  array (
    0 => 'TIZEN SM-Z130!',
    1 => 'TIZEN SM-Z300!',
    2 => 'TIZEN Emulator',
  ),
  '@TM' => 
  array (
    0 => 'TM1',
  ),
  '@XU' => 
  array (
    0 => 'xu3',
  ),
  '@Z3' => 
  array (
    0 => 'Z3 Z910F',
  ),
);
