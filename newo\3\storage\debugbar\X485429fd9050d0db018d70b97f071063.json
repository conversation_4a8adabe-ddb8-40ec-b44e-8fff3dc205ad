{"__meta": {"id": "X485429fd9050d0db018d70b97f071063", "datetime": "2025-06-08 16:25:15", "utime": **********.89629, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.294592, "end": **********.896312, "duration": 0.6017200946807861, "duration_str": "602ms", "measures": [{"label": "Booting", "start": **********.294592, "relative_start": 0, "end": **********.812965, "relative_end": **********.812965, "duration": 0.5183730125427246, "duration_str": "518ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.812979, "relative_start": 0.5183870792388916, "end": **********.896314, "relative_end": 1.9073486328125e-06, "duration": 0.08333492279052734, "duration_str": "83.33ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45137888, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0146, "accumulated_duration_str": "14.6ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8492172, "duration": 0.01308, "duration_str": "13.08ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 89.589}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.875665, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 89.589, "width_percent": 5.753}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (17) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.884927, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 95.342, "width_percent": 4.658}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/eyJpdiI6Ild1N2JBYjlWemdxYnI1bkswNlJQdFE9PSIsInZhbHVlIjoiazE4Z3o4RmU0TnNjTnNSbzVUcGlzQT09IiwibWFjIjoiZmU5NTlhNTJmMzU5MzI0NzQ4ZTliMzZlNzEyNTRiMzUwMDcyOGQ5NGMxNWUyOTI3OGI4NjZjNTk5Yjg5ZDg5MSIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-829886346 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-829886346\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-846944385 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-846944385\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2006849454 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2006849454\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-642261634 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6Ild1N2JBYjlWemdxYnI1bkswNlJQdFE9PSIsInZhbHVlIjoiazE4Z3o4RmU0TnNjTnNSbzVUcGlzQT09IiwibWFjIjoiZmU5NTlhNTJmMzU5MzI0NzQ4ZTliMzZlNzEyNTRiMzUwMDcyOGQ5NGMxNWUyOTI3OGI4NjZjNTk5Yjg5ZDg5MSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwl%7C0%7C1982; _clsk=1y4k55%7C1749399896435%7C6%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkVtc1BqOVQwSVlzUmRIdTB3QWZUMEE9PSIsInZhbHVlIjoiSlB3YkxuUU5zU2NFODZUVW1MaElvNTNQb0c4ZzBXazY2VVcydjltQTFZdDZwV2ZjRHFYdnpDSUNLOGVmN1QzcDBkTkw4dUZlRjVGRjBVWEZRU1ZtTkp6ZnlTS20rTDJTOTNjMk1Dc0tzQUNPTEtTODVEbk9LejJDUHVxM0dkZHNkKzBieC91eVFxZ2tRWHVYSm14QnNhYWcybG9EeWwxa2h0a1pCNmpiM0cvOEZERThhQytUc3l6MmJTaDArV25GU1BMSHNKemYyS3hqdEtpVXU1SEF3YzBmRDNxT0NiMUdyZWdYVDJ4aTRFOXp5Nmh6a2xwZG1od2RBWndVT1pnSUw2MnlVWXVCT053RHRDaGVoa3BOU1RPQUhSMEc3cnNKeVpiWHlvcXV6Z2lNc3FrY0tvTzhJY0tzN1VzQWk3b1hxNm1lSUhPZDhqZXU4dGQ1TEZyZ1dPRXdmMjEyZUpxOFVlNEsrRTVWdmlBRktvT0xrQ2dNL1BOZVpIVHU0TnpTbHJlNVpuWjJHM0hvNjh0NXpERGV1Y0hiUDUyNHdTN1ovbzVBSzlaNjMzYWphcHluNjBWekMyR3k5RnI0WEhMdExSRGszRmU3OVd5T3o5WFdOS2VXWjd0cjJOM2dHd1diNmt4cXliNlhwcWxzVWtFVEFVeWlrRkVCcHhMY3FOdWwiLCJtYWMiOiI0Zjk0YjRmZDhhY2ZmZWQ1NWEwNzg3NGQzYThiNjI0ZmUxYzdjOTlmMzA2N2IwNzk5OGMyNzg2ZTNmODk5ZTZhIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImhJdjlia0YzekVVTytzNnN5eFpDa0E9PSIsInZhbHVlIjoibEtRbUN5MGZWRjA5dDdVdDVsK2FMQytQM3dlWkZyd1d0cGxYVUM5TGdkUTlzOExGN0tLZTBQS3N1TGNPTk5CSTh3TWlIZXh6aUw1TE5aSnNBeCtiSlVGSFF1VEd3dGpEdXJzclBxTVNqcm5CRjRUOVdQd00xeUxWZVcrcUlnWUFqZ3RZOTZGaXZLaWV2YjFaVTR1c2ZBeTc3WDBFNmkzaE5lN0Ewbmp6citsSzZEU1d5SjJrNXBLNzcrczVOZG5WLytxbThZeFZ6NmtuTHNTNHNHWlVrbExVYkV0ZS8zS3Y5SzM5QlIyOVNkbW9CWHp4WEtuSEgzUkxvUCs5TkpDUXVrcVh0Z3BmaEYzQUNMYklxd0d1RWp3ZVRpY0I5R01kWlIrTllIY2hqY0d4VjQvR2c4SXVpRTVpYVBpRzJmajRaOGQ3UFRIUjhwSmVnQ3BXbStTVTE1SnZvdzZHcFFPbTBkczk3SEtaYVA4N0R6eTBxUWxFRDVPNDY3cDh6b1hEQ2VKK1dXZ01qVVRzZXhuR0xmMGhQQ0I3MC9EbjI2VlpCTkpXSFdSaWN4RTZLbXhId2JqTm9lZ292c3Z6LzZyZDdyeW9KT1NGeEF5UlJrZHg4ZGIwS096dlVuMFAzYnp6SmhSdnlRN0Nvb2VWZmxaSHNNQ2dpaVBUS0lON0tuLzUiLCJtYWMiOiI3ZGYzZDZjZTllNGZiMTM5NDYzNGFkNjY2YWE3MTM4OGMyZjY3NDViNjFkNTQxOTNkNDg5OTI5OGYwNThkNzA0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-642261634\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1222856617 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">COI6IIrAbucReABHBN7o3ZaIzRbwBCI1gfwrcfCo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1222856617\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1022507992 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 16:25:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IllBWDBnUlFQZ0oxK29UcTg3QWVOWFE9PSIsInZhbHVlIjoiYjd4MnZ6bVg2anFJN0kyY2owenFLdXM2bnYvN1ovVHJPbUtzOUNxYzZWcFM3UlREUWxyNVJNT0IrUzVEenR1WXc0OFFlQ3JEU05ZVDdCWFBFZy95U3duMVdKcWhHc2lJVHVJb01WM1pLVWlqU09iTStnNU4vVThTeW1BYWNQRys5ZnN0aTYyQXJ4dktOOExVdUI4YkkvT0VaSCtDQ0RBeFdqbC9USE9vTk96ei9mR0FiZG55Ym9KNUJYWEY5WEpCTHliMzBubEFyUWVHNWJ2Mk5Scmg4bWMvUU1LQXMzVjZIc2M5a3VVdUhyQ1dndzBzRU13b292cTJ2VXcrM2lLNGwvZy9QdEZrc1NOdVJJWFZBY1VyVy8rWE1Xd1Y5M0t0KzFTNTY5NzN6SncyTmIwMWVrSlBWZ3dYTkkvekxua2pEOTFZMGxZNXlRNFpqRFFIMDlHS3ZRcjcrcVF5R1hxZmNpNjRwbmRleThVTGh3akdvTmVNbkN4V0xHOHhKUGpVVFlJWVdGb21MaGl1d0k2ZGFaNEswV09pRkFicUZiOHpvdHdyR2NNNDQ0THBES3Jtc0FodDhYTXdsZG5Eb21raVQwMSs5NkcxbjFrUGQvTmF6T0RSY0lyUVBvSzBkcUpaRytWajltZ3ozUTdadWxObE1ULzAwZHB6YUtvM2ZTamkiLCJtYWMiOiI3MmNlOTk4ZDEzNzVhNTAxZjUyOWU4YjQ5OWYyMjA2NDdlMDVlYWZmNzY0NWU3MzUyYWFkN2Y1NTU3MmY1MDk0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 18:25:15 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IklOU3JCQ3M3VUNwcDYxMmJPeWhWSkE9PSIsInZhbHVlIjoici9aVUtmUUtGaDVBamhsK2djbjgrcDJWSkNmb3Z6Ty90Sng5TnRScGFoNkh6elZaaHpZemw4ZmZ1RzB5R3FmWVdWbmRwaE5JNFpkR1crU3FVRzRoYkRDQkdEbmlvdkJLaUt3ZWd3allWcFNwQUY1bFRxYXJyRzF4VXIrTWs0OTRlbE1lb1ZnTERTVjZhUzlXY0Jnb25BdHV5T0dBU3ZhMkhtYXNPYWN1N2pDVFR2TW9GMndidXp2Rm1JdmZCWThIbmRjUkN0ZUMvdzJZaHJVWmJhZ1VicXE1MTJQZkczSDk4WEN6V0xab3FKZUhiaHVUYXQ2Qi9jRkY1ZEdGWWpzbUZBZTBUc2ZaUU9nb2h3c3N5TUZDcklOVVhYbDdsNGlYSFRRWEp4Z3BaS0ZIWTFkaXFVdytrTUh5eDNVWUN3U2lRT3YrV0pTUUdsVHB2SWtxSlRHV0lUWElLTHhybWtuQUU2aFQ4aGZqcnJSMS9WWTQ4dHE5eTRKeDQ1SkdwNUh3Q1hrZVJmRFJNd0lhZENKc1FHZ01QRVBmSXdPclVXUHBnN3NTTFNSb29ZYUdLTHh1azhDa0pudmFYSlBTZWsxY3R6NjZrQTlFVUg4QlV4SGVMbGVNakhsU2lwcEI4c1JQRnlFeTFWTnFxQ0dkK01wVTJMNjRFb0d1K003MjBMaWsiLCJtYWMiOiJiYTYyNTA2NGIwMzIyNzFjYmY2ODQ2MWI0MzExZGU5Mjg0MjI3NDI4OTRiYWE1ZTYyZGU5YWI2NmZmYTQ4NjZjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 18:25:15 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IllBWDBnUlFQZ0oxK29UcTg3QWVOWFE9PSIsInZhbHVlIjoiYjd4MnZ6bVg2anFJN0kyY2owenFLdXM2bnYvN1ovVHJPbUtzOUNxYzZWcFM3UlREUWxyNVJNT0IrUzVEenR1WXc0OFFlQ3JEU05ZVDdCWFBFZy95U3duMVdKcWhHc2lJVHVJb01WM1pLVWlqU09iTStnNU4vVThTeW1BYWNQRys5ZnN0aTYyQXJ4dktOOExVdUI4YkkvT0VaSCtDQ0RBeFdqbC9USE9vTk96ei9mR0FiZG55Ym9KNUJYWEY5WEpCTHliMzBubEFyUWVHNWJ2Mk5Scmg4bWMvUU1LQXMzVjZIc2M5a3VVdUhyQ1dndzBzRU13b292cTJ2VXcrM2lLNGwvZy9QdEZrc1NOdVJJWFZBY1VyVy8rWE1Xd1Y5M0t0KzFTNTY5NzN6SncyTmIwMWVrSlBWZ3dYTkkvekxua2pEOTFZMGxZNXlRNFpqRFFIMDlHS3ZRcjcrcVF5R1hxZmNpNjRwbmRleThVTGh3akdvTmVNbkN4V0xHOHhKUGpVVFlJWVdGb21MaGl1d0k2ZGFaNEswV09pRkFicUZiOHpvdHdyR2NNNDQ0THBES3Jtc0FodDhYTXdsZG5Eb21raVQwMSs5NkcxbjFrUGQvTmF6T0RSY0lyUVBvSzBkcUpaRytWajltZ3ozUTdadWxObE1ULzAwZHB6YUtvM2ZTamkiLCJtYWMiOiI3MmNlOTk4ZDEzNzVhNTAxZjUyOWU4YjQ5OWYyMjA2NDdlMDVlYWZmNzY0NWU3MzUyYWFkN2Y1NTU3MmY1MDk0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 18:25:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IklOU3JCQ3M3VUNwcDYxMmJPeWhWSkE9PSIsInZhbHVlIjoici9aVUtmUUtGaDVBamhsK2djbjgrcDJWSkNmb3Z6Ty90Sng5TnRScGFoNkh6elZaaHpZemw4ZmZ1RzB5R3FmWVdWbmRwaE5JNFpkR1crU3FVRzRoYkRDQkdEbmlvdkJLaUt3ZWd3allWcFNwQUY1bFRxYXJyRzF4VXIrTWs0OTRlbE1lb1ZnTERTVjZhUzlXY0Jnb25BdHV5T0dBU3ZhMkhtYXNPYWN1N2pDVFR2TW9GMndidXp2Rm1JdmZCWThIbmRjUkN0ZUMvdzJZaHJVWmJhZ1VicXE1MTJQZkczSDk4WEN6V0xab3FKZUhiaHVUYXQ2Qi9jRkY1ZEdGWWpzbUZBZTBUc2ZaUU9nb2h3c3N5TUZDcklOVVhYbDdsNGlYSFRRWEp4Z3BaS0ZIWTFkaXFVdytrTUh5eDNVWUN3U2lRT3YrV0pTUUdsVHB2SWtxSlRHV0lUWElLTHhybWtuQUU2aFQ4aGZqcnJSMS9WWTQ4dHE5eTRKeDQ1SkdwNUh3Q1hrZVJmRFJNd0lhZENKc1FHZ01QRVBmSXdPclVXUHBnN3NTTFNSb29ZYUdLTHh1azhDa0pudmFYSlBTZWsxY3R6NjZrQTlFVUg4QlV4SGVMbGVNakhsU2lwcEI4c1JQRnlFeTFWTnFxQ0dkK01wVTJMNjRFb0d1K003MjBMaWsiLCJtYWMiOiJiYTYyNTA2NGIwMzIyNzFjYmY2ODQ2MWI0MzExZGU5Mjg0MjI3NDI4OTRiYWE1ZTYyZGU5YWI2NmZmYTQ4NjZjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 18:25:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1022507992\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1903153140 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6Ild1N2JBYjlWemdxYnI1bkswNlJQdFE9PSIsInZhbHVlIjoiazE4Z3o4RmU0TnNjTnNSbzVUcGlzQT09IiwibWFjIjoiZmU5NTlhNTJmMzU5MzI0NzQ4ZTliMzZlNzEyNTRiMzUwMDcyOGQ5NGMxNWUyOTI3OGI4NjZjNTk5Yjg5ZDg5MSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1903153140\", {\"maxDepth\":0})</script>\n"}}