{"__meta": {"id": "X63f75dffca7e6219ee1de662de0e9bca", "datetime": "2025-06-08 15:40:19", "utime": **********.790032, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749397218.930655, "end": **********.790058, "duration": 0.8594028949737549, "duration_str": "859ms", "measures": [{"label": "Booting", "start": 1749397218.930655, "relative_start": 0, "end": **********.691987, "relative_end": **********.691987, "duration": 0.7613320350646973, "duration_str": "761ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.692, "relative_start": 0.7613449096679688, "end": **********.790061, "relative_end": 3.0994415283203125e-06, "duration": 0.09806108474731445, "duration_str": "98.06ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45137312, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.021769999999999998, "accumulated_duration_str": "21.77ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.734773, "duration": 0.019739999999999997, "duration_str": "19.74ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 90.675}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.767478, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 90.675, "width_percent": 4.593}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (17) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.777873, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 95.269, "width_percent": 4.731}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2028243875 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwl%7C0%7C1982; _clsk=o6pnpa%7C1749397215476%7C9%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ii9ObFRNcnpzYi9LUmppQ0daU1dLb1E9PSIsInZhbHVlIjoiUndhcU90OWdzWUlrblBPL2o2RllYaG16U21KUVZBOExPSktQNm5xZzkrM0ZQNTNTMis5QkkvVXpTRVpBeWdkVE56Y1UrdDcyTWtjVmFOa0t2ZDJ6L1RoVzVQVU9EZmx1bzZWaVRWZ3kyb2JoaWRJTENSL1RXV205V3dFdkUvTm1Ndm12eCtZTVVDSjkvUnJuVERxOTFSRGEwVVRadGY4ck1pMENab1VNQkM3QVZFd1crVUVRTndMS0c1c3I4YWMvNEU4WDVybjFtYlU5eS9QMUh1dXVLZ09FbnZBNEM0eFNtaEY4UkR6UEpKT1dhcGE0bGR3dUpOdmJ1OFh4T0RqWWpBcDkrMndsRENvVytrOG1VeVZSK1FWUlZJMGdZUEJGaytnTVc3NU9YOWpGMUdRbU9nRXc3eFBlK2RnejlLVkIzeXpGQjZNNVNSMEVodHVRdUZYbW5lNFk2cE5FOFgvZGU5SCt0WHFPVXdtWkZ2Nkcwa0QrZ2d4T2YyNFUzQjgzQ09TYU8xSEZoRTI4a1RmQVJyN0JKdDNLYmdCOGVTbGUzdWt6OG9heWF2Z0Z4QnJiS2hPRmNBNkJWR3ZVMWhRbmRNbXZWU01ucVkxRnRwZU9yTHRveTYvU2l2VG94eW82VXdvMXRkQVoyc0lsQUN0eXJPRlJZYWNMOXMzbmhFZDAiLCJtYWMiOiI1NDVjMDM1N2UyNzFhMzJmZmFiNmM4Y2UyOGUwNTcyNTAyOTcwNTQ1OWFiZmNjODYwNGRjNTU5YTJkY2JlNWI1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Im40bE1PS2VkTmFTc1VISzEvUWdOR2c9PSIsInZhbHVlIjoieUZ6N1Fac1JXZFpDYmpZMGFha0tTMEtsTVlDNlJ4WGZkUmhxTzVsTGM1WWRnbGFhWVFYYmY3K2U4ck9xZUx2bTB4K1ZVRGxOQjFnaFg2TmhRUXQ3aEhuQ3ZlOC80b0daTFEwKzliVVpXeVo5V3ZJNm1tWTJQUkh0c1p4WEM4Z05mQmxMV1RyTTNMQnZHUituaDY2UDZwRkxQVnpJU1cwUS9IMUNObng5TTc3cFgrTDY2cE0xM0dORDFPN0JJY040bFppK3pRMVk2L00xU1JUOEhuOFlUYXpwekxZaWR3TlZPckNvSHNhaUNWWlRnOHFzbUNGWmZqeWxBSmtFS3dkcUd3dW9KUmlOVDFodHZmSVB5VE1mdXlvdmVpQStTc09yQXhHUG9IQkU5L3dEYkcvbnNIVmNXOWRtSFdjK2Jwa3JNK3hDRGIzUWZnOFNWRjhZZ2d3QjFDZlJDSUJzZ2NmVU1tWTFyL3MvZXk1eXFRUW1pKytOb0lyUFhzSnhIR1BvaTZMRnBPV1hFbWNod0JCNk5TUEdqdmxVb2x5NmFITHhaS29HR1lDcDlHRkFsTWJEWURJWCtjbWZpTW9CeXV4ZTRZS2dLWGpQYmlMaW1lRDJoQ2NJL2I4NExPdm1id0grWHdSK3FaUk16Zmw2WWRpR0J6dC9ybUYrWisxU2I0aEIiLCJtYWMiOiJlODIxYTBiZTc0MjhkMjc2Mjg2YWI4MjM5Yzk2ZDg5NDgzMTM4MTE1NjU0ZDIyNjdiYmQzMTlmOWZmZjc1NDM1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2028243875\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1488494112 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">COI6IIrAbucReABHBN7o3ZaIzRbwBCI1gfwrcfCo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1488494112\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1162051378 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:40:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InhyMktGMnJLTkVORXlKT2w2Nm5yaUE9PSIsInZhbHVlIjoiL3ByV2ZKRit1L3gza1JmaUdzeTlmNUZhTU1EWXpleVBTK2Z1TTdIdm5pYkZ5NHlvUWJPckRoTUpyNzlGNkxIdlJQY2tzeTk2djJFWnhJV1BGRlliS3prWE0rdE5ubjlVRUpxaG9nY1ZSelRVQ3N1bUZRWTE3QisvTlRDZXl3RW5wN0RUekxvSzArSWQ4a1g4bVh6TmJHL0pCenBCUFl5aHQ5NVpZZEp4S2p4Ti9odUFteTVPK1RPckJlYzRoeThzaEN0YksrVVM2SGFDK1VabHVQQWQxb2pqeklpUWtsWmlJOTdPWEpMOU9jVEdnYk82OGlCellhYmwyNzZwa3YzUlJHSmlXMjVpdW5WR1lHOUsvNnVHakpPZmhLNm1DcXFTMjFrdTVJcWswWXllYnNYeUpRcU0rL2VZQnFkNE9TRGdpcXJENVgyVHI5ak1XUTBYWUFuRTdybVVIZXk5Tzl5NTM0MDVpR2lGYnNlRU9xTVc1MG1idEMwQ0VwRG5oVExDelZzamdhZ1g1dXJpTHF1ZnIwY1lPb2hxa3NBV1dqZk11QzVzd05yZ2FLajZ3SVJBQi9PMHgxOTJRTUg3dG1ua3hWdld5UncwS29ZWUlRcGlqdXhVS1AyNVBWcjVMSjJ3OHM4OXRkSWthZXhRTmRhQlBneGdHMTVlc3Vld1lDSVQiLCJtYWMiOiIyZjFiNTgzNjUwMzY3ZDgxYmE5MjlmOTQ4YTE3NTczZmYwOTc5YTNjZGNlMTM1ZTJjYjU2ZWE0MTYzZTA0MGUzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:40:19 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IndPd1U1QUR5ZmNmS0NoWmZSUUNUZHc9PSIsInZhbHVlIjoibGtpQVRVZFI5Q3ZNUnNDbnY1bHZHRVkwZlB5ZDVBbC84MWZzZW01LzJRTGJZOGxmK1RRMXdvc0ZORDZYOUdzdDBZQkhhaGRDUmtaZlRLbG0zM0FmeElXY1dIWXVLSzYyM1ZYTWVaQWdPWXNTelhEQjhSdjczdFlzOHg0S3hYY0duMy9JU0tyUVpWWXNVajdoQ3NjL2VCdTY1RS9VUG1BVXdlcWN0TjI3alhyNGUvQVV6eXJjNk1QV3lEQnM4Q1dkNk5MTml4U1BKbHBvZ2htYzZOK0laSUFNTHAvL0NnRWJmNGVPLzh5RlllQUtHeDRSb3Nxcy9sQTdCcnRhWTIzSkVhdWp1NnBra0RrdnB2bzFaTytGODNNY0MzUU5hcnpzMkVtYU5TR211VklNMWtVS0ZnNGNoZWFZRDE3TFhkL0crMWcxUTl2bmFSY0N2TlM2a01tRXdxdXp0bGdxY2lubFRCYTFocjFoU1JaS3VMRnQrdEx2b1J6TFJuTVk1Wmo4aXlUUUFzMmJHRWFTYTBXUDcvNkc4ZmRzZmZ6N1pYNlhtaUNJS3FZSUw0QythbmhJUmVzd2tYa2ppRUw2T1J3UEJoMnVwMlUrVDdTRXBYSFdrNmJHZVhQQnZuR1h4UWswMVkwTXBGYmNmUXFYaEVlejhKSHc3L3hoWXJrWVR6bzkiLCJtYWMiOiI1ODZiOGZiZmYzYTExNzMxNDU3NTM5MmMzYjQ5MWQwNmE4NzVhNGViM2RmOTQ2MDQ2NGMwNWI0NDdjYjBkMGExIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:40:19 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InhyMktGMnJLTkVORXlKT2w2Nm5yaUE9PSIsInZhbHVlIjoiL3ByV2ZKRit1L3gza1JmaUdzeTlmNUZhTU1EWXpleVBTK2Z1TTdIdm5pYkZ5NHlvUWJPckRoTUpyNzlGNkxIdlJQY2tzeTk2djJFWnhJV1BGRlliS3prWE0rdE5ubjlVRUpxaG9nY1ZSelRVQ3N1bUZRWTE3QisvTlRDZXl3RW5wN0RUekxvSzArSWQ4a1g4bVh6TmJHL0pCenBCUFl5aHQ5NVpZZEp4S2p4Ti9odUFteTVPK1RPckJlYzRoeThzaEN0YksrVVM2SGFDK1VabHVQQWQxb2pqeklpUWtsWmlJOTdPWEpMOU9jVEdnYk82OGlCellhYmwyNzZwa3YzUlJHSmlXMjVpdW5WR1lHOUsvNnVHakpPZmhLNm1DcXFTMjFrdTVJcWswWXllYnNYeUpRcU0rL2VZQnFkNE9TRGdpcXJENVgyVHI5ak1XUTBYWUFuRTdybVVIZXk5Tzl5NTM0MDVpR2lGYnNlRU9xTVc1MG1idEMwQ0VwRG5oVExDelZzamdhZ1g1dXJpTHF1ZnIwY1lPb2hxa3NBV1dqZk11QzVzd05yZ2FLajZ3SVJBQi9PMHgxOTJRTUg3dG1ua3hWdld5UncwS29ZWUlRcGlqdXhVS1AyNVBWcjVMSjJ3OHM4OXRkSWthZXhRTmRhQlBneGdHMTVlc3Vld1lDSVQiLCJtYWMiOiIyZjFiNTgzNjUwMzY3ZDgxYmE5MjlmOTQ4YTE3NTczZmYwOTc5YTNjZGNlMTM1ZTJjYjU2ZWE0MTYzZTA0MGUzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:40:19 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IndPd1U1QUR5ZmNmS0NoWmZSUUNUZHc9PSIsInZhbHVlIjoibGtpQVRVZFI5Q3ZNUnNDbnY1bHZHRVkwZlB5ZDVBbC84MWZzZW01LzJRTGJZOGxmK1RRMXdvc0ZORDZYOUdzdDBZQkhhaGRDUmtaZlRLbG0zM0FmeElXY1dIWXVLSzYyM1ZYTWVaQWdPWXNTelhEQjhSdjczdFlzOHg0S3hYY0duMy9JU0tyUVpWWXNVajdoQ3NjL2VCdTY1RS9VUG1BVXdlcWN0TjI3alhyNGUvQVV6eXJjNk1QV3lEQnM4Q1dkNk5MTml4U1BKbHBvZ2htYzZOK0laSUFNTHAvL0NnRWJmNGVPLzh5RlllQUtHeDRSb3Nxcy9sQTdCcnRhWTIzSkVhdWp1NnBra0RrdnB2bzFaTytGODNNY0MzUU5hcnpzMkVtYU5TR211VklNMWtVS0ZnNGNoZWFZRDE3TFhkL0crMWcxUTl2bmFSY0N2TlM2a01tRXdxdXp0bGdxY2lubFRCYTFocjFoU1JaS3VMRnQrdEx2b1J6TFJuTVk1Wmo4aXlUUUFzMmJHRWFTYTBXUDcvNkc4ZmRzZmZ6N1pYNlhtaUNJS3FZSUw0QythbmhJUmVzd2tYa2ppRUw2T1J3UEJoMnVwMlUrVDdTRXBYSFdrNmJHZVhQQnZuR1h4UWswMVkwTXBGYmNmUXFYaEVlejhKSHc3L3hoWXJrWVR6bzkiLCJtYWMiOiI1ODZiOGZiZmYzYTExNzMxNDU3NTM5MmMzYjQ5MWQwNmE4NzVhNGViM2RmOTQ2MDQ2NGMwNWI0NDdjYjBkMGExIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:40:19 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1162051378\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-7******** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-7********\", {\"maxDepth\":0})</script>\n"}}