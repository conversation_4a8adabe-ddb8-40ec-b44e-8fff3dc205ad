{"__meta": {"id": "Xc64528a1e4a1dc5fb7d65bd8d6e8afa6", "datetime": "2025-06-08 15:42:46", "utime": **********.178278, "method": "GET", "uri": "/pos-delevery-pay?total_price=22&pos_id=41", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749397365.41421, "end": **********.178299, "duration": 0.7640888690948486, "duration_str": "764ms", "measures": [{"label": "Booting", "start": 1749397365.41421, "relative_start": 0, "end": **********.032762, "relative_end": **********.032762, "duration": 0.6185519695281982, "duration_str": "619ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.032776, "relative_start": 0.6185660362243652, "end": **********.178301, "relative_end": 2.1457672119140625e-06, "duration": 0.1455249786376953, "duration_str": "146ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52379552, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x pos.bill_type_delivery", "param_count": null, "params": [], "start": **********.160471, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/pos/bill_type_delivery.blade.phppos.bill_type_delivery", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Fpos%2Fbill_type_delivery.blade.php&line=1", "ajax": false, "filename": "bill_type_delivery.blade.php", "line": "?"}, "render_count": 1, "name_original": "pos.bill_type_delivery"}]}, "route": {"uri": "GET pos-delevery-pay", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@deleveryBillType", "namespace": null, "prefix": "", "where": [], "as": "pos.delevery.billtype", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1520\" onclick=\"\">app/Http/Controllers/PosController.php:1520-1536</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.019069999999999997, "accumulated_duration_str": "19.07ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.085229, "duration": 0.015519999999999999, "duration_str": "15.52ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 81.384}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.1142669, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 81.384, "width_percent": 4.929}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 17 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["17", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.1397738, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 86.314, "width_percent": 7.761}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (17) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.1442828, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 94.074, "width_percent": 5.926}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => manage delevery, result => true, user => 17, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-421454102 data-indent-pad=\"  \"><span class=sf-dump-note>manage delevery</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage delevery</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-421454102\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.150861, "xdebug_link": null}]}, "session": {"_token": "SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/eyJpdiI6InRWOGJ2eHRKM1Fpc2NXaGVRbGREekE9PSIsInZhbHVlIjoiZjN3YzRMbnc0K3FRanU5bW1yTnVhZz09IiwibWFjIjoiZjE4NTMxYjk4YWZhMzQxNWZlNDMwYjNhZmVlZDNjY2EwODMzNzQzMDk3MTU0NWQ5NTM3MTFmNmY3MjVkMDlmOCIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17"}, "request": {"path_info": "/pos-delevery-pay", "status_code": "<pre class=sf-dump id=sf-dump-362590765 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-362590765\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-702298685 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>total_price</span>\" => \"<span class=sf-dump-str title=\"2 characters\">22</span>\"\n  \"<span class=sf-dump-key>pos_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">41</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-702298685\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1497603823 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1497603823\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-584704005 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6InRWOGJ2eHRKM1Fpc2NXaGVRbGREekE9PSIsInZhbHVlIjoiZjN3YzRMbnc0K3FRanU5bW1yTnVhZz09IiwibWFjIjoiZjE4NTMxYjk4YWZhMzQxNWZlNDMwYjNhZmVlZDNjY2EwODMzNzQzMDk3MTU0NWQ5NTM3MTFmNmY3MjVkMDlmOCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwl%7C0%7C1982; _clsk=o6pnpa%7C1749397363959%7C13%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InQ3dnRpY093a0Y5MzdWcWhHeFZBSUE9PSIsInZhbHVlIjoicDQ1OGxGbllVOVBtTzFOMHdPYXY1Y2Z0VVc4R05LVkNGMmlJK3JxUHlkZk9YVGNBcUNNem9weGxoVTY4NzdXVzNYZy9hOEJDSnBNZm1HWFg5bFFJV0I4UWVDVC9iaGhFTGdzanBxOVplejFVMjM2V1k5bVN5WnlwaEJ5V3NGNTMxdW1sYmdFTXdMaStTT2VnQ25UUFhiWTFnaFp0Z0dESG9MRTNrTFZWNDZmaW12V0xyOWZYWmJZdGJWSkl6UjdWQ3V0VjhZaXRQTE1iV2JoUXA1OTVnRTRtanJXQkVSVnJzdWpLV0hHb2NTeU5ZSURRS2gwaVJtdDh2dmdpNXQvR3orbjFIVHd6MHgwRDVscWx6NnF1ZUUzbVNBYWp6Q1RucXlkMjBFQTVPdDJCM0dkckl3ZjQ1ZkpsTVg2UHZYVy9DdXUwdjl2MndQaWNBcVVCYVpNajkrYjVKQmFyczc1WDhaSm9pNmVVWDFSOW5xdk9FS3hubGtGNHFBcUxTVHdJczZQNlUzNkFEMC9CK3A0a0tpOUpSNXRCeEJiMk1pNGFSdlNXRTBWYTFxeFp4SDNnUjFVWkU3M0kyUm4xdjRqL0t3VFFza3BYclpSTGhjd084c1JjRGdqN090Vm85Z1RwaHMrZDgwajJLZ2tOemdhdFlRUmE4SFJESTZ0QUF5ZzkiLCJtYWMiOiI4NmE4NTBjODc4MDRhMWQ1ZmIwNjg0YzNiMGM1ZThiMWMyMjM0YTNjNmY2NTM2MWI5YjhlODgwM2U4NjU3NjY0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImFhL1ZsV3FSUkd0TisvKzBUWlhzM3c9PSIsInZhbHVlIjoiYkw5NjJTU2ovaTFDd2xSRFNnU2Fud3MyZU1FTFZrU1NzVUhMVWhVdlJZUzd1S09hS05qMk1KR0FoK09tejVYTExnRklCTWF1Ly9NOTRXZVJHdTE3NEl0d1NMYWN5OFRVY2F5NitQcVhpUTVrbnF6R25XblBkZEJ0Wm5DamZ0ZE4yMFl3R3U0WWZwbDNad2tDakpHV0RoS05Ybzc2V3JrN3BPY0hLREh0a044SnF4QWxha3ZhSkcxSk56b1FLeFR1eHNKTThMMUVOSkdJS0NZa1JWdWFHVnJTL1hGZlpXcEREV0gwdGlEWnVJMjNNMWVEb09FRUtWNEhlMHVsUURWTFRGMEtFelBGZ0FiWHZ4cUFWTnJsLzdaeklGTEhpVHFoeDlnZUJ0WWV1OWV6bWhDMEZ2a01PNi9oU2NSdldzYjVSL2VPYXNLeDkzQTV6NWJSa3Fxa2EvNlRwRkQvR3U5U2JGSnhZRDN6eDZsbjJ1cy9YVWpxN2xpOVRxanNEUkNQRW5uRmowTVhDazVXVVUvZHA1NUZYWWxBaWZQUlpLamdBZnR1dm1jZ1VTaDBHSXFLYlhja1ZsUTNTa2lFaEJucjFFcVRLRTVmdXNVQmdSZ3gwNHYwT3E3bUZwRXdxVHZ5YmRQMEpjd212UWdWK1NYbXFISHAyRnBOTVBYMUY5ZTYiLCJtYWMiOiJmMjcyYmE2YjE2NTc0YTg1Y2QwYzMwODk5N2I1MWE0ZmMyMzNmYzhjNTRmNTMzMGZhZTY2NjE4MDk3Y2IwNzllIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-584704005\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1441432199 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">COI6IIrAbucReABHBN7o3ZaIzRbwBCI1gfwrcfCo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1441432199\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-934018313 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:42:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik4rbnNhYVNxbUgrSHF4QXpqWlU1Mnc9PSIsInZhbHVlIjoid2tQSzArVVU0aXRqbGRMN2dmWENRUzBrTWsrNU9WTGdSeG5jby9PeHhjWHExa0J0VGtaUEZBQ3NZVERmbnE1WEgvTlFFaWVQOW1yaWtuTG5ES0FZazJrRUZscGlkYy9OYWp1YkEvUzRPOTJlOG1xRU0zeUhkOXB1aksrdCtmSHBmZ2dDeVJFWU5lQjZtVVkrTC9pdlUxUE93aFYyUHU2aVA1R21PcW9LVzh5NXhJOWM3WHZWNjdOQ2p6ZnBDTk14TjJ3TzEyZUt4OVpSUEdwang1Z0RaN3Nvb0YrZkluaFZ2WVA2bitBVWJkQTVWcnc5dk1YdTRBTjB2V3o2NXY3Q0JpU2ExYWdNQlJGZitxWUZmbFZjRjJIM1h1bndwa2JHdVlXWlFGWnJBdklzazRsZEFhalh1VXhya1BMb2lad0FTZ28vMHp0RVkyMUgwN3Y5Z1hmeUlveUduK0h6VitGUzF5emx6TXdzbUg4b0h4bTVhWmRBZFdGbjhEV1VSQWhnL1h6NTdDMmxDdW80NkIybjkwRTU3cnM5Qkd4VG0vK3IzM2hLekZhYnhPOUlCSXlTUjdWVGp5eEtTNDFpNXl1THNBbU9JWXdKL3NMckVWSUJ0Mk9GbThPekRJTjhEaFk1WXlXSDErUzJGanRBazlqYkxSOWMzR29CVGFsODdJbDYiLCJtYWMiOiI1NzAzZDQxNTQ2YzhmODU4ODM0Y2E5M2FlM2RmMjk2NTQ5ZWQxMzNiYjQ0NTJmYzBlMzVhNTAxNzM5NDdlNWU0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:42:46 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlZKUEcyMzdHbjFET09WVGFldTUxcUE9PSIsInZhbHVlIjoiVEF1Uk81MmpYeEJFNjlBMWJOTXdhWTdmOTlIQzBQNlV6L2Jvblp1WmZCN1hRdzdjVGhBOFhPYVBGUnJzdFlZeld2ejU2WFhiVnp1TWFvd0E3NUdoTGFPaTliK3RzRmJwc2dwT1FjRmUvTTlXaElDTjBsbmhycCtuUFRJRHJTOWh2eTBUUEpHUC94MlBGV0xhaGdGeUhXQ3F2MjlQYnNja09sRXBZNkg2enZ1dTdRNzR4VENjM29qSTA5aFI4dmRSejl2bnFhUVU5WWhiQ3VVcXM0WHkzclRNakpZSHVvU29relFwNjM1Q2o4V2J3bUMyRlBlaHZkSjREUjRDMUFoaUlIYVZsVnYvcW9zblc0VE1wVjhSRzRWN25Fc1QrMmlTWGE1aDkxMzhFekh2QzFYeWxuTWdESyt2QVRtRzk1Z2IrVURCcFYxWEZ0R20rOVBaN1Urb29KTXd3cVB5NU04WHd6dDBKMTAvZEI3eFNPc0FIUjFqOWZ2bGQrWm8zWU9DdFN6bFVRdjhtTWorNXN5Rm5MTG9lUG5HZ0I4U1h6b1hsNWJqbW15TkpxTDJJZDdGTklXaTdMUmorME1Femwydi96UjdQZFl0enpsZElNVmJKTHYraER5aUZDRU4xUGY4eUhCcHlWN1FOMlRaRTFjSThtcHl0c1VPd1phYWc5d1AiLCJtYWMiOiIwODJkNzUzMWQxM2UxODk5NjUyZTE3Mjc0NjMzNmZmYWE1MGViNGEwMTExM2QzNzE4YTYyMmUzM2JjYzkyN2Q2IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:42:46 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik4rbnNhYVNxbUgrSHF4QXpqWlU1Mnc9PSIsInZhbHVlIjoid2tQSzArVVU0aXRqbGRMN2dmWENRUzBrTWsrNU9WTGdSeG5jby9PeHhjWHExa0J0VGtaUEZBQ3NZVERmbnE1WEgvTlFFaWVQOW1yaWtuTG5ES0FZazJrRUZscGlkYy9OYWp1YkEvUzRPOTJlOG1xRU0zeUhkOXB1aksrdCtmSHBmZ2dDeVJFWU5lQjZtVVkrTC9pdlUxUE93aFYyUHU2aVA1R21PcW9LVzh5NXhJOWM3WHZWNjdOQ2p6ZnBDTk14TjJ3TzEyZUt4OVpSUEdwang1Z0RaN3Nvb0YrZkluaFZ2WVA2bitBVWJkQTVWcnc5dk1YdTRBTjB2V3o2NXY3Q0JpU2ExYWdNQlJGZitxWUZmbFZjRjJIM1h1bndwa2JHdVlXWlFGWnJBdklzazRsZEFhalh1VXhya1BMb2lad0FTZ28vMHp0RVkyMUgwN3Y5Z1hmeUlveUduK0h6VitGUzF5emx6TXdzbUg4b0h4bTVhWmRBZFdGbjhEV1VSQWhnL1h6NTdDMmxDdW80NkIybjkwRTU3cnM5Qkd4VG0vK3IzM2hLekZhYnhPOUlCSXlTUjdWVGp5eEtTNDFpNXl1THNBbU9JWXdKL3NMckVWSUJ0Mk9GbThPekRJTjhEaFk1WXlXSDErUzJGanRBazlqYkxSOWMzR29CVGFsODdJbDYiLCJtYWMiOiI1NzAzZDQxNTQ2YzhmODU4ODM0Y2E5M2FlM2RmMjk2NTQ5ZWQxMzNiYjQ0NTJmYzBlMzVhNTAxNzM5NDdlNWU0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:42:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlZKUEcyMzdHbjFET09WVGFldTUxcUE9PSIsInZhbHVlIjoiVEF1Uk81MmpYeEJFNjlBMWJOTXdhWTdmOTlIQzBQNlV6L2Jvblp1WmZCN1hRdzdjVGhBOFhPYVBGUnJzdFlZeld2ejU2WFhiVnp1TWFvd0E3NUdoTGFPaTliK3RzRmJwc2dwT1FjRmUvTTlXaElDTjBsbmhycCtuUFRJRHJTOWh2eTBUUEpHUC94MlBGV0xhaGdGeUhXQ3F2MjlQYnNja09sRXBZNkg2enZ1dTdRNzR4VENjM29qSTA5aFI4dmRSejl2bnFhUVU5WWhiQ3VVcXM0WHkzclRNakpZSHVvU29relFwNjM1Q2o4V2J3bUMyRlBlaHZkSjREUjRDMUFoaUlIYVZsVnYvcW9zblc0VE1wVjhSRzRWN25Fc1QrMmlTWGE1aDkxMzhFekh2QzFYeWxuTWdESyt2QVRtRzk1Z2IrVURCcFYxWEZ0R20rOVBaN1Urb29KTXd3cVB5NU04WHd6dDBKMTAvZEI3eFNPc0FIUjFqOWZ2bGQrWm8zWU9DdFN6bFVRdjhtTWorNXN5Rm5MTG9lUG5HZ0I4U1h6b1hsNWJqbW15TkpxTDJJZDdGTklXaTdMUmorME1Femwydi96UjdQZFl0enpsZElNVmJKTHYraER5aUZDRU4xUGY4eUhCcHlWN1FOMlRaRTFjSThtcHl0c1VPd1phYWc5d1AiLCJtYWMiOiIwODJkNzUzMWQxM2UxODk5NjUyZTE3Mjc0NjMzNmZmYWE1MGViNGEwMTExM2QzNzE4YTYyMmUzM2JjYzkyN2Q2IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:42:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-934018313\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1444963687 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6InRWOGJ2eHRKM1Fpc2NXaGVRbGREekE9PSIsInZhbHVlIjoiZjN3YzRMbnc0K3FRanU5bW1yTnVhZz09IiwibWFjIjoiZjE4NTMxYjk4YWZhMzQxNWZlNDMwYjNhZmVlZDNjY2EwODMzNzQzMDk3MTU0NWQ5NTM3MTFmNmY3MjVkMDlmOCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1444963687\", {\"maxDepth\":0})</script>\n"}}