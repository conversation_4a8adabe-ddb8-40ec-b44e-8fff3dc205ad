{"__meta": {"id": "X76271b96f2f61aa7d0c02b23a668d936", "datetime": "2025-06-08 16:17:59", "utime": 1749399479.020564, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.45162, "end": 1749399479.020581, "duration": 0.5689609050750732, "duration_str": "569ms", "measures": [{"label": "Booting", "start": **********.45162, "relative_start": 0, "end": **********.948836, "relative_end": **********.948836, "duration": 0.49721598625183105, "duration_str": "497ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.948849, "relative_start": 0.49722886085510254, "end": 1749399479.020584, "relative_end": 3.0994415283203125e-06, "duration": 0.07173514366149902, "duration_str": "71.74ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45139448, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00516, "accumulated_duration_str": "5.16ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.985811, "duration": 0.00312, "duration_str": "3.12ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 60.465}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": 1749399479.000546, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 60.465, "width_percent": 16.279}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (17) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": 1749399479.0092251, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 76.744, "width_percent": 23.256}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1905305291 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1905305291\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1548087222 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1548087222\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-533869756 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-533869756\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2086479113 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwl%7C0%7C1982; _clsk=1y4k55%7C1749399475493%7C3%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ikl3cllyelFvSEZOSWV2TGU2YjFuWXc9PSIsInZhbHVlIjoiTjF2WGZmWVRaT1NYcXRkT3lSTVJJamFlTFdZNENCOXprbE5JUzJtcDRkc0lsWi92Z3RuanpNcXBsSjZFS29BQitMMGNOVXBuQ3dtNU5CS1lZUXhzdGhxOTg0c2JadmtZK1Vza0I0cElPNmRWTzdZL3NCOEVyeUdacGo3WmxmTUVleThIb1NtYlA4OGtKMG42bnN3VGxLTVZha3ZOK2hua01OQ3pmU0pkS1JLSDZZTHZsL0g3TjVRNkVLNjBDZW5wRW9jSXIxZUxMcUFGaExrK3hBNmExUnVxZGJra0I5RGdEdStMWmpjWkJoamh2ZnBtdzlmOTI4ZS9zdjJNdzk3OHlodXN1NVI2ZHpZa3pzNFpXQ043dmNKcEVoRHVCbTlhQndlek9yc2d3RU1rYzJBdlMrTWJISEgrdkVpWkdaZGQxMEpZMG9aQW5WYWtRUldLUzZkNDBNeWd4ZmsvRGhVVk1wZ0gwRzg2WUsrRTlPMGlvTXQwQ3BGQ1V0TS9XYkN6VGY3YXV1ZGJQUk1aZlpnZTc0c3l3dnFHRXpBWStZVE1keWhsaTBpWVRTMUJOTzNoUmYvNzYrN3QwZzY4QWxNcjUrV1dqWHJETTNuMGxuTXVhaXRzcU5zR3RRTVZzeXhzTG9lT1QveUtkUXBhejd3ZUhtaUlIWGQ4NzZ2ZUxpbUMiLCJtYWMiOiIzMzUwZGY1OTQ2ODdhODdkYzNmODY0MjdmMjJiYWI2MjYwY2Y5OTBhOWQ3MGFhYzNmOWY0ZTc1Yzc4OTMxNGE3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkdqS3Bqd2YzaVNvSjRTOGl6aDhsb1E9PSIsInZhbHVlIjoiR0NsOE43ajBNTmZudThLQ2lBTUZDUkw1dUx0QTdRSkQ1UWJoTE1MNC9DNFljdVI4dWhxdHJVdUtBUVRHblZVTnZKSTFkN1BOdU02NXh4OWc5VnJFMVpLenhuTlBqMGg0VWsyRFlCQ3ZEeTl2RlZhS2Fkdm5RTC9pbVRFRjZGRHYwZTF5TnR5VkRmaUNaRFYwUWJ5SWxPUkNsZTgvN2l0RHN6SkxMRUsvVm4ybGFkRjF5dG5tNXlYVWdvcCt3SDB0eTNNWkZoeDRoWk5tN2c5bXpzcFdUbkhYUEpmdEk1TWdWTVp5VmNUSUliN3JiY2wzYWFOTlNySXdML2VZWGw2aDR2NUd4S1R0WVpHZkJRcHJKNkNncUhCR2tSVFJBNlREUUJ1RzcrNXFwNVV1dFNiZUVPZlRpU3VNVXAwUlNqUHpnMWNGTzdBUlpGeGRqOWtwQ1laUWNlaTZXMms0cUZzYS9kVjZnaFkrSmhVMUZ3ZUZ2bG9aS011Nmc1NmErejhOSWxscVNpS0Q0QU4yTSt3K1M5bklYSGgyYTlyK3pSTXNmVXNwVWZiUmR4Q3QzWm5Dc1A3Sk1mbFVwKzlMd2M1b0dlZzlLTUZCM2hkMHBNWUtpaWxrdXNQM3ZsOE1udjFXWVpSQVk4ZTJYNW9ySFlEeFdYRkR0aDhlTjBIRWViTEMiLCJtYWMiOiI2NjM0NDM4YjhiNDBlNDM0ZjU2YjQ4ZjY3OTMzNWI3NjAzZDA4ZjFhNjNmMjBhZGFjMDEyNzJiNDMyZDc2MzUyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2086479113\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1861107984 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">COI6IIrAbucReABHBN7o3ZaIzRbwBCI1gfwrcfCo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1861107984\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1113473828 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 16:17:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlU1TXZrZStyWm5DL2ZZdGZNRS91Y3c9PSIsInZhbHVlIjoiRlNMc1d3R2t2dTVSKytVODZMeFdjNHhyWFd2bHlhQ3ZCQkZOeXcxWlBsN3Z2Q2lMV2tHY3lIYnkrR0pjMnFlMmlyRXcxdytvbjU5SWwreHp5dDFtTE41SHR0bm9kUkJVQUNOak5HMGozUnI0Q1cwN05DWXFmZkEzSTJnbDVFbnJ6UWZBb2JvOTNVMjBqY1dNbnZrMjNYQ1FvbFd4RW5zMzJHcEtaZGxJWnJCUitSclBjalJ3NjF0dkgzSkQyS1J1eXp0UkFMV3JnR2NrTmhsTHV6d3dQd05kN0tMQWdrSkNxL2VhTEtERjFPYm5uTVRvM2NTc1B3NVl2SWZGNDErTm9lYWJ4SFYwVFpMNm5yRUFzdFdTdVI1WSthVE9FTGVCMk9Xakh4dlhZTVhGVjBxajhnNGZUYWJDZGs0MEkwQW9oRjdVemhrVEk5Tk5sZWFSbEdrWjhoRkpIYXhMd3FXL2h1L0hqWWFKalFEUnJsZlp6dTZ4VmV1bHZ1N1NidkphYlhUWTl5d0VGdXF0Tm80SGJvYzFudHBpbEN4NENsM1dmT21obG1MOHRZRUg1SncxS3JiMUh3K1VUVjN5bE54VjhTdXlVeVo3OUhqVFFtUU1ISXBsT256TEJQZzRwdEVVeUUvZ3NNWkw0dU51UWFNSEQxMDg2R0s5d2FZSmxCMzAiLCJtYWMiOiI5YjNiZmQyNjA5Zjk3NTQxMDM1YzUxY2E5NGJhODVmMTA0NDg1NjRjOGRhZDRhZTZlMzJhYmEzODZhMjU3YWZhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 18:17:59 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjJUTzEraG5xcGQ4Smt3YWxKSFF1VFE9PSIsInZhbHVlIjoiM281b3Fsb2FhSmRQNWZCZHFaU0NZOG1KSVh2RFFwSTBTbkc1amN4b1M3a0xOSjV2Vk5TV01JYUdiY1lUWGR5YkVkU0drWDh1UkwwaE95TzJ2Ry9lNlJZVHJCWE41MmxqcGNsc1F2bjdTUHJKUWlTVkdWemVCa213KzhIWEZUOUppWkJ5L3NHQ0FLUTNwdlV5NU1Ya2VIR1p4WlhkVm8yOE5VOG91YXdUUGpITDd2ZFZnendDdUthUUVCZFRrdjROdGFQTGlNWUlSY2g1d2VqaGs0U0htaEkzbnVhd0srS1hoQU9IUXBrNWFnRCtDQ1Q5b0g4Z2tqaWZZRy9TV3ZGUC9JcmEvRklLQjBNOFpHS2g1YmpVUUFKaWRlWXRSU3JNTnl6dERQYnpyR0tqMGMrUkh1VkRpeWdGeWhFbkJiem5la1Q1T01Ga1ZjdVNUMkhET3ZUWFU2T1JVcGlYV0c0ZFlyS1J6UTA5SVFxUFBQNG5XcXRybjQ1elNkbXFhcmhrUDNQUytPdk5BSmlJNkxvTzdrMkp3M1h2NmJ5YW1zZVljSVcvT3F2NkR6VzNrUFVreE82ZkZGYmVkZ3JVN2ppaUdlNEVsdHVhMzBaeVVvZi82UXIwNUoxdVFZRDBFakVTUEtJWkhiOStVK2RxMDVGdXl4U0oveENJUVJtdCtkcVIiLCJtYWMiOiI5NmQ3ZGU3OGM3YzQyNzE5ZjRkY2MwNzc4MDc4YmNjNDBiMGM5ZmQ1NjE4ZjEzYzQ3NzQ5NjRmYjljZmVmYmU1IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 18:17:59 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlU1TXZrZStyWm5DL2ZZdGZNRS91Y3c9PSIsInZhbHVlIjoiRlNMc1d3R2t2dTVSKytVODZMeFdjNHhyWFd2bHlhQ3ZCQkZOeXcxWlBsN3Z2Q2lMV2tHY3lIYnkrR0pjMnFlMmlyRXcxdytvbjU5SWwreHp5dDFtTE41SHR0bm9kUkJVQUNOak5HMGozUnI0Q1cwN05DWXFmZkEzSTJnbDVFbnJ6UWZBb2JvOTNVMjBqY1dNbnZrMjNYQ1FvbFd4RW5zMzJHcEtaZGxJWnJCUitSclBjalJ3NjF0dkgzSkQyS1J1eXp0UkFMV3JnR2NrTmhsTHV6d3dQd05kN0tMQWdrSkNxL2VhTEtERjFPYm5uTVRvM2NTc1B3NVl2SWZGNDErTm9lYWJ4SFYwVFpMNm5yRUFzdFdTdVI1WSthVE9FTGVCMk9Xakh4dlhZTVhGVjBxajhnNGZUYWJDZGs0MEkwQW9oRjdVemhrVEk5Tk5sZWFSbEdrWjhoRkpIYXhMd3FXL2h1L0hqWWFKalFEUnJsZlp6dTZ4VmV1bHZ1N1NidkphYlhUWTl5d0VGdXF0Tm80SGJvYzFudHBpbEN4NENsM1dmT21obG1MOHRZRUg1SncxS3JiMUh3K1VUVjN5bE54VjhTdXlVeVo3OUhqVFFtUU1ISXBsT256TEJQZzRwdEVVeUUvZ3NNWkw0dU51UWFNSEQxMDg2R0s5d2FZSmxCMzAiLCJtYWMiOiI5YjNiZmQyNjA5Zjk3NTQxMDM1YzUxY2E5NGJhODVmMTA0NDg1NjRjOGRhZDRhZTZlMzJhYmEzODZhMjU3YWZhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 18:17:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjJUTzEraG5xcGQ4Smt3YWxKSFF1VFE9PSIsInZhbHVlIjoiM281b3Fsb2FhSmRQNWZCZHFaU0NZOG1KSVh2RFFwSTBTbkc1amN4b1M3a0xOSjV2Vk5TV01JYUdiY1lUWGR5YkVkU0drWDh1UkwwaE95TzJ2Ry9lNlJZVHJCWE41MmxqcGNsc1F2bjdTUHJKUWlTVkdWemVCa213KzhIWEZUOUppWkJ5L3NHQ0FLUTNwdlV5NU1Ya2VIR1p4WlhkVm8yOE5VOG91YXdUUGpITDd2ZFZnendDdUthUUVCZFRrdjROdGFQTGlNWUlSY2g1d2VqaGs0U0htaEkzbnVhd0srS1hoQU9IUXBrNWFnRCtDQ1Q5b0g4Z2tqaWZZRy9TV3ZGUC9JcmEvRklLQjBNOFpHS2g1YmpVUUFKaWRlWXRSU3JNTnl6dERQYnpyR0tqMGMrUkh1VkRpeWdGeWhFbkJiem5la1Q1T01Ga1ZjdVNUMkhET3ZUWFU2T1JVcGlYV0c0ZFlyS1J6UTA5SVFxUFBQNG5XcXRybjQ1elNkbXFhcmhrUDNQUytPdk5BSmlJNkxvTzdrMkp3M1h2NmJ5YW1zZVljSVcvT3F2NkR6VzNrUFVreE82ZkZGYmVkZ3JVN2ppaUdlNEVsdHVhMzBaeVVvZi82UXIwNUoxdVFZRDBFakVTUEtJWkhiOStVK2RxMDVGdXl4U0oveENJUVJtdCtkcVIiLCJtYWMiOiI5NmQ3ZGU3OGM3YzQyNzE5ZjRkY2MwNzc4MDc4YmNjNDBiMGM5ZmQ1NjE4ZjEzYzQ3NzQ5NjRmYjljZmVmYmU1IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 18:17:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1113473828\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-977644131 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-977644131\", {\"maxDepth\":0})</script>\n"}}