<?php

/* This file is automatically generated, do not edit manually! */

namespace WhichBrowser\Data;

BrowserIds::$ANDROID_BROWSERS = [
    'com.agilebits.onepassword'                                                                           => '1Password',
    'com.browser2345'                                                                                     => '2345 Browser',
    'com.browser2345_ucc'                                                                                 => '2345 Browser',
    'com.browser2345hd'                                                                                   => '2345 Browser HD',
    'air.stage.web.view'                                                                                  => 'Adobe AIR',
    'air.stagewebview'                                                                                    => 'Adobe AIR',
    'air.StageWebViewBridgeTest.debug'                                                                    => 'Adobe AIR',
    'air.StageWebViewVideo.debug'                                                                         => 'Adobe AIR',
    'com.adobe.phonegap.app'                                                                              => 'Adobe Phonegap',
    'com.adobe.reader'                                                                                    => 'Adobe Reader',
    'com.adobe.shadow.android'                                                                            => 'Adobe Shadow',
    'com.airwatch.browser'                                                                                => 'AirWatch Browser',
    'com.aliyun.mobile.browser'                                                                           => 'Aliyun Browser',
    'net.adgjm.angel'                                                                                     => 'Angel Browser',
    'com.apc.browser'                                                                                     => 'APC',
    'com.apc.browser.standard'                                                                            => 'APC',
    'com.apc.browser.standard6j8s'                                                                        => 'APC',
    'com.apusapps.browser'                                                                                => 'APUS Browser',
    'com.apusapps.browser.lark'                                                                           => 'APUS Browser',
    'com.apusapps.browser.turbo'                                                                          => 'APUS Browser',
    'com.asus.browser'                                                                                    => 'Asus Browser',
    'com.wAuroraWebBrowser'                                                                               => 'Aurora Web Browser',
    'bdmobile.android.app'                                                                                => 'Baidu Browser',
    'com.baidu.blink.testapp'                                                                             => 'Baidu Browser',
    'com.baidu.blink.v38'                                                                                 => 'Baidu Browser',
    'com.baidu.browser.apps'                                                                              => 'Baidu Browser',
    'com.baidu.browser.apps.lite'                                                                         => 'Baidu Browser',
    'com.baidu.browser.apps_mr'                                                                           => 'Baidu Browser',
    'com.baidu.browser.apps_neo'                                                                          => 'Baidu Browser',
    'com.baidu.browser.apps_sj'                                                                           => 'Baidu Browser',
    'com.baidu.browser.apps_yt'                                                                           => 'Baidu Browser',
    'com.baidu.browser.app_bw'                                                                            => 'Baidu Browser',
    'com.baidu.browser.blink.apps'                                                                        => 'Baidu Browser',
    'com.baidu.browser.brower'                                                                            => 'Baidu Browser',
    'com.baidu.browser.chrome'                                                                            => 'Baidu Browser',
    'com.baidu.browser.inter'                                                                             => 'Baidu Browser',
    'com.baidu.browser.inter.mini'                                                                        => 'Baidu Browser',
    'com.baidu.browser.interyb'                                                                           => 'Baidu Browser',
    'com.baidu.browser.lab'                                                                               => 'Baidu Browser',
    'com.baidu.browser.pad'                                                                               => 'Baidu Browser',
    'com.baidu.browserhd.inter'                                                                           => 'Baidu Browser',
    'com.baidu.demo.webview'                                                                              => 'Baidu Browser',
    'com.baidu.hi'                                                                                        => 'Baidu Browser',
    'com.baidu.searchbox'                                                                                 => 'Baidu Browser',
    'com.baidu.searchbox.pad'                                                                             => 'Baidu Browser',
    'com.baidu.searchbox_bbk'                                                                             => 'Baidu Browser',
    'com.baidu.searchbox_coolpad'                                                                         => 'Baidu Browser',
    'com.baidu.searchbox_gionee'                                                                          => 'Baidu Browser',
    'com.baidu.searchbox_hisense'                                                                         => 'Baidu Browser',
    'com.baidu.searchbox_htc'                                                                             => 'Baidu Browser',
    'com.baidu.searchbox_huawei'                                                                          => 'Baidu Browser',
    'com.baidu.searchbox_ktouch'                                                                          => 'Baidu Browser',
    'com.baidu.searchbox_lenovo'                                                                          => 'Baidu Browser',
    'com.baidu.searchbox_oppo'                                                                            => 'Baidu Browser',
    'com.baidu.searchbox_samsung'                                                                         => 'Baidu Browser',
    'com.baidu.searchbox_sj'                                                                              => 'Baidu Browser',
    'com.baidu.searchbox_sony'                                                                            => 'Baidu Browser',
    'com.baidu.searchbox_tianyi'                                                                          => 'Baidu Browser',
    'com.baidu.searchbox_zte'                                                                             => 'Baidu Browser',
    'com.baidu.zeustest'                                                                                  => 'Baidu Browser',
    'com.wBestBrowser'                                                                                    => 'Best Browser',
    'com.boatbrowser.free'                                                                                => 'Boat Browser',
    'com.boatbrowser.tablet'                                                                              => 'Boat Browser',
    'com.boatgo.browser'                                                                                  => 'Boat Browser Mini',
    'com.ericsson.research.mario'                                                                         => 'Bowser',
    'com.dotsandlines.carbon'                                                                             => 'Carbon',
    'app.chameleon_browser'                                                                               => 'Chameleon browser',
    'com.coship.mmcp'                                                                                     => 'Coship MMCP',
    'com.coship.tvos.web'                                                                                 => 'Coship MMCP',
    'com.coship.webapp'                                                                                   => 'Coship MMCP',
    'net.daum.android.daum'                                                                               => 'Daum',
    'com.phikod.deviceportinfo'                                                                           => 'Device Info Android',
    'com.dolphin.browser.android.jp'                                                                      => 'Dolphin',
    'com.dolphin.browser.id'                                                                              => 'Dolphin',
    'com.dolphin.browser.pad'                                                                             => 'Dolphin',
    'com.dolphin.browser.tuna'                                                                            => 'Dolphin',
    'com.dolphin.web.browser.android'                                                                     => 'Dolphin',
    'mobi.mgeek.TunnyBrowser'                                                                             => 'Dolphin',
    'mobile.mgeek.TunnyBrowser'                                                                           => 'Dolphin',
    'com.dolphin.browser.xf'                                                                              => 'Dolphin (Fighter Edition)',
    'com.dolphin.browser.lab.cn'                                                                          => 'Dolphin Beta',
    'com.dolphin.browser.lab.en'                                                                          => 'Dolphin Beta',
    'com.dolphin.browser.express.web'                                                                     => 'Dolphin Express',
    'com.dolphin.browser.express.web.beta'                                                                => 'Dolphin Express',
    'com.dolphin.browser'                                                                                 => 'Dolphin Mini',
    'com.dolphin.browser.cn'                                                                              => 'Dolphin Mini',
    'com.dolphin.browser.zero'                                                                            => 'Dolphin Zero',
    'com.duckduckgo.mobile.android'                                                                       => 'DuckDuckGo',
    'easy.browser'                                                                                        => 'Easy Browser',
    'easy.browser.classic'                                                                                => 'Easy Browser',
    'easy.browser.com'                                                                                    => 'Easy Browser',
    'it.blogspot.fedeveloper'                                                                             => 'Emergency Browser',
    'com.easy.browser'                                                                                    => 'ES Browser',
    'org.espier.browser'                                                                                  => 'Espier Browser',
    'com.exsoul'                                                                                          => 'Exsoul Web Browser',
    'com.jv.falcon'                                                                                       => 'Falcon',
    'com.jv.falcon.pro'                                                                                   => 'Falcon Pro',
    'com.devhd.feedly'                                                                                    => 'Feedly',
    'com.firemonkeybrowser'                                                                               => 'FireMonkey',
    'com.comespice.browser'                                                                               => 'Flash Browser',
    'com.funnyeden.browser'                                                                               => 'Flash Browser',
    'com.whattheapps.fbrowser'                                                                            => 'Flash Browser',
    'flipboard.app'                                                                                       => 'Flipboard',
    'flipboard.cn'                                                                                        => 'Flipboard',
    'com.lwi.android.flapps'                                                                              => 'Floating Apps',
    'com.lwi.android.flappsfull'                                                                          => 'Floating Apps',
    'gpc.myweb.hinet.net.PopupWeb'                                                                        => 'Floating Browser Flux',
    'com.fortune.browser'                                                                                 => 'Fortune Web Browser',
    'com.browser.frogbrowser'                                                                             => 'Frog Browser',
    'com.crowbar.beaverbrowser'                                                                           => 'Frost Browser',
    'com.crowbar.beaverlite'                                                                              => 'Frost Browser',
    'galaxy.browser.gb.free'                                                                              => 'Galaxy Browser',
    'galaxy.browser.gb.pro'                                                                               => 'Galaxy Browser',
    'com.mybro.browsah'                                                                                   => 'Genie Browser',
    'com.noinnion.android.greader.reader'                                                                 => 'gReader',
    'com.noinnion.android.greader.readerpro'                                                              => 'gReader Pro',
    'jp.ddo.pigsty.HabitBrowser'                                                                          => 'Habit Browser',
    'jp.ddo.pigsty.Habit_Browser'                                                                         => 'Habit Browser',
    'com.baidu.hao123'                                                                                    => 'Hao123',
    'com.baidu.hao123.chrome.browser'                                                                     => 'Hao123',
    'com.baidu.hao123.global'                                                                             => 'Hao123',
    'com.htc.browser'                                                                                     => 'HTC Sense Browser',
    'com.htc.sense.browser'                                                                               => 'HTC Sense Browser',
    'com.huawei.android.browser'                                                                          => 'Huawei Emotion Browser',
    'com.huawei.browser'                                                                                  => 'Huawei Emotion Browser',
    'jp.co.lunascape.android.ilunascape'                                                                  => 'iLunascape 2',
    'nu.tommie.inbrowser'                                                                                 => 'InBrowser',
    'nu.tommie.inbrowser.beta'                                                                            => 'InBrowser Beta',
    'com.infamous.browser'                                                                                => 'Infamous Browser',
    'derek.iSurf'                                                                                         => 'iSurf',
    'com.jerky.browser'                                                                                   => 'Javelin Incognito Browser',
    'com.jerky.browser2'                                                                                  => 'Javelin Incognito Browser',
    'com.adsi.kioware.client.mobile.app'                                                                  => 'KioWare Kiosk',
    'com.kroniapp.browse'                                                                                 => 'Kronia Browser',
    'com.web.browser.labaadibrowser'                                                                      => 'Labaadi Browser',
    'com.vng.android.zingbrowser'                                                                         => 'Laban',
    'com.lastpass.lpandroid'                                                                              => 'LastPass',
    'com.lenovo.browser'                                                                                  => 'Lenovo Browser',
    'com.lenovo.lps.simple'                                                                               => 'Lenovo Browser',
    'com.light.browser'                                                                                   => 'Light Browser',
    'acr.browser.barebones'                                                                               => 'Lightning Browser',
    'acr.browser.butterfly'                                                                               => 'Lightning Browser',
    'acr.browser.lightning'                                                                               => 'Lightning Browser',
    'acr.browser.lightning2'                                                                              => 'Lightning Browser',
    'acr.browser.lightningq16w'                                                                           => 'Lightning Browser',
    'acr.browser.spartancompany'                                                                          => 'Lightning Browser',
    'acr.browser.spartancompanypaid'                                                                      => 'Lightning Browser',
    'acr.browser.thunder'                                                                                 => 'Lightning Browser',
    'com.MoNTE.Lime'                                                                                      => 'Lime',
    'com.alphonso.pulse'                                                                                  => 'LinkedIn Pulse',
    'com.logio.logos'                                                                                     => 'Logos Browser',
    'it.lombardo.Lombardo'                                                                                => 'Lombardo',
    'com.powerpoint45.lucidbrowser'                                                                       => 'Lucid Browser',
    'com.powerpoint45.lucidbrowserfree'                                                                   => 'Lucid Browser',
    'com.fiberlink.maas360.android.securebrowser'                                                         => 'MaaS360 Browser',
    'com.mx.browser'                                                                                      => 'Maxthon',
    'com.mx.browser.appendix'                                                                             => 'Maxthon',
    'com.mx.browser.fast'                                                                                 => 'Maxthon',
    'com.mx.browser.free.mx100000000000'                                                                  => 'Maxthon',
    'com.mx.browser.free.mx100000001915'                                                                  => 'Maxthon',
    'com.mx.browser.free.mx100000002422'                                                                  => 'Maxthon',
    'com.mx.browser.free.mx100000003135'                                                                  => 'Maxthon',
    'com.mx.browser.free.mx100000003415'                                                                  => 'Maxthon',
    'com.mx.browser.free.mx100000004211'                                                                  => 'Maxthon',
    'com.mx.browser.free.mx100000004981'                                                                  => 'Maxthon',
    'com.mx.browser.free.mx100000005137'                                                                  => 'Maxthon',
    'com.mx.browser.free.mx200000000239'                                                                  => 'Maxthon',
    'com.mx.browser.free.mx200000006760'                                                                  => 'Maxthon',
    'com.mx.browser.free.mx200000013070'                                                                  => 'Maxthon',
    'com.mx.browser.free.mx200000014602'                                                                  => 'Maxthon',
    'com.mx.browser.free.mx200000014853'                                                                  => 'Maxthon',
    'com.mx.browser.hg'                                                                                   => 'Maxthon',
    'com.mx.browser.kids'                                                                                 => 'Maxthon',
    'com.mx.browser.mtk'                                                                                  => 'Maxthon',
    'com.mx.browser.tablet'                                                                               => 'Maxthon',
    'jp.co.inos.c1Browser'                                                                                => 'MDM Browser',
    'com.ilegendsoft.mercury'                                                                             => 'Mercury',
    'com.dmkho.mbm'                                                                                       => 'MiniBrowser Mobile',
    'com.dmkho.mbmbeta'                                                                                   => 'MiniBrowser Mobile',
    'com.mseven.msecure'                                                                                  => 'mSecure',
    'com.visvanoid.secretbrowse'                                                                          => 'My Internet',
    'com.fevdev.nakedbrowser'                                                                             => 'Naked Browser',
    'com.fevdev.nakedbrowserNDM'                                                                          => 'Naked Browser',
    'com.fevdev.nakedbrowserpro'                                                                          => 'Naked Browser Pro',
    'com.nanobrowser'                                                                                     => 'Nano Browser',
    'com.bos.ebrowser'                                                                                    => 'Navegador eBrowser',
    'com.nhn.android.navercafe'                                                                           => 'Naver',
    'com.nhn.android.naverdic'                                                                            => 'Naver',
    'com.nhn.android.search'                                                                              => 'Naver',
    'com.access_company.android.livebrowser'                                                              => 'NetFront Life Browser',
    'com.access_company.android.nflifebrowser.lite'                                                       => 'NetFront Life Browser',
    'com.access_company.android.nflifeconnect'                                                            => 'NetFront Life Connect',
    'com.jiubang.browser'                                                                                 => 'Next Browser',
    'com.ninesky.browser'                                                                                 => 'Ninesky',
    'com.ninesky.browsercn'                                                                               => 'Ninesky',
    'com.ninesky.browserzh'                                                                               => 'Ninesky',
    'com.ninesky.nqbrowsercn'                                                                             => 'Ninesky',
    'com.ninesky.nsbrowser'                                                                               => 'Ninesky',
    'com.ninesky.sebrowser'                                                                               => 'Ninesky',
    'com.appsverse.privatebrowser'                                                                        => 'Nova Private Browser',
    'com.browser.nowadvanced'                                                                             => 'Now Browser',
    'com.browser.nowbasic'                                                                                => 'Now Browser',
    'com.browser.nowpro'                                                                                  => 'Now Browser',
    'com.browser.nowpro412t'                                                                              => 'Now Browser',
    'com.wNewVisionBrowser'                                                                               => 'NVision Browser',
    'com.compal.android.browser'                                                                          => 'Ocean Browser',
    'com.maskyn.oldbrowser'                                                                               => 'Old But Gold Internet Browser',
    'com.tencent.ibibo.mtt'                                                                               => 'One Browser',
    'info.guardianproject.browser'                                                                        => 'Orweb',
    'com.oupeng.browser'                                                                                  => 'Oupeng Browser',
    'com.oupeng.browser.beta10'                                                                           => 'Oupeng Browser',
    'com.oupeng.browser.turbobeta'                                                                        => 'Oupeng Browser',
    'com.oupeng.browserpre.cmcc'                                                                          => 'Oupeng Browser',
    'com.oupeng.mini.android'                                                                             => 'Oupeng Browser',
    'com.oupeng.xbrowser'                                                                                 => 'Oupeng Browser',
    'com.oupeng.xbrowser.beta'                                                                            => 'Oupeng Browser',
    'com.myboyfriendisageek.airbrowser'                                                                   => 'OverSkreen',
    'com.panasonic.pavc.viera.vieraremote2'                                                               => 'Panasonic TV Remote',
    'com.wPcBrowser'                                                                                      => 'PC Browser Mini',
    'com.penguinbrowser.penguinbrowser'                                                                   => 'Penguin browser',
    'com.appsverse.photon'                                                                                => 'Photon Browser',
    'com.appsverse.photonamazon'                                                                          => 'Photon Browser',
    'com.levelup.touiteur'                                                                                => 'Plume',
    'com.ideashower.readitlater.pro'                                                                      => 'Pocket',
    'com.gflam.portal'                                                                                    => 'Portal',
    'com.hat.privatebrowser'                                                                              => 'Private Browser',
    'com.JamesBecwar.FreePrivateBrowser'                                                                  => 'Private Browser',
    'com.JamesBecwar.PrivateBrowser'                                                                      => 'Private Browser',
    'com.qihoa.browser'                                                                                   => 'Qihoo 360 Browser',
    'com.qihoo.androidbrowser'                                                                            => 'Qihoo 360 Browser',
    'com.qihoo.appstore'                                                                                  => 'Qihoo 360 Browser',
    'com.qihoo.browser'                                                                                   => 'Qihoo 360 Browser',
    'com.qihoo.browsertest'                                                                               => 'Qihoo 360 Browser',
    'com.qihoo.express.browser'                                                                           => 'Qihoo 360 Browser',
    'com.qihoo.expressbrowser'                                                                            => 'Qihoo 360 Browser',
    'com.qihoo.haosou'                                                                                    => 'Qihoo 360 Browser',
    'com.qihoo.padbrowser'                                                                                => 'Qihoo 360 Browser',
    'com.qihoo.padbrowser7'                                                                               => 'Qihoo 360 Browser',
    'com.qihoo360.mobilesafe'                                                                             => 'Qihoo 360 Browser',
    'com.qihoo360.mobilesafe_meizu'                                                                       => 'Qihoo 360 Browser',
    'com.qihoo360.mobilesafe_mtk6573'                                                                     => 'Qihoo 360 Browser',
    'com.tencent.android.pad'                                                                             => 'QQ Browser',
    'com.tencent.blink'                                                                                   => 'QQ Browser',
    'com.tencent.hd.qq'                                                                                   => 'QQ Browser',
    'com.tencent.internat.mtt'                                                                            => 'QQ Browser',
    'com.tencent.international.mtt'                                                                       => 'QQ Browser',
    'com.tencent.mobileqq'                                                                                => 'QQ Browser',
    'com.tencent.mtt'                                                                                     => 'QQ Browser',
    'com.tencent.padbrowser'                                                                              => 'QQ Browser',
    'appinventor.ai_progetto2003.SCAN'                                                                    => 'QR Barcode Scanner',
    'com.rarster.QuantumBrowser'                                                                          => 'QuantumBrowser',
    'net.virifi.android.quickbrowser'                                                                     => 'Quick ICS Browser',
    'net.virifi.android.quickbrowserpro'                                                                  => 'Quick ICS Browser',
    'com.bjy.quicklinkbrowser'                                                                            => 'Quick Link Browser',
    'com.rapid.browser'                                                                                   => 'Rapid Browser Pro',
    'com.rapid.browser2'                                                                                  => 'Rapid Browser Pro',
    'com.andrewshu.android.reddit'                                                                        => 'Reddit is fun',
    'com.andrewshu.android.redditdonation'                                                                => 'Reddit is fun',
    'reddit.news'                                                                                         => 'Reddit News',
    'com.laurencedawson.reddit_sync.dev'                                                                  => 'Reddit Sync Dev',
    'com.laurencedawson.reddit_sync.pro'                                                                  => 'Reddit Sync Pro',
    'org.quantumbadger.redreader'                                                                         => 'RedReader',
    'de.bomhard.android.RetroBrowser'                                                                     => 'RetroBrowser',
    'com.rocket.browser'                                                                                  => 'Rocket Browser',
    'com.springdesign.screenshare.browser.client'                                                         => 'ScreenShare',
    'com.springdesign.screenshare.browser.server'                                                         => 'ScreenShare',
    'com.shark.sharkbrowser'                                                                              => 'Shark Browser',
    'com.sina.weibo'                                                                                      => 'Sina Weibo',
    'com.sina.weibog3'                                                                                    => 'Sina Weibo',
    'com.sithagi.sithbrowser'                                                                             => 'Sith Browser',
    'com.skyfire.browser'                                                                                 => 'SkyFire',
    'com.skyfire.browser.toolbar'                                                                         => 'SkyFire',
    'com.skyfire.consumer.browser'                                                                        => 'Skyfire',
    'jp.co.fenrir.android.sleipnir'                                                                       => 'Sleipnir',
    'jp.co.fenrir.android.sleipnir_black'                                                                 => 'Sleipnir',
    'jp.co.fenrir.android.sleipnir_ngp'                                                                   => 'Sleipnir',
    'jp.co.fenrir.android.sleipnir_test'                                                                  => 'Sleipnir',
    'jp.gocro.smartnews.android'                                                                          => 'SmartNews',
    'com.cmcc.sofabrowser'                                                                                => 'Sofa Browser',
    'sogou.mobile.explorer'                                                                               => 'Sogou Mobile',
    'com.solo.browser'                                                                                    => 'Solo',
    'com.sonymobile.smallbrowser'                                                                         => 'Sony Small Browser',
    'com.speedy.browser'                                                                                  => 'Speedy Browser',
    'com.appestry.split_browser'                                                                          => 'Split Browser',
    'com.appestry.split_browser_trial'                                                                    => 'Split Browser',
    'com.stumbleupon.android.app'                                                                         => 'StumbleUpon',
    'iron.web.jalepano.browser'                                                                           => 'Super Fast Browser',
    'com.gears42.surefox'                                                                                 => 'SureFox',
    'com.mns.android.swing'                                                                               => 'Swing Browser',
    'net.biniok.tampermonkey'                                                                             => 'Tampermonkey',
    'com.tcl.browser'                                                                                     => 'TCL Browser',
    'com.thuban.browser'                                                                                  => 'Thuban Handset Browser',
    'org.tint'                                                                                            => 'Tint Browser',
    'com.twidroid'                                                                                        => 'UberSocial',
    'com.uc.browser'                                                                                      => 'UC Browser',
    'com.uc.browser.en2ly5'                                                                               => 'UC Browser',
    'com.uc.browser.hd.x86'                                                                               => 'UC Browser',
    'com.uc.browser.hdx3kg'                                                                               => 'UC Browser',
    'com.UCMobile'                                                                                        => 'UC Browser',
    'com.UCMobile.labs'                                                                                   => 'UC Browser',
    'com.UCMobile.ucsdk'                                                                                  => 'UC Browser',
    'com.UCMobile295d'                                                                                    => 'UC Browser',
    'com.UCMobile2jm4'                                                                                    => 'UC Browser',
    'com.UCMobile9jpo'                                                                                    => 'UC Browser',
    'com.UCMobileodw6'                                                                                    => 'UC Browser',
    'com.uc.browser.hd'                                                                                   => 'UC Browser HD',
    'com.uc.browser.en'                                                                                   => 'UC Browser Mini',
    'com.tencent.mm'                                                                                      => 'WeChat',
    'sui.mRelease'                                                                                        => 'xScope Browser Pro',
    'jp.co.yahoo.android.ybrowser'                                                                        => 'Yahoo! JAPAN',
    'jp.co.yahoo.android.yjtop'                                                                           => 'Yahoo! JAPAN',
    'ru.yandex.shell'                                                                                     => 'Yandex Shell',
    'com.zetakey.browser'                                                                                 => 'Zetakey',
    'org.zirco'                                                                                           => 'Zirco Browser',
    'com.zte.browser'                                                                                     => 'ZTE Browser',
];
