# [YooKassa API SDK](../home.md)

# Class: \YooKassa\Common\Exceptions\InvalidPropertyValueTypeException
### Namespace: [\YooKassa\Common\Exceptions](../namespaces/yookassa-common-exceptions.md)
---

---
### Constants
* No constants found

---
### Methods
| Visibility | Name | Flag | Summary |
| ----------:| ---- | ---- | ------- |
| public | [__construct()](../classes/YooKassa-Common-Exceptions-InvalidPropertyValueTypeException.md#method___construct) |  | InvalidPropertyValueTypeException constructor. |
| public | [getProperty()](../classes/YooKassa-Common-Exceptions-InvalidPropertyException.md#method_getProperty) |  |  |
| public | [getType()](../classes/YooKassa-Common-Exceptions-InvalidPropertyValueTypeException.md#method_getType) |  |  |

---
### Details
* File: [lib/Common/Exceptions/InvalidPropertyValueTypeException.php](../../lib/Common/Exceptions/InvalidPropertyValueTypeException.php)
* Package: Default
* Class Hierarchy:  
  * [\InvalidArgumentException](\InvalidArgumentException)
  * [\YooKassa\Common\Exceptions\InvalidPropertyException](../classes/YooKassa-Common-Exceptions-InvalidPropertyException.md)
  * \YooKassa\Common\Exceptions\InvalidPropertyValueTypeException

---
## Methods
<a name="method___construct" class="anchor"></a>
#### public __construct() : mixed

```php
public __construct(string $message = &#039;&#039;, int $code, string $property = &#039;&#039;, mixed|null $value = null) : mixed
```

**Summary**

InvalidPropertyValueTypeException constructor.

**Details:**
* Inherited From: [\YooKassa\Common\Exceptions\InvalidPropertyValueTypeException](../classes/YooKassa-Common-Exceptions-InvalidPropertyValueTypeException.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string</code> | message  |  |
| <code lang="php">int</code> | code  |  |
| <code lang="php">string</code> | property  |  |
| <code lang="php">mixed OR null</code> | value  |  |

**Returns:** mixed - 


<a name="method_getProperty" class="anchor"></a>
#### public getProperty() : string

```php
public getProperty() : string
```

**Details:**
* Inherited From: [\YooKassa\Common\Exceptions\InvalidPropertyException](../classes/YooKassa-Common-Exceptions-InvalidPropertyException.md)

**Returns:** string - 


<a name="method_getType" class="anchor"></a>
#### public getType() : string

```php
public getType() : string
```

**Details:**
* Inherited From: [\YooKassa\Common\Exceptions\InvalidPropertyValueTypeException](../classes/YooKassa-Common-Exceptions-InvalidPropertyValueTypeException.md)

**Returns:** string - 



---

### Top Namespaces

* [\YooKassa](../namespaces/yookassa.md)

---

### Reports
* [Errors - 0](../reports/errors.md)
* [Markers - 0](../reports/markers.md)
* [Deprecated - 25](../reports/deprecated.md)

---

This document was automatically generated from source code comments on 2024-07-02 using [phpDocumentor](http://www.phpdoc.org/)

&copy; 2024 YooMoney