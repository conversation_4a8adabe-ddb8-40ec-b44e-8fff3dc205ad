# [YooKassa API SDK](../home.md)

# Class: \YooKassa\Request\Payments\PaymentData\PaymentDataB2bSberbank
### Namespace: [\YooKassa\Request\Payments\PaymentData](../namespaces/yookassa-request-payments-paymentdata.md)
---
**Summary:**

Класс, представляющий модель PaymentMethodDataB2bSberbank.

**Description:**

Данные для оплаты через СберБанк Бизнес Онлайн.

---
### Constants
* No constants found

---
### Properties
| Visibility | Name | Flag | Summary |
| ----------:| ---- | ---- | ------- |
| public | [$payment_purpose](../classes/YooKassa-Request-Payments-PaymentData-PaymentDataB2bSberbank.md#property_payment_purpose) |  | Назначение платежа (не больше 210 символов). |
| public | [$paymentPurpose](../classes/YooKassa-Request-Payments-PaymentData-PaymentDataB2bSberbank.md#property_paymentPurpose) |  | Назначение платежа (не больше 210 символов). |
| public | [$type](../classes/YooKassa-Request-Payments-PaymentData-AbstractPaymentData.md#property_type) |  | Тип метода оплаты |
| public | [$vat_data](../classes/YooKassa-Request-Payments-PaymentData-PaymentDataB2bSberbank.md#property_vat_data) |  | Данные об НДС. |
| public | [$vatData](../classes/YooKassa-Request-Payments-PaymentData-PaymentDataB2bSberbank.md#property_vatData) |  | Данные об НДС. |
| protected | [$_type](../classes/YooKassa-Request-Payments-PaymentData-AbstractPaymentData.md#property__type) |  |  |

---
### Methods
| Visibility | Name | Flag | Summary |
| ----------:| ---- | ---- | ------- |
| public | [__construct()](../classes/YooKassa-Request-Payments-PaymentData-PaymentDataB2bSberbank.md#method___construct) |  |  |
| public | [__get()](../classes/YooKassa-Common-AbstractObject.md#method___get) |  | Возвращает значение свойства. |
| public | [__isset()](../classes/YooKassa-Common-AbstractObject.md#method___isset) |  | Проверяет наличие свойства. |
| public | [__set()](../classes/YooKassa-Common-AbstractObject.md#method___set) |  | Устанавливает значение свойства. |
| public | [__unset()](../classes/YooKassa-Common-AbstractObject.md#method___unset) |  | Удаляет свойство. |
| public | [fromArray()](../classes/YooKassa-Common-AbstractObject.md#method_fromArray) |  | Устанавливает значения свойств текущего объекта из массива. |
| public | [getPaymentPurpose()](../classes/YooKassa-Request-Payments-PaymentData-PaymentDataB2bSberbank.md#method_getPaymentPurpose) |  | Возвращает назначение платежа. |
| public | [getType()](../classes/YooKassa-Request-Payments-PaymentData-AbstractPaymentData.md#method_getType) |  | Возвращает тип метода оплаты. |
| public | [getValidator()](../classes/YooKassa-Common-AbstractObject.md#method_getValidator) |  |  |
| public | [getVatData()](../classes/YooKassa-Request-Payments-PaymentData-PaymentDataB2bSberbank.md#method_getVatData) |  | Возвращает назначение платежа. |
| public | [jsonSerialize()](../classes/YooKassa-Common-AbstractObject.md#method_jsonSerialize) |  | Возвращает ассоциативный массив со свойствами текущего объекта для его дальнейшей JSON сериализации. |
| public | [offsetExists()](../classes/YooKassa-Common-AbstractObject.md#method_offsetExists) |  | Проверяет наличие свойства. |
| public | [offsetGet()](../classes/YooKassa-Common-AbstractObject.md#method_offsetGet) |  | Возвращает значение свойства. |
| public | [offsetSet()](../classes/YooKassa-Common-AbstractObject.md#method_offsetSet) |  | Устанавливает значение свойства. |
| public | [offsetUnset()](../classes/YooKassa-Common-AbstractObject.md#method_offsetUnset) |  | Удаляет свойство. |
| public | [setPaymentPurpose()](../classes/YooKassa-Request-Payments-PaymentData-PaymentDataB2bSberbank.md#method_setPaymentPurpose) |  | Устанавливает назначение платежа. |
| public | [setVatData()](../classes/YooKassa-Request-Payments-PaymentData-PaymentDataB2bSberbank.md#method_setVatData) |  | Устанавливает назначение платежа. |
| public | [toArray()](../classes/YooKassa-Common-AbstractObject.md#method_toArray) |  | Возвращает ассоциативный массив со свойствами текущего объекта для его дальнейшей JSON сериализации Является алиасом метода AbstractObject::jsonSerialize(). |
| protected | [getUnknownProperties()](../classes/YooKassa-Common-AbstractObject.md#method_getUnknownProperties) |  | Возвращает массив свойств которые не существуют, но были заданы у объекта. |
| protected | [setType()](../classes/YooKassa-Request-Payments-PaymentData-AbstractPaymentData.md#method_setType) |  | Устанавливает тип метода оплаты. |
| protected | [validatePropertyValue()](../classes/YooKassa-Common-AbstractObject.md#method_validatePropertyValue) |  |  |

---
### Details
* File: [lib/Request/Payments/PaymentData/PaymentDataB2bSberbank.php](../../lib/Request/Payments/PaymentData/PaymentDataB2bSberbank.php)
* Package: YooKassa\Request
* Class Hierarchy:  
  * [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)
  * [\YooKassa\Request\Payments\PaymentData\AbstractPaymentData](../classes/YooKassa-Request-Payments-PaymentData-AbstractPaymentData.md)
  * \YooKassa\Request\Payments\PaymentData\PaymentDataB2bSberbank

* See Also:
  * [](https://yookassa.ru/developers/api)

---
### Tags
| Tag | Version | Description |
| --- | ------- | ----------- |
| category |  | Class |
| author |  | <EMAIL> |

---
## Properties
<a name="property_payment_purpose"></a>
#### public $payment_purpose : string
---
***Description***

Назначение платежа (не больше 210 символов).

**Type:** <a href="../string"><abbr title="string">string</abbr></a>

**Details:**


<a name="property_paymentPurpose"></a>
#### public $paymentPurpose : string
---
***Description***

Назначение платежа (не больше 210 символов).

**Type:** <a href="../string"><abbr title="string">string</abbr></a>

**Details:**


<a name="property_type"></a>
#### public $type : string
---
***Description***

Тип метода оплаты

**Type:** <a href="../string"><abbr title="string">string</abbr></a>

**Details:**
* Inherited From: [\YooKassa\Request\Payments\PaymentData\AbstractPaymentData](../classes/YooKassa-Request-Payments-PaymentData-AbstractPaymentData.md)


<a name="property_vat_data"></a>
#### public $vat_data : \YooKassa\Model\Payment\PaymentMethod\B2b\Sberbank\AbstractVatData
---
***Description***

Данные об НДС.

**Type:** <a href="../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-AbstractVatData.html"><abbr title="\YooKassa\Model\Payment\PaymentMethod\B2b\Sberbank\AbstractVatData">AbstractVatData</abbr></a>

**Details:**


<a name="property_vatData"></a>
#### public $vatData : \YooKassa\Model\Payment\PaymentMethod\B2b\Sberbank\AbstractVatData
---
***Description***

Данные об НДС.

**Type:** <a href="../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-AbstractVatData.html"><abbr title="\YooKassa\Model\Payment\PaymentMethod\B2b\Sberbank\AbstractVatData">AbstractVatData</abbr></a>

**Details:**


<a name="property__type"></a>
#### protected $_type : ?string
---
**Type:** <a href="../?string"><abbr title="?string">?string</abbr></a>

**Details:**
* Inherited From: [\YooKassa\Request\Payments\PaymentData\AbstractPaymentData](../classes/YooKassa-Request-Payments-PaymentData-AbstractPaymentData.md)



---
## Methods
<a name="method___construct" class="anchor"></a>
#### public __construct() : mixed

```php
public __construct(?array $data = []) : mixed
```

**Details:**
* Inherited From: [\YooKassa\Request\Payments\PaymentData\PaymentDataB2bSberbank](../classes/YooKassa-Request-Payments-PaymentData-PaymentDataB2bSberbank.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">?array</code> | data  |  |

**Returns:** mixed - 


<a name="method___get" class="anchor"></a>
#### public __get() : mixed

```php
public __get(string $propertyName) : mixed
```

**Summary**

Возвращает значение свойства.

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string</code> | propertyName  | Имя свойства |

**Returns:** mixed - Значение свойства


<a name="method___isset" class="anchor"></a>
#### public __isset() : bool

```php
public __isset(string $propertyName) : bool
```

**Summary**

Проверяет наличие свойства.

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string</code> | propertyName  | Имя проверяемого свойства |

**Returns:** bool - True если свойство имеется, false если нет


<a name="method___set" class="anchor"></a>
#### public __set() : void

```php
public __set(string $propertyName, mixed $value) : void
```

**Summary**

Устанавливает значение свойства.

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string</code> | propertyName  | Имя свойства |
| <code lang="php">mixed</code> | value  | Значение свойства |

**Returns:** void - 


<a name="method___unset" class="anchor"></a>
#### public __unset() : void

```php
public __unset(string $propertyName) : void
```

**Summary**

Удаляет свойство.

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string</code> | propertyName  | Имя удаляемого свойства |

**Returns:** void - 


<a name="method_fromArray" class="anchor"></a>
#### public fromArray() : void

```php
public fromArray(array|\Traversable $sourceArray) : void
```

**Summary**

Устанавливает значения свойств текущего объекта из массива.

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">array OR \Traversable</code> | sourceArray  | Ассоциативный массив с настройками |

**Returns:** void - 


<a name="method_getPaymentPurpose" class="anchor"></a>
#### public getPaymentPurpose() : string|null

```php
public getPaymentPurpose() : string|null
```

**Summary**

Возвращает назначение платежа.

**Details:**
* Inherited From: [\YooKassa\Request\Payments\PaymentData\PaymentDataB2bSberbank](../classes/YooKassa-Request-Payments-PaymentData-PaymentDataB2bSberbank.md)

**Returns:** string|null - Назначение платежа


<a name="method_getType" class="anchor"></a>
#### public getType() : string|null

```php
public getType() : string|null
```

**Summary**

Возвращает тип метода оплаты.

**Details:**
* Inherited From: [\YooKassa\Request\Payments\PaymentData\AbstractPaymentData](../classes/YooKassa-Request-Payments-PaymentData-AbstractPaymentData.md)

**Returns:** string|null - Тип метода оплаты


<a name="method_getValidator" class="anchor"></a>
#### public getValidator() : \YooKassa\Validator\Validator

```php
public getValidator() : \YooKassa\Validator\Validator
```

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

**Returns:** \YooKassa\Validator\Validator - 


<a name="method_getVatData" class="anchor"></a>
#### public getVatData() : \YooKassa\Model\Payment\PaymentMethod\B2b\Sberbank\AbstractVatData|null

```php
public getVatData() : \YooKassa\Model\Payment\PaymentMethod\B2b\Sberbank\AbstractVatData|null
```

**Summary**

Возвращает назначение платежа.

**Details:**
* Inherited From: [\YooKassa\Request\Payments\PaymentData\PaymentDataB2bSberbank](../classes/YooKassa-Request-Payments-PaymentData-PaymentDataB2bSberbank.md)

**Returns:** \YooKassa\Model\Payment\PaymentMethod\B2b\Sberbank\AbstractVatData|null - Данные об НДС


<a name="method_jsonSerialize" class="anchor"></a>
#### public jsonSerialize() : array

```php
public jsonSerialize() : array
```

**Summary**

Возвращает ассоциативный массив со свойствами текущего объекта для его дальнейшей JSON сериализации.

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

**Returns:** array - Ассоциативный массив со свойствами текущего объекта


<a name="method_offsetExists" class="anchor"></a>
#### public offsetExists() : bool

```php
public offsetExists(string $offset) : bool
```

**Summary**

Проверяет наличие свойства.

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string</code> | offset  | Имя проверяемого свойства |

**Returns:** bool - True если свойство имеется, false если нет


<a name="method_offsetGet" class="anchor"></a>
#### public offsetGet() : mixed

```php
public offsetGet(string $offset) : mixed
```

**Summary**

Возвращает значение свойства.

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string</code> | offset  | Имя свойства |

**Returns:** mixed - Значение свойства


<a name="method_offsetSet" class="anchor"></a>
#### public offsetSet() : void

```php
public offsetSet(string $offset, mixed $value) : void
```

**Summary**

Устанавливает значение свойства.

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string</code> | offset  | Имя свойства |
| <code lang="php">mixed</code> | value  | Значение свойства |

**Returns:** void - 


<a name="method_offsetUnset" class="anchor"></a>
#### public offsetUnset() : void

```php
public offsetUnset(string $offset) : void
```

**Summary**

Удаляет свойство.

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string</code> | offset  | Имя удаляемого свойства |

**Returns:** void - 


<a name="method_setPaymentPurpose" class="anchor"></a>
#### public setPaymentPurpose() : self

```php
public setPaymentPurpose(string|null $payment_purpose) : self
```

**Summary**

Устанавливает назначение платежа.

**Details:**
* Inherited From: [\YooKassa\Request\Payments\PaymentData\PaymentDataB2bSberbank](../classes/YooKassa-Request-Payments-PaymentData-PaymentDataB2bSberbank.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string OR null</code> | payment_purpose  | Назначение платежа |

**Returns:** self - 


<a name="method_setVatData" class="anchor"></a>
#### public setVatData() : self

```php
public setVatData(\YooKassa\Model\Payment\PaymentMethod\B2b\Sberbank\AbstractVatData|array|null $vat_data) : self
```

**Summary**

Устанавливает назначение платежа.

**Details:**
* Inherited From: [\YooKassa\Request\Payments\PaymentData\PaymentDataB2bSberbank](../classes/YooKassa-Request-Payments-PaymentData-PaymentDataB2bSberbank.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">\YooKassa\Model\Payment\PaymentMethod\B2b\Sberbank\AbstractVatData OR array OR null</code> | vat_data  | Данные об НДС |

**Returns:** self - 


<a name="method_toArray" class="anchor"></a>
#### public toArray() : array

```php
public toArray() : array
```

**Summary**

Возвращает ассоциативный массив со свойствами текущего объекта для его дальнейшей JSON сериализации
Является алиасом метода AbstractObject::jsonSerialize().

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

**Returns:** array - Ассоциативный массив со свойствами текущего объекта


<a name="method_getUnknownProperties" class="anchor"></a>
#### protected getUnknownProperties() : array

```php
protected getUnknownProperties() : array
```

**Summary**

Возвращает массив свойств которые не существуют, но были заданы у объекта.

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

**Returns:** array - Ассоциативный массив с не существующими у текущего объекта свойствами


<a name="method_setType" class="anchor"></a>
#### protected setType() : self

```php
protected setType(string|null $type) : self
```

**Summary**

Устанавливает тип метода оплаты.

**Details:**
* Inherited From: [\YooKassa\Request\Payments\PaymentData\AbstractPaymentData](../classes/YooKassa-Request-Payments-PaymentData-AbstractPaymentData.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string OR null</code> | type  | Тип метода оплаты |

**Returns:** self - 


<a name="method_validatePropertyValue" class="anchor"></a>
#### protected validatePropertyValue() : mixed

```php
protected validatePropertyValue(string $propertyName, mixed $propertyValue) : mixed
```

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string</code> | propertyName  |  |
| <code lang="php">mixed</code> | propertyValue  |  |

**Returns:** mixed - 



---

### Top Namespaces

* [\YooKassa](../namespaces/yookassa.md)

---

### Reports
* [Errors - 0](../reports/errors.md)
* [Markers - 0](../reports/markers.md)
* [Deprecated - 25](../reports/deprecated.md)

---

This document was automatically generated from source code comments on 2024-07-02 using [phpDocumentor](http://www.phpdoc.org/)

&copy; 2024 YooMoney