# [YooKassa API SDK](../home.md)

# Class: \YooKassa\Request\Payments\CreateCaptureResponse
### Namespace: [\YooKassa\Request\Payments](../namespaces/yookassa-request-payments.md)
---
**Summary:**

Класс, представляющий модель CreateCaptureResponse.

**Description:**

Объект ответа от API на запрос подтверждения платежа.

---
### Constants
| Visibility | Name | Flag | Summary |
| ----------:| ---- | ---- | ------- |
| public | [MAX_LENGTH_DESCRIPTION](../classes/YooKassa-Model-Payment-Payment.md#constant_MAX_LENGTH_DESCRIPTION) |  | Максимальная длина строки описания платежа |
| public | [MAX_LENGTH_MERCHANT_CUSTOMER_ID](../classes/YooKassa-Model-Payment-Payment.md#constant_MAX_LENGTH_MERCHANT_CUSTOMER_ID) |  | Максимальная длина строки идентификатора покупателя в вашей системе |

---
### Properties
| Visibility | Name | Flag | Summary |
| ----------:| ---- | ---- | ------- |
| public | [$amount](../classes/YooKassa-Model-Payment-Payment.md#property_amount) |  | Сумма заказа |
| public | [$authorization_details](../classes/YooKassa-Model-Payment-Payment.md#property_authorization_details) |  | Данные об авторизации платежа |
| public | [$authorizationDetails](../classes/YooKassa-Model-Payment-Payment.md#property_authorizationDetails) |  | Данные об авторизации платежа |
| public | [$cancellation_details](../classes/YooKassa-Model-Payment-Payment.md#property_cancellation_details) |  | Комментарий к отмене платежа |
| public | [$cancellationDetails](../classes/YooKassa-Model-Payment-Payment.md#property_cancellationDetails) |  | Комментарий к отмене платежа |
| public | [$captured_at](../classes/YooKassa-Model-Payment-Payment.md#property_captured_at) |  | Время подтверждения платежа магазином |
| public | [$capturedAt](../classes/YooKassa-Model-Payment-Payment.md#property_capturedAt) |  | Время подтверждения платежа магазином |
| public | [$confirmation](../classes/YooKassa-Model-Payment-Payment.md#property_confirmation) |  | Способ подтверждения платежа |
| public | [$created_at](../classes/YooKassa-Model-Payment-Payment.md#property_created_at) |  | Время создания заказа |
| public | [$createdAt](../classes/YooKassa-Model-Payment-Payment.md#property_createdAt) |  | Время создания заказа |
| public | [$deal](../classes/YooKassa-Model-Payment-Payment.md#property_deal) |  | Данные о сделке, в составе которой проходит платеж |
| public | [$description](../classes/YooKassa-Model-Payment-Payment.md#property_description) |  | Описание транзакции |
| public | [$expires_at](../classes/YooKassa-Model-Payment-Payment.md#property_expires_at) |  | Время, до которого можно бесплатно отменить или подтвердить платеж |
| public | [$expiresAt](../classes/YooKassa-Model-Payment-Payment.md#property_expiresAt) |  | Время, до которого можно бесплатно отменить или подтвердить платеж |
| public | [$id](../classes/YooKassa-Model-Payment-Payment.md#property_id) |  | Идентификатор платежа |
| public | [$income_amount](../classes/YooKassa-Model-Payment-Payment.md#property_income_amount) |  | Сумма платежа, которую получит магазин |
| public | [$incomeAmount](../classes/YooKassa-Model-Payment-Payment.md#property_incomeAmount) |  | Сумма платежа, которую получит магазин |
| public | [$merchant_customer_id](../classes/YooKassa-Model-Payment-Payment.md#property_merchant_customer_id) |  | Идентификатор покупателя в вашей системе, например электронная почта или номер телефона |
| public | [$merchantCustomerId](../classes/YooKassa-Model-Payment-Payment.md#property_merchantCustomerId) |  | Идентификатор покупателя в вашей системе, например электронная почта или номер телефона |
| public | [$metadata](../classes/YooKassa-Model-Payment-Payment.md#property_metadata) |  | Метаданные платежа указанные мерчантом |
| public | [$paid](../classes/YooKassa-Model-Payment-Payment.md#property_paid) |  | Признак оплаты заказа |
| public | [$payment_method](../classes/YooKassa-Model-Payment-Payment.md#property_payment_method) |  | Способ проведения платежа |
| public | [$paymentMethod](../classes/YooKassa-Model-Payment-Payment.md#property_paymentMethod) |  | Способ проведения платежа |
| public | [$receipt_registration](../classes/YooKassa-Model-Payment-Payment.md#property_receipt_registration) |  | Состояние регистрации фискального чека |
| public | [$receiptRegistration](../classes/YooKassa-Model-Payment-Payment.md#property_receiptRegistration) |  | Состояние регистрации фискального чека |
| public | [$recipient](../classes/YooKassa-Model-Payment-Payment.md#property_recipient) |  | Получатель платежа |
| public | [$refundable](../classes/YooKassa-Model-Payment-Payment.md#property_refundable) |  | Возможность провести возврат по API |
| public | [$refunded_amount](../classes/YooKassa-Model-Payment-Payment.md#property_refunded_amount) |  | Сумма возвращенных средств платежа |
| public | [$refundedAmount](../classes/YooKassa-Model-Payment-Payment.md#property_refundedAmount) |  | Сумма возвращенных средств платежа |
| public | [$status](../classes/YooKassa-Model-Payment-Payment.md#property_status) |  | Текущее состояние платежа |
| public | [$test](../classes/YooKassa-Model-Payment-Payment.md#property_test) |  | Признак тестовой операции |
| public | [$transfers](../classes/YooKassa-Model-Payment-Payment.md#property_transfers) |  | Данные о распределении платежа между магазинами |
| protected | [$_amount](../classes/YooKassa-Model-Payment-Payment.md#property__amount) |  |  |
| protected | [$_authorization_details](../classes/YooKassa-Model-Payment-Payment.md#property__authorization_details) |  | Данные об авторизации платежа |
| protected | [$_cancellation_details](../classes/YooKassa-Model-Payment-Payment.md#property__cancellation_details) |  | Комментарий к статусу canceled: кто отменил платеж и по какой причине |
| protected | [$_captured_at](../classes/YooKassa-Model-Payment-Payment.md#property__captured_at) |  |  |
| protected | [$_confirmation](../classes/YooKassa-Model-Payment-Payment.md#property__confirmation) |  |  |
| protected | [$_created_at](../classes/YooKassa-Model-Payment-Payment.md#property__created_at) |  |  |
| protected | [$_deal](../classes/YooKassa-Model-Payment-Payment.md#property__deal) |  |  |
| protected | [$_description](../classes/YooKassa-Model-Payment-Payment.md#property__description) |  |  |
| protected | [$_expires_at](../classes/YooKassa-Model-Payment-Payment.md#property__expires_at) |  | Время, до которого можно бесплатно отменить или подтвердить платеж. В указанное время платеж в статусе `waiting_for_capture` будет автоматически отменен. |
| protected | [$_id](../classes/YooKassa-Model-Payment-Payment.md#property__id) |  |  |
| protected | [$_income_amount](../classes/YooKassa-Model-Payment-Payment.md#property__income_amount) |  |  |
| protected | [$_merchant_customer_id](../classes/YooKassa-Model-Payment-Payment.md#property__merchant_customer_id) |  |  |
| protected | [$_metadata](../classes/YooKassa-Model-Payment-Payment.md#property__metadata) |  |  |
| protected | [$_paid](../classes/YooKassa-Model-Payment-Payment.md#property__paid) |  |  |
| protected | [$_payment_method](../classes/YooKassa-Model-Payment-Payment.md#property__payment_method) |  |  |
| protected | [$_receipt_registration](../classes/YooKassa-Model-Payment-Payment.md#property__receipt_registration) |  |  |
| protected | [$_recipient](../classes/YooKassa-Model-Payment-Payment.md#property__recipient) |  |  |
| protected | [$_refundable](../classes/YooKassa-Model-Payment-Payment.md#property__refundable) |  |  |
| protected | [$_refunded_amount](../classes/YooKassa-Model-Payment-Payment.md#property__refunded_amount) |  |  |
| protected | [$_status](../classes/YooKassa-Model-Payment-Payment.md#property__status) |  |  |
| protected | [$_test](../classes/YooKassa-Model-Payment-Payment.md#property__test) |  | Признак тестовой операции. |
| protected | [$_transfers](../classes/YooKassa-Model-Payment-Payment.md#property__transfers) |  |  |

---
### Methods
| Visibility | Name | Flag | Summary |
| ----------:| ---- | ---- | ------- |
| public | [__construct()](../classes/YooKassa-Common-AbstractObject.md#method___construct) |  | AbstractObject constructor. |
| public | [__get()](../classes/YooKassa-Common-AbstractObject.md#method___get) |  | Возвращает значение свойства. |
| public | [__isset()](../classes/YooKassa-Common-AbstractObject.md#method___isset) |  | Проверяет наличие свойства. |
| public | [__set()](../classes/YooKassa-Common-AbstractObject.md#method___set) |  | Устанавливает значение свойства. |
| public | [__unset()](../classes/YooKassa-Common-AbstractObject.md#method___unset) |  | Удаляет свойство. |
| public | [fromArray()](../classes/YooKassa-Common-AbstractObject.md#method_fromArray) |  | Устанавливает значения свойств текущего объекта из массива. |
| public | [getAmount()](../classes/YooKassa-Model-Payment-Payment.md#method_getAmount) |  | Возвращает сумму. |
| public | [getAuthorizationDetails()](../classes/YooKassa-Model-Payment-Payment.md#method_getAuthorizationDetails) |  | Возвращает данные об авторизации платежа. |
| public | [getCancellationDetails()](../classes/YooKassa-Model-Payment-Payment.md#method_getCancellationDetails) |  | Возвращает комментарий к статусу canceled: кто отменил платеж и по какой причине. |
| public | [getCapturedAt()](../classes/YooKassa-Model-Payment-Payment.md#method_getCapturedAt) |  | Возвращает время подтверждения платежа магазином или null, если время не задано. |
| public | [getConfirmation()](../classes/YooKassa-Model-Payment-Payment.md#method_getConfirmation) |  | Возвращает способ подтверждения платежа. |
| public | [getCreatedAt()](../classes/YooKassa-Model-Payment-Payment.md#method_getCreatedAt) |  | Возвращает время создания заказа. |
| public | [getDeal()](../classes/YooKassa-Model-Payment-Payment.md#method_getDeal) |  | Возвращает данные о сделке, в составе которой проходит платеж. |
| public | [getDescription()](../classes/YooKassa-Model-Payment-Payment.md#method_getDescription) |  | Возвращает описание транзакции |
| public | [getExpiresAt()](../classes/YooKassa-Model-Payment-Payment.md#method_getExpiresAt) |  | Возвращает время до которого можно бесплатно отменить или подтвердить платеж, или null, если оно не задано. |
| public | [getId()](../classes/YooKassa-Model-Payment-Payment.md#method_getId) |  | Возвращает идентификатор платежа. |
| public | [getIncomeAmount()](../classes/YooKassa-Model-Payment-Payment.md#method_getIncomeAmount) |  | Возвращает сумму платежа, которую получит магазин, значение `amount` за вычетом комиссии ЮKassa. |
| public | [getMerchantCustomerId()](../classes/YooKassa-Model-Payment-Payment.md#method_getMerchantCustomerId) |  | Возвращает идентификатор покупателя в вашей системе. |
| public | [getMetadata()](../classes/YooKassa-Model-Payment-Payment.md#method_getMetadata) |  | Возвращает метаданные платежа установленные мерчантом |
| public | [getPaid()](../classes/YooKassa-Model-Payment-Payment.md#method_getPaid) |  | Проверяет, был ли уже оплачен заказ. |
| public | [getPaymentMethod()](../classes/YooKassa-Model-Payment-Payment.md#method_getPaymentMethod) |  | Возвращает используемый способ проведения платежа. |
| public | [getReceiptRegistration()](../classes/YooKassa-Model-Payment-Payment.md#method_getReceiptRegistration) |  | Возвращает состояние регистрации фискального чека. |
| public | [getRecipient()](../classes/YooKassa-Model-Payment-Payment.md#method_getRecipient) |  | Возвращает получателя платежа. |
| public | [getRefundable()](../classes/YooKassa-Model-Payment-Payment.md#method_getRefundable) |  | Проверяет возможность провести возврат по API. |
| public | [getRefundedAmount()](../classes/YooKassa-Model-Payment-Payment.md#method_getRefundedAmount) |  | Возвращает сумму возвращенных средств. |
| public | [getStatus()](../classes/YooKassa-Model-Payment-Payment.md#method_getStatus) |  | Возвращает состояние платежа. |
| public | [getTest()](../classes/YooKassa-Model-Payment-Payment.md#method_getTest) |  | Возвращает признак тестовой операции. |
| public | [getTransfers()](../classes/YooKassa-Model-Payment-Payment.md#method_getTransfers) |  | Возвращает массив распределения денег между магазинами. |
| public | [getValidator()](../classes/YooKassa-Common-AbstractObject.md#method_getValidator) |  |  |
| public | [jsonSerialize()](../classes/YooKassa-Common-AbstractObject.md#method_jsonSerialize) |  | Возвращает ассоциативный массив со свойствами текущего объекта для его дальнейшей JSON сериализации. |
| public | [offsetExists()](../classes/YooKassa-Common-AbstractObject.md#method_offsetExists) |  | Проверяет наличие свойства. |
| public | [offsetGet()](../classes/YooKassa-Common-AbstractObject.md#method_offsetGet) |  | Возвращает значение свойства. |
| public | [offsetSet()](../classes/YooKassa-Common-AbstractObject.md#method_offsetSet) |  | Устанавливает значение свойства. |
| public | [offsetUnset()](../classes/YooKassa-Common-AbstractObject.md#method_offsetUnset) |  | Удаляет свойство. |
| public | [setAmount()](../classes/YooKassa-Model-Payment-Payment.md#method_setAmount) |  | Устанавливает сумму платежа. |
| public | [setAuthorizationDetails()](../classes/YooKassa-Model-Payment-Payment.md#method_setAuthorizationDetails) |  | Устанавливает данные об авторизации платежа. |
| public | [setCancellationDetails()](../classes/YooKassa-Model-Payment-Payment.md#method_setCancellationDetails) |  | Устанавливает комментарий к статусу canceled: кто отменил платеж и по какой причине. |
| public | [setCapturedAt()](../classes/YooKassa-Model-Payment-Payment.md#method_setCapturedAt) |  | Устанавливает время подтверждения платежа магазином |
| public | [setConfirmation()](../classes/YooKassa-Model-Payment-Payment.md#method_setConfirmation) |  | Устанавливает способ подтверждения платежа. |
| public | [setCreatedAt()](../classes/YooKassa-Model-Payment-Payment.md#method_setCreatedAt) |  | Устанавливает время создания заказа. |
| public | [setDeal()](../classes/YooKassa-Model-Payment-Payment.md#method_setDeal) |  | Устанавливает данные о сделке, в составе которой проходит платеж. |
| public | [setDescription()](../classes/YooKassa-Model-Payment-Payment.md#method_setDescription) |  | Устанавливает описание транзакции |
| public | [setExpiresAt()](../classes/YooKassa-Model-Payment-Payment.md#method_setExpiresAt) |  | Устанавливает время до которого можно бесплатно отменить или подтвердить платеж. |
| public | [setId()](../classes/YooKassa-Model-Payment-Payment.md#method_setId) |  | Устанавливает идентификатор платежа. |
| public | [setIncomeAmount()](../classes/YooKassa-Model-Payment-Payment.md#method_setIncomeAmount) |  | Устанавливает сумму платежа, которую получит магазин, значение `amount` за вычетом комиссии ЮKassa |
| public | [setMerchantCustomerId()](../classes/YooKassa-Model-Payment-Payment.md#method_setMerchantCustomerId) |  | Устанавливает идентификатор покупателя в вашей системе. |
| public | [setMetadata()](../classes/YooKassa-Model-Payment-Payment.md#method_setMetadata) |  | Устанавливает метаданные платежа. |
| public | [setPaid()](../classes/YooKassa-Model-Payment-Payment.md#method_setPaid) |  | Устанавливает флаг оплаты заказа. |
| public | [setPaymentMethod()](../classes/YooKassa-Model-Payment-Payment.md#method_setPaymentMethod) |  | Устанавливает используемый способ проведения платежа. |
| public | [setReceiptRegistration()](../classes/YooKassa-Model-Payment-Payment.md#method_setReceiptRegistration) |  | Устанавливает состояние регистрации фискального чека |
| public | [setRecipient()](../classes/YooKassa-Model-Payment-Payment.md#method_setRecipient) |  | Устанавливает получателя платежа. |
| public | [setRefundable()](../classes/YooKassa-Model-Payment-Payment.md#method_setRefundable) |  | Устанавливает возможность провести возврат по API. |
| public | [setRefundedAmount()](../classes/YooKassa-Model-Payment-Payment.md#method_setRefundedAmount) |  | Устанавливает сумму возвращенных средств. |
| public | [setStatus()](../classes/YooKassa-Model-Payment-Payment.md#method_setStatus) |  | Устанавливает статус платежа |
| public | [setTest()](../classes/YooKassa-Model-Payment-Payment.md#method_setTest) |  | Устанавливает признак тестовой операции. |
| public | [setTransfers()](../classes/YooKassa-Model-Payment-Payment.md#method_setTransfers) |  | Устанавливает массив распределения денег между магазинами. |
| public | [toArray()](../classes/YooKassa-Common-AbstractObject.md#method_toArray) |  | Возвращает ассоциативный массив со свойствами текущего объекта для его дальнейшей JSON сериализации Является алиасом метода AbstractObject::jsonSerialize(). |
| protected | [getUnknownProperties()](../classes/YooKassa-Common-AbstractObject.md#method_getUnknownProperties) |  | Возвращает массив свойств которые не существуют, но были заданы у объекта. |
| protected | [validatePropertyValue()](../classes/YooKassa-Common-AbstractObject.md#method_validatePropertyValue) |  |  |

---
### Details
* File: [lib/Request/Payments/CreateCaptureResponse.php](../../lib/Request/Payments/CreateCaptureResponse.php)
* Package: YooKassa\Request
* Class Hierarchy:   
  * [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)
  * [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)
  * [\YooKassa\Request\Payments\AbstractPaymentResponse](../classes/YooKassa-Request-Payments-AbstractPaymentResponse.md)
  * \YooKassa\Request\Payments\CreateCaptureResponse

* See Also:
  * [](https://yookassa.ru/developers/api)

---
### Tags
| Tag | Version | Description |
| --- | ------- | ----------- |
| category |  | Class |
| author |  | <EMAIL> |

---
## Constants
<a name="constant_MAX_LENGTH_DESCRIPTION" class="anchor"></a>
###### MAX_LENGTH_DESCRIPTION
Inherited from [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)

Максимальная длина строки описания платежа

```php
MAX_LENGTH_DESCRIPTION = 128
```


<a name="constant_MAX_LENGTH_MERCHANT_CUSTOMER_ID" class="anchor"></a>
###### MAX_LENGTH_MERCHANT_CUSTOMER_ID
Inherited from [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)

Максимальная длина строки идентификатора покупателя в вашей системе

```php
MAX_LENGTH_MERCHANT_CUSTOMER_ID = 200
```



---
## Properties
<a name="property_amount"></a>
#### public $amount : \YooKassa\Model\AmountInterface
---
***Description***

Сумма заказа

**Type:** <a href="../classes/YooKassa-Model-AmountInterface.html"><abbr title="\YooKassa\Model\AmountInterface">AmountInterface</abbr></a>

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)


<a name="property_authorization_details"></a>
#### public $authorization_details : \YooKassa\Model\Payment\AuthorizationDetailsInterface
---
***Description***

Данные об авторизации платежа

**Type:** <a href="../classes/YooKassa-Model-Payment-AuthorizationDetailsInterface.html"><abbr title="\YooKassa\Model\Payment\AuthorizationDetailsInterface">AuthorizationDetailsInterface</abbr></a>

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)


<a name="property_authorizationDetails"></a>
#### public $authorizationDetails : \YooKassa\Model\Payment\AuthorizationDetailsInterface
---
***Description***

Данные об авторизации платежа

**Type:** <a href="../classes/YooKassa-Model-Payment-AuthorizationDetailsInterface.html"><abbr title="\YooKassa\Model\Payment\AuthorizationDetailsInterface">AuthorizationDetailsInterface</abbr></a>

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)


<a name="property_cancellation_details"></a>
#### public $cancellation_details : \YooKassa\Model\CancellationDetailsInterface
---
***Description***

Комментарий к отмене платежа

**Type:** <a href="../classes/YooKassa-Model-CancellationDetailsInterface.html"><abbr title="\YooKassa\Model\CancellationDetailsInterface">CancellationDetailsInterface</abbr></a>

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)


<a name="property_cancellationDetails"></a>
#### public $cancellationDetails : \YooKassa\Model\CancellationDetailsInterface
---
***Description***

Комментарий к отмене платежа

**Type:** <a href="../classes/YooKassa-Model-CancellationDetailsInterface.html"><abbr title="\YooKassa\Model\CancellationDetailsInterface">CancellationDetailsInterface</abbr></a>

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)


<a name="property_captured_at"></a>
#### public $captured_at : \DateTime
---
***Description***

Время подтверждения платежа магазином

**Type:** \DateTime

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)


<a name="property_capturedAt"></a>
#### public $capturedAt : \DateTime
---
***Description***

Время подтверждения платежа магазином

**Type:** \DateTime

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)


<a name="property_confirmation"></a>
#### public $confirmation : \YooKassa\Model\Payment\Confirmation\AbstractConfirmation
---
***Description***

Способ подтверждения платежа

**Type:** <a href="../classes/YooKassa-Model-Payment-Confirmation-AbstractConfirmation.html"><abbr title="\YooKassa\Model\Payment\Confirmation\AbstractConfirmation">AbstractConfirmation</abbr></a>

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)


<a name="property_created_at"></a>
#### public $created_at : \DateTime
---
***Description***

Время создания заказа

**Type:** \DateTime

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)


<a name="property_createdAt"></a>
#### public $createdAt : \DateTime
---
***Description***

Время создания заказа

**Type:** \DateTime

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)


<a name="property_deal"></a>
#### public $deal : \YooKassa\Model\Deal\PaymentDealInfo
---
***Description***

Данные о сделке, в составе которой проходит платеж

**Type:** <a href="../classes/YooKassa-Model-Deal-PaymentDealInfo.html"><abbr title="\YooKassa\Model\Deal\PaymentDealInfo">PaymentDealInfo</abbr></a>

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)


<a name="property_description"></a>
#### public $description : string
---
***Description***

Описание транзакции

**Type:** <a href="../string"><abbr title="string">string</abbr></a>

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)


<a name="property_expires_at"></a>
#### public $expires_at : \DateTime
---
***Description***

Время, до которого можно бесплатно отменить или подтвердить платеж

**Type:** \DateTime

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)


<a name="property_expiresAt"></a>
#### public $expiresAt : \DateTime
---
***Description***

Время, до которого можно бесплатно отменить или подтвердить платеж

**Type:** \DateTime

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)


<a name="property_id"></a>
#### public $id : string
---
***Description***

Идентификатор платежа

**Type:** <a href="../string"><abbr title="string">string</abbr></a>

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)


<a name="property_income_amount"></a>
#### public $income_amount : \YooKassa\Model\AmountInterface
---
***Description***

Сумма платежа, которую получит магазин

**Type:** <a href="../classes/YooKassa-Model-AmountInterface.html"><abbr title="\YooKassa\Model\AmountInterface">AmountInterface</abbr></a>

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)


<a name="property_incomeAmount"></a>
#### public $incomeAmount : \YooKassa\Model\AmountInterface
---
***Description***

Сумма платежа, которую получит магазин

**Type:** <a href="../classes/YooKassa-Model-AmountInterface.html"><abbr title="\YooKassa\Model\AmountInterface">AmountInterface</abbr></a>

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)


<a name="property_merchant_customer_id"></a>
#### public $merchant_customer_id : string
---
***Description***

Идентификатор покупателя в вашей системе, например электронная почта или номер телефона

**Type:** <a href="../string"><abbr title="string">string</abbr></a>

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)


<a name="property_merchantCustomerId"></a>
#### public $merchantCustomerId : string
---
***Description***

Идентификатор покупателя в вашей системе, например электронная почта или номер телефона

**Type:** <a href="../string"><abbr title="string">string</abbr></a>

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)


<a name="property_metadata"></a>
#### public $metadata : \YooKassa\Model\Metadata
---
***Description***

Метаданные платежа указанные мерчантом

**Type:** <a href="../classes/YooKassa-Model-Metadata.html"><abbr title="\YooKassa\Model\Metadata">Metadata</abbr></a>

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)


<a name="property_paid"></a>
#### public $paid : bool
---
***Description***

Признак оплаты заказа

**Type:** <a href="../bool"><abbr title="bool">bool</abbr></a>

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)


<a name="property_payment_method"></a>
#### public $payment_method : \YooKassa\Model\Payment\PaymentMethod\AbstractPaymentMethod
---
***Description***

Способ проведения платежа

**Type:** <a href="../classes/YooKassa-Model-Payment-PaymentMethod-AbstractPaymentMethod.html"><abbr title="\YooKassa\Model\Payment\PaymentMethod\AbstractPaymentMethod">AbstractPaymentMethod</abbr></a>

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)


<a name="property_paymentMethod"></a>
#### public $paymentMethod : \YooKassa\Model\Payment\PaymentMethod\AbstractPaymentMethod
---
***Description***

Способ проведения платежа

**Type:** <a href="../classes/YooKassa-Model-Payment-PaymentMethod-AbstractPaymentMethod.html"><abbr title="\YooKassa\Model\Payment\PaymentMethod\AbstractPaymentMethod">AbstractPaymentMethod</abbr></a>

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)


<a name="property_receipt_registration"></a>
#### public $receipt_registration : string
---
***Description***

Состояние регистрации фискального чека

**Type:** <a href="../string"><abbr title="string">string</abbr></a>

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)


<a name="property_receiptRegistration"></a>
#### public $receiptRegistration : string
---
***Description***

Состояние регистрации фискального чека

**Type:** <a href="../string"><abbr title="string">string</abbr></a>

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)


<a name="property_recipient"></a>
#### public $recipient : \YooKassa\Model\Payment\RecipientInterface
---
***Description***

Получатель платежа

**Type:** <a href="../classes/YooKassa-Model-Payment-RecipientInterface.html"><abbr title="\YooKassa\Model\Payment\RecipientInterface">RecipientInterface</abbr></a>

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)


<a name="property_refundable"></a>
#### public $refundable : bool
---
***Description***

Возможность провести возврат по API

**Type:** <a href="../bool"><abbr title="bool">bool</abbr></a>

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)


<a name="property_refunded_amount"></a>
#### public $refunded_amount : \YooKassa\Model\AmountInterface
---
***Description***

Сумма возвращенных средств платежа

**Type:** <a href="../classes/YooKassa-Model-AmountInterface.html"><abbr title="\YooKassa\Model\AmountInterface">AmountInterface</abbr></a>

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)


<a name="property_refundedAmount"></a>
#### public $refundedAmount : \YooKassa\Model\AmountInterface
---
***Description***

Сумма возвращенных средств платежа

**Type:** <a href="../classes/YooKassa-Model-AmountInterface.html"><abbr title="\YooKassa\Model\AmountInterface">AmountInterface</abbr></a>

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)


<a name="property_status"></a>
#### public $status : string
---
***Description***

Текущее состояние платежа

**Type:** <a href="../string"><abbr title="string">string</abbr></a>

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)


<a name="property_test"></a>
#### public $test : bool
---
***Description***

Признак тестовой операции

**Type:** <a href="../bool"><abbr title="bool">bool</abbr></a>

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)


<a name="property_transfers"></a>
#### public $transfers : \YooKassa\Common\ListObjectInterface|\YooKassa\Model\Payment\TransferInterface[]
---
***Description***

Данные о распределении платежа между магазинами

**Type:** <a href="../\YooKassa\Common\ListObjectInterface|\YooKassa\Model\Payment\TransferInterface[]"><abbr title="\YooKassa\Common\ListObjectInterface|\YooKassa\Model\Payment\TransferInterface[]">TransferInterface[]</abbr></a>

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)


<a name="property__amount"></a>
#### protected $_amount : ?\YooKassa\Model\AmountInterface
---
**Type:** <a href="../?\YooKassa\Model\AmountInterface"><abbr title="?\YooKassa\Model\AmountInterface">AmountInterface</abbr></a>

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)


<a name="property__authorization_details"></a>
#### protected $_authorization_details : ?\YooKassa\Model\Payment\AuthorizationDetailsInterface
---
**Summary**

Данные об авторизации платежа

**Type:** <a href="../?\YooKassa\Model\Payment\AuthorizationDetailsInterface"><abbr title="?\YooKassa\Model\Payment\AuthorizationDetailsInterface">AuthorizationDetailsInterface</abbr></a>

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)


<a name="property__cancellation_details"></a>
#### protected $_cancellation_details : ?\YooKassa\Model\CancellationDetailsInterface
---
**Summary**

Комментарий к статусу canceled: кто отменил платеж и по какой причине

**Type:** <a href="../?\YooKassa\Model\CancellationDetailsInterface"><abbr title="?\YooKassa\Model\CancellationDetailsInterface">CancellationDetailsInterface</abbr></a>

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)


<a name="property__captured_at"></a>
#### protected $_captured_at : ?\DateTime
---
**Type:** <a href="../?\DateTime"><abbr title="?\DateTime">DateTime</abbr></a>
Время подтверждения платежа магазином
**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)


<a name="property__confirmation"></a>
#### protected $_confirmation : ?\YooKassa\Model\Payment\Confirmation\AbstractConfirmation
---
**Type:** <a href="../?\YooKassa\Model\Payment\Confirmation\AbstractConfirmation"><abbr title="?\YooKassa\Model\Payment\Confirmation\AbstractConfirmation">AbstractConfirmation</abbr></a>
Способ подтверждения платежа
**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)


<a name="property__created_at"></a>
#### protected $_created_at : ?\DateTime
---
**Type:** <a href="../?\DateTime"><abbr title="?\DateTime">DateTime</abbr></a>
Время создания заказа
**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)


<a name="property__deal"></a>
#### protected $_deal : ?\YooKassa\Model\Deal\PaymentDealInfo
---
**Type:** <a href="../?\YooKassa\Model\Deal\PaymentDealInfo"><abbr title="?\YooKassa\Model\Deal\PaymentDealInfo">PaymentDealInfo</abbr></a>
Данные о сделке, в составе которой проходит платеж. Необходимо передавать, если вы проводите Безопасную сделку
**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)


<a name="property__description"></a>
#### protected $_description : ?string
---
**Type:** <a href="../?string"><abbr title="?string">?string</abbr></a>
Описание платежа
**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)


<a name="property__expires_at"></a>
#### protected $_expires_at : ?\DateTime
---
**Summary**

Время, до которого можно бесплатно отменить или подтвердить платеж. В указанное время платеж в статусе
`waiting_for_capture` будет автоматически отменен.

**Type:** <a href="../?\DateTime"><abbr title="?\DateTime">DateTime</abbr></a>
Время, до которого можно бесплатно отменить или подтвердить платеж
**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)


<a name="property__id"></a>
#### protected $_id : ?string
---
**Type:** <a href="../?string"><abbr title="?string">?string</abbr></a>
Идентификатор платежа
**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)


<a name="property__income_amount"></a>
#### protected $_income_amount : ?\YooKassa\Model\AmountInterface
---
**Type:** <a href="../?\YooKassa\Model\AmountInterface"><abbr title="?\YooKassa\Model\AmountInterface">AmountInterface</abbr></a>

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)


<a name="property__merchant_customer_id"></a>
#### protected $_merchant_customer_id : ?string
---
**Type:** <a href="../?string"><abbr title="?string">?string</abbr></a>
Идентификатор покупателя в вашей системе, например электронная почта или номер телефона. Не более 200 символов.
Присутствует, если вы хотите запомнить банковскую карту и отобразить ее при повторном платеже в виджете ЮKassa
**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)


<a name="property__metadata"></a>
#### protected $_metadata : ?\YooKassa\Model\Metadata
---
**Type:** <a href="../?\YooKassa\Model\Metadata"><abbr title="?\YooKassa\Model\Metadata">Metadata</abbr></a>
Метаданные платежа указанные мерчантом
**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)


<a name="property__paid"></a>
#### protected $_paid : bool
---
**Type:** <a href="../bool"><abbr title="bool">bool</abbr></a>
Признак оплаты заказа
**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)


<a name="property__payment_method"></a>
#### protected $_payment_method : ?\YooKassa\Model\Payment\PaymentMethod\AbstractPaymentMethod
---
**Type:** <a href="../?\YooKassa\Model\Payment\PaymentMethod\AbstractPaymentMethod"><abbr title="?\YooKassa\Model\Payment\PaymentMethod\AbstractPaymentMethod">AbstractPaymentMethod</abbr></a>
Способ проведения платежа
**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)


<a name="property__receipt_registration"></a>
#### protected $_receipt_registration : ?string
---
**Type:** <a href="../?string"><abbr title="?string">?string</abbr></a>
Состояние регистрации фискального чека
**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)


<a name="property__recipient"></a>
#### protected $_recipient : ?\YooKassa\Model\Payment\RecipientInterface
---
**Type:** <a href="../?\YooKassa\Model\Payment\RecipientInterface"><abbr title="?\YooKassa\Model\Payment\RecipientInterface">RecipientInterface</abbr></a>
Получатель платежа
**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)


<a name="property__refundable"></a>
#### protected $_refundable : bool
---
**Type:** <a href="../bool"><abbr title="bool">bool</abbr></a>
Возможность провести возврат по API
**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)


<a name="property__refunded_amount"></a>
#### protected $_refunded_amount : ?\YooKassa\Model\AmountInterface
---
**Type:** <a href="../?\YooKassa\Model\AmountInterface"><abbr title="?\YooKassa\Model\AmountInterface">AmountInterface</abbr></a>
Сумма возвращенных средств платежа
**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)


<a name="property__status"></a>
#### protected $_status : ?string
---
**Type:** <a href="../?string"><abbr title="?string">?string</abbr></a>
Текущее состояние платежа
**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)


<a name="property__test"></a>
#### protected $_test : bool
---
**Summary**

Признак тестовой операции.

**Type:** <a href="../bool"><abbr title="bool">bool</abbr></a>

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)


<a name="property__transfers"></a>
#### protected $_transfers : ?\YooKassa\Common\ListObject
---
**Type:** <a href="../?\YooKassa\Common\ListObject"><abbr title="?\YooKassa\Common\ListObject">ListObject</abbr></a>
Данные о распределении платежа между магазинами
**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)



---
## Methods
<a name="method___construct" class="anchor"></a>
#### public __construct() : mixed

```php
public __construct(array|null $data = []) : mixed
```

**Summary**

AbstractObject constructor.

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">array OR null</code> | data  |  |

**Returns:** mixed - 


<a name="method___get" class="anchor"></a>
#### public __get() : mixed

```php
public __get(string $propertyName) : mixed
```

**Summary**

Возвращает значение свойства.

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string</code> | propertyName  | Имя свойства |

**Returns:** mixed - Значение свойства


<a name="method___isset" class="anchor"></a>
#### public __isset() : bool

```php
public __isset(string $propertyName) : bool
```

**Summary**

Проверяет наличие свойства.

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string</code> | propertyName  | Имя проверяемого свойства |

**Returns:** bool - True если свойство имеется, false если нет


<a name="method___set" class="anchor"></a>
#### public __set() : void

```php
public __set(string $propertyName, mixed $value) : void
```

**Summary**

Устанавливает значение свойства.

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string</code> | propertyName  | Имя свойства |
| <code lang="php">mixed</code> | value  | Значение свойства |

**Returns:** void - 


<a name="method___unset" class="anchor"></a>
#### public __unset() : void

```php
public __unset(string $propertyName) : void
```

**Summary**

Удаляет свойство.

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string</code> | propertyName  | Имя удаляемого свойства |

**Returns:** void - 


<a name="method_fromArray" class="anchor"></a>
#### public fromArray() : void

```php
public fromArray(array|\Traversable $sourceArray) : void
```

**Summary**

Устанавливает значения свойств текущего объекта из массива.

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">array OR \Traversable</code> | sourceArray  | Ассоциативный массив с настройками |

**Returns:** void - 


<a name="method_getAmount" class="anchor"></a>
#### public getAmount() : \YooKassa\Model\AmountInterface|null

```php
public getAmount() : \YooKassa\Model\AmountInterface|null
```

**Summary**

Возвращает сумму.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)

**Returns:** \YooKassa\Model\AmountInterface|null - Сумма платежа


<a name="method_getAuthorizationDetails" class="anchor"></a>
#### public getAuthorizationDetails() : null|\YooKassa\Model\Payment\AuthorizationDetailsInterface

```php
public getAuthorizationDetails() : null|\YooKassa\Model\Payment\AuthorizationDetailsInterface
```

**Summary**

Возвращает данные об авторизации платежа.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)

**Returns:** null|\YooKassa\Model\Payment\AuthorizationDetailsInterface - Данные об авторизации платежа


<a name="method_getCancellationDetails" class="anchor"></a>
#### public getCancellationDetails() : null|\YooKassa\Model\CancellationDetailsInterface

```php
public getCancellationDetails() : null|\YooKassa\Model\CancellationDetailsInterface
```

**Summary**

Возвращает комментарий к статусу canceled: кто отменил платеж и по какой причине.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)

**Returns:** null|\YooKassa\Model\CancellationDetailsInterface - Комментарий к статусу canceled


<a name="method_getCapturedAt" class="anchor"></a>
#### public getCapturedAt() : null|\DateTime

```php
public getCapturedAt() : null|\DateTime
```

**Summary**

Возвращает время подтверждения платежа магазином или null, если время не задано.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)

**Returns:** null|\DateTime - Время подтверждения платежа магазином


<a name="method_getConfirmation" class="anchor"></a>
#### public getConfirmation() : \YooKassa\Model\Payment\Confirmation\AbstractConfirmation|null

```php
public getConfirmation() : \YooKassa\Model\Payment\Confirmation\AbstractConfirmation|null
```

**Summary**

Возвращает способ подтверждения платежа.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)

**Returns:** \YooKassa\Model\Payment\Confirmation\AbstractConfirmation|null - Способ подтверждения платежа


<a name="method_getCreatedAt" class="anchor"></a>
#### public getCreatedAt() : \DateTime|null

```php
public getCreatedAt() : \DateTime|null
```

**Summary**

Возвращает время создания заказа.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)

**Returns:** \DateTime|null - Время создания заказа


<a name="method_getDeal" class="anchor"></a>
#### public getDeal() : \YooKassa\Model\Deal\PaymentDealInfo|null

```php
public getDeal() : \YooKassa\Model\Deal\PaymentDealInfo|null
```

**Summary**

Возвращает данные о сделке, в составе которой проходит платеж.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)

**Returns:** \YooKassa\Model\Deal\PaymentDealInfo|null - Данные о сделке, в составе которой проходит платеж


<a name="method_getDescription" class="anchor"></a>
#### public getDescription() : string|null

```php
public getDescription() : string|null
```

**Summary**

Возвращает описание транзакции

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)

**Returns:** string|null - 


<a name="method_getExpiresAt" class="anchor"></a>
#### public getExpiresAt() : null|\DateTime

```php
public getExpiresAt() : null|\DateTime
```

**Summary**

Возвращает время до которого можно бесплатно отменить или подтвердить платеж, или null, если оно не задано.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)

**Returns:** null|\DateTime - Время, до которого можно бесплатно отменить или подтвердить платеж


<a name="method_getId" class="anchor"></a>
#### public getId() : string|null

```php
public getId() : string|null
```

**Summary**

Возвращает идентификатор платежа.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)

**Returns:** string|null - Идентификатор платежа


<a name="method_getIncomeAmount" class="anchor"></a>
#### public getIncomeAmount() : \YooKassa\Model\AmountInterface|null

```php
public getIncomeAmount() : \YooKassa\Model\AmountInterface|null
```

**Summary**

Возвращает сумму платежа, которую получит магазин, значение `amount` за вычетом комиссии ЮKassa.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)

**Returns:** \YooKassa\Model\AmountInterface|null - Сумма платежа, которую получит магазин


<a name="method_getMerchantCustomerId" class="anchor"></a>
#### public getMerchantCustomerId() : string|null

```php
public getMerchantCustomerId() : string|null
```

**Summary**

Возвращает идентификатор покупателя в вашей системе.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)

**Returns:** string|null - Идентификатор покупателя в вашей системе


<a name="method_getMetadata" class="anchor"></a>
#### public getMetadata() : \YooKassa\Model\Metadata|null

```php
public getMetadata() : \YooKassa\Model\Metadata|null
```

**Summary**

Возвращает метаданные платежа установленные мерчантом

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)

**Returns:** \YooKassa\Model\Metadata|null - Метаданные платежа указанные мерчантом


<a name="method_getPaid" class="anchor"></a>
#### public getPaid() : bool

```php
public getPaid() : bool
```

**Summary**

Проверяет, был ли уже оплачен заказ.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)

**Returns:** bool - Признак оплаты заказа, true если заказ оплачен, false если нет


<a name="method_getPaymentMethod" class="anchor"></a>
#### public getPaymentMethod() : \YooKassa\Model\Payment\PaymentMethod\AbstractPaymentMethod|null

```php
public getPaymentMethod() : \YooKassa\Model\Payment\PaymentMethod\AbstractPaymentMethod|null
```

**Summary**

Возвращает используемый способ проведения платежа.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)

**Returns:** \YooKassa\Model\Payment\PaymentMethod\AbstractPaymentMethod|null - Способ проведения платежа


<a name="method_getReceiptRegistration" class="anchor"></a>
#### public getReceiptRegistration() : string|null

```php
public getReceiptRegistration() : string|null
```

**Summary**

Возвращает состояние регистрации фискального чека.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)

**Returns:** string|null - Состояние регистрации фискального чека


<a name="method_getRecipient" class="anchor"></a>
#### public getRecipient() : null|\YooKassa\Model\Payment\RecipientInterface

```php
public getRecipient() : null|\YooKassa\Model\Payment\RecipientInterface
```

**Summary**

Возвращает получателя платежа.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)

**Returns:** null|\YooKassa\Model\Payment\RecipientInterface - Получатель платежа или null, если получатель не задан


<a name="method_getRefundable" class="anchor"></a>
#### public getRefundable() : bool

```php
public getRefundable() : bool
```

**Summary**

Проверяет возможность провести возврат по API.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)

**Returns:** bool - Возможность провести возврат по API, true если есть, false если нет


<a name="method_getRefundedAmount" class="anchor"></a>
#### public getRefundedAmount() : \YooKassa\Model\AmountInterface|null

```php
public getRefundedAmount() : \YooKassa\Model\AmountInterface|null
```

**Summary**

Возвращает сумму возвращенных средств.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)

**Returns:** \YooKassa\Model\AmountInterface|null - Сумма возвращенных средств платежа


<a name="method_getStatus" class="anchor"></a>
#### public getStatus() : string|null

```php
public getStatus() : string|null
```

**Summary**

Возвращает состояние платежа.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)

**Returns:** string|null - Текущее состояние платежа


<a name="method_getTest" class="anchor"></a>
#### public getTest() : bool

```php
public getTest() : bool
```

**Summary**

Возвращает признак тестовой операции.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)

**Returns:** bool - Признак тестовой операции


<a name="method_getTransfers" class="anchor"></a>
#### public getTransfers() : \YooKassa\Model\Payment\TransferInterface[]|\YooKassa\Common\ListObjectInterface

```php
public getTransfers() : \YooKassa\Model\Payment\TransferInterface[]|\YooKassa\Common\ListObjectInterface
```

**Summary**

Возвращает массив распределения денег между магазинами.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)

**Returns:** \YooKassa\Model\Payment\TransferInterface[]|\YooKassa\Common\ListObjectInterface - Массив распределения денег


<a name="method_getValidator" class="anchor"></a>
#### public getValidator() : \YooKassa\Validator\Validator

```php
public getValidator() : \YooKassa\Validator\Validator
```

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

**Returns:** \YooKassa\Validator\Validator - 


<a name="method_jsonSerialize" class="anchor"></a>
#### public jsonSerialize() : array

```php
public jsonSerialize() : array
```

**Summary**

Возвращает ассоциативный массив со свойствами текущего объекта для его дальнейшей JSON сериализации.

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

**Returns:** array - Ассоциативный массив со свойствами текущего объекта


<a name="method_offsetExists" class="anchor"></a>
#### public offsetExists() : bool

```php
public offsetExists(string $offset) : bool
```

**Summary**

Проверяет наличие свойства.

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string</code> | offset  | Имя проверяемого свойства |

**Returns:** bool - True если свойство имеется, false если нет


<a name="method_offsetGet" class="anchor"></a>
#### public offsetGet() : mixed

```php
public offsetGet(string $offset) : mixed
```

**Summary**

Возвращает значение свойства.

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string</code> | offset  | Имя свойства |

**Returns:** mixed - Значение свойства


<a name="method_offsetSet" class="anchor"></a>
#### public offsetSet() : void

```php
public offsetSet(string $offset, mixed $value) : void
```

**Summary**

Устанавливает значение свойства.

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string</code> | offset  | Имя свойства |
| <code lang="php">mixed</code> | value  | Значение свойства |

**Returns:** void - 


<a name="method_offsetUnset" class="anchor"></a>
#### public offsetUnset() : void

```php
public offsetUnset(string $offset) : void
```

**Summary**

Удаляет свойство.

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string</code> | offset  | Имя удаляемого свойства |

**Returns:** void - 


<a name="method_setAmount" class="anchor"></a>
#### public setAmount() : self

```php
public setAmount(\YooKassa\Model\AmountInterface|array|null $amount = null) : self
```

**Summary**

Устанавливает сумму платежа.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">\YooKassa\Model\AmountInterface OR array OR null</code> | amount  | Сумма платежа |

**Returns:** self - 


<a name="method_setAuthorizationDetails" class="anchor"></a>
#### public setAuthorizationDetails() : self

```php
public setAuthorizationDetails(\YooKassa\Model\Payment\AuthorizationDetailsInterface|array|null $authorization_details = null) : self
```

**Summary**

Устанавливает данные об авторизации платежа.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">\YooKassa\Model\Payment\AuthorizationDetailsInterface OR array OR null</code> | authorization_details  | Данные об авторизации платежа |

**Returns:** self - 


<a name="method_setCancellationDetails" class="anchor"></a>
#### public setCancellationDetails() : self

```php
public setCancellationDetails(\YooKassa\Model\CancellationDetailsInterface|array|null $cancellation_details = null) : self
```

**Summary**

Устанавливает комментарий к статусу canceled: кто отменил платеж и по какой причине.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">\YooKassa\Model\CancellationDetailsInterface OR array OR null</code> | cancellation_details  | Комментарий к статусу canceled |

**Returns:** self - 


<a name="method_setCapturedAt" class="anchor"></a>
#### public setCapturedAt() : self

```php
public setCapturedAt(\DateTime|string|null $captured_at = null) : self
```

**Summary**

Устанавливает время подтверждения платежа магазином

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">\DateTime OR string OR null</code> | captured_at  | Время подтверждения платежа магазином |

**Returns:** self - 


<a name="method_setConfirmation" class="anchor"></a>
#### public setConfirmation() : self

```php
public setConfirmation(\YooKassa\Model\Payment\Confirmation\AbstractConfirmation|array|null $confirmation = null) : self
```

**Summary**

Устанавливает способ подтверждения платежа.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">\YooKassa\Model\Payment\Confirmation\AbstractConfirmation OR array OR null</code> | confirmation  | Способ подтверждения платежа |

**Returns:** self - 


<a name="method_setCreatedAt" class="anchor"></a>
#### public setCreatedAt() : self

```php
public setCreatedAt(\DateTime|string|null $created_at = null) : self
```

**Summary**

Устанавливает время создания заказа.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">\DateTime OR string OR null</code> | created_at  | Время создания заказа |

**Returns:** self - 


<a name="method_setDeal" class="anchor"></a>
#### public setDeal() : self

```php
public setDeal(null|array|\YooKassa\Model\Deal\PaymentDealInfo $deal = null) : self
```

**Summary**

Устанавливает данные о сделке, в составе которой проходит платеж.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">null OR array OR \YooKassa\Model\Deal\PaymentDealInfo</code> | deal  | Данные о сделке, в составе которой проходит платеж |

**Returns:** self - 


<a name="method_setDescription" class="anchor"></a>
#### public setDescription() : self

```php
public setDescription(string|null $description = null) : self
```

**Summary**

Устанавливает описание транзакции

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string OR null</code> | description  |  |

**Returns:** self - 


<a name="method_setExpiresAt" class="anchor"></a>
#### public setExpiresAt() : self

```php
public setExpiresAt(\DateTime|string|null $expires_at = null) : self
```

**Summary**

Устанавливает время до которого можно бесплатно отменить или подтвердить платеж.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">\DateTime OR string OR null</code> | expires_at  | Время, до которого можно бесплатно отменить или подтвердить платеж |

**Returns:** self - 


<a name="method_setId" class="anchor"></a>
#### public setId() : self

```php
public setId(string|null $id = null) : self
```

**Summary**

Устанавливает идентификатор платежа.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string OR null</code> | id  | Идентификатор платежа |

**Returns:** self - 


<a name="method_setIncomeAmount" class="anchor"></a>
#### public setIncomeAmount() : self

```php
public setIncomeAmount(\YooKassa\Model\AmountInterface|array|null $income_amount = null) : self
```

**Summary**

Устанавливает сумму платежа, которую получит магазин, значение `amount` за вычетом комиссии ЮKassa

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">\YooKassa\Model\AmountInterface OR array OR null</code> | income_amount  |  |

**Returns:** self - 


<a name="method_setMerchantCustomerId" class="anchor"></a>
#### public setMerchantCustomerId() : self

```php
public setMerchantCustomerId(string|null $merchant_customer_id = null) : self
```

**Summary**

Устанавливает идентификатор покупателя в вашей системе.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string OR null</code> | merchant_customer_id  | Идентификатор покупателя в вашей системе, например электронная почта или номер телефона. Не более 200 символов |

**Returns:** self - 


<a name="method_setMetadata" class="anchor"></a>
#### public setMetadata() : self

```php
public setMetadata(\YooKassa\Model\Metadata|array|null $metadata = null) : self
```

**Summary**

Устанавливает метаданные платежа.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">\YooKassa\Model\Metadata OR array OR null</code> | metadata  | Метаданные платежа указанные мерчантом |

**Returns:** self - 


<a name="method_setPaid" class="anchor"></a>
#### public setPaid() : self

```php
public setPaid(bool $paid) : self
```

**Summary**

Устанавливает флаг оплаты заказа.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">bool</code> | paid  | Признак оплаты заказа |

**Returns:** self - 


<a name="method_setPaymentMethod" class="anchor"></a>
#### public setPaymentMethod() : self

```php
public setPaymentMethod(\YooKassa\Model\Payment\PaymentMethod\AbstractPaymentMethod|array|null $payment_method) : self
```

**Summary**

Устанавливает используемый способ проведения платежа.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">\YooKassa\Model\Payment\PaymentMethod\AbstractPaymentMethod OR array OR null</code> | payment_method  | Способ проведения платежа |

**Returns:** self - 


<a name="method_setReceiptRegistration" class="anchor"></a>
#### public setReceiptRegistration() : self

```php
public setReceiptRegistration(string|null $receipt_registration = null) : self
```

**Summary**

Устанавливает состояние регистрации фискального чека

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string OR null</code> | receipt_registration  | Состояние регистрации фискального чека |

**Returns:** self - 


<a name="method_setRecipient" class="anchor"></a>
#### public setRecipient() : self

```php
public setRecipient(\YooKassa\Model\Payment\RecipientInterface|array|null $recipient = null) : self
```

**Summary**

Устанавливает получателя платежа.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">\YooKassa\Model\Payment\RecipientInterface OR array OR null</code> | recipient  | Объект с информацией о получателе платежа |

**Returns:** self - 


<a name="method_setRefundable" class="anchor"></a>
#### public setRefundable() : self

```php
public setRefundable(bool $refundable) : self
```

**Summary**

Устанавливает возможность провести возврат по API.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">bool</code> | refundable  | Возможность провести возврат по API |

**Returns:** self - 


<a name="method_setRefundedAmount" class="anchor"></a>
#### public setRefundedAmount() : self

```php
public setRefundedAmount(\YooKassa\Model\AmountInterface|array|null $refunded_amount = null) : self
```

**Summary**

Устанавливает сумму возвращенных средств.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">\YooKassa\Model\AmountInterface OR array OR null</code> | refunded_amount  | Сумма возвращенных средств платежа |

**Returns:** self - 


<a name="method_setStatus" class="anchor"></a>
#### public setStatus() : self

```php
public setStatus(string|null $status = null) : self
```

**Summary**

Устанавливает статус платежа

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string OR null</code> | status  | Статус платежа |

**Returns:** self - 


<a name="method_setTest" class="anchor"></a>
#### public setTest() : self

```php
public setTest(bool $test = null) : self
```

**Summary**

Устанавливает признак тестовой операции.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">bool</code> | test  | Признак тестовой операции |

**Returns:** self - 


<a name="method_setTransfers" class="anchor"></a>
#### public setTransfers() : self

```php
public setTransfers(\YooKassa\Common\ListObjectInterface|array|null $transfers = null) : self
```

**Summary**

Устанавливает массив распределения денег между магазинами.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\Payment](../classes/YooKassa-Model-Payment-Payment.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">\YooKassa\Common\ListObjectInterface OR array OR null</code> | transfers  | Массив распределения денег |

**Returns:** self - 


<a name="method_toArray" class="anchor"></a>
#### public toArray() : array

```php
public toArray() : array
```

**Summary**

Возвращает ассоциативный массив со свойствами текущего объекта для его дальнейшей JSON сериализации
Является алиасом метода AbstractObject::jsonSerialize().

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

**Returns:** array - Ассоциативный массив со свойствами текущего объекта


<a name="method_getUnknownProperties" class="anchor"></a>
#### protected getUnknownProperties() : array

```php
protected getUnknownProperties() : array
```

**Summary**

Возвращает массив свойств которые не существуют, но были заданы у объекта.

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

**Returns:** array - Ассоциативный массив с не существующими у текущего объекта свойствами


<a name="method_validatePropertyValue" class="anchor"></a>
#### protected validatePropertyValue() : mixed

```php
protected validatePropertyValue(string $propertyName, mixed $propertyValue) : mixed
```

**Details:**
* Inherited From: [\YooKassa\Common\AbstractObject](../classes/YooKassa-Common-AbstractObject.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string</code> | propertyName  |  |
| <code lang="php">mixed</code> | propertyValue  |  |

**Returns:** mixed - 



---

### Top Namespaces

* [\YooKassa](../namespaces/yookassa.md)

---

### Reports
* [Errors - 0](../reports/errors.md)
* [Markers - 0](../reports/markers.md)
* [Deprecated - 25](../reports/deprecated.md)

---

This document was automatically generated from source code comments on 2024-07-02 using [phpDocumentor](http://www.phpdoc.org/)

&copy; 2024 YooMoney