{"__meta": {"id": "X2e192548d94631d9806bc2649693b89e", "datetime": "2025-06-08 15:44:12", "utime": **********.147912, "method": "GET", "uri": "/customer/check/warehouse?customer_id=6&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749397451.615737, "end": **********.147935, "duration": 0.5321979522705078, "duration_str": "532ms", "measures": [{"label": "Booting", "start": 1749397451.615737, "relative_start": 0, "end": **********.079905, "relative_end": **********.079905, "duration": 0.46416807174682617, "duration_str": "464ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.079916, "relative_start": 0.46417903900146484, "end": **********.147937, "relative_end": 2.1457672119140625e-06, "duration": 0.06802105903625488, "duration_str": "68.02ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45280704, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00669, "accumulated_duration_str": "6.69ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.116065, "duration": 0.00519, "duration_str": "5.19ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 77.578}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.132647, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 77.578, "width_percent": 12.407}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.13866, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "ty", "start_percent": 89.985, "width_percent": 10.015}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-245211774 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-245211774\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-694953695 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-694953695\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-608404604 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-608404604\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-664865158 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749396510751%7C76%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik5UQUxXZWNteFpSaVBuMVJra1NURUE9PSIsInZhbHVlIjoidXJWcXUvQzlZeFB3cGdVVG92Y2xoQ0R0UDRGRlVGSW1qcEJGWktubGFJR0pOd2VCcDI1NEZQN29lc29IYStzR1BabHdsNWllelZjSkRnb24vcG9JOWZsS1U5ZlZvZEtuS0pYTngzZzYzRDUxZ1piWGNHb2o0N2ljZ1FGcU5qZ1VzdW1sR3ZMNE8vWXR6ZjVaRFBaNTdObHJBeHRTUkN1U2dpZ0lLOFpPL080S3NqUytxeEJMVTJGdDUvQ2p4VURpcW9jRnlIYVJweFRxL3pmMFNRVEtkRFAyWnNOcitNMVl5dk5CY0kyZUpCY24yQ2NSVFBnZS91bmdlY2FISTU2aGU1UDY2MWVmbHFUSkhHRUJtektmcGxBQ25iYys5NkRXeHFITlFJUmx6aU93RHB1bmFRTVp1aTFLYVVkWC9uV0Vha2VJUk9jdldlbmVPQXZ0M3Jaejg0eXhYTm9tOWFlMG9COXpvSXFlQkFNUGI1OWw4SmpycnkzUEtza2ZnYnZBUVdDTTVqb1RSOGdjQUtuS1hrak9EUWQzZTN5VWthenp4bktYaEFzV09VVTdTVnlzaG56MEVuTk45WmhRTjRpejZlTmFyUm1leTl2Wm05T2hUc3JSWVYvQlFVQUZlcjk0YXBGK1YwamFnUGRJendvczJsWWM0RzlFQ0FweDdnNEUiLCJtYWMiOiI1NzI3ZjBjNTIwOTlkMDdlZGYwMjVjNDc5NDA5YzUzM2YxOTczNTdjZjgzMjljNDc0Yzk5YmIyZDBlNTM1N2Q5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlhNS2pvcFdSNkEwWEZwaGwvUHFtK1E9PSIsInZhbHVlIjoiaDRQK2tQUkJLMUpiMlNBem5icHNRejBzaVNYWmtLTFJEM0RKMzg2aW94QS9CVlVQMnY5bTRsQ2diSGFzMncrdlBjNmRjUHg0cTRxU2MyMEJuM3pJdVN3c29lWXFVSlgzWWN6NktHZll1TEZXeW1IVUhTb1d1NGd5YXMwUUd5WHBBckpuVkNtd09mSElnV1pKdlBra2x1cUx0L1NEMWtFSkx4U284ajlCUlB1Q2tnR3llMnhlbmJsYnpZa0Z4bDkvVE5GSlZlWE8wQjBPMUcxNDc5Z1VBUHNuMG1JUnA3QmJxRmR1R3lUa3hzN29CRWUvUXlyb2oxaFBrUFJnWHdOakFyNDNaWm9tNGZUaE5XRGd5MFJKZEZRWXhQZVBKNldBamlVR2h2S3A4cnh5c1hNckdxNlB4WVB1RmJML0JQZWNWRFVaWFV3R3BINVFaMGx6RklGR3AyQ1A4Z04yVk41ci9IdkdYVlpxMGN4eENNYUxaS2JIMzVyYmtJQyszd1Z4Mi92MlNuL2J3d0ZyK3libUs5TGtxQWp5cHdKSnZRd3pDMUpuMVFNaE1UY25ZUVNYOTcvVXFISlJZTDN0QUdZY0RlODlVTEd0WmNNODBrU3JEWGVLNVhhSy9yNG9IV1FUSHNQanJTR3d6YTR4dUNGOGxUNGpkL0ZBTWo3UXhVai8iLCJtYWMiOiJiODFiYWM3NjhlOGNiNWEwN2NmNWJiODljNWE4ZTg1OGZmM2Y2NDI0ODcwY2JjYWUzY2I1ZTI0ZGIxMDg0YWEyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-664865158\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-805280946 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xNV7SvMX3AgbiuDVO62eNXfryfZD4KbWr3rEIkXt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-805280946\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-853870237 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:44:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImMzQmZ5YmtySENGSnhaTVlic2l5R0E9PSIsInZhbHVlIjoiYTZyVllaS1BEQzQwbmhDR3M3cmUyd3o0a1JXYTNFVGhoU2ppVWRtV01ya0VXSDNCa29BdmxJQ3V1czhBWFBXcUNwR2xNVEZGaXp4SjJyZWhWUmRsZGV1cGExb21mL21TK2FWeHIySG5BRUtJSVMwSG45TmVlMDhoVHpvLzFLRHVtMlRpa2JoWFI2K2hQUytkcjBFWnY1MDQzeDlqZUszTkJNZDdaVEhwWngxeDkwZkhObXlDU1VQZ1h5WXlTSTZpTkk4UDZ0WSt4c245TVF6R0VmaTZjdm81MFFDNEx1OW5Sb1pWVEVGaCtCWFJ2by9uQmRTdlI1L3gzVlAxb0VwSE1TZy9HeTlpNHFqRW1TNGpjWmkzVmFaQXBWbXpTSFNGL2MzNUg2ckJOV0sxOVduVnZ4VVRaTGRkZHJ0eWpsVUtGckhkM0FkY1BIVThrTXhCUmNWeVNXcU1kaUFabGE0TkRWSHNuZnlYRG1NQkJTejlWMXo2VWo3VFlOclNwS29EUlpCdW9Zc0ZvR090MWh0ZnNxQzBvQlpZWGJGV1dQM0pLQ2piWUdMcmtualI0MkcyZTF6UGs3VEJiTUloWVRlWUtwdEp3THViOHNHMWxhZmdITkFGRnVrS1pObEtKZUNQdkQ5T3VVWUZnQnFQeUppdzczMnpqZGxGdC94ZlFRRGYiLCJtYWMiOiJlYWMzMTY4YWU1ZmEzMGZjMmVjNmRjYzdkMzUxOTY1ODBmZTlkNzAyNzgxYTJhNGRjZGZiNDhlY2RkNjU3Zjk0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:44:12 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ik1XYXZGRkV6MURKMUZYUnFWektCK3c9PSIsInZhbHVlIjoiNFF5T2V2a2FCYmQ4a2dZY21zWEFhYzh4a0pDajZ3K2VSRk5mdTVQb3dRVXBJbXAwWVNEajc4VE1EeHBCTS9xMmYzUFVROWljbXRpbXpJcXNra1p4NitCaks5YWIvU2ZrVENBN3B1TDRHLzNYT001MGJ5ay80NlMySEY4b0VWcHcrYy9lTDFaYTFaWVpuTUYwQVVrYTh4UWZDSGg1c1lCRGFqVmowdzFVY1NhOXpVYmVvaHg2ZXd1NkpHNTNScXN6UjJIZXNSdlpXRGF1cktObG8rTXpVNU8zUC9EcGJGRVMzOFBMdlBCdG5mS1d0eU9tYVdjelFhQWRtL0ZaQWphUHBsMjYzbmpubW1HeVpybUd6ZjRzY1Iva0g0aUJFS3B1ZFpjWUdQQVlSMVljMFRhcFQ3T25aV25vMTNtUWlTeXh3WFlxemZIT3llRlFnR1ZNYmdIQWMxZFpOa0FtNEp1dk5McVVHVXV6S0VrS005a3ZOeWtRbXgyOFE4NVYvbVhNWXZlTkJxS1FxS1VZcnAxYmZoR2tTSUVyWnJmdldHUFQrMmU0emM1WjdmSUVxdXVFQjZiMHF0OWI1dmRuVlBTZ3NUSW96L0xTa0w5eHhSbVhNZWRQOVNxVm90L1MvcXRqczZPaHljN0JQSlVReGxLdFg5anRIMDNIU1p5bjdKNnciLCJtYWMiOiIzOGI2MTk2MjkyMjIwOTQ3N2JiYWRiYjRkODk0YmM0YWE3YjYwZmVhYTc3MjA1ZTI2OWU3MDBiYTFhNTA5Yjk3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:44:12 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImMzQmZ5YmtySENGSnhaTVlic2l5R0E9PSIsInZhbHVlIjoiYTZyVllaS1BEQzQwbmhDR3M3cmUyd3o0a1JXYTNFVGhoU2ppVWRtV01ya0VXSDNCa29BdmxJQ3V1czhBWFBXcUNwR2xNVEZGaXp4SjJyZWhWUmRsZGV1cGExb21mL21TK2FWeHIySG5BRUtJSVMwSG45TmVlMDhoVHpvLzFLRHVtMlRpa2JoWFI2K2hQUytkcjBFWnY1MDQzeDlqZUszTkJNZDdaVEhwWngxeDkwZkhObXlDU1VQZ1h5WXlTSTZpTkk4UDZ0WSt4c245TVF6R0VmaTZjdm81MFFDNEx1OW5Sb1pWVEVGaCtCWFJ2by9uQmRTdlI1L3gzVlAxb0VwSE1TZy9HeTlpNHFqRW1TNGpjWmkzVmFaQXBWbXpTSFNGL2MzNUg2ckJOV0sxOVduVnZ4VVRaTGRkZHJ0eWpsVUtGckhkM0FkY1BIVThrTXhCUmNWeVNXcU1kaUFabGE0TkRWSHNuZnlYRG1NQkJTejlWMXo2VWo3VFlOclNwS29EUlpCdW9Zc0ZvR090MWh0ZnNxQzBvQlpZWGJGV1dQM0pLQ2piWUdMcmtualI0MkcyZTF6UGs3VEJiTUloWVRlWUtwdEp3THViOHNHMWxhZmdITkFGRnVrS1pObEtKZUNQdkQ5T3VVWUZnQnFQeUppdzczMnpqZGxGdC94ZlFRRGYiLCJtYWMiOiJlYWMzMTY4YWU1ZmEzMGZjMmVjNmRjYzdkMzUxOTY1ODBmZTlkNzAyNzgxYTJhNGRjZGZiNDhlY2RkNjU3Zjk0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:44:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ik1XYXZGRkV6MURKMUZYUnFWektCK3c9PSIsInZhbHVlIjoiNFF5T2V2a2FCYmQ4a2dZY21zWEFhYzh4a0pDajZ3K2VSRk5mdTVQb3dRVXBJbXAwWVNEajc4VE1EeHBCTS9xMmYzUFVROWljbXRpbXpJcXNra1p4NitCaks5YWIvU2ZrVENBN3B1TDRHLzNYT001MGJ5ay80NlMySEY4b0VWcHcrYy9lTDFaYTFaWVpuTUYwQVVrYTh4UWZDSGg1c1lCRGFqVmowdzFVY1NhOXpVYmVvaHg2ZXd1NkpHNTNScXN6UjJIZXNSdlpXRGF1cktObG8rTXpVNU8zUC9EcGJGRVMzOFBMdlBCdG5mS1d0eU9tYVdjelFhQWRtL0ZaQWphUHBsMjYzbmpubW1HeVpybUd6ZjRzY1Iva0g0aUJFS3B1ZFpjWUdQQVlSMVljMFRhcFQ3T25aV25vMTNtUWlTeXh3WFlxemZIT3llRlFnR1ZNYmdIQWMxZFpOa0FtNEp1dk5McVVHVXV6S0VrS005a3ZOeWtRbXgyOFE4NVYvbVhNWXZlTkJxS1FxS1VZcnAxYmZoR2tTSUVyWnJmdldHUFQrMmU0emM1WjdmSUVxdXVFQjZiMHF0OWI1dmRuVlBTZ3NUSW96L0xTa0w5eHhSbVhNZWRQOVNxVm90L1MvcXRqczZPaHljN0JQSlVReGxLdFg5anRIMDNIU1p5bjdKNnciLCJtYWMiOiIzOGI2MTk2MjkyMjIwOTQ3N2JiYWRiYjRkODk0YmM0YWE3YjYwZmVhYTc3MjA1ZTI2OWU3MDBiYTFhNTA5Yjk3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:44:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-853870237\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-75699956 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-75699956\", {\"maxDepth\":0})</script>\n"}}