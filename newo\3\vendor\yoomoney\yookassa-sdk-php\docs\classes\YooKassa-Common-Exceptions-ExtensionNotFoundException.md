# [YooKassa API SDK](../home.md)

# Class: \YooKassa\Common\Exceptions\ExtensionNotFoundException
### Namespace: [\YooKassa\Common\Exceptions](../namespaces/yookassa-common-exceptions.md)
---
**Summary:**

Требуемое PHP расширение не установлено.


---
### Constants
* No constants found

---
### Methods
| Visibility | Name | Flag | Summary |
| ----------:| ---- | ---- | ------- |
| public | [__construct()](../classes/YooKassa-Common-Exceptions-ExtensionNotFoundException.md#method___construct) |  | Constructor. |

---
### Details
* File: [lib/Common/Exceptions/ExtensionNotFoundException.php](../../lib/Common/Exceptions/ExtensionNotFoundException.php)
* Package: Default
* Class Hierarchy: 
  * [\Exception](\Exception)
  * \YooKassa\Common\Exceptions\ExtensionNotFoundException

---
## Methods
<a name="method___construct" class="anchor"></a>
#### public __construct() : mixed

```php
public __construct(string $name, int $code) : mixed
```

**Summary**

Constructor.

**Details:**
* Inherited From: [\YooKassa\Common\Exceptions\ExtensionNotFoundException](../classes/YooKassa-Common-Exceptions-ExtensionNotFoundException.md)

##### Parameters:
| Type | Name | Description |
| ---- | ---- | ----------- |
| <code lang="php">string</code> | name  | extension name |
| <code lang="php">int</code> | code  | error code |

**Returns:** mixed - 



---

### Top Namespaces

* [\YooKassa](../namespaces/yookassa.md)

---

### Reports
* [Errors - 0](../reports/errors.md)
* [Markers - 0](../reports/markers.md)
* [Deprecated - 25](../reports/deprecated.md)

---

This document was automatically generated from source code comments on 2024-07-02 using [phpDocumentor](http://www.phpdoc.org/)

&copy; 2024 YooMoney