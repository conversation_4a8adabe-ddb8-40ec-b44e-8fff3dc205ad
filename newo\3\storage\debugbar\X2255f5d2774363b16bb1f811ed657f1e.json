{"__meta": {"id": "X2255f5d2774363b16bb1f811ed657f1e", "datetime": "2025-06-08 15:28:29", "utime": 1749396509.084984, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749396507.792985, "end": 1749396509.085017, "duration": 1.29203200340271, "duration_str": "1.29s", "measures": [{"label": "Booting", "start": 1749396507.792985, "relative_start": 0, "end": **********.818873, "relative_end": **********.818873, "duration": 1.0258879661560059, "duration_str": "1.03s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.818893, "relative_start": 1.0259079933166504, "end": 1749396509.085021, "relative_end": 4.0531158447265625e-06, "duration": 0.2661280632019043, "duration_str": "266ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50304960, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.964592, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": **********.992376, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x landingpage::layouts.buttons", "param_count": null, "params": [], "start": 1749396509.05433, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.phplandingpage::layouts.buttons", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2FModules%2FLandingPage%2FResources%2Fviews%2Flayouts%2Fbuttons.blade.php&line=1", "ajax": false, "filename": "buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "landingpage::layouts.buttons"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": 1749396509.066068, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.020810000000000002, "accumulated_duration_str": "20.81ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.902828, "duration": 0.00491, "duration_str": "4.91ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 23.594}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'ty' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.915867, "duration": 0.00857, "duration_str": "8.57ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "ty", "start_percent": 23.594, "width_percent": 41.182}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.932127, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "ty", "start_percent": 64.777, "width_percent": 5.478}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.976396, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 70.255, "width_percent": 5.094}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.php", "line": 10}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1749396509.0009048, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 75.348, "width_percent": 4.565}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4081}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4123}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1749396509.027369, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4081", "source": "app/Models/Utility.php:4081", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4081", "ajax": false, "filename": "Utility.php", "line": "4081"}, "connection": "ty", "start_percent": 79.914, "width_percent": 4.853}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4082}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4123}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1749396509.036479, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4082", "source": "app/Models/Utility.php:4082", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4082", "ajax": false, "filename": "Utility.php", "line": "4082"}, "connection": "ty", "start_percent": 84.767, "width_percent": 5.142}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1749396509.042717, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 89.909, "width_percent": 4.95}, {"sql": "select * from `landing_page_settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "Modules/LandingPage/Entities/LandingPageSetting.php", "file": "C:\\laragon\\www\\to\\newo\\3\\Modules\\LandingPage\\Entities\\LandingPageSetting.php", "line": 27}, {"index": 19, "namespace": "view", "name": "landingpage::layouts.buttons", "file": "C:\\laragon\\www\\to\\newo\\3\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": 1749396509.05992, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "LandingPageSetting.php:27", "source": "Modules/LandingPage/Entities/LandingPageSetting.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2FModules%2FLandingPage%2FEntities%2FLandingPageSetting.php&line=27", "ajax": false, "filename": "LandingPageSetting.php", "line": "27"}, "connection": "ty", "start_percent": 94.858, "width_percent": 5.142}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zwhbybWIFgD0V8vcs5UDckvQqgxmNlpNFuY6aAW0", "url": "array:1 [\n  \"intended\" => \"http://localhost/pos\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-313603303 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-313603303\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1064143272 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1064143272\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-316465834 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-316465834\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1199425411 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749396295914%7C75%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkpYLytYMTM3SDNHOWFpajNmUkJJbUE9PSIsInZhbHVlIjoibVJ6c2tqVmljNzQ2OXRkSU95TTVrSC9pL0YzcURMYnJpcWFrMmpVUnl6TzI4enQ4aEoxYzB4OHczNlo0V1dNSjl1ZGo1VFdIVS9EL3c1cDJDdGwxY1hHVU43eHk0cnJBV3p6Z0JCNERzTkJpQ0lkZnJOVDBvNm9Mb043MkxwWFZaNFRUcFpxVGdpU1VndENjVFlmclZhTjFJaEV6NHNZNGwwVDhCeGNzYU4xTGFuQnBBMit1dXlLYnUxOVQ4N0pPMjdQOHlTVlJIMkUwejVDRzdZTjNUVkFFT3lZejNCTGFVZGdxcTh1TzQzTExFTm5hMDVpRDI0VlhaaWNnUDVOa1VJSFRUNE9kQzVhdjM1cmtnWTIzS3dYcCtOM0NJdTIzbW9OMENhK01sL0VkOC9jR1RuT3lHL2hZS0RMMkpOQTdNbjgxM0hTY00yNjllZFIzUzdWbTN0dGc0ZlZyMVZTM0RyY2lIUUpIQ0w4ODd4NTNQNjVTMWFFQ3daOEZnNkVNYzlSc0p5Wk5BRWMrU0E4MnZkanMyeWFML2VkQ3ByY0hKOWVPckU4dGp3dnJHZ3ZMZGJldWg3RUdROGk1VWhZSkdnNERGelErTTB3bjExRElTaDNxNm15R1BFR2ZKaTFCMU1XZE1yT0VtTjBYOWZGRzdSK1NESmJwLzJvVllZcmEiLCJtYWMiOiJiMTZjOWY5ODE0NWU0NjBiZDRkN2JmODNiY2MzMzA3MTAwNDBmNDFkZGI4M2VjMzYyZDJhOWI5NzBkNjJmMTFlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Img4MVI3Y01ycTFpT3FSUE9xSnF6ZWc9PSIsInZhbHVlIjoiYVo1dHhnYXdXdkMrd3l6MzRuSGlSbEdycS94ZEp5Z1k2cGtKVm5aMUx6RGhGSUFpcHE2UHZEaVgrMkJWeHpsZmQvYXEwckNEQjJVT1E2YmJWNmJsclltd1hkZ1lYN1lKWjNucGJ1ZTY1MUpyWGpXZW1pN3dBdlllT0xNUDhWYk4vTXp6bnNPRkc0THM0aGMySjZqZWhtUEZCRG1xOHBnK2h1OG5abis1U0lYZWFmdS9RYTRIU1ZUS2ZqN2wwb0twdU5nMGFwNW0rQllxaUE5WlNRbm5PcVQxOHJuRXpjS0pmNjNwSHRwMEZKaG9GeTFuNHMwenlqcFZqemhkNXNLc2daUzNzQzRGMWNjMEpvSWxLQnVUTW1CVjQzQVk3TXBHUVVPUjhzWHZrQkdqay84R3pOOUU2WG50bnVrMkxkSlRaWms0SjArZk5LdmVyYXdCNk1Ha3RONDlIQlZteXVXYnltUFFoTWlVam4zYVVlbXZidEplVTNXSkFsOUM5TUE0SWZBM2V2YWlxbkI1UkhvYi9SM0FBRnNlcW1LdWZVU0FaeUc4VWJETW9DMU5KZXpLMHpiTWNMMmlxc3A2Zk5HQ0hmZTJqYlJucVREMVFoUlhrT0FmV21JTWhtZG40YUNxSHZJeXNtZTlZV20zbzhUV1kvVm5kNUgyZkZpVVh4WXYiLCJtYWMiOiIxODAzNWM4ZDZiNzQ1ZGNiYzMxZDRlOTdiOGMzOWI5YzQ1ZWUyNzU5NjM2NmQ2ZjRiZGM0NGNiYzM5ZTk0NDc5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1199425411\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-217433176 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwhbybWIFgD0V8vcs5UDckvQqgxmNlpNFuY6aAW0</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIWjrnRWzYDONkaLm13J6bKPIlYrULPTkrDqhwz4</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-217433176\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1059066096 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:28:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InAyTUxhZjJyUWRHTUxSbEVsdjFYU0E9PSIsInZhbHVlIjoiK1BEdGpPb29obG1pR2xLdXNuMWlFWVhvWEFsSGpYL296c0dCaXVEc3c4b3JtWFVYdzJMU1dJKzBZMkRUM3NSNXBXUkhGZHJzQ3BQQ1gzRHM1MjBTZHVXQzNCZ2txcHlENFBBa3dYMmJSbUFzNHZKOGtxdkF6ZDlYUDc1bFgrTlMwM2NWUGErakRKM3drdWdqc04zWWh1bldJTjlveVg5ajJpVWdLUDlwMUhHcnl4U1ZCaS9SbENaWEs4UVNTd205c05rek05UVE1b0lpS3pMbmpHKzNTVGtpSFFmdURGb1pVTUV2V1J1REVIZ0VGeWp2TWVnbzFobTVWOXlJcmdBRXVIZ0s5VDhMUkNoMEdwVnVLN2hzZUM1WVFubDRKRFNBNTIwWHVNSk4rcFVTWW41VzFtdkRVRVMyS0RIcjBsbjErRDdoR1lNSWxYUFB2VzFqaUJJL2RmajRpbHdaTi8rSnhnL1IveFk0Uk4rKzNJbTdWOUllZE5MdThkTUluWDFRZGtuZFBFUlMrUmlLc2tJSVdRM3l3L2xaalUzTG5raG10WnJyZ0RyRmJ0dlBpTGhUcEFDeHUvNW5NZWk2c0kxcUEwWTc1K2pBTWdrVFMxNGM4Qjg4cUdpWDVvL2M3WHIzbWQzTHdKUXhqeXJvRVM5OFJkcHNyWmJwY0hiYWd3UHgiLCJtYWMiOiIzMWYwMGYwNGYwNDdiNjhjMTVkNzg3ZmYyODdmZDk2YmUzZmQ0MTJlYWU5MGQ1MzQxZDI0MTdhYTcwNWViNzNkIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:28:29 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImZzOVl0S3NVTitGTDVlcUdSU2dLSVE9PSIsInZhbHVlIjoiMTlha1pSaEV0VFVPd09PTzFxSkRGY0w4MFAvWHg0bitkQmdIbnlkc1NjdkJuMHBLUUtMT1pQbWo3S3NHNUpCbnVzVitVeDJqNzA0aVd5aklDWS8xSU9vd3ZGQ3U3MjF6NFdDODVVQmc1NnZWV1VydWZVMzhDazF1eWxITVp4Qi9Mb0cyV0FaK2hIaU5rSUZMNEtLYjJJd0phVDArRkJ0V2R0bWEyMkhxbThZNXExZkhjMGRmN2NsS3htUzh5K0ZuOWJYY1oweHphc0RBVzBmZGJGdWZLUE1CWHNYSVlTanRPYVNoc0hBQlFhRXlZMWFlaTlDbzMxdE5hekhIcDdHYnhQVllTY3IzNmFXbE5idHZ1bm9qdW1naTFTL3lMR1NyMHFnQVpPQ3pKYlBSS1Q4dVJRQU5TTWhOcEVGWnNWOFk5V25YbUNGekYvWWRzR1gyRnZlYmdQUUdmYzJqemVTNlowY3VKblNMT0RmcmxsRHRlamxyR3BTY3hOZDNKaHorZXZvYlVUdndxRjlEQXdGOEM3aDF0cU5VTXdUdmowS2RjK3J1L2svcnUxYmlyY0gzdmw3MFlEZXYySGRncHIwNzhUTEU3cGtSeTJIY3pPZENBVmVNOUxBeHlFcGZPakdTMWF5S1p6RDRNM2Y1RDhqWjdsV2RodzNEOVF2SjZnWEIiLCJtYWMiOiI4MjM3ODlkNTM4YTlmMTQyNjUzNzg5OWE5MDliMDhlMmNhZGU0YzNlZDRhNDIyOWRjMjgxYTYwOWZhM2FkZmMwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:28:29 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InAyTUxhZjJyUWRHTUxSbEVsdjFYU0E9PSIsInZhbHVlIjoiK1BEdGpPb29obG1pR2xLdXNuMWlFWVhvWEFsSGpYL296c0dCaXVEc3c4b3JtWFVYdzJMU1dJKzBZMkRUM3NSNXBXUkhGZHJzQ3BQQ1gzRHM1MjBTZHVXQzNCZ2txcHlENFBBa3dYMmJSbUFzNHZKOGtxdkF6ZDlYUDc1bFgrTlMwM2NWUGErakRKM3drdWdqc04zWWh1bldJTjlveVg5ajJpVWdLUDlwMUhHcnl4U1ZCaS9SbENaWEs4UVNTd205c05rek05UVE1b0lpS3pMbmpHKzNTVGtpSFFmdURGb1pVTUV2V1J1REVIZ0VGeWp2TWVnbzFobTVWOXlJcmdBRXVIZ0s5VDhMUkNoMEdwVnVLN2hzZUM1WVFubDRKRFNBNTIwWHVNSk4rcFVTWW41VzFtdkRVRVMyS0RIcjBsbjErRDdoR1lNSWxYUFB2VzFqaUJJL2RmajRpbHdaTi8rSnhnL1IveFk0Uk4rKzNJbTdWOUllZE5MdThkTUluWDFRZGtuZFBFUlMrUmlLc2tJSVdRM3l3L2xaalUzTG5raG10WnJyZ0RyRmJ0dlBpTGhUcEFDeHUvNW5NZWk2c0kxcUEwWTc1K2pBTWdrVFMxNGM4Qjg4cUdpWDVvL2M3WHIzbWQzTHdKUXhqeXJvRVM5OFJkcHNyWmJwY0hiYWd3UHgiLCJtYWMiOiIzMWYwMGYwNGYwNDdiNjhjMTVkNzg3ZmYyODdmZDk2YmUzZmQ0MTJlYWU5MGQ1MzQxZDI0MTdhYTcwNWViNzNkIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:28:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImZzOVl0S3NVTitGTDVlcUdSU2dLSVE9PSIsInZhbHVlIjoiMTlha1pSaEV0VFVPd09PTzFxSkRGY0w4MFAvWHg0bitkQmdIbnlkc1NjdkJuMHBLUUtMT1pQbWo3S3NHNUpCbnVzVitVeDJqNzA0aVd5aklDWS8xSU9vd3ZGQ3U3MjF6NFdDODVVQmc1NnZWV1VydWZVMzhDazF1eWxITVp4Qi9Mb0cyV0FaK2hIaU5rSUZMNEtLYjJJd0phVDArRkJ0V2R0bWEyMkhxbThZNXExZkhjMGRmN2NsS3htUzh5K0ZuOWJYY1oweHphc0RBVzBmZGJGdWZLUE1CWHNYSVlTanRPYVNoc0hBQlFhRXlZMWFlaTlDbzMxdE5hekhIcDdHYnhQVllTY3IzNmFXbE5idHZ1bm9qdW1naTFTL3lMR1NyMHFnQVpPQ3pKYlBSS1Q4dVJRQU5TTWhOcEVGWnNWOFk5V25YbUNGekYvWWRzR1gyRnZlYmdQUUdmYzJqemVTNlowY3VKblNMT0RmcmxsRHRlamxyR3BTY3hOZDNKaHorZXZvYlVUdndxRjlEQXdGOEM3aDF0cU5VTXdUdmowS2RjK3J1L2svcnUxYmlyY0gzdmw3MFlEZXYySGRncHIwNzhUTEU3cGtSeTJIY3pPZENBVmVNOUxBeHlFcGZPakdTMWF5S1p6RDRNM2Y1RDhqWjdsV2RodzNEOVF2SjZnWEIiLCJtYWMiOiI4MjM3ODlkNTM4YTlmMTQyNjUzNzg5OWE5MDliMDhlMmNhZGU0YzNlZDRhNDIyOWRjMjgxYTYwOWZhM2FkZmMwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:28:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1059066096\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1885897869 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zwhbybWIFgD0V8vcs5UDckvQqgxmNlpNFuY6aAW0</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1885897869\", {\"maxDepth\":0})</script>\n"}}