<?php

namespace WhichBrowser\Data;

DeviceModels::$WM_INDEX = array (
  '@' => 
  array (
    0 => '.*T5555!',
    1 => '.*T8585!',
    2 => '.*S521!',
    3 => '.*T33(20|33|35)!',
    4 => '.*T5353!',
    5 => '.*P3700!',
    6 => '.*T82(82|83)!',
    7 => '.*T73(73|80)!',
    8 => '.*T7272!',
    9 => '.*T2223!',
  ),
  '@-G' => 
  array (
    0 => '-GalaxyII',
    1 => '-GalaxyMini',
  ),
  '@-P' => 
  array (
    0 => '-P525!',
    1 => '-P526!',
    2 => '-P527!',
    3 => '-P552w!',
    4 => '-P552!',
    5 => '-P565!',
    6 => '-P735!',
  ),
  '@12' => 
  array (
    0 => 1210,
  ),
  '@A3' => 
  array (
    0 => 'A3100',
    1 => 'A3300c',
  ),
  '@A4' => 
  array (
    0 => 'A4500!',
  ),
  '@AL' => 
  array (
    0 => 'Alltel HTC PPC6800',
    1 => 'Alltel HTC Touch',
    2 => 'Alltel HTC Touch Diamond',
  ),
  '@AM' => 
  array (
    0 => 'Ameo!',
  ),
  '@BI' => 
  array (
    0 => 'Bird T900',
  ),
  '@C6' => 
  array (
    0 => 'C6625',
  ),
  '@CH' => 
  array (
    0 => 'CHT 9100!',
  ),
  '@CO' => 
  array (
    0 => 'Coolpad F800',
  ),
  '@DA' => 
  array (
    0 => 'Datalogic Memor',
  ),
  '@DO' => 
  array (
    0 => 'Dopod838Pro',
    1 => 'DopodD810',
    2 => 'dopod-S900c',
    3 => 'dopodT5588',
    4 => 'dopodT8388',
    5 => 'dopodT8588',
  ),
  '@DX' => 
  array (
    0 => 'DX900',
  ),
  '@E1' => 
  array (
    0 => 'E100',
  ),
  '@ES' => 
  array (
    0 => 'ES405B!',
  ),
  '@F9' => 
  array (
    0 => 'F900',
  ),
  '@GA' => 
  array (
    0 => 'garmin-asus-Nuvifone-M10',
  ),
  '@GI' => 
  array (
    0 => 'GIGABYTE-MS800',
  ),
  '@GM' => 
  array (
    0 => 'GM730',
    1 => 'GM750Q',
  ),
  '@GT' => 
  array (
    0 => 'GT-B5722',
    1 => 'GT-B6520',
    2 => 'GT-B7300',
    3 => 'GT-B7320!',
    4 => 'GT-B7330!',
    5 => 'GT-B7350',
    6 => 'GT-B7610',
    7 => 'GT-C6625',
    8 => 'GT-I8000!',
  ),
  '@GW' => 
  array (
    0 => 'GW550',
    1 => 'GW825',
  ),
  '@H-' => 
  array (
    0 => 'H-21',
  ),
  '@HD' => 
  array (
    0 => '(HTC )?HD mini!',
    1 => '(HTC )?HD2!',
  ),
  '@HP' => 
  array (
    0 => 'HP iPAQ 510',
    1 => 'HPiPAQ510!',
    2 => 'HPiPAQ610',
    3 => 'HPiPAQ910!',
    4 => 'HP iPAQ h6300',
    5 => 'HP iPAQ hw6500!',
    6 => 'HPiPAQhw6900!',
    7 => 'HPiPAQrw6800!',
    8 => 'HPiPAQrw6815!',
    9 => 'HPiPAQDataMessenger!',
    10 => 'HPiPAQVoiceMessenger!',
    11 => 'HPiPAQGlisten',
    12 => 'HP KB1',
  ),
  '@HT' => 
  array (
    0 => 'HTC8500',
    1 => 'HTC P3300',
    2 => 'HTC love',
    3 => 'HTC P3350',
    4 => 'HTC P3400!',
    5 => 'HTC P3450!',
    6 => 'HTC HERALD',
    7 => 'HTC P6300',
    8 => 'HTC P6500',
    9 => 'HTC X7500',
    10 => 'HTC X7510',
    11 => '(HTC )?HD mini!',
    12 => '(HTC )?HD2!',
    13 => 'HTC T8290',
    14 => 'HTC MAX 4G',
    15 => 'HTC MTeoR',
    16 => 'HTC Prophet',
    17 => 'HTC Snap!',
    18 => 'HTC Maple S520',
    19 => 'HTC S620!',
    20 => 'HTC Excalibur',
    21 => 'HTC S630',
    22 => 'HTC S710',
    23 => 'HTC S730',
    24 => 'HTC S740',
    25 => 'HTC Touch',
    26 => 'HTC Elf',
    27 => 'HTC 6900',
    28 => 'HTC6900',
    29 => 'HTC6900-MR1',
    30 => 'HTC TouchDual',
    31 => 'HTC Touch 3G!',
    32 => 'HTC Jade',
    33 => 'HTC Mega!',
    34 => 'HTC Touch2!',
    35 => 'HTC Touch Cruise!',
    36 => 'HTC P3650',
    37 => 'HTC P3651',
    38 => 'HTC Polaris',
    39 => 'HTC Touch Diamond2!',
    40 => 'HTC Touch Diamond!',
    41 => 'HTC Diamond',
    42 => 'HTC P3701',
    43 => 'HTC P5500',
    44 => 'HTC P5310!',
    45 => 'HTC Touch HD2!',
    46 => 'HTC Touch HD!',
    47 => 'HTC Touch Pro2!',
    48 => 'HTC Touch Pro!',
    49 => 'HTC P4600',
    50 => 'HTC Raphael',
    51 => 'HTC Touch Viva!',
    52 => 'HTC Trinity',
    53 => 'HTC P3600',
    54 => 'HTC P3600i',
    55 => 'HTC v3600',
    56 => 'HTC TyTN II',
    57 => 'HTC Kaiser',
    58 => 'HTC TyTN!',
    59 => 'HTC Mercury',
    60 => 'HTC wizard',
    61 => 'HTCPPC6850!',
    62 => 'HTC VDA V',
    63 => 'HTC v1510',
    64 => 'HTC VPACompactIV',
    65 => 'HT01A',
  ),
  '@I-' => 
  array (
    0 => 'i-mate JAMA',
    1 => 'i-mate JAMAQ',
    2 => 'i-mate JAMin',
    3 => 'i-mate JASJAM',
    4 => 'i-mate JAQ',
    5 => 'i-mate JAQ3',
    6 => 'i-mate K-JAM',
    7 => 'i-mate PDAL',
    8 => 'i-mate 6150',
    9 => 'i-mate 8150!',
    10 => 'i-mate 8502',
    11 => 'i-mate 9502!',
  ),
  '@K-' => 
  array (
    0 => 'K-Touch W366!',
  ),
  '@KD' => 
  array (
    0 => 'KDDI-HT01',
  ),
  '@KY' => 
  array (
    0 => 'Kyocera-E4000',
  ),
  '@LE' => 
  array (
    0 => 'Lenovo ET860',
    1 => 'LENOVO-ET980',
  ),
  '@LG' => 
  array (
    0 => 'LG-GM730!',
    1 => 'LG-GM750!',
    2 => 'LG-GW550!',
    3 => 'LG-KS20',
    4 => 'LG-MS25',
    5 => 'LGE VS750',
  ),
  '@M1' => 
  array (
    0 => 'M1i',
  ),
  '@M5' => 
  array (
    0 => 'M530w!',
  ),
  '@M9' => 
  array (
    0 => 'M930!',
  ),
  '@MC' => 
  array (
    0 => 'MC45!',
    1 => 'MC659B!',
  ),
  '@MD' => 
  array (
    0 => 'MDA III!',
    1 => 'MDA compact II!',
    2 => 'MDA compact!',
    3 => 'MDA Pro!',
    4 => 'MDA Touch!',
    5 => 'MDA Vario!',
  ),
  '@NU' => 
  array (
    0 => 'Nuvifone-M10',
    1 => 'Nuvifone-M20',
  ),
  '@O2' => 
  array (
    0 => 'o2 Xda comet',
    1 => 'O2 Xda 2mini',
    2 => 'O2 Xda 2s',
  ),
  '@P3' => 
  array (
    0 => 'P3470!',
    1 => 'P3450',
    2 => 'P3650',
    3 => 'P3650t',
    4 => 'P3600!',
  ),
  '@P4' => 
  array (
    0 => 'P400',
    1 => 'P4350!',
    2 => 'P4550',
  ),
  '@P5' => 
  array (
    0 => 'P550',
  ),
  '@P7' => 
  array (
    0 => 'P750',
  ),
  '@PA' => 
  array (
    0 => 'Palm500!',
    1 => 'Palm750',
    2 => 'Palm850!',
    3 => 'Palm Treo850e',
    4 => 'PANTECH-C810',
  ),
  '@PP' => 
  array (
    0 => 'PPC6850!',
  ),
  '@Q-' => 
  array (
    0 => 'Q-UMTS',
  ),
  '@Q8' => 
  array (
    0 => 'Q8',
  ),
  '@Q9' => 
  array (
    0 => 'Q9',
    1 => 'Q9c!',
    2 => 'Q9m!',
  ),
  '@S0' => 
  array (
    0 => 'S01SH',
    1 => 'S01SH2',
  ),
  '@S1' => 
  array (
    0 => 'S11HT',
    1 => 'S12HT',
  ),
  '@S2' => 
  array (
    0 => 'S200',
    1 => 'S21HT',
    2 => 'S22HT',
  ),
  '@SC' => 
  array (
    0 => 'SCH-M490',
    1 => 'SCH-M710',
    2 => 'SCH-M715',
    3 => 'SCH-i220!',
    4 => 'SCH-i225!',
    5 => 'SCH-i760!',
    6 => 'SCH-i770!',
    7 => 'SCHI910!',
    8 => 'SCH-i920',
    9 => 'SC-01B',
  ),
  '@SF' => 
  array (
    0 => 'SFR v3650!',
  ),
  '@SG' => 
  array (
    0 => 'SGH-i600!',
    1 => 'SGH-i601!',
    2 => 'SGH-I607',
    3 => 'SGH-i608!',
    4 => 'SGH-I617',
    5 => 'SGH-I637',
    6 => 'SGH-i640!',
    7 => 'SGH-i718',
    8 => 'SGH-i718plus',
    9 => 'SGH-i728',
    10 => 'SGH-i780!',
    11 => 'SGH-i900!',
    12 => 'SGH-i908E',
  ),
  '@SI' => 
  array (
    0 => 'SIE-SX66',
    1 => 'sigmarion3',
  ),
  '@SP' => 
  array (
    0 => 'Sprint:MotoQ9c',
    1 => 'Sprint Treo850e',
    2 => 'SPH-M7200',
    3 => 'SPH-M7350',
    4 => 'SPH-M8400',
    5 => 'SPHI325!',
    6 => 'Spice D1100',
    7 => 'SPV E600',
    8 => 'SPV E650',
    9 => 'SPV M700',
    10 => 'SPV M2000',
    11 => 'SPV M3100',
    12 => 'Sprint S511',
    13 => 'Sprint:SPH-ip830w',
    14 => 'Sprint:PPC6600-1',
    15 => 'Sprint:PPC-6700',
    16 => 'Sprint:PPC6800',
    17 => 'Sprint PPC6850SP',
    18 => 'Sprint MP6850SP',
    19 => 'Sprint MP6900SP',
    20 => 'Sprint MP6950SP',
  ),
  '@T-' => 
  array (
    0 => 'T-Mobile Atlas',
    1 => 'T-Mobile Dash',
    2 => 'T-Mobile LEO',
    3 => 'T-Mobile Cleopatra',
    4 => 'T-01A!',
    5 => 'T-01B!',
  ),
  '@T5' => 
  array (
    0 => 'T5388',
  ),
  '@T9' => 
  array (
    0 => 'T9188',
    1 => 'T9199',
  ),
  '@TG' => 
  array (
    0 => '(Toshiba[- ])?TG01!',
  ),
  '@TO' => 
  array (
    0 => '(Toshiba[- ])?TG01!',
  ),
  '@TR' => 
  array (
    0 => 'Treo800w',
  ),
  '@V1' => 
  array (
    0 => 'v1415',
    1 => 'v1240',
    2 => 'v1210',
    3 => 'v1640',
  ),
  '@VE' => 
  array (
    0 => 'Velocity',
  ),
  '@VI' => 
  array (
    0 => 'VIP12!',
  ),
  '@VP' => 
  array (
    0 => 'VPA Touch',
  ),
  '@W3' => 
  array (
    0 => 'W366',
  ),
  '@WS' => 
  array (
    0 => 'WS003SH',
    1 => 'WS004SH',
    2 => 'WS007SH',
    3 => 'WS011SH',
    4 => 'WS020SH',
    5 => 'WS027SH',
  ),
  '@X0' => 
  array (
    0 => 'X01T',
    1 => 'X02T!',
    2 => 'X01HT',
    3 => 'X02HT',
    4 => 'X03HT',
    5 => 'X04HT',
    6 => 'X05HT',
    7 => 'X01SC',
  ),
  '@X1' => 
  array (
    0 => 'X1',
    1 => 'X1[ia]!',
  ),
  '@X2' => 
  array (
    0 => 'X2',
    1 => 'X2[ia]!',
  ),
  '@XD' => 
  array (
    0 => 'Xda diamond',
    1 => 'Xda neo',
    2 => 'Xda nova',
    3 => 'Xda orbit',
    4 => 'Xda terra',
    5 => 'Xda trion',
  ),
  '@XV' => 
  array (
    0 => 'XV6175!',
    1 => 'XV6850!',
    2 => 'XV6875!',
    3 => 'XV6900',
    4 => 'XV6975',
  ),
  '@ZT' => 
  array (
    0 => 'ZTE E N72',
    1 => 'ZTE E X70',
  ),
);
