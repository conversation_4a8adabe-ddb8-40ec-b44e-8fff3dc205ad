@extends('layouts.admin')
@section('page-title')
    {{__('ملخص فواتير نقاط البيع')}}
@endsection
@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{route('dashboard')}}">{{__('Dashboard')}}</a></li>
    <li class="breadcrumb-item"><a href="{{route('invoice.processing.index')}}">{{__('معالجة فواتير المبيعات')}}</a></li>
    <li class="breadcrumb-item">{{__('ملخص فواتير نقاط البيع')}}</li>
@endsection
@push('css-page')
    <link rel="stylesheet" href="{{ asset('css/datatable/buttons.dataTables.min.css') }}">
@endpush

@section('action-btn')
    <div class="float-end">
        <!-- زر طباعة الباركود/الطباعة الحرارية -->
        <button type="button" class="btn btn-success rounded" id="thermal-print-btn"
                title="{{ __('طباعة الفواتير الحرارية') }}">
            <i class="ti ti-device-mobile me-1"></i> {{ __('🖨️ طباعة حرارية') }}
        </button>
    </div>
@endsection

@section('content')
    <div id="printableArea">
        <div class="row mt-3">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5>{{ __('ملخص فواتير نقاط البيع') }}</h5>
                    </div>
                    <div class="card-body table-border-style">
                        <div class="table-responsive">
                            <table class="table datatable">
                                <thead>
                                <tr>
                                    <th>{{__('رقم الفاتورة')}}</th>
                                    <th>{{ __('تاريخ') }}</th>
                                    <th>{{ __('عميل') }}</th>
                                    <th>{{ __('مستودع') }}</th>
                                    <th>{{ __('المجموع الفرعي') }}</th>
                                    <th>{{ __('الخصم') }}</th>
                                    <th>{{ __('المجموع') }}</th>
                                    <th>{{ __('طريقة الدفع') }}</th>
                                    <th>{{ __('منشئ الفاتورة') }}</th>
                                    <th>{{ __('الإجراءات') }}</th>
                                </tr>
                                </thead>

                                <tbody>
                                @forelse ($posPayments as $pos)
                                    <tr>
                                        <td class="Id">
                                            <a href="{{ route('invoice.processing.show', $pos->id) }}" class="btn btn-outline-primary">
                                                {{ AUth::user()->posNumberFormat($pos->id) }}
                                            </a>
                                        </td>

                                        <td>{{ Auth::user()->dateFormat($pos->pos_date) }}</td>
                                        <td>{{ !empty($pos->customer) ? $pos->customer->name : __('Walk-in Customer') }}</td>
                                        <td>{{ !empty($pos->warehouse) ? $pos->warehouse->name : '' }}</td>
                                        <td>{{ Auth::user()->priceFormat($pos->getSubTotal()) }}</td>
                                        <td>{{ Auth::user()->priceFormat($pos->getTotalDiscount()) }}</td>
                                        <td>{{ Auth::user()->priceFormat($pos->getTotal()) }}</td>
                                        <td>
                                            @if(!empty($pos->customer) && $pos->customer->is_delivery)
                                                @if($pos->is_payment_set)
                                                    <span class="badge bg-success text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('تم التحصيل من مندوب التوصيل') }} ✅</span>
                                                @else
                                                    <span class="badge bg-warning text-dark" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('جاري التحصيل') }} 🚚</span>
                                                @endif
                                            @elseif(isset($pos->posPayment) && $pos->posPayment->payment_type)
                                                @if($pos->posPayment->payment_type == 'cash')
                                                    <span class="badge bg-success text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('نقد') }} 💵</span>
                                                @elseif($pos->posPayment->payment_type == 'network')
                                                    <span class="badge bg-info text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('شبكة') }} 💳</span>
                                                @elseif($pos->posPayment->payment_type == 'split')
                                                    <span class="badge bg-warning text-dark" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('دفع مقسم') }} 💳 💵</span>
                                                @else
                                                    <span class="badge bg-danger text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('غير مدفوع') }}</span>
                                                @endif
                                            @else
                                                <span class="badge bg-danger text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('غير مدفوع') }}</span>
                                            @endif
                                        </td>
                                        <td>
                                            {{ !empty($pos->createdBy) ? $pos->createdBy->name : __('غير معروف') }}
                                        </td>
                                        <td class="Action">
                                            <a href="{{ route('pos.thermal.print', $pos->id) }}" class="btn btn-sm btn-success" target="_blank" data-bs-toggle="tooltip" title="{{ __('طباعة الفاتورة الحرارية') }}">
                                                <i class="ti ti-printer"></i>
                                            </a>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="10" class="text-center">{{ __('لا توجد فواتير متاحة') }}</td>
                                    </tr>
                                @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Invoice Selection Modal for Thermal Printing -->
    <div class="modal fade" id="invoiceSelectionModal" tabindex="-1" role="dialog" aria-labelledby="invoiceSelectionModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title" id="invoiceSelectionModalLabel">
                        <i class="ti ti-device-mobile me-2"></i>{{ __('اختيار فاتورة للطباعة الحرارية') }}
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Loading Spinner -->
                    <div id="invoices-loading" class="text-center py-4">
                        <div class="spinner-border text-success" role="status">
                            <span class="visually-hidden">{{ __('Loading...') }}</span>
                        </div>
                        <p class="mt-2">{{ __('جاري تحميل قائمة الفواتير...') }}</p>
                    </div>

                    <!-- Invoices List -->
                    <div id="invoices-list" style="display: none;">
                        <!-- Shift Information -->
                        <div class="alert alert-success mb-3" id="shift-info-alert">
                            <div class="d-flex align-items-center">
                                <i class="ti ti-clock me-2"></i>
                                <div>
                                    <strong>{{ __('معلومات الشفت الحالي') }}</strong><br>
                                    <small id="shift-details">{{ __('جاري تحميل معلومات الشفت...') }}</small>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <i class="ti ti-info-circle me-2"></i>
                            {{ __('اختر الفاتورة التي تريد طباعتها حرارياً') }}
                        </div>

                        <!-- Search Box -->
                        <div class="mb-3">
                            <div class="input-group">
                                <span class="input-group-text"><i class="ti ti-search"></i></span>
                                <input type="text" class="form-control" id="invoice-search" placeholder="{{ __('البحث برقم الفاتورة أو اسم العميل...') }}">
                            </div>
                        </div>

                        <!-- Invoices Table -->
                        <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                            <table class="table table-hover table-striped">
                                <thead class="table-success sticky-top">
                                    <tr>
                                        <th>{{ __('رقم الفاتورة') }}</th>
                                        <th>{{ __('التاريخ') }}</th>
                                        <th>{{ __('العميل') }}</th>
                                        <th>{{ __('المستودع') }}</th>
                                        <th>{{ __('المبلغ') }}</th>
                                        <th>{{ __('طريقة الدفع') }}</th>
                                        <th>{{ __('الإجراء') }}</th>
                                    </tr>
                                </thead>
                                <tbody id="invoices-table-body">
                                    <!-- سيتم تحميل الفواتير هنا عبر JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Error Message -->
                    <div id="invoices-error" style="display: none;" class="alert alert-danger">
                        <i class="ti ti-alert-circle me-2"></i>
                        <span id="error-message"></span>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="ti ti-x me-1"></i>{{ __('إغلاق') }}
                    </button>
                    <button type="button" class="btn btn-success" id="refresh-invoices">
                        <i class="ti ti-refresh me-1"></i>{{ __('تحديث القائمة') }}
                    </button>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script-page')
<script>
$(document).ready(function() {

    // معالج النقر لزر الطباعة الحرارية
    $(document).on('click', '#thermal-print-btn', function(e) {
        e.preventDefault();

        var $btn = $(this);
        var originalText = $btn.html();

        // إظهار حالة التحميل
        $btn.prop('disabled', true).html('<span class="spinner-border spinner-border-sm me-2" role="status"></span>{{ __("جاري التحميل...") }}');

        // فتح النافذة المنبثقة لاختيار الفاتورة
        $('#invoiceSelectionModal').modal('show');

        // تحميل قائمة الفواتير
        loadInvoicesList();

        // إعادة تفعيل الزر بعد فتح المودال
        setTimeout(function() {
            $btn.prop('disabled', false).html(originalText);
        }, 1000);
    });

    // معالج النقر لزر "تحديث القائمة"
    $(document).on('click', '#refresh-invoices', function(e) {
        e.preventDefault();
        loadInvoicesList();
    });

    // دالة تحميل قائمة الفواتير
    function loadInvoicesList() {
        // إظهار مؤشر التحميل
        $('#invoices-loading').show();
        $('#invoices-list').hide();
        $('#invoices-error').hide();

        $.ajax({
            url: '{{ route("pos.invoices-list") }}',
            type: 'GET',
            timeout: 10000, // 10 seconds timeout
            success: function(response) {
                console.log('Invoices response:', response);
                if (response.success) {
                    displayInvoicesList(response.invoices || [], response.shift_info);
                    if (response.message) {
                        show_toastr('info', response.message, 'info');
                    }
                } else {
                    showInvoicesError(response.message || '{{ __("لا توجد فواتير للعرض") }}');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', xhr, status, error);
                var errorMessage = '{{ __("حدث خطأ أثناء تحميل قائمة الفواتير") }}';

                if (xhr.status === 403) {
                    errorMessage = '{{ __("ليس لديك صلاحية للوصول. يرجى التحقق من صلاحياتك.") }}';
                } else if (xhr.status === 404) {
                    errorMessage = '{{ __("لم يتم العثور على نقطة نهاية قائمة الفواتير.") }}';
                } else if (xhr.status === 500) {
                    errorMessage = '{{ __("حدث خطأ في الخادم. يرجى المحاولة مرة أخرى.") }}';
                } else if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                } else if (status === 'timeout') {
                    errorMessage = '{{ __("انتهت مهلة الطلب. يرجى المحاولة مرة أخرى.") }}';
                }

                showInvoicesError(errorMessage);
            }
        });
    }

    // دالة عرض قائمة الفواتير
    function displayInvoicesList(invoices, shiftInfo = null) {
        $('#invoices-loading').hide();
        $('#invoices-error').hide();

        // عرض معلومات الشفت
        if (shiftInfo) {
            var shiftDetails = `
                {{ __('معرف الشفت') }}: <strong>${shiftInfo.shift_id}</strong> |
                {{ __('تم فتحه في') }}: <strong>${shiftInfo.opened_at}</strong> |
                {{ __('إجمالي الفواتير') }}: <strong>${shiftInfo.total_invoices}</strong>
            `;
            if (shiftInfo.warehouse_name) {
                shiftDetails += ` | {{ __('المستودع') }}: <strong>${shiftInfo.warehouse_name}</strong>`;
            }
            $('#shift-details').html(shiftDetails);
            $('#shift-info-alert').removeClass('alert-warning').addClass('alert-success').show();
        } else {
            // عرض رسالة عدم وجود شفت مفتوح
            $('#shift-details').html('{{ __("لا يوجد شفت مفتوح. عرض الفواتير الحديثة.") }}');
            $('#shift-info-alert').removeClass('alert-success').addClass('alert-warning').show();
        }

        var tableBody = $('#invoices-table-body');
        tableBody.empty();

        if (invoices.length === 0) {
            var noInvoicesMessage = shiftInfo ?
                '{{ __("لا توجد فواتير في الشفت الحالي") }}' :
                '{{ __("لا توجد فواتير حديثة") }}';
            var noInvoicesSubtext = shiftInfo ?
                '{{ __("قم بإنشاء بعض الفواتير في هذا الشفت لرؤيتها هنا") }}' :
                '{{ __("قم بإنشاء بعض الفواتير لرؤيتها هنا") }}';

            tableBody.append(`
                <tr>
                    <td colspan="7" class="text-center py-4">
                        <i class="ti ti-inbox me-2"></i>${noInvoicesMessage}
                        <br><small class="text-muted">${noInvoicesSubtext}</small>
                    </td>
                </tr>
            `);
        } else {
            $.each(invoices, function(index, invoice) {
                var paymentBadge = getPaymentTypeBadge(invoice.payment_type);

                var row = `
                    <tr data-invoice-id="${invoice.id}" data-pos-number="${invoice.pos_number}" data-customer="${invoice.customer_name}">
                        <td><strong>${invoice.pos_number}</strong></td>
                        <td>${invoice.date}</td>
                        <td>${invoice.customer_name}</td>
                        <td>${invoice.warehouse_name}</td>
                        <td><strong>${invoice.total}</strong></td>
                        <td>${paymentBadge}</td>
                        <td>
                            <button type="button" class="btn btn-success btn-sm thermal-print-invoice"
                                    data-invoice-id="${invoice.id}"
                                    data-thermal-url="${invoice.thermal_print_url}"
                                    title="{{ __('طباعة حرارية') }}">
                                <i class="ti ti-device-mobile me-1"></i>{{ __('طباعة') }}
                            </button>
                        </td>
                    </tr>
                `;
                tableBody.append(row);
            });
        }

        $('#invoices-list').show();
    }

    // دالة عرض رسالة الخطأ
    function showInvoicesError(message) {
        $('#invoices-loading').hide();
        $('#invoices-list').hide();
        $('#error-message').text(message);
        $('#invoices-error').show();
    }

    // دالة الحصول على شارة نوع الدفع
    function getPaymentTypeBadge(paymentType) {
        switch(paymentType) {
            case 'cash':
                return '<span class="badge bg-success">{{ __("نقد") }}</span>';
            case 'network':
                return '<span class="badge bg-info">{{ __("شبكة") }}</span>';
            case 'mixed':
                return '<span class="badge bg-warning">{{ __("مختلط") }}</span>';
            case 'pending_collection':
                return '<span class="badge bg-warning">{{ __("جاري التحصيل") }}</span>';
            case 'delivery_collected':
                return '<span class="badge bg-success">{{ __("تم التحصيل من مندوب التوصيل") }}</span>';
            default:
                return '<span class="badge bg-secondary">' + paymentType + '</span>';
        }
    }

    // معالج النقر لزر الطباعة الحرارية في قائمة الفواتير
    $(document).on('click', '.thermal-print-invoice', function(e) {
        e.preventDefault();

        var invoiceId = $(this).data('invoice-id');
        var thermalUrl = $(this).data('thermal-url');
        var posNumber = $(this).closest('tr').data('pos-number');

        if (confirm('{{ __("هل تريد طباعة الفاتورة رقم") }} ' + posNumber + ' {{ __("حرارياً؟") }}')) {
            // فتح نافذة الطباعة الحرارية
            var printWindow = window.open(thermalUrl, '_blank', 'width=450,height=850,scrollbars=yes,resizable=yes,menubar=no,toolbar=no,location=no,status=no');

            if (printWindow) {
                // إغلاق النافذة المنبثقة
                $('#invoiceSelectionModal').modal('hide');

                // عرض رسالة نجاح
                show_toastr('success', '{{ __("تم فتح نافذة الطباعة الحرارية للفاتورة رقم") }} ' + posNumber, 'success');

                // التركيز على النافذة الجديدة
                printWindow.focus();
            } else {
                show_toastr('error', '{{ __("فشل في فتح نافذة الطباعة. يرجى السماح بالنوافذ المنبثقة.") }}', 'error');
            }
        }
    });

    // معالج البحث في قائمة الفواتير
    $(document).on('keyup', '#invoice-search', function() {
        var searchTerm = $(this).val().toLowerCase();

        $('#invoices-table-body tr').each(function() {
            var row = $(this);
            var posNumber = row.data('pos-number') ? row.data('pos-number').toString().toLowerCase() : '';
            var customer = row.data('customer') ? row.data('customer').toString().toLowerCase() : '';

            if (posNumber.includes(searchTerm) || customer.includes(searchTerm)) {
                row.show();
            } else {
                row.hide();
            }
        });
    });

});
</script>

<!-- CSS للتأثيرات البصرية -->
<style>
    /* تأثير النبضة للزر */
    .btn-pulse {
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% {
            transform: scale(1);
            box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
        }
        70% {
            transform: scale(1.05);
            box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
        }
        100% {
            transform: scale(1);
            box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
        }
    }

    /* تأثير التحويم على الزر */
    #thermal-print-btn:hover {
        transform: translateY(-2px);
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.4);
    }

    /* تحسين مظهر المودال */
    .modal-header.bg-success {
        background: linear-gradient(45deg, #28a745, #20c997) !important;
    }

    /* تحسين مظهر الجدول */
    .table-success th {
        background: linear-gradient(45deg, #28a745, #20c997) !important;
        color: white !important;
    }

    /* تأثير التحويم على أزرار الطباعة في الجدول */
    .thermal-print-invoice:hover {
        transform: scale(1.1);
        transition: all 0.2s ease;
    }

    /* تحسين مظهر شارات الدفع */
    .badge {
        font-size: 0.8rem;
        padding: 0.4em 0.7em;
    }
</style>
@endpush