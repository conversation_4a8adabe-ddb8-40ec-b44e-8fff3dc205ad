

<?php $__env->startSection('page-title'); ?>
    <?php echo e(__('Financial record')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>"><?php echo e(__('Dashboard')); ?></a></li>
    <li class="breadcrumb-item"><?php echo e(__('Financial record')); ?></li>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('css-page'); ?>
    <link rel="stylesheet" href="<?php echo e(asset('css/datatable/buttons.dataTables.min.css')); ?>">
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
    <div id="printableArea">
        <div class="mt-3 row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-body table-border-style">
                        <div class="table-responsive">
                            <table class="table datatable">
                                <thead>
                                    <tr>
                                        <th><?php echo e(__('Opening Balance')); ?></th>
                                        <th><?php echo e(__('Current Cash')); ?></th>
                                        <th><?php echo e(__('Overnetwork Cash')); ?></th>
                                        <th><?php echo e(__('Delivery Cash')); ?></th>
                                        <th><?php echo e(__('Total Cash')); ?></th>
                                        <th><?php echo e(__('Deficit')); ?></th>
                                        <th><?php echo e(__('Received Advance')); ?></th>
                                        <th><?php echo e(__('Created At')); ?></th>
                                    </tr>
                                </thead>

                                <tbody>
                                    <?php if($deliveryFinancialRecord): ?>
                                        <tr>
                                            <td><?php echo e('N/A'); ?></td>
                                            <td><?php echo e('N/A'); ?></td>
                                            <td><?php echo e($deliveryFinancialRecord->overnetwork_cash ?? '0.00'); ?></td>
                                            <td><?php echo e($deliveryFinancialRecord->delivery_cash ?? '0.00'); ?></td>
                                            <td><?php echo e('N/A'); ?></td>
                                            <td><?php echo e('N/A'); ?></td>
                                            <td><?php echo e('N/A'); ?></td>
                                            <td><?php echo e($deliveryFinancialRecord->created_at); ?></td>
                                        </tr>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="7" class="text-center text-dark">
                                                <p><?php echo e(__('No Data Found')); ?></p>
                                            </td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    
    <div class="row mt-5">
        <h3 class="mb-4"><?php echo e(__('Payment voucher')); ?></h3>
        <div class="col-md-12">
            <div class="card">
                <div class="card-body table-border-style">
                    <div class="table-responsive">
                        <table class="table payment-voucher-table">
                            <thead>
                                <tr>
                                    <th><?php echo e(__('ID')); ?></th>
                                    <th><?php echo e(__('Payment Amount')); ?></th>
                                    <th><?php echo e(__('Issue')); ?></th>
                                    <th><?php echo e(__('Payed to')); ?></th>
                                    <th><?php echo e(__('Voucher Status')); ?></th>
                                    <th><?php echo e(__('Payment Method')); ?></th>
                                    <th><?php echo e(__('Date')); ?></th>
                                </tr>
                            </thead>

                            <tbody>

                                <?php $__empty_1 = true; $__currentLoopData = $paymentVouchers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $payment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr>
                                        <td class="Id">
                                            <a href="<?php echo e(route('payment.voucher.show', $payment->id)); ?>"
                                                class="btn <?php echo e($payment->created_by == Auth::user()->id ? 'btn-outline-primary' : 'btn-outline-warning'); ?>"><?php echo e($payment->custome_id); ?></a>

                                        </td>
                                        <td> <?php echo e($payment->payment_amount); ?> </td>
                                        <td><?php echo e($payment->creator?->name); ?></td>
                                        <td><?php echo e($payment->payTo?->name); ?></td>
                                        <td>
                                            <span
                                                class="purchase_status badge <?php echo e($payment->status == 'pending' ? 'bg-warning' : 'bg-success'); ?>  p-2 px-3 rounded"><?php echo e($payment->status == 'pending' ? __('Waiting') : __('Accepted')); ?></span>
                                        </td>
                                        <td>
                                            <span><?php echo e(ucwords(str_replace('_', ' ', $payment->payment_method))); ?></span>
                                        </td>
                                        <td>
                                            <span><?php echo e($payment->date); ?></span>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="7" class="text-center text-dark">
                                            <p><?php echo e(__('No Data Found')); ?></p>
                                        </td>
                                    </tr>
                                <?php endif; ?>

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    
    <div class="row mt-5">
        <h3 class="mb-4"><?php echo e(__('Receipt voucher')); ?></h3>
        <div class="col-md-12">
            <div class="card">
                <div class="card-body table-border-style">
                    <div class="table-responsive">
                        <table class="table receipt-voucher-table">
                            <thead>
                                <tr>
                                    <th><?php echo e(__('ID')); ?></th>
                                    <th><?php echo e(__('Payment Amount')); ?></th>
                                    <th><?php echo e(__('Issue')); ?></th>
                                    <th><?php echo e(__('Recipient from')); ?></th>
                                    <th><?php echo e(__('Voucher Status')); ?></th>
                                    <th><?php echo e(__('Payment Method')); ?></th>
                                    <th><?php echo e(__('Date')); ?></th>
                                </tr>
                            </thead>

                            <tbody>

                                <?php $__empty_1 = true; $__currentLoopData = $receiptVouchers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $receipt): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr>
                                        <td class="Id">
                                            <a href="<?php echo e(route('receipt.voucher.show', $receipt->id)); ?>"
                                                class="btn <?php echo e($receipt->created_by == Auth::user()->id ? 'btn-outline-primary' : 'btn-outline-warning'); ?>"><?php echo e($receipt->custome_id); ?></a>

                                        </td>
                                        <td> <?php echo e($receipt->payment_amount); ?> </td>
                                        <td><?php echo e($receipt->creator?->name); ?></td>
                                        <td><?php echo e($receipt->receiptFrom?->name); ?></td>
                                        <td>
                                            <span
                                                class="purchase_status badge <?php echo e($receipt->status == 'pending' ? 'bg-warning' : 'bg-success'); ?> p-2 px-3 rounded"><?php echo e($receipt->status == 'pending' ? __('Waiting') : __('Accepted')); ?></span>
                                        </td>
                                        <td>
                                            <span><?php echo e(ucwords(str_replace('_', ' ', $receipt->payment_method))); ?></span>
                                        </td>
                                        <td>
                                            <span><?php echo e($receipt->date); ?></span>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="7" class="text-center text-dark">
                                            <p><?php echo e(__('No Data Found')); ?></p>
                                        </td>
                                    </tr>
                                <?php endif; ?>

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom-js'); ?>
    <script>
        $(document).ready(function() {
            const receiptDataTable = new simpleDatatables.DataTable(".receipt-voucher-table");
            const paymentDataTable = new simpleDatatables.DataTable(".payment-voucher-table");
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\to\newo\3\resources\views/pos/financial_record/index_delivery.blade.php ENDPATH**/ ?>