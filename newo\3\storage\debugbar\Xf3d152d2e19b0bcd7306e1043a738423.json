{"__meta": {"id": "Xf3d152d2e19b0bcd7306e1043a738423", "datetime": "2025-06-08 15:31:12", "utime": **********.778089, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749396671.923655, "end": **********.778116, "duration": 0.8544609546661377, "duration_str": "854ms", "measures": [{"label": "Booting", "start": 1749396671.923655, "relative_start": 0, "end": **********.674152, "relative_end": **********.674152, "duration": 0.7504968643188477, "duration_str": "750ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.674165, "relative_start": 0.7505099773406982, "end": **********.778118, "relative_end": 1.9073486328125e-06, "duration": 0.10395288467407227, "duration_str": "104ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45140048, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.029800000000000004, "accumulated_duration_str": "29.8ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.716507, "duration": 0.02798, "duration_str": "27.98ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 93.893}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.756429, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 93.893, "width_percent": 2.785}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (17) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.766094, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 96.678, "width_percent": 3.322}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/eyJpdiI6ImNiYzkwakxlbFl5M0hmWXd6cnhmZWc9PSIsInZhbHVlIjoiaWxjU3hBK1J4cjlkSXBwVGZ2eXNWZz09IiwibWFjIjoiYmJkM2U2NzNiY2EzMmQyY2VlY2YyNjdhNmQxNjY4ZGM2NTgxOWJlMmVmMWJjZWUzNDYzYzA3MTViZjAzYzRhNCIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1532106479 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1532106479\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1797732121 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1797732121\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2135681405 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2135681405\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-808531086 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6ImNiYzkwakxlbFl5M0hmWXd6cnhmZWc9PSIsInZhbHVlIjoiaWxjU3hBK1J4cjlkSXBwVGZ2eXNWZz09IiwibWFjIjoiYmJkM2U2NzNiY2EzMmQyY2VlY2YyNjdhNmQxNjY4ZGM2NTgxOWJlMmVmMWJjZWUzNDYzYzA3MTViZjAzYzRhNCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwl%7C0%7C1982; _clsk=o6pnpa%7C1749396668812%7C4%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im4yWllydjBDQlJFb0FBK3hYc1gybmc9PSIsInZhbHVlIjoidld4R2FJTnplUjdjeWRhUGMrakNIWU1XZ1JRbW1qeWVwTmJralNiaHpGdE53dFN6ckFnTGYrWDRBQ3RqcVR6T0ZaWHpGOGRDQWIrTGo4MDZKQnVkaGJEanRFU24yczVvdkN6blhIcDFMWGhhRWZxeEhJNHkxZS9aUjZkMDlTaW9YUTdGL3VLcTQyTEN0WTBDSWRSZ0RhbHR5QVNOSlV0S0U4aGhvVDgyUkozRVdEU0tYc1VITUR6YlREYkRwbElzcjJrYXVaK0NVRXRFNld5N1JNd3RtaGFPVGtWc2hKdllyeU80NE5ibUVtU3k4MTJGc1A5U2ZFM0s4ajc0M3R2Z0R2VEJ1eGpiRFcyYVkzcmdRbGdrYTNDaGwybVBSZmJmckxZVkw5ZW1PUkNvSHBSZjFFa29vRnhaZU1OTDdjbzVBZGNNWk9VMkFTTGZkdTRSd0ljUXZiRUNrZWFKVnJhWkxSYTc4NzZuYitCOTVyd1NtK2IxWlhoeU5LRlp6MUkzemFBemhwdjNJU0lXeE9rZ1plbVZSakhxamg1aDd0QmVRSy9pczFxZ0Nrc1NCbXJqcEpmbzN4eVlCYzlsdnNxSUJQaS8rbFYvdm5POFg1VTF0Ymlhb1dTWXgxOXJVRXZQREFGS3Q3U2h5MlZWUTN4dnZiTC8rTk0rWi9pTUYxM0giLCJtYWMiOiI3Y2E4NThjMjk0OThlYTQ0MDQ0MGNjOTIwYjNiZDRlNmU3ZDc4MDdkYzI0YjRjMjMyODU3NDkyNDM2YjAwNGJkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlFOd0l4ZW9nNmdKeTJZUHBuejMzMVE9PSIsInZhbHVlIjoiQzh6TmswN2J4ajlqRXhMZW80cmE0ZkV6cCt5Y1R3Nm40aUlrOXA1czNFOTZXK3FpU0JISHBUZU16VlFNb1JneGxlTy9LTUdyNVJFbGl4N1B4RFdTa2FpSkZUcEVhVmtHTkNkYjdBOFRpZHhqK2xGQ3doOHBUdUVvbEdsTVVxSmN0ZjQxS1ZwWC9LdW1Oam5lUjg5dmFndzc3RkIyL1lYcVJSa20yUVlmaGMwNEVSMkx3SkVFUGxBRFNWVHljQXR0MHNGNWtESmJtMFdHb0Q2TFJjR2phcDRYOGdua3JTYVVXSGZXRVdwU2VEVlphT3h1TW45TnUrRlZjcWFNejJJK0RzK0ZLQnFEL0dxeGhRMjIzTTBQT3lCVXNSUGI5SUhLUHVkd213LzhuUjI3bm0rYUhac1o3V0Z2WHdsTVp5ajFWQWZwa0xWMHJ2cmY5Qkc3MXBpV3VsV0VqMGJDekJSQWZZRVlsN3dOSWdVUCsyeW1JemdReDBzbk5FU2dBWDFVM1VweHZtK2pEcS9KQ1NQZExqWHZFWlZYWWgxeHNLN0ZOVlZHQzVCNmQwclB5UDRwcGlBV1Q2ZkpUV09sZkdzb3R5OXgrbGNtSUtqaTdqNjREZ2tTODJVTzFHL2YrWFJjZFFPNFg0aUkzRjJpRXo4WVhxZDhwV05TWW9YVk5oOXAiLCJtYWMiOiIwZWQ3MWQxMGY4YmE0ZTRmOTlmNTQxYTlmYWQ2ODBmMmU2ODg0Y2NmMDBlOTNlOWIyYWY5MjIyNTBjMDQ5MGRiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-808531086\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1275488040 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">COI6IIrAbucReABHBN7o3ZaIzRbwBCI1gfwrcfCo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1275488040\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-842518492 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:31:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlFkaHJNZkV1MEY1bjczK3Nma1hSaEE9PSIsInZhbHVlIjoiL0RVZ2pwdFArMEdOYjErZlVicC9iNmJZTjNXVCtUWE5qZG1jTFNpZ0NlR2Y0UCt0QXE1RzZmUTZZS0dNcVN6ejVJbkc4aUJQd3pXdTc5UXExTXk0djB2T25oZ0h5MWNHYjZYNXUwdlVnSnhSZ3ZMR0MyM3Q0Z2ZsTkhTeTFiOTQyV1RWS1Z3cS9FbUlFUW5NazBrSnhFY0pmRnkrMjhhdG96SkFJRlF2UHpoaVJWK2ZHcnViVEhRRXRwZzNYaGRJRE1ydWh3UGp2ZUFsTXRvd3c5VVpOVVh0dTU4ZWpXdUJMSjRLMTJ4NUFjZXFFa243bSt4TGNGdjh1SmZvWkZ5RGxObzhYZkl4NjdHMUdjWFJsdVhPYlBkT2dkaGJvR0NNSGxRTWYya2VwUnV6ZHRvR2pwM1BYbkw1SGxDc1l2bVRwOUZpaVd1Y0lZMVZzOW5scEJQSEtCWUFhcDFqNXA1NERacllDVEVvUDdUK3BqMUo4RHVRcGZOQmtMdUtOOUdRTmU2clZ4S2VvUFpwb1ZXbFBUYVduL3VhRjBvci9BN1ZnZXUvYTVvNThXNjEzU0l2QXNiTjVrSVBjZ09tWlAvYTNoKzFWQ2ZOZ21VWFE1VVZtZmo4b2R5NmpObkdncXpsQ052bnR6S1BuT3d2eldFRFh4Y0Uram10Q08yNC9lT0oiLCJtYWMiOiJhODdlZmFkM2QyZDY5NmE2MmRjY2UzNzM4MzNmZjBjNmNmYzNiMDQ2MzNhNzZmZTdmYjUxODllNzM2YmU4MzE1IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:31:12 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IitjcUVyKzZpbGg5Q2dTODRac3NSTUE9PSIsInZhbHVlIjoiazN3T0J4aWFjOXJmdm1NOEMzbHo3cWV4SDFwbWtWS3JvRFJUR1FhdUt3aS9rR2lGQnRJdkRRUnBXa2hlWEphdmV1MzdJZXcva0Y4MTd3Um0zZVBZM1JvR1dNLzRVZmN2MFR6Tlg3a1JQNXZTSWdvcjE2N0NmNE1TV1NvcGl0Wkl6QWJZR0tZSzhSMkNCL3M2LzM1N3kxbjFNOWZENy9TVjZseTBJVE9ybW1rU25GUzdpS1JINEsvTjZpSmxpVWZHb1lOUjdkaUxQbmFta2RPSGZBSUY4SE1uTTQ1WUJWd0UyNFBTa1hGMkJCSkd3aHN4ckkrN0l6dVphUEUxd2xmWTVkakJodTZocVhOZU1hQ1hESUhud0tOamxjaEN1V01wb1AwMkwzQXF4SnE1L2M1cElkc0lNdGlFaW44engzZllITGZzNjBWTlJaOTBFcFZRNWh1eG9oTHJWTjloUnQ1d09VbExwQ2Q0anB3dGxkQUVNempoNDFMdGo1V1ZnUkYzeFpqSldJQXUxQkNpYWtGMlhqMFJ3RVpSb21PVkM4UktnQmR0K0ZnYXRVNjFiazJ1aUhEYUpoK1lEMG9vY1ErUUI4QjdRTUhZcXhrdnlpUXNweUZ5NGtxaGJCaDdDOFI4bFlsWm5lbFlUZmpRMDVhUzVuL1NBMHlsZ1c3Zmk5WHUiLCJtYWMiOiI2Zjc0ZmZmOWMyY2Y3MTBjZTk4MTlhMTQxYjc0ZjI5ZmE5NTE4ZmUyMjc1YTE4Yjc1MjFhYjRlNjVhZWExMGRjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:31:12 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlFkaHJNZkV1MEY1bjczK3Nma1hSaEE9PSIsInZhbHVlIjoiL0RVZ2pwdFArMEdOYjErZlVicC9iNmJZTjNXVCtUWE5qZG1jTFNpZ0NlR2Y0UCt0QXE1RzZmUTZZS0dNcVN6ejVJbkc4aUJQd3pXdTc5UXExTXk0djB2T25oZ0h5MWNHYjZYNXUwdlVnSnhSZ3ZMR0MyM3Q0Z2ZsTkhTeTFiOTQyV1RWS1Z3cS9FbUlFUW5NazBrSnhFY0pmRnkrMjhhdG96SkFJRlF2UHpoaVJWK2ZHcnViVEhRRXRwZzNYaGRJRE1ydWh3UGp2ZUFsTXRvd3c5VVpOVVh0dTU4ZWpXdUJMSjRLMTJ4NUFjZXFFa243bSt4TGNGdjh1SmZvWkZ5RGxObzhYZkl4NjdHMUdjWFJsdVhPYlBkT2dkaGJvR0NNSGxRTWYya2VwUnV6ZHRvR2pwM1BYbkw1SGxDc1l2bVRwOUZpaVd1Y0lZMVZzOW5scEJQSEtCWUFhcDFqNXA1NERacllDVEVvUDdUK3BqMUo4RHVRcGZOQmtMdUtOOUdRTmU2clZ4S2VvUFpwb1ZXbFBUYVduL3VhRjBvci9BN1ZnZXUvYTVvNThXNjEzU0l2QXNiTjVrSVBjZ09tWlAvYTNoKzFWQ2ZOZ21VWFE1VVZtZmo4b2R5NmpObkdncXpsQ052bnR6S1BuT3d2eldFRFh4Y0Uram10Q08yNC9lT0oiLCJtYWMiOiJhODdlZmFkM2QyZDY5NmE2MmRjY2UzNzM4MzNmZjBjNmNmYzNiMDQ2MzNhNzZmZTdmYjUxODllNzM2YmU4MzE1IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:31:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IitjcUVyKzZpbGg5Q2dTODRac3NSTUE9PSIsInZhbHVlIjoiazN3T0J4aWFjOXJmdm1NOEMzbHo3cWV4SDFwbWtWS3JvRFJUR1FhdUt3aS9rR2lGQnRJdkRRUnBXa2hlWEphdmV1MzdJZXcva0Y4MTd3Um0zZVBZM1JvR1dNLzRVZmN2MFR6Tlg3a1JQNXZTSWdvcjE2N0NmNE1TV1NvcGl0Wkl6QWJZR0tZSzhSMkNCL3M2LzM1N3kxbjFNOWZENy9TVjZseTBJVE9ybW1rU25GUzdpS1JINEsvTjZpSmxpVWZHb1lOUjdkaUxQbmFta2RPSGZBSUY4SE1uTTQ1WUJWd0UyNFBTa1hGMkJCSkd3aHN4ckkrN0l6dVphUEUxd2xmWTVkakJodTZocVhOZU1hQ1hESUhud0tOamxjaEN1V01wb1AwMkwzQXF4SnE1L2M1cElkc0lNdGlFaW44engzZllITGZzNjBWTlJaOTBFcFZRNWh1eG9oTHJWTjloUnQ1d09VbExwQ2Q0anB3dGxkQUVNempoNDFMdGo1V1ZnUkYzeFpqSldJQXUxQkNpYWtGMlhqMFJ3RVpSb21PVkM4UktnQmR0K0ZnYXRVNjFiazJ1aUhEYUpoK1lEMG9vY1ErUUI4QjdRTUhZcXhrdnlpUXNweUZ5NGtxaGJCaDdDOFI4bFlsWm5lbFlUZmpRMDVhUzVuL1NBMHlsZ1c3Zmk5WHUiLCJtYWMiOiI2Zjc0ZmZmOWMyY2Y3MTBjZTk4MTlhMTQxYjc0ZjI5ZmE5NTE4ZmUyMjc1YTE4Yjc1MjFhYjRlNjVhZWExMGRjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:31:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-842518492\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-708132556 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6ImNiYzkwakxlbFl5M0hmWXd6cnhmZWc9PSIsInZhbHVlIjoiaWxjU3hBK1J4cjlkSXBwVGZ2eXNWZz09IiwibWFjIjoiYmJkM2U2NzNiY2EzMmQyY2VlY2YyNjdhNmQxNjY4ZGM2NTgxOWJlMmVmMWJjZWUzNDYzYzA3MTViZjAzYzRhNCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-708132556\", {\"maxDepth\":0})</script>\n"}}