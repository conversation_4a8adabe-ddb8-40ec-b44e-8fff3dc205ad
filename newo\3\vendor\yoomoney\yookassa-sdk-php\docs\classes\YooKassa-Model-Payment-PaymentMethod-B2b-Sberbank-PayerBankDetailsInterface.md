# [YooKassa API SDK](../home.md)

# Interface: PayerBankDetailsInterface
### Namespace: [\YooKassa\Model\Payment\PaymentMethod\B2b\Sberbank](../namespaces/yookassa-model-payment-paymentmethod-b2b-sberbank.md)
---
**Summary:**

Interface PayerBankDetailsInterface.

---
### Constants
* No constants found

---
### Methods
| Visibility | Name | Flag | Summary |
| ----------:| ---- | ---- | ------- |
| public | [getAccount()](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetailsInterface.md#method_getAccount) |  | Возвращает номер счета организации. |
| public | [getAddress()](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetailsInterface.md#method_getAddress) |  | Возвращает адрес организации. |
| public | [getBankBik()](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetailsInterface.md#method_getBankBik) |  | Возвращает БИК банка организации. |
| public | [getBankBranch()](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetailsInterface.md#method_getBankBranch) |  | Возвращает отделение банка организации. |
| public | [getBankName()](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetailsInterface.md#method_getBankName) |  | Возвращает наименование банка организации. |
| public | [getFullName()](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetailsInterface.md#method_getFullName) |  | Возвращает полное наименование организации. |
| public | [getInn()](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetailsInterface.md#method_getInn) |  | Возвращает ИНН организации. |
| public | [getKpp()](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetailsInterface.md#method_getKpp) |  | Возвращает КПП организации. |
| public | [getShortName()](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetailsInterface.md#method_getShortName) |  | Возвращает сокращенное наименование организации. |

---
### Details
* File: [lib/Model/Payment/PaymentMethod/B2b/Sberbank/PayerBankDetailsInterface.php](../../lib/Model/Payment/PaymentMethod/B2b/Sberbank/PayerBankDetailsInterface.php)
* Package: \YooKassa\Model
* See Also:
  * [](https://yookassa.ru/developers/api)

---
### Tags
| Tag | Version | Description |
| --- | ------- | ----------- |
| category |  | Class |
| author |  | <EMAIL> |
| property |  | Полное наименование организации |
| property |  | Сокращенное наименование организации |
| property |  | Адрес организации |
| property |  | ИНН организации |
| property |  | КПП организации |
| property |  | Наименование банка организации |
| property |  | Отделение банка организации |
| property |  | БИК банка организации |
| property |  | Номер счета организации |

---
## Methods
<a name="method_getFullName" class="anchor"></a>
#### public getFullName() : string|null

```php
public getFullName() : string|null
```

**Summary**

Возвращает полное наименование организации.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\PaymentMethod\B2b\Sberbank\PayerBankDetailsInterface](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetailsInterface.md)

**Returns:** string|null - Полное наименование организации


<a name="method_getShortName" class="anchor"></a>
#### public getShortName() : string|null

```php
public getShortName() : string|null
```

**Summary**

Возвращает сокращенное наименование организации.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\PaymentMethod\B2b\Sberbank\PayerBankDetailsInterface](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetailsInterface.md)

**Returns:** string|null - Сокращенное наименование организации


<a name="method_getAddress" class="anchor"></a>
#### public getAddress() : string|null

```php
public getAddress() : string|null
```

**Summary**

Возвращает адрес организации.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\PaymentMethod\B2b\Sberbank\PayerBankDetailsInterface](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetailsInterface.md)

**Returns:** string|null - Адрес организации


<a name="method_getInn" class="anchor"></a>
#### public getInn() : string|null

```php
public getInn() : string|null
```

**Summary**

Возвращает ИНН организации.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\PaymentMethod\B2b\Sberbank\PayerBankDetailsInterface](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetailsInterface.md)

**Returns:** string|null - ИНН организации


<a name="method_getKpp" class="anchor"></a>
#### public getKpp() : string|null

```php
public getKpp() : string|null
```

**Summary**

Возвращает КПП организации.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\PaymentMethod\B2b\Sberbank\PayerBankDetailsInterface](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetailsInterface.md)

**Returns:** string|null - КПП организации


<a name="method_getBankName" class="anchor"></a>
#### public getBankName() : string|null

```php
public getBankName() : string|null
```

**Summary**

Возвращает наименование банка организации.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\PaymentMethod\B2b\Sberbank\PayerBankDetailsInterface](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetailsInterface.md)

**Returns:** string|null - Наименование банка организации


<a name="method_getBankBranch" class="anchor"></a>
#### public getBankBranch() : string|null

```php
public getBankBranch() : string|null
```

**Summary**

Возвращает отделение банка организации.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\PaymentMethod\B2b\Sberbank\PayerBankDetailsInterface](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetailsInterface.md)

**Returns:** string|null - Отделение банка организации


<a name="method_getBankBik" class="anchor"></a>
#### public getBankBik() : string|null

```php
public getBankBik() : string|null
```

**Summary**

Возвращает БИК банка организации.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\PaymentMethod\B2b\Sberbank\PayerBankDetailsInterface](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetailsInterface.md)

**Returns:** string|null - БИК банка организации


<a name="method_getAccount" class="anchor"></a>
#### public getAccount() : string|null

```php
public getAccount() : string|null
```

**Summary**

Возвращает номер счета организации.

**Details:**
* Inherited From: [\YooKassa\Model\Payment\PaymentMethod\B2b\Sberbank\PayerBankDetailsInterface](../classes/YooKassa-Model-Payment-PaymentMethod-B2b-Sberbank-PayerBankDetailsInterface.md)

**Returns:** string|null - Номер счета организации




---

### Top Namespaces

* [\YooKassa](../namespaces/yookassa.md)

---

### Reports
* [Errors - 0](../reports/errors.md)
* [Markers - 0](../reports/markers.md)
* [Deprecated - 25](../reports/deprecated.md)

---

This document was automatically generated from source code comments on 2024-07-02 using [phpDocumentor](http://www.phpdoc.org/)

&copy; 2024 YooMoney