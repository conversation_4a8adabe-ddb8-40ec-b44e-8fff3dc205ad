{"__meta": {"id": "X0275370533d8a6f14255eb824c00d2cb", "datetime": "2025-06-08 15:29:35", "utime": **********.425912, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749396574.579936, "end": **********.425934, "duration": 0.8459980487823486, "duration_str": "846ms", "measures": [{"label": "Booting", "start": 1749396574.579936, "relative_start": 0, "end": **********.32146, "relative_end": **********.32146, "duration": 0.7415239810943604, "duration_str": "742ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.321474, "relative_start": 0.7415380477905273, "end": **********.425936, "relative_end": 1.9073486328125e-06, "duration": 0.1044619083404541, "duration_str": "104ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45152368, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.014559999999999998, "accumulated_duration_str": "14.56ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.369706, "duration": 0.01236, "duration_str": "12.36ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 84.89}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.3985019, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 84.89, "width_percent": 6.593}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.410251, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 91.484, "width_percent": 8.516}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1572972136 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1572972136\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-273928580 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-273928580\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1490904927 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1490904927\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-483749539 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=xh9goj%7C1749396565821%7C10%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkFBa2ZMTmg4cUhXUE5ncW5pZzhqT1E9PSIsInZhbHVlIjoiRWwvNWZkOGQyZ05SMjFzNlJENHczL3o0WDVMdUhwT3lsbTExK1pmUjg5UHQ4cHFlWU9LdE1kZmVMeit5eWxyUElxK002bmp0TmwrWU1oY3BvM2FHQ2IxZkZsMW9xSVAxNXFJYzIxeUo0ZWhtelNHRUdBSzgrTnlwcHUzWUFBVjZUV283UXgwVXEyTFEvVDloQS9SMkJXN1M3TzNlc0hhaU1tdWZKdXhRM1pVVmREOVBYWHFWeHAvbjZKQUQvaVBIVTNFb2RXT3NNOHIwdG5lU2orYmJuYkljSUxKWGx1cFc2Znl1cmNVSndUdmovbHdQeTNlcEJITVY5ck1iWDloUkZwSElweHpoM1I5RzQzdDhHNzNsbzdKN2pPclhDTHJTZmF3cGdpQ3pBd2J5UFFhZVBUcGQ4NTdYUGFFN2VQZE1oWDVrY1NLN2Y5Q0Z1WWRRT3dMQnJoRElhL3FwVkNmS3ZXd1MxcFpReVZpdzR2eFZNeEZuaFJKbVgrTmdHbTBQSXh2bk83TDNuSUp1Z1g5Mm5MVG0yVmFNYWlxU3Vmc2Z3dXFhYkVubXpHbjM3NjVTa0FFemxVeTVOa2ZLVXlaeW9DWFlqcHBuUzhiRjZNU3lRdDVkb2VVYUsxZzZnTGR6YWUwbDY2dWV0dE9mWVNqMlNFZ1JPOGZjSHdDektnK3giLCJtYWMiOiIyNmMyOWY5MDQyNDM4MGRhMjZiNWZkMzBlNTJjYjU0OGNhODJiNzE0MTdmNWVhODNiNDIyYjNlMWIyM2U1MTUyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Imlwa2hQYUswaWNhMlUvOS9jRjg5ckE9PSIsInZhbHVlIjoidXJiQ09lalhzOHMzN3BBVzIwdGRNZVF5aCtjTm1TdE4rYlcwdHZMU0dJYWdpUHF6YXZsZ3pRR0hSMVdJOXNLMEhuYXZHRXVmSVJCekZUS085QWR4ZEgrVDRTd0dWZkt4K0ZoQVpNd0pZV0o2RGJYTWtUaWxiOEhVd1Zzc09FT25Qcmw1azMyRlZFdTFxRU5DSzJwVm0wdVV2VlZwazhXeTVMR3d2eG1HY051TTMxWStqSERsU0RHQWtNcS9aZ3h3TDN3TFBhbVJBL3ZHZUNNM24wVFVKTHBqaTg0eDl1TXoyQXJtUXptWXkzQW5yUkJFNXJyT2pMZ1ZrTHh2ZCtXQ2JqdWE5SU12c3hXYUpwQ2pUZEJOQVlpYzJFalZ0S0NUNGpiRFVJUjZCVXZMTENTZk1GY09JNGlNQUJnTGxCLzgzRis5TWJnd3hJTUhqZGRyYkh3VEpkaXNxVnQyb1dvSkVnaGJ0S0hLNStwUFY0bkMrbm9BYUxrMDI1eE45YkZjbHkwb01ab3ZjYzVIVnc4bGgyd3NMcjVNZlJ3bkJoNWduSXl5NHFxY3Vsci8xOG9BNTkzZXJ6WENvN0tmS2ZtclhuNE53Y25BMS9DZ0xtcnpWa0xsNEJBeXNBV2xmajEyTUZadmlOSElUb1pFUmg5NytwVlhOZWw4Ym85TVJ5RWIiLCJtYWMiOiJhZDZjOTRhMzI0MDcxZGUyZDUyMmIzZTkzMDk5MTUyMmZkMmI0ZWY0ZTQzZjc2MmI0MTRmMDk2ZDMyZWRhODkwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-483749539\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-134184882 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">l7xz5q4Zkpyo7Zd0rhHsE8M6pi3wac4XFWhsi0lQ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-134184882\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1824145119 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:29:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik51KzllV1l4VXVwNi9pWEk5R2ZiNmc9PSIsInZhbHVlIjoiaXlYZEVrdXZCNEpVelovZHFhNE5xMWV5WGZyVXI0TnZYeHU1NTlmT1lKQVZOYzVPeTdKU3pIK0h1SWx4eEw3TW5HUjdtNVRJMmFTMFhVaE44VTBJMjRvazFrSFFnS1BjWkJYcmk0ejBYMzBybm5pSmwwYzlLVWtRQlZXSmUvNFIwcUtENlQ0SWVKbjZ6L0ZyRWJ5MzBDejgxMVJud1gyeFA4aDNnQ1o2RnVBVkx6Y1VwQWs2c3IrNkFMblUyVkkrMkZJZEp1a1hpeGFTSlFCWUdoMk1NbDV0TFhGc3BreTJVUm1xUEJBM0wrUFROdzdRcXl4cFpzTVQyOTJ3bTlRQnBETFZ4UGVQNnJGTk1IejN1MlZwV2J0bFF1YTl4N2grK2R1SHlTZ2JRT0w3WWVTeXc2ZWd3U003Nkl4WldxSXJwUUJlTE90SWlyMlkwUXJQRitJVWdCMTc5Wk5QdVpYUkNxeEQwSlRxOW5CTW01dzcyalM0Z3cydEl6bUdFYVJjK0tWRi9zQ1FPNEVaOWtxdGp4eWczYmNhM3JhT0kzS0tyR1BIMVkzMlVTd0lNR3JNODR2ZmVQQjMrc2llbFlnOWRsd2hxVmVnS3g3QTJUTlJ3ZnY2VkNiNUM0Wmc0VU1LL3Nvbzh6a0pDRUJjY05OMkdwWXIzYlhDUGFUWlUzQnQiLCJtYWMiOiI1NTUyODgxMjA2OWQ5YjhhMTFiZmIxZjBlODRmNGM2NWJjMjk5NDBiYTJkNWQ1NjQzMTcwYjUzOWZiYTFkNzY0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:29:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlR3SXdrTXRZZklZQmhZM05hLzVXTHc9PSIsInZhbHVlIjoiNlZlUFFPMEFyVzBTMlFqYjBVcGRpc2ZqOWxQTnlnbjFBT012eFlhckVGWWZoRlhLQStndExHVDYrdlJCajcxa0hTdXVTR1ZvcmJZbENOMmZ1aGtMV2c0Tk5Gdm9FRG9XdlNrRjdqNmFNUHJ0QkNOZmRrODY3MCswV1F1U0dRUjdpUXpKU2UwNGoxWXEwVUxKZVNqV0drY3M3RTBGL1AvbVM1MjcwWjEvYzVZUDdxZDBnMlQrd3ZyLzBIR2hHdU05Unc1elJHdGdxRkdYaDRNMjc0U29rSktrTG1GNFBhUXBCTWRYdGQya0F2V3lBYUs3Z3NjeFErUnZxeUF4N0VHeDIvM3NRL3d3M1VDMVJocCsyTGsvbFd5dW9JUXo1aWI3KzdhNjBOU1B6ck9BUHhvb2hxT1lwcWlzM3FQL2ljdUsyUmRJVCt6YkJseTBuMmFRYk1PeTB1SXQrcEpJMzlJdExzcDJyU08rYVFMNjQxblJiV1pBVmp6YVhKN3A1NzYvcVlRRmg0c2w2WHVvZ0pJSGVsWUJBbFpjeWwrR1psMWNhcHUwN2ViSEw3c084d0RTRmxBT0hQellJVkp1dHZidzVDb3RvUGVGbEpNM1VlMnk4cStvQ05uOFJrb2RncndtS2MwM1JtYmRBbW43cldjdEFkTy8yY213bSsxMXl0S2QiLCJtYWMiOiJjOWJlNTBmYjc0M2ZiN2NiYjg5ZDUwNDc1Y2FkMTE0Y2Q0MmM5MTE1M2I5ODM1MzcwYThkOGY5YWU0NDkzMzNhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:29:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik51KzllV1l4VXVwNi9pWEk5R2ZiNmc9PSIsInZhbHVlIjoiaXlYZEVrdXZCNEpVelovZHFhNE5xMWV5WGZyVXI0TnZYeHU1NTlmT1lKQVZOYzVPeTdKU3pIK0h1SWx4eEw3TW5HUjdtNVRJMmFTMFhVaE44VTBJMjRvazFrSFFnS1BjWkJYcmk0ejBYMzBybm5pSmwwYzlLVWtRQlZXSmUvNFIwcUtENlQ0SWVKbjZ6L0ZyRWJ5MzBDejgxMVJud1gyeFA4aDNnQ1o2RnVBVkx6Y1VwQWs2c3IrNkFMblUyVkkrMkZJZEp1a1hpeGFTSlFCWUdoMk1NbDV0TFhGc3BreTJVUm1xUEJBM0wrUFROdzdRcXl4cFpzTVQyOTJ3bTlRQnBETFZ4UGVQNnJGTk1IejN1MlZwV2J0bFF1YTl4N2grK2R1SHlTZ2JRT0w3WWVTeXc2ZWd3U003Nkl4WldxSXJwUUJlTE90SWlyMlkwUXJQRitJVWdCMTc5Wk5QdVpYUkNxeEQwSlRxOW5CTW01dzcyalM0Z3cydEl6bUdFYVJjK0tWRi9zQ1FPNEVaOWtxdGp4eWczYmNhM3JhT0kzS0tyR1BIMVkzMlVTd0lNR3JNODR2ZmVQQjMrc2llbFlnOWRsd2hxVmVnS3g3QTJUTlJ3ZnY2VkNiNUM0Wmc0VU1LL3Nvbzh6a0pDRUJjY05OMkdwWXIzYlhDUGFUWlUzQnQiLCJtYWMiOiI1NTUyODgxMjA2OWQ5YjhhMTFiZmIxZjBlODRmNGM2NWJjMjk5NDBiYTJkNWQ1NjQzMTcwYjUzOWZiYTFkNzY0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:29:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlR3SXdrTXRZZklZQmhZM05hLzVXTHc9PSIsInZhbHVlIjoiNlZlUFFPMEFyVzBTMlFqYjBVcGRpc2ZqOWxQTnlnbjFBT012eFlhckVGWWZoRlhLQStndExHVDYrdlJCajcxa0hTdXVTR1ZvcmJZbENOMmZ1aGtMV2c0Tk5Gdm9FRG9XdlNrRjdqNmFNUHJ0QkNOZmRrODY3MCswV1F1U0dRUjdpUXpKU2UwNGoxWXEwVUxKZVNqV0drY3M3RTBGL1AvbVM1MjcwWjEvYzVZUDdxZDBnMlQrd3ZyLzBIR2hHdU05Unc1elJHdGdxRkdYaDRNMjc0U29rSktrTG1GNFBhUXBCTWRYdGQya0F2V3lBYUs3Z3NjeFErUnZxeUF4N0VHeDIvM3NRL3d3M1VDMVJocCsyTGsvbFd5dW9JUXo1aWI3KzdhNjBOU1B6ck9BUHhvb2hxT1lwcWlzM3FQL2ljdUsyUmRJVCt6YkJseTBuMmFRYk1PeTB1SXQrcEpJMzlJdExzcDJyU08rYVFMNjQxblJiV1pBVmp6YVhKN3A1NzYvcVlRRmg0c2w2WHVvZ0pJSGVsWUJBbFpjeWwrR1psMWNhcHUwN2ViSEw3c084d0RTRmxBT0hQellJVkp1dHZidzVDb3RvUGVGbEpNM1VlMnk4cStvQ05uOFJrb2RncndtS2MwM1JtYmRBbW43cldjdEFkTy8yY213bSsxMXl0S2QiLCJtYWMiOiJjOWJlNTBmYjc0M2ZiN2NiYjg5ZDUwNDc1Y2FkMTE0Y2Q0MmM5MTE1M2I5ODM1MzcwYThkOGY5YWU0NDkzMzNhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:29:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1824145119\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1282519728 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1282519728\", {\"maxDepth\":0})</script>\n"}}