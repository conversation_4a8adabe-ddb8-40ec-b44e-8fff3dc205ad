{"__meta": {"id": "Xaacb58e77d62a4a0b27f01f701e184c9", "datetime": "2025-06-08 17:34:46", "utime": **********.512496, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749404085.92291, "end": **********.512515, "duration": 0.5896050930023193, "duration_str": "590ms", "measures": [{"label": "Booting", "start": 1749404085.92291, "relative_start": 0, "end": **********.421599, "relative_end": **********.421599, "duration": 0.4986889362335205, "duration_str": "499ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.42161, "relative_start": 0.4987001419067383, "end": **********.512518, "relative_end": 2.86102294921875e-06, "duration": 0.09090781211853027, "duration_str": "90.91ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44107528, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02571, "accumulated_duration_str": "25.71ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.457168, "duration": 0.02274, "duration_str": "22.74ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 88.448}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.491546, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 88.448, "width_percent": 4.24}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.500011, "duration": 0.00188, "duration_str": "1.88ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 92.688, "width_percent": 7.312}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/roles\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1874461898 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1874461898\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1500458740 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1500458740\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-331601917 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-331601917\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-578775584 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=d25zvz%7C1749404084440%7C2%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImJ3NjQwcWZROE9KWnJZU2Vsem9HWnc9PSIsInZhbHVlIjoidkdpNmkxN3dESWZXVjN6eXJRUjNoYytFaDNTUWVWWWJ6VTBybFJQbkJyTnlkd2dIYnJ6djV6WFQwekU1Tnk3Z0pGUlo4Q0FZcGRnaWs5TXRlcFhQZGRwWHFtUFh4cDBDSHFFL1dRV052Q1BBdWxrdjB1ejlWdkRVa1B3ZXNoYkRXT2pnZ3FLTWU4c1FIVTdLdHNpZ3oxbTVzZnFZcU5HVUY5SjVwVGJoRmFVd1pvekx4WG9nMXN2MTh4S1NJWGNKU1RwTHVyUVJhZ1Z6dzJUNVlBQzhEcjltTlErWk9GcnRFVU1CcWlDazZMSU01TktCb0VLRFluT3dCc3YzWE5veDYzdkJNVFlhamJpWlI5L0x4UzVXMSswMVRYb1BYS0dVR25INlJhSWhsTEtDNnJzZm82TTYwcE5KNUcvZ1o4cWJiSktrL2k0VUU3ZzViYkZFbkcrQWdMWHEwTytIYkJmSVJOQ01GaFJ2a2RZRkVkVGM4eHQvYlBsU2UwZDRTRW5tdTNzNkdld1dsUStSdDNtL2dhelpoNzdPYUxBczNQME96YkwyWVJqUnJWQjhkMTF2cEhYZXRVbGFGa0k3YVdFZ1hmam81RVhIY3RCekVOdG5aVWNsRCtoeUhjNzZ3MldwRDd2RmsyZjAwMGRUUnhVMlNIc1ppZ3lkdEs2K0N1Ym4iLCJtYWMiOiIwYzhkMWUwMjM4OGNiZTY5Y2EzMGIwMDVjZTE0MmM4MTA2MThiNWZjZjFkZWQ0MjZmNjEyMTk5NzU0ODBlZWFjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ijl3MXVYU3lHeGx5dE9tVk41V0ZUZmc9PSIsInZhbHVlIjoidW9uQ1gzVmhNMG9DVENQaktIOVJXaUNCZWl0WHRnd0R5amg3TFVIK0Jsa0haa3NIcXhPc1NsUVZKbjJwR1NUUktyUUxTODBQditUcUg5MERDWlQ0RnBBYTVtelFuaEsvRHBvMC9BZEMzUnVtdVZ2VDc1YlhhaHBrV1dMQ3ZUUFJLU09JN2JZUzZBYS9MNmlvSHd6RFZPd3FTdjRlN2MxZmxQM1VPUWxYZDhrQmMwcksrOXV4L1ZUR202blViU3E0MWJwRjBGNisxclo5THJzc0VMK0h6TnhFdVZ0V2J4cE5tYkNmUmdjYk9CN216TjZWZTBWdXgzV0h2TFQzWFh2L1cvN0NaMCtmVVFtbHlhemRNMi9IRFY2aisyNXNIa1NjWVhMekUzSEx5QUJYNE1KWmx3UVFSYk0wY2N4SmVRNUxmcTVZNHlVZCtKVHBHajBPMEJpSjdDREpPeWp2dVY2eUliSXkzRmxXWHV4eC9XcWtzOG5tSWVMT2FsWXBDUmdKbEFnWTEvdGNjLzRGY2s2c2ZERmlOMWx0UmlLSWFZUmt0Y1A4VjRtZWxHcWV6R3B4Q0syT1lWMUc3eEk0amxTS3NzUTRwWC9mVWVlS1hZTVk3Vm80aWFub2ZVUmR1Q2did0ExRnRxMEVnbEpDSE1PemJ2NWNyaS9RMEc0TmgyTDIiLCJtYWMiOiI5N2EyODRiZjU0NTQ3N2RlMzMyOGRlNjQzNzQzYTVjZjJmNzZmOWI4M2U3YWFiZTcxZTE1MWE3NGQ5ODU2ZGYyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-578775584\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-617378140 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">l7xz5q4Zkpyo7Zd0rhHsE8M6pi3wac4XFWhsi0lQ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-617378140\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1651157606 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 17:34:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjVkaFZXd28yZ3hKOUhKRVVhKytaVmc9PSIsInZhbHVlIjoiMWJKOSs3QnFFOCs0dU1xeW1vZXZTYms4YlNXaGtta2YwSFMzMHdka2Q3ejFEaVVnR2xoZ3hqamVCaGpXclkyNWh0NDdmSkpyWlpOSVFpYkFJV2ovR0ZaOHdVb00zMWMxM00rbkxsMEdxSHhhOXN2WWF0SlpENUVnYjFiV2hIZTk0YjlncU5ZZUVUTEIySjBhZ21rTFF0a1RTcGw2dmY3aGdlSWN0REw2ZGNFakhhQlYrNFNDdlE4bm9OMC91Y2VnRkdZZisxWHBWSjlidnZXMTlZRnVJUW1QL1A1WGJmOXNLNFN5WjFrMkhBajF6cUtVd0pxL05TbDlpeUtSaVQyaWlpZUVlZUxSNXM4ZzlwY3RmbHpjbFRuYUtyQ2RNMzZaaGJyQ2orejdTSjFhYkdXNjY2ZlNJT0YxZEhNRWk2RHFldXBiVkpVMzR5bjJ4ZzUxczEzWG1ILzFSUVIrVC80MmxvMHVKd1RpalptY2cvWWF5TWdwSUZ4SDNpUlNJb2hKUmcza2NCdDBaelhsVHVyWDVDenVaV2M4MmZBcjlodE1pME9vQXUyRHIvelBIVExubStjK1V5UXV1M0xOdU8xWUl2Q1NiRHA0NE5lYU9BN0RIdzJodWY1ZWlMSGtZRE96cGE2TFJmMG1ZdkVMZ3VzbktxejZGSmhpc1MzbnJTWEkiLCJtYWMiOiI0OGMyYWI2MGZkNTIwOGFiMTQ2NmI2ZTgwMWI2OGQ2MzVmMTcwYWQ2NGY3YWNiZWJjYzk0ZGE4ZWMzYzY5MzU2IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 19:34:46 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjZ2ZmFCVzNGZ1JhdjBqbi9YaUNnSUE9PSIsInZhbHVlIjoiUFhoMUFHL21yQ2plN3lDWi9KNzRhandnUVdia214NFF1TGZKVjNlblZrL05pUXFsbDVZVU1vaHhQY1djRG1Nd05wQ1diVDR3Z2t2M1RSbk5VQ1diUGRiMEljeG1uaUJqR1FCTUFmL3JlVXMzMDNiUzR5MFFtSkZJYVh3Y216WWNvbkdPU2ZtU3M4VEF5TlVEM0krOFYzcVZHelF1NzUxYVhZRk4yWXNaR01QREpsM2owWENjd0RNaHpOYnBURXJVeG5la0tlWnFQQ29Kb0FMTU4wZXlTTStiYVZ2bWd0YlphNS9TOEpqTmIwZ1Mvbmk5cEVHa2FnbjBGdzBRRmxRL0liZ0l1alorYS91S3lROUNPV2tKeDVyYjl3U1liWTNhMGJ2NHp0MTc4ZkNLQTc0KzV0UW1CcU1US2VTRVZwRXd2cDYwL24yd3FhUU9uTFZURTNEdGUrY3N0eGJacC9IY1VYRElsajJsQU5mNEpHd25VQzVReVZuRjVPaW9OMSsxQVdKby9vV1V3NjhUQkVrb1BYSytBYjZULzU0TStrcnc0VkNCRiszYXdYY1pPL21HbnpkSTZSc3BSYit3L05JNjY5Zk9ocjloM2RtcDFNaVVsYXFqbWp0NDc0cU5kVUdMNjFEVEI2dkNZYzAxUnFHMTMxdjF3Tm1paEZLbk8ydjYiLCJtYWMiOiJmYjQwMDI2NmUzOWEyOGI1YWYzNzI2NzVjNDA1NjQwZTdlZTY3MTQ0NWUwZjAwNWNjODY0MDEzODg4YzRmZGM1IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 19:34:46 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjVkaFZXd28yZ3hKOUhKRVVhKytaVmc9PSIsInZhbHVlIjoiMWJKOSs3QnFFOCs0dU1xeW1vZXZTYms4YlNXaGtta2YwSFMzMHdka2Q3ejFEaVVnR2xoZ3hqamVCaGpXclkyNWh0NDdmSkpyWlpOSVFpYkFJV2ovR0ZaOHdVb00zMWMxM00rbkxsMEdxSHhhOXN2WWF0SlpENUVnYjFiV2hIZTk0YjlncU5ZZUVUTEIySjBhZ21rTFF0a1RTcGw2dmY3aGdlSWN0REw2ZGNFakhhQlYrNFNDdlE4bm9OMC91Y2VnRkdZZisxWHBWSjlidnZXMTlZRnVJUW1QL1A1WGJmOXNLNFN5WjFrMkhBajF6cUtVd0pxL05TbDlpeUtSaVQyaWlpZUVlZUxSNXM4ZzlwY3RmbHpjbFRuYUtyQ2RNMzZaaGJyQ2orejdTSjFhYkdXNjY2ZlNJT0YxZEhNRWk2RHFldXBiVkpVMzR5bjJ4ZzUxczEzWG1ILzFSUVIrVC80MmxvMHVKd1RpalptY2cvWWF5TWdwSUZ4SDNpUlNJb2hKUmcza2NCdDBaelhsVHVyWDVDenVaV2M4MmZBcjlodE1pME9vQXUyRHIvelBIVExubStjK1V5UXV1M0xOdU8xWUl2Q1NiRHA0NE5lYU9BN0RIdzJodWY1ZWlMSGtZRE96cGE2TFJmMG1ZdkVMZ3VzbktxejZGSmhpc1MzbnJTWEkiLCJtYWMiOiI0OGMyYWI2MGZkNTIwOGFiMTQ2NmI2ZTgwMWI2OGQ2MzVmMTcwYWQ2NGY3YWNiZWJjYzk0ZGE4ZWMzYzY5MzU2IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 19:34:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjZ2ZmFCVzNGZ1JhdjBqbi9YaUNnSUE9PSIsInZhbHVlIjoiUFhoMUFHL21yQ2plN3lDWi9KNzRhandnUVdia214NFF1TGZKVjNlblZrL05pUXFsbDVZVU1vaHhQY1djRG1Nd05wQ1diVDR3Z2t2M1RSbk5VQ1diUGRiMEljeG1uaUJqR1FCTUFmL3JlVXMzMDNiUzR5MFFtSkZJYVh3Y216WWNvbkdPU2ZtU3M4VEF5TlVEM0krOFYzcVZHelF1NzUxYVhZRk4yWXNaR01QREpsM2owWENjd0RNaHpOYnBURXJVeG5la0tlWnFQQ29Kb0FMTU4wZXlTTStiYVZ2bWd0YlphNS9TOEpqTmIwZ1Mvbmk5cEVHa2FnbjBGdzBRRmxRL0liZ0l1alorYS91S3lROUNPV2tKeDVyYjl3U1liWTNhMGJ2NHp0MTc4ZkNLQTc0KzV0UW1CcU1US2VTRVZwRXd2cDYwL24yd3FhUU9uTFZURTNEdGUrY3N0eGJacC9IY1VYRElsajJsQU5mNEpHd25VQzVReVZuRjVPaW9OMSsxQVdKby9vV1V3NjhUQkVrb1BYSytBYjZULzU0TStrcnc0VkNCRiszYXdYY1pPL21HbnpkSTZSc3BSYit3L05JNjY5Zk9ocjloM2RtcDFNaVVsYXFqbWp0NDc0cU5kVUdMNjFEVEI2dkNZYzAxUnFHMTMxdjF3Tm1paEZLbk8ydjYiLCJtYWMiOiJmYjQwMDI2NmUzOWEyOGI1YWYzNzI2NzVjNDA1NjQwZTdlZTY3MTQ0NWUwZjAwNWNjODY0MDEzODg4YzRmZGM1IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 19:34:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1651157606\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1441122036 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fB97EisGngzrs2mmI0rExFvMSxxMuh5468nJLOx5</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1441122036\", {\"maxDepth\":0})</script>\n"}}