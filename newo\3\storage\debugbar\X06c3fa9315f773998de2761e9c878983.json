{"__meta": {"id": "X06c3fa9315f773998de2761e9c878983", "datetime": "2025-06-08 16:25:03", "utime": 1749399903.022872, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.363777, "end": 1749399903.022893, "duration": 0.6591160297393799, "duration_str": "659ms", "measures": [{"label": "Booting", "start": **********.363777, "relative_start": 0, "end": **********.912249, "relative_end": **********.912249, "duration": 0.5484721660614014, "duration_str": "548ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.912262, "relative_start": 0.5484850406646729, "end": 1749399903.022896, "relative_end": 3.0994415283203125e-06, "duration": 0.11063408851623535, "duration_str": "111ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45678600, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02627, "accumulated_duration_str": "26.27ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.959201, "duration": 0.02432, "duration_str": "24.32ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 92.577}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.9975219, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 92.577, "width_percent": 4.111}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": 1749399903.007856, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 96.688, "width_percent": 3.312}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1479585252 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1479585252\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-296517621 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-296517621\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1297300341 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1297300341\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-762357078 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; _clsk=u1db3a%7C1749399875574%7C4%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkJMek0wUVl3KzBzMnorMWNGN1dWMnc9PSIsInZhbHVlIjoidXNnQlRxMm9hbWRnMFpQOWhTYWVqMldxVkczaXdGanZXTExOMVBWbGE0d1Y4N1hrVlFFZmg3ekZlVTUvdytCK1dOMldTK1hQMnkyNzllcXdaS0s1VmVEWUVJa245SWtqaTBWK2JjdGNBOGprZ2NlaCtOK2pjY1hnVXFvQU0rd1pBNUM2Q3FkVE1qakhMbXlyc0Q5WjBlM29yWWRVZEErYVc3Z3hZTFVyN1VkdkF5QWNWUUJLZGlKTWRHSTNad0pCZUJXQTBXUE8wZStBR2V5bnJNVHNzSnVlb0t0cjJDK28zaG5BZGhFU0NXZnpkbUwwdE4yNTJrS2VXRTM4Q3B3bSt1VmtGTEdHM08xVElLTjJiTGVjekVkOTBMdUFyMlZadGpuOXU5ajc5VGh2WmhRZ05jcDFmNjBEV3RWYlNkb2xLY2liNEpjQ1A2MlJ6NmhselpKVVErdVBLbEU4MUQ5MzhZdDNybVJmL083TCswdkZtaEJocGIreUptbmZERUhkdkIwRnVHeUFzQWlIbTRpa1BtZ3hodnE1NUJDb3o5TVBJUVBYcUE0M1JITEFnTElKdVlWaGJoUkxISTFsbERlUGdBM0Q4YXhQemZma2dCbi8zcGNlcjBpZDYrbmJVTUxXOWFGTFZBZm9YWXRFbnNrZmhEUXdNYm1FQ0t4OTZaL1oiLCJtYWMiOiIzNjEwNzg0NDA2MTc0OGJjMWQyNDM0ZTYyNTU3YzI1Mjc4MzQ1ZjUwZmVjZTJhZWEzYWFiNjI2NDllZDE4MmM4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkFpS2xCOXdYZVdRNGVqTGtmMlFoWFE9PSIsInZhbHVlIjoiaFM1ZWJGS2pGWTlLS2luSmkremlRdHdYYjl3MTJ0QVFFd0VOcmZHRkVKM0tuTEhCZWQ4UnZXaUh2czI0RDR1bGtYaWlVR05JZVVkcjBDdExqeC9wKzRGY3NVY3k0elptWFdkVXpsZ2k0QUdlSWlSY0xFZHVaM2hvZU84Y0lnZkpnR3Q1NzF0RmxNQlVkTm9mY1MzOHJMK0NOc2Jqd2ZqWkg4R3IzUzYyYkZwMkxPWUJCUkYyY01TNlhPU20rcGtOVTFtVG1VRXBGeW9uQ1dsL2t6QXdNdG54RWxxZDdlQUp5T3FFNHEveU9Ca09EUWl5NmdRbjlsbUtMeWhLTjljZm8zaU1WSlZxWE54ejdWbko1ZUNBK1dvVjNaK0ZqOXlJYXNuUUxGZ2g5SFMrd1pFS1Y1a2dtc044RTJiTXd1WmZEelgzMWgxWlhMUmYvVE1VaGdNY3JXOEp6REJkR3RYdUp1T0pTV2tSTmlCcm9oM3l0SXNJR1RtYnpOdnVJanVkdDh3S3hGT29GQk84eXoyMUpzZG9NMDdaVjNTTjNYWWQ0ZGZ0VHltZlkzREVlYjlPSXZwa0pCVnpHODNLS0JxQ1lDMkplOUV4U2QyOVlHRVk2QUV0MWJuWGovck9RbUFlVTdPWlNRQXhOMXdKd2JCc2pmNnQxYlhtYlZZMk1CMlgiLCJtYWMiOiI2OTQ5ZWZmMGViODhmNDBlNzg3YTRkZjY5OTIyNjQ0ZTJmODc2ZjhhNTc0OTA4ZTQ4MTJmMGFkOWEzMzc1Y2E1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-762357078\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-666327872 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xNV7SvMX3AgbiuDVO62eNXfryfZD4KbWr3rEIkXt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-666327872\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1475465121 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 16:25:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik5zSVZ2QmM0SE1vRkxPNVpodGhLYlE9PSIsInZhbHVlIjoiS216V2FuMVh5S0YyOW11dmg2Y29TVTQxV1p3QjltUG1qaXJZQ0MySk44bTdNSHFJYmp1TmYvTHhmend4UGdRbmNXMG5yQjlVQXFZZXdLdVFlTzZkdlh5TWtCYkF6S2c3MndhamlTcWJJN3RzOVdHSUdpZDRPSWs1Nk1sbklPa2lmNTRaUU9UZnRwd3pLNWpCZ0NtbkwzRkhHZXlQLysxU21MdTVaeXBDS0NFK2hmUmFmS2VjekFGbTFTWHNldU5FaG5KS2ZmQUVRR0lUQmIxWnFib2lxMnBjWDd6Z3dwYlpVOXJmSFVaZi9ENU8rdE15b2NWNHJGWFJlRVdLQzVCVzUyQk4rbTFGY1RsTU1XLzZUVGdaMGN5TTgxV1JRYVNYbFg4aUM0Z1liQzlnSUlzMG5kb1hUMGtRNWpGTmN2bHRNWkM2T0pNanE3bmlWd3NKbFhGYjcrK3k1dFRkYlNwTUZzN0d1VkxBMnpOcDVweTlTZzRrdzNVcmdsTnVzZ2s0cG9qYnhIRjQxVTdXSEFVdWNEeDVUQlZhV0haWllsZTlKekxhQTdXd0dTcVdOa0NnNjdhc1pKQUY0aWU2MklCcUFlZlJGbDBKUFZiLzBMa1dMMXZ2OGo2UTJyU2VTOWVEMC9sT0dkVlRCTHhObXdWeTdOYkRoOEN1bGUxWVM1OUMiLCJtYWMiOiJiY2RiYWQzNTdiYzViNGE5OWJlMjJiM2U5MGJlOTI4NGQ2MWQyNjk3MzI2NjU2ZGRmZWU4OTljNWRhNWFlZjg1IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 18:25:03 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InplSm9ta0dPTm96SWJxRDM4TUZrNUE9PSIsInZhbHVlIjoiOE5CSmFCd2dYSFVDTDNnRTJBOXdUWGJseklrMTJMcnNEVE1tUnpyT1JMdldjalovVUw5UHVwakxxNndBb2FHUDFRL0pya1RUL0YwZUVIbTJLQWYyWGRmVExIRWtyZzkzQWZzTzljZlBHTTN4cld6ZUZKSGZhcC9XdHZEd1BtV2NvVzBMMXhKWjNoV256VDF2cWNPQjM2aFBUbFlDTW9NUGFNdkY5bS9Weml1Y0RvdEh4RmsyM2Vvc0FOcGtRUW44bGtMSy9YYlB3eEErWndRZHNPRG9yUHV1RVBYUjMzKzNRV1R4YzdleVBVQmg1enVUaEEwQjFoQk9XQTB2anE0RUs4Q1hadStKVDg0QmFwTXFUKzJJbFFGMGliTnlaSlp2a2UwYUxic29Fd21jK0dmM3VuZnl2SE5ESU9uTU5BRHFLeWpUMHpmdHZJT1Z4THE4S1AzU0puSm1JQ3Jna2dYRkdHQUlWYWtVbVh3eXdBVmRkWFp5dmxEaXQxM04rTm5ob1Z5d1drWWo0ZkJUdFV1Mm9UQ2t6TVJKT1lON0N4RW1DSmowMHJya2FTNHF5K1pEalF6aFo3NVdVYllPU0k0cW0xNGtpQWZ3bGxkVkNBS25NOTlhb0pFVzZGUkhWYnZQbEJqNUllRFpadHoveGFTOEdtQks1WENIblN5eXVnSHIiLCJtYWMiOiJjM2RmMjIxNjc1ZTUyNjQ4M2M2ZWZjMWQ4ZGNjOWNlMDc2ZmQyOTFiOGNhZGNhODcwYjYyYWQwODlmNjY0YmI0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 18:25:03 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik5zSVZ2QmM0SE1vRkxPNVpodGhLYlE9PSIsInZhbHVlIjoiS216V2FuMVh5S0YyOW11dmg2Y29TVTQxV1p3QjltUG1qaXJZQ0MySk44bTdNSHFJYmp1TmYvTHhmend4UGdRbmNXMG5yQjlVQXFZZXdLdVFlTzZkdlh5TWtCYkF6S2c3MndhamlTcWJJN3RzOVdHSUdpZDRPSWs1Nk1sbklPa2lmNTRaUU9UZnRwd3pLNWpCZ0NtbkwzRkhHZXlQLysxU21MdTVaeXBDS0NFK2hmUmFmS2VjekFGbTFTWHNldU5FaG5KS2ZmQUVRR0lUQmIxWnFib2lxMnBjWDd6Z3dwYlpVOXJmSFVaZi9ENU8rdE15b2NWNHJGWFJlRVdLQzVCVzUyQk4rbTFGY1RsTU1XLzZUVGdaMGN5TTgxV1JRYVNYbFg4aUM0Z1liQzlnSUlzMG5kb1hUMGtRNWpGTmN2bHRNWkM2T0pNanE3bmlWd3NKbFhGYjcrK3k1dFRkYlNwTUZzN0d1VkxBMnpOcDVweTlTZzRrdzNVcmdsTnVzZ2s0cG9qYnhIRjQxVTdXSEFVdWNEeDVUQlZhV0haWllsZTlKekxhQTdXd0dTcVdOa0NnNjdhc1pKQUY0aWU2MklCcUFlZlJGbDBKUFZiLzBMa1dMMXZ2OGo2UTJyU2VTOWVEMC9sT0dkVlRCTHhObXdWeTdOYkRoOEN1bGUxWVM1OUMiLCJtYWMiOiJiY2RiYWQzNTdiYzViNGE5OWJlMjJiM2U5MGJlOTI4NGQ2MWQyNjk3MzI2NjU2ZGRmZWU4OTljNWRhNWFlZjg1IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 18:25:03 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InplSm9ta0dPTm96SWJxRDM4TUZrNUE9PSIsInZhbHVlIjoiOE5CSmFCd2dYSFVDTDNnRTJBOXdUWGJseklrMTJMcnNEVE1tUnpyT1JMdldjalovVUw5UHVwakxxNndBb2FHUDFRL0pya1RUL0YwZUVIbTJLQWYyWGRmVExIRWtyZzkzQWZzTzljZlBHTTN4cld6ZUZKSGZhcC9XdHZEd1BtV2NvVzBMMXhKWjNoV256VDF2cWNPQjM2aFBUbFlDTW9NUGFNdkY5bS9Weml1Y0RvdEh4RmsyM2Vvc0FOcGtRUW44bGtMSy9YYlB3eEErWndRZHNPRG9yUHV1RVBYUjMzKzNRV1R4YzdleVBVQmg1enVUaEEwQjFoQk9XQTB2anE0RUs4Q1hadStKVDg0QmFwTXFUKzJJbFFGMGliTnlaSlp2a2UwYUxic29Fd21jK0dmM3VuZnl2SE5ESU9uTU5BRHFLeWpUMHpmdHZJT1Z4THE4S1AzU0puSm1JQ3Jna2dYRkdHQUlWYWtVbVh3eXdBVmRkWFp5dmxEaXQxM04rTm5ob1Z5d1drWWo0ZkJUdFV1Mm9UQ2t6TVJKT1lON0N4RW1DSmowMHJya2FTNHF5K1pEalF6aFo3NVdVYllPU0k0cW0xNGtpQWZ3bGxkVkNBS25NOTlhb0pFVzZGUkhWYnZQbEJqNUllRFpadHoveGFTOEdtQks1WENIblN5eXVnSHIiLCJtYWMiOiJjM2RmMjIxNjc1ZTUyNjQ4M2M2ZWZjMWQ4ZGNjOWNlMDc2ZmQyOTFiOGNhZGNhODcwYjYyYWQwODlmNjY0YmI0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 18:25:03 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1475465121\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-360444399 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-360444399\", {\"maxDepth\":0})</script>\n"}}