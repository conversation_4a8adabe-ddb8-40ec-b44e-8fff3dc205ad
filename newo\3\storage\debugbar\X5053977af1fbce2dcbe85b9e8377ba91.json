{"__meta": {"id": "X5053977af1fbce2dcbe85b9e8377ba91", "datetime": "2025-06-08 16:17:50", "utime": **********.645795, "method": "GET", "uri": "/pos-delevery-pay?total_price=22&pos_id=41", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.035736, "end": **********.645818, "duration": 0.6100819110870361, "duration_str": "610ms", "measures": [{"label": "Booting", "start": **********.035736, "relative_start": 0, "end": **********.505951, "relative_end": **********.505951, "duration": 0.47021484375, "duration_str": "470ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.505974, "relative_start": 0.47023797035217285, "end": **********.645821, "relative_end": 3.0994415283203125e-06, "duration": 0.1398470401763916, "duration_str": "140ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52379552, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x pos.bill_type_delivery", "param_count": null, "params": [], "start": **********.631586, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/pos/bill_type_delivery.blade.phppos.bill_type_delivery", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Fpos%2Fbill_type_delivery.blade.php&line=1", "ajax": false, "filename": "bill_type_delivery.blade.php", "line": "?"}, "render_count": 1, "name_original": "pos.bill_type_delivery"}]}, "route": {"uri": "GET pos-delevery-pay", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@deleveryBillType", "namespace": null, "prefix": "", "where": [], "as": "pos.delevery.billtype", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1520\" onclick=\"\">app/Http/Controllers/PosController.php:1520-1536</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.025380000000000003, "accumulated_duration_str": "25.38ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 17 limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5533319, "duration": 0.02255, "duration_str": "22.55ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 88.849}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.587399, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 88.849, "width_percent": 3.625}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 17 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["17", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.610839, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 92.474, "width_percent": 4.255}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (17) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.615263, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 96.73, "width_percent": 3.27}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => manage delevery, result => true, user => 17, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-533230646 data-indent-pad=\"  \"><span class=sf-dump-note>manage delevery</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage delevery</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>17</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-533230646\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.621023, "xdebug_link": null}]}, "session": {"_token": "SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos/eyJpdiI6IkFRYWh6YzFtM3ZKUitTR0xaMzNPL3c9PSIsInZhbHVlIjoiTTQ3YTVSVCs2WjlicXFPditOMVY3dz09IiwibWFjIjoiOWZhOWU0ZTY4ZmJmMGYwM2JhN2E5OWZiNDNlMzZjYjg2MTlkZjU4Yjk2NWEwNmE2MDVmMjkwMmY4NGY0ZmRiMSIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "17"}, "request": {"path_info": "/pos-delevery-pay", "status_code": "<pre class=sf-dump id=sf-dump-1082105052 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1082105052\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-630864309 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>total_price</span>\" => \"<span class=sf-dump-str title=\"2 characters\">22</span>\"\n  \"<span class=sf-dump-key>pos_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">41</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-630864309\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-557034324 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-557034324\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-550224797 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6IkFRYWh6YzFtM3ZKUitTR0xaMzNPL3c9PSIsInZhbHVlIjoiTTQ3YTVSVCs2WjlicXFPditOMVY3dz09IiwibWFjIjoiOWZhOWU0ZTY4ZmJmMGYwM2JhN2E5OWZiNDNlMzZjYjg2MTlkZjU4Yjk2NWEwNmE2MDVmMjkwMmY4NGY0ZmRiMSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1936 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v13bw3%7C2%7Cfwl%7C0%7C1982; _clsk=1y4k55%7C1749399469579%7C2%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjNVMnJhQWlTR0ozajl6b2ErdlFLRkE9PSIsInZhbHVlIjoiR1pmcXVzMGRUcjRhMkVIelpCN0hWcDhtRlJENW12K0pRUlZxUy9rODVCWWh2TkZJZ1R0WnlyWFpYM2ZuY3lzR3hrRThuMkRSSEI4QjNQczY1cjA3eEhCaVJKN3Awa0ZSSGo4NXVPM0pkdXFIZ0gwYVM0UHAybVBvd0s1enlqZ0tFSmZUYVRiUmFsdHBaakVOZms4cWpCNDRodDY5S09yVTNkUzgyN2tZaTJCUCtmMERVT1gxRFRyRzloWm5IM3ltRzdhOFdsV05mMW1tTFZDbnZ6cU1MNzJIeTdYejdXdmg2a29YZzdIOGRGZ0huTUhiWEpSZEhRQzZGNWE5VEppSFpESDFXTUcwbTZhd3FkRHJlUzVnRFdsTjRhbHY5WnQ2TUVCUHRJRWd2VkdoMFZkVmJTMmxWMlNUMVBvZGZXa2lpUFQ5SFNraHRjdWpLYjR3dG8wKzY0cGhUTTRHVzNzUXh6aGo5elB5Wm9tQm1KcFhUU1EvaTdWUUlYdlhhakY2bFVndE1IdDZOZy84eVBwaEVzQU9vM0VXOEJlQnJhUmJ5U2JrN0NxMWt1blNwekJGUnJQVVgrNEpoL010RUhBZXZndTdWUjBOYUpZYU9uM1J4L0h5SlhxOE45U3RXMkJkVklxaitLQWVvSERSekpxS3BmVVhheEtBaDVWQTVYYzEiLCJtYWMiOiJjM2ZjNTczNDdjNjcwYzQxYWJkMWNiOGYzNzk5NTAxMzhhMDEyYTc1YTk2MDA5MWQzYWUxZjNlMTFlYzM3M2Y5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IitielFLMzI5OU40Q1dIaHd2MDJha3c9PSIsInZhbHVlIjoiUGlTZDZFeHpiTGk1NHJVSzFrSVhwei9ydk8yandabEk0eWs3QnozeC9mbUlVWUI2bnBmb3BDMExBZlUxVXVXdGlxck9tUndsNW5Uays4QzZ0R3psNU9ZbTFSL1d4UFBLUFVXd3l5WTYzdFErUGw4M0p5anh2SVV5Q0czYWROcDNSNlN6SUZIU3czVmtDUytoNnMrakhlVUNhOXBSRVB6Q05ONHNHejlGL0hwUVZGYnBBbmQrVThkckZKRDFTSStYSUIvb3JKRDdNYnoxV1NxdnhqSDl3cHg4cmNqSnhxTHZXRURVazEyOVZZSGd1K1Z4NkJUalVLUGhjTzRLODVLVERpdkVZUWJNNlVKY3VuaXA5L1pLOWVLMzdEZHBmUjZKVkVqZXV1c1FCRHpVQ2QrQkxKOXZiTFpldlRxeVBraVVmdXMwOXlheDluMkRkRU9menU0YlRXazVLZWJrTDFrbUEydVB4eWZLMDlTbHl1Ky9hRHJGRHh5azJidDRBRGZaMnNxYkVWempNcnU5bVVLeWRlWWVOL0RyQ25GQ0lKWVJGd0RsWmRxajZDU2hRRmZ5akEreC9tREFjU2xSTStRdW5PYTdSTG5peGhwc2NpenU2R0thV1BWQWI2eDA1c3BWQ2ZzenJyUUQvdjd2anB6M3crZmY0WC9pRXFzazE0M1UiLCJtYWMiOiI0MTZlMTYyOTFkY2YwOTE1NTBkYmU5NjM4Njk1OTcxNThlMmIwZGI0ZWZjNTYxMDdjZTljNThlMDI0YjMxZTFhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-550224797\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-647678313 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">COI6IIrAbucReABHBN7o3ZaIzRbwBCI1gfwrcfCo</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-647678313\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1333358087 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 16:17:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlZibmhqN3JTSTc5aG9FWjBJT1V2V2c9PSIsInZhbHVlIjoicTdxaFRDZWt2SFk3Lzhsc3dMU3BSUzJ6TSs3NEp0ekIyb2ZoaE9tWlErcEZGbEwzNVVHdWNscmFQVmdhVVFkZXRqMWFUcWNRajgrWW4yYytDbHkxRzFIQTZpUmZiRTdyN0dXREx4cEVUUCt5eXF0RGlCYjd3UUpVbDJyRzRucFM1a01CN3NtZzI1K1VZZkQvaEVneEtMMHFZTHBsd2t2ZmNNNUYwMzVvU2JYWUJSc0ZReDA0Mjlwb0hxQklyaVhNK1BqbVR2SlROTEVwTGswdE1PcmoydEdjcDhvb09nZHA4bXB1bGxjWEpabmllQXR5R2R3QWdVaFM5dGp4a1ZvSzFwcUhFQWFrODdhYm1EcklxOVd1K25vWlQyN1lCR0lIT0NBV25vTWFpK3BWN0Nyc1NoZkVDanRjbmEvK2FuTnc2bjRBbFprcU5oSWlBZ3J6d3VXYkx0Qmlqa05pN1pHWmdnZGZTN0p0T1BrWFM4eXBCN2NIODlmWXhERUdUWGdqai85SGRpY1ArN1ZUQVZGWWY5Vy9kOEpSUU1SNXBKVWhkanJOS0xTRzUrMHhuWTR6ZkNXMS8xSTliTlZqY3RlWHlpMGlINUtFTUkzMWdNajAxL1BnOTVPVTZ0NEtnVjM5c1hjWDJYSmJRcDlKYytuNGI4MHZBN09yMHZodktBYjAiLCJtYWMiOiI4ZDQ3MDc1MjdlYTk5OWM5ZDA3MDVjMzQwOGRhNmRjNjg3ODI0MGZmNDAyOGIyNDFlYTI5OTc5YjA1ODg4YmFlIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 18:17:50 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjZHSy81ZVRzdXpoYzVTOVNTa2RPS1E9PSIsInZhbHVlIjoiN2pZZk9ZbDlkbkxwS1kzU1Nxckx3M3AwQjRDWDk1dFhjTzVvTVliWnI3NlVLTVM0eDZvY0kxZnhlbzArYjRMVmF3ZFdBdkZ2a2I3bS9SaDdmRkVEQkZKVkx2bUd3cXR0Ky82SHptaE10S1dEcFZVSTZqbWVtV0hDMnh5eDl6MmlqVnFMSTIvdmhueGN5MklaT0J0L05qY3hqaURhSmpFamZFRkxlczc1dytsOHI0QXdXcmg0WDR3S1BoU3Z1c3hZVk5SRzNYQStjMXU1SnVXcVRBN3FjTkM2TEZQRkdiQnl3Q3Z0ZjVIVk1FWkNlUnhjVVdZWDFydzF2TzlONHBIcGRBclBDZlJmRmpGRWxDZk00UkVuTlkvSnFvYWEybi9nLzgxdDJIUkEyVUx0ajE3OGRkRDRsYXF2TDVYcVhCQkU1dnVOenJSZmcveUFHVVUvVy9EZmNFenJQTGxmK09VY2ZuTUNtNFp3dG5hMGZ6VFdHOTcxbWtaeFdld3Z1T2djOUwyajJGUndJbXFianlYWnlERko1L0tWYW1NVEpjYlNLU3BWMERDSFlSYnNEZFpxOXZrejF2SHZaODB1aEV4cXI4UlkvOHRBUXVMWkhQeW5zNHd3N0srMldVNk5oQ0JPL0kxTUdadmFIU1Y3Mkk2M1BndWhWcFV6NXY4RFEySHUiLCJtYWMiOiJmNDQzMmMyMDRmYzNkYzgyZTUyNzgyZTVmOWM5YmIyMzRjMmEzMmUxZGY0ZTczYjkwZWZlYjI2NTJhZGQ2MGIyIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 18:17:50 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlZibmhqN3JTSTc5aG9FWjBJT1V2V2c9PSIsInZhbHVlIjoicTdxaFRDZWt2SFk3Lzhsc3dMU3BSUzJ6TSs3NEp0ekIyb2ZoaE9tWlErcEZGbEwzNVVHdWNscmFQVmdhVVFkZXRqMWFUcWNRajgrWW4yYytDbHkxRzFIQTZpUmZiRTdyN0dXREx4cEVUUCt5eXF0RGlCYjd3UUpVbDJyRzRucFM1a01CN3NtZzI1K1VZZkQvaEVneEtMMHFZTHBsd2t2ZmNNNUYwMzVvU2JYWUJSc0ZReDA0Mjlwb0hxQklyaVhNK1BqbVR2SlROTEVwTGswdE1PcmoydEdjcDhvb09nZHA4bXB1bGxjWEpabmllQXR5R2R3QWdVaFM5dGp4a1ZvSzFwcUhFQWFrODdhYm1EcklxOVd1K25vWlQyN1lCR0lIT0NBV25vTWFpK3BWN0Nyc1NoZkVDanRjbmEvK2FuTnc2bjRBbFprcU5oSWlBZ3J6d3VXYkx0Qmlqa05pN1pHWmdnZGZTN0p0T1BrWFM4eXBCN2NIODlmWXhERUdUWGdqai85SGRpY1ArN1ZUQVZGWWY5Vy9kOEpSUU1SNXBKVWhkanJOS0xTRzUrMHhuWTR6ZkNXMS8xSTliTlZqY3RlWHlpMGlINUtFTUkzMWdNajAxL1BnOTVPVTZ0NEtnVjM5c1hjWDJYSmJRcDlKYytuNGI4MHZBN09yMHZodktBYjAiLCJtYWMiOiI4ZDQ3MDc1MjdlYTk5OWM5ZDA3MDVjMzQwOGRhNmRjNjg3ODI0MGZmNDAyOGIyNDFlYTI5OTc5YjA1ODg4YmFlIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 18:17:50 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjZHSy81ZVRzdXpoYzVTOVNTa2RPS1E9PSIsInZhbHVlIjoiN2pZZk9ZbDlkbkxwS1kzU1Nxckx3M3AwQjRDWDk1dFhjTzVvTVliWnI3NlVLTVM0eDZvY0kxZnhlbzArYjRMVmF3ZFdBdkZ2a2I3bS9SaDdmRkVEQkZKVkx2bUd3cXR0Ky82SHptaE10S1dEcFZVSTZqbWVtV0hDMnh5eDl6MmlqVnFMSTIvdmhueGN5MklaT0J0L05qY3hqaURhSmpFamZFRkxlczc1dytsOHI0QXdXcmg0WDR3S1BoU3Z1c3hZVk5SRzNYQStjMXU1SnVXcVRBN3FjTkM2TEZQRkdiQnl3Q3Z0ZjVIVk1FWkNlUnhjVVdZWDFydzF2TzlONHBIcGRBclBDZlJmRmpGRWxDZk00UkVuTlkvSnFvYWEybi9nLzgxdDJIUkEyVUx0ajE3OGRkRDRsYXF2TDVYcVhCQkU1dnVOenJSZmcveUFHVVUvVy9EZmNFenJQTGxmK09VY2ZuTUNtNFp3dG5hMGZ6VFdHOTcxbWtaeFdld3Z1T2djOUwyajJGUndJbXFianlYWnlERko1L0tWYW1NVEpjYlNLU3BWMERDSFlSYnNEZFpxOXZrejF2SHZaODB1aEV4cXI4UlkvOHRBUXVMWkhQeW5zNHd3N0srMldVNk5oQ0JPL0kxTUdadmFIU1Y3Mkk2M1BndWhWcFV6NXY4RFEySHUiLCJtYWMiOiJmNDQzMmMyMDRmYzNkYzgyZTUyNzgyZTVmOWM5YmIyMzRjMmEzMmUxZGY0ZTczYjkwZWZlYjI2NTJhZGQ2MGIyIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 18:17:50 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1333358087\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-180856213 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SV8Q0RtuVKxB41fDufgZ800YUyZti8Hr4IRps1HJ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"221 characters\">http://localhost/pos/eyJpdiI6IkFRYWh6YzFtM3ZKUitTR0xaMzNPL3c9PSIsInZhbHVlIjoiTTQ3YTVSVCs2WjlicXFPditOMVY3dz09IiwibWFjIjoiOWZhOWU0ZTY4ZmJmMGYwM2JhN2E5OWZiNDNlMzZjYjg2MTlkZjU4Yjk2NWEwNmE2MDVmMjkwMmY4NGY0ZmRiMSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>17</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-180856213\", {\"maxDepth\":0})</script>\n"}}