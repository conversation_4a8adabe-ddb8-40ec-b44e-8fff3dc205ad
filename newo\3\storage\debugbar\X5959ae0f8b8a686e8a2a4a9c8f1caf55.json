{"__meta": {"id": "X5959ae0f8b8a686e8a2a4a9c8f1caf55", "datetime": "2025-06-08 15:30:42", "utime": **********.3542, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749396641.674202, "end": **********.354222, "duration": 0.6800200939178467, "duration_str": "680ms", "measures": [{"label": "Booting", "start": 1749396641.674202, "relative_start": 0, "end": **********.233121, "relative_end": **********.233121, "duration": 0.5589189529418945, "duration_str": "559ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.233134, "relative_start": 0.5589320659637451, "end": **********.354224, "relative_end": 1.9073486328125e-06, "duration": 0.12108993530273438, "duration_str": "121ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48219040, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.007770000000000001, "accumulated_duration_str": "7.77ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.283817, "duration": 0.00361, "duration_str": "3.61ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 46.461}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.3000782, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 46.461, "width_percent": 8.623}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.323817, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 55.084, "width_percent": 10.296}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.32767, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 65.38, "width_percent": 8.752}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.33568, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "ty", "start_percent": 74.131, "width_percent": 17.503}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.34184, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "ty", "start_percent": 91.634, "width_percent": 8.366}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2129614769 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2129614769\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.333714, "xdebug_link": null}]}, "session": {"_token": "WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1652152663 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1652152663\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1385231500 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1385231500\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-552354755 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-552354755\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1339482629 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2810 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=v838g7%7C2%7Cfwl%7C0%7C1961; laravel_session=eyJpdiI6ImhCYzE4RmhjcGk1WlRJTFl1ZG5yd0E9PSIsInZhbHVlIjoiWGFaRVVCRGsvUVhPNWU5WFloMTBkV3BQU2E5ZDdROWt0YXAxb1AycDJ0RzB1V2RWWUQzUDVOTFFyOWFpN0NJNUtKRXp2Z2cxTTVBRW5mc0pqaDVNTnkxM25YWEZqTEVGS1pGVkpjaWtHenp0WEREVGM0MEFZZ3Z6VG9EWURWb2d4VjRyc0d0V0FWYUlqSFQyVTZOc1ZFelNsVzBCdEFJSWx1Q08zV3M2Q0EvQ25OVGN4Rk5VcUlMaUxNWXU1THBWUFZDd2x5RWRLZ2Q3RStHdG5tN0FQY3lTN1F1YTJXOFVwNkx3VWFWM3JDcFJuTi9OQ2xjb0RHQVpCaEdtbTZBSnRYdVVsQXJudnREblgzcGYwcTRwZEJlVm5EZHZoT3Y5WHNKbXJtTTh4SGZOZjlkd0d3UDJ5OUlIY3VjNlJkejEvZ0lZVE53bFhMdFVRVzk5S3VxQ1VhOVVVV0JVOS9VOUwxWmJYWEJFSFdYU1lOVFJyL1BsQlk5Z2hwanh3QjNFdXdWRW9QYm0zTHRDSmhnWkxkSEJONHQyTTM1bHFlL1VKaWRIRGFVNVBaZE5iU1FidXFCVlpOYUdEK1hYWXNCZUExSmNYSllUZHVPR3pqQ0dmN3hNTUhhTWxTMTl0VXBmSnY3QmhBTEtraDE1UzRGa1ZsL3d6aG81Mjd3azhrMzciLCJtYWMiOiIxODUyZDNlMGFiNjgxNGY2ODU4Y2ExZDRmNWY5YzQxZTcyMTM1ZTc1YzVmNzZkNmJiOThmODkyZGQ5Y2IzYTQxIiwidGFnIjoiIn0%3D; _clsk=1adle91%7C1749396510751%7C76%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IitZR2YramlYYzVsOXRVUDNhN005d3c9PSIsInZhbHVlIjoiVll3SlBSSGUwWlpvNkNsc1FTQUVOZFc2NHoyd3M3NG9wWGU5STNvYUVxNnROaG5LbHlUNzhWekNGelRNeGhueERUakwwU1NRYjcwdkVLMW5iU2NjcFk0RG5HcnVVMWMzNzl5NW50RXZlOFpCN0FtemhwKzBIVGZiTXpmSWEyVnNoOTBQR21LN0dKNFUxZklmdG9MbFpVOC9kNEphaDBqbWNPOTNZYjFrbXVTK0NSTnRMUzhZWXBOcnRVU2k3MUNnZ25JOWQyUTFGQzAxc0VGNDVQSjd3TG92bVlVdkhkMWtnZ3hXc09WREwyOVFKSC8yYTlCT0NYOFZlbmJLM0NuVHRNbXZ0MUpYT1oxQzI4dmt1QTBFckQxOG1RbElVTHJZQkJtQVEvdmIybkM1SzBKUmZ0dVliWldQaUJZN0p6UlJZM1crZmRSWktBUlFVdVdjNHBwV0c0RDhJNnkwaXo5aWE4UVN4bGZwejhBbFdZZUovYmIyc2ZSNEdMMTM1b2RramU0RjNhWGFweWhlQ2g0cEZsWUpVVFBOQ0x4OGZMRUgyUkp6QTFSenVzaGllYXVKenZFdndZc1B3RUd1OEF0MERESlA5U2djKzM1WWczRjVYWnFEcnlGN1FHaE4vbmxkZjM0VHFjVUVJME9iVCs3RW5lbGZLa21RYXRFVis0TVgiLCJtYWMiOiIwNzkwMDY4YzAyOWI5NmQwOGY3NWIwNzY4MThkOTUxYTEyOWFlOTFlMTk4OTQ0YzkzYjM1ZGU0OTc2OTAyODJmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlBhZUU3UkxmT3NOSTJaSGhpelBuOGc9PSIsInZhbHVlIjoienAvVExvTVQxalhIbHpYNUE0TEN1U1ZJYUxlM0FoVlFoNWNNL0xtU0tTZytCWGw1aWZ4VlRzeGJqMllzbEFaMm9DcDdrTWR5clZuVlRzbngxcEQxUkZ3VnN1ZzFGaVNZc3JCNkhXUk1TVHFCRUVRUjlHOTBmcFc5VWtDK0JYUHE5bGFwZ1JEc1ROZzdSYUNINGFpclhuZkFxUStFaW1KemxJM2trZ05YZkR0Z2VUbWNYUE5wMnpsUDNncS9xNklkQUxRb1B4TmF3WWQ2aUR5MGN6TnNEYmtndzMxM0ZtSGdmdVRwR29JZ2p5b0kvTEZwdWxnQUs0eUF2SHF3M3ppN3hNSmVhNkFEcEhpbXpCKzR6Q1F3U29lRUlHMU8wNmUxbzFOMjQyNzFNN3E3NFhXWVhQd0ZZc1BjOTVkYmZOYkRDSUhrQWtjL1V5T1p5L3p5S2ZaUmtnSUR1TmJ0amFCM0RZUXlia3hzVCtHRFNmKzloTW81aHU0aS90L2NHRUdGb1JpSTc0NkhaRGhack5lSGhWaWFBVW1OSWZvTlp1bEZhblg2R2ErNFN0bDgyTnM4U29zSWFveERXRTYyYjZodHpURFROOEJ4WlRTM2REUHVNaU1hSElrOEg5cEh2QXBOQmkxYXhHdUs4cE4rbXliV2hRS3JnbWtLdDdKQWFEUEYiLCJtYWMiOiIxZTA0YjFkN2E5MThkZTk4MzBlNjIwODgxNDFhMmI1YTNlYzE3ZTBkYTBlZWM5MWRhMTdiZThiZTQ3NWU5YmRmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1339482629\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2065366428 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qD4uHjqBr5Yg0djWa5KbKMRBXnQzZzidgyqci1Ns</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xNV7SvMX3AgbiuDVO62eNXfryfZD4KbWr3rEIkXt</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2065366428\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-567288532 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 15:30:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkNyK2dJY3JoQjZ2VmV0aEFIek9DbWc9PSIsInZhbHVlIjoiOHQxVEp6NUNvbnpkVFlIMkhwREJ5VkxmT2JNZU1HYm55M21aMW5XS2I3bmhGell2d1BDdnRVcCtPRzVLUk11cWtoaE1yU3JnNDMycktmb0lCcnU4QUhWeGwvUUh3WFAyOXpFbFVmdTErUUF0cEI3eUE4elA0RTMvME95WlRlRWUwLytRNGljdjBKZWdGY1BVM1RDNWNzZklSSlplZEx5bHRjNkM2QnI4WUNhMlZ5bHAySWtEYWhCSURURW5nRFp5NU1RSkJ4dUttTmR2MHhYRW02RVJGRWZaVGJmRk9PS3dYVVFzVkVsZ2RTRFVzdmVjeHo3cC9MRjBvZmFMb1ZwNXpEMFluWS9PWUtGdktTRjZXdUJ0K2EwS3RDbS95S29qRno5M05qUWdXdkNZUXNLb3NUcmR2aHVzYkpPUC9IQTJVZUIwTFpBZjJWUEw5dytzNEx1aUJuYVlOalFsbEtmZjIzbWZRRkVYNUVLcUVzSHpZZXhOSVREdW91MitsTnFNWTQ0SXJSU1kwOXBxQ1dDSW1pYmpjWFIxZTErTXNTelJHcWJpQnNFM0FZbVIvY1lGWjBhazZYTitLUDNkQnNqMnZreGxza2lHTkRqZ1dIaDlESVJGRWZmV2gxbWJjOUZpcWYvZTZNZkpsOVpXaUJpY0J6MGtiTlRHU1Y0K3RIL1YiLCJtYWMiOiJlMWE2NjZmNjJlMzlmODRiOTJlYThkNGJlNTIyOTQyOGUzZDk4M2FiZjc5Nzk0NTEyNWJlMWU5ZTM1NzY5NWMzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:30:42 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InlBTktaK0ZmWG9pSWlZYlN3d0RDb3c9PSIsInZhbHVlIjoiYlA1K292V0RDZkhZTjhRL2llMFNKT3QzNzBoWStPaGlkRTFUMk5yMUVhK0FEdDl0L0dJTTlVUzdocmI5TndKNVl2QVZoT0V1WndiaEFNTUxEQitsS1JZWkpkQzM5ejE0OGF1b2oxNTk4YUFjZXp2Mys1a3h3VkFxQmdJSmUyNnA3dUtZRDN5RndQRFU4TSszTUVGU3JUNGtISm9hRDd0b1llbU80b1Z4V2ptWHdQRU9NeVBVdW9oZ1VwUmRGOFZYazdIcDRJbWovV3RaR3pQbXh6WlJVQm93RzVOc3VyM1ZMY204T0M1elhRNnBXRmZxK0lsSlJZVWR5eCtoMjNMajVVem90T2R6V2E3UlNBWElwdnM5ajF6dys1YkNMTWV4Q1MyWGZQZEVhejJTMmh3Y3NOTG1zOVVyUHl1UThlZVltQ3JOQ1FCeklCYXJjRWpQVERmYkRXU0NLY2VwNVhJVzJsQjB2cW9DUGM1VHNUbTRSYVplbXpCcmpOZmppdDlsNHFwVS9PME9NT0w3WmF4TlJ1YTg0YmtXS1NQaFY1Q0V5Z1poWGloMHI3Yit6MXEydlA5eFFwZEZKdjhkMW1NaEI4ZzV2VFhWd1VHOUNOU2NNcW54ZGN5eVA1Y1pRREpNYUY4MHRRRlJxMG1FWHBzOFE5aTA4WkJZU1dxRjdEdksiLCJtYWMiOiJjNTg1ZTQ1YmRlOGQwNmE4NjRlYjY4ZGM2ZDI2NWI1Y2Q1MTUxYTljZmY5Y2UzMWMwMmQ4NmNjZGI0MDQzNjZiIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 17:30:42 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkNyK2dJY3JoQjZ2VmV0aEFIek9DbWc9PSIsInZhbHVlIjoiOHQxVEp6NUNvbnpkVFlIMkhwREJ5VkxmT2JNZU1HYm55M21aMW5XS2I3bmhGell2d1BDdnRVcCtPRzVLUk11cWtoaE1yU3JnNDMycktmb0lCcnU4QUhWeGwvUUh3WFAyOXpFbFVmdTErUUF0cEI3eUE4elA0RTMvME95WlRlRWUwLytRNGljdjBKZWdGY1BVM1RDNWNzZklSSlplZEx5bHRjNkM2QnI4WUNhMlZ5bHAySWtEYWhCSURURW5nRFp5NU1RSkJ4dUttTmR2MHhYRW02RVJGRWZaVGJmRk9PS3dYVVFzVkVsZ2RTRFVzdmVjeHo3cC9MRjBvZmFMb1ZwNXpEMFluWS9PWUtGdktTRjZXdUJ0K2EwS3RDbS95S29qRno5M05qUWdXdkNZUXNLb3NUcmR2aHVzYkpPUC9IQTJVZUIwTFpBZjJWUEw5dytzNEx1aUJuYVlOalFsbEtmZjIzbWZRRkVYNUVLcUVzSHpZZXhOSVREdW91MitsTnFNWTQ0SXJSU1kwOXBxQ1dDSW1pYmpjWFIxZTErTXNTelJHcWJpQnNFM0FZbVIvY1lGWjBhazZYTitLUDNkQnNqMnZreGxza2lHTkRqZ1dIaDlESVJGRWZmV2gxbWJjOUZpcWYvZTZNZkpsOVpXaUJpY0J6MGtiTlRHU1Y0K3RIL1YiLCJtYWMiOiJlMWE2NjZmNjJlMzlmODRiOTJlYThkNGJlNTIyOTQyOGUzZDk4M2FiZjc5Nzk0NTEyNWJlMWU5ZTM1NzY5NWMzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:30:42 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InlBTktaK0ZmWG9pSWlZYlN3d0RDb3c9PSIsInZhbHVlIjoiYlA1K292V0RDZkhZTjhRL2llMFNKT3QzNzBoWStPaGlkRTFUMk5yMUVhK0FEdDl0L0dJTTlVUzdocmI5TndKNVl2QVZoT0V1WndiaEFNTUxEQitsS1JZWkpkQzM5ejE0OGF1b2oxNTk4YUFjZXp2Mys1a3h3VkFxQmdJSmUyNnA3dUtZRDN5RndQRFU4TSszTUVGU3JUNGtISm9hRDd0b1llbU80b1Z4V2ptWHdQRU9NeVBVdW9oZ1VwUmRGOFZYazdIcDRJbWovV3RaR3pQbXh6WlJVQm93RzVOc3VyM1ZMY204T0M1elhRNnBXRmZxK0lsSlJZVWR5eCtoMjNMajVVem90T2R6V2E3UlNBWElwdnM5ajF6dys1YkNMTWV4Q1MyWGZQZEVhejJTMmh3Y3NOTG1zOVVyUHl1UThlZVltQ3JOQ1FCeklCYXJjRWpQVERmYkRXU0NLY2VwNVhJVzJsQjB2cW9DUGM1VHNUbTRSYVplbXpCcmpOZmppdDlsNHFwVS9PME9NT0w3WmF4TlJ1YTg0YmtXS1NQaFY1Q0V5Z1poWGloMHI3Yit6MXEydlA5eFFwZEZKdjhkMW1NaEI4ZzV2VFhWd1VHOUNOU2NNcW54ZGN5eVA1Y1pRREpNYUY4MHRRRlJxMG1FWHBzOFE5aTA4WkJZU1dxRjdEdksiLCJtYWMiOiJjNTg1ZTQ1YmRlOGQwNmE4NjRlYjY4ZGM2ZDI2NWI1Y2Q1MTUxYTljZmY5Y2UzMWMwMmQ4NmNjZGI0MDQzNjZiIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 17:30:42 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-567288532\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1883077730 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WDYOqlhimZPr2RvnDCYApeOwlkeUoczMDEEfuGHP</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1883077730\", {\"maxDepth\":0})</script>\n"}}